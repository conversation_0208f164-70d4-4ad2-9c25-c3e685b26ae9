# プラグインシステム設定ガイド

このドキュメントでは、LLMプラットフォームのプラグインシステムの設定方法と技術的な実装の詳細について説明します。

## 目次

1. [概要](#概要)
2. [ENABLE_PLUGINSフラグ](#enable_pluginsフラグ)
   - [設定タイミングと優先順位](#設定タイミングと優先順位)
   - [保存形式](#保存形式)
   - [使用箇所](#使用箇所)
3. [技術的な実装詳細](#技術的な実装詳細)
4. [プラグインシステムの起動方法](#プラグインシステムの起動方法)
5. [トラブルシューティング](#トラブルシューティング)

## 概要

プラグインシステムは、LLMプラットフォームの機能を拡張するためのモジュラーなアーキテクチャを提供します。このシステムは、`ENABLE_PLUGINS`フラグによって制御され、有効または無効にすることができます。

## ENABLE_PLUGINSフラグ

`ENABLE_PLUGINS`は、プラグインシステムを有効にするかどうかを制御するブールフラグです。このフラグが`true`の場合、システムはプラグイン機能を読み込んで使用します。`false`の場合、システムは標準のチャットAPIを使用し、プラグイン機能は読み込まれません。

### 設定タイミングと優先順位

`ENABLE_PLUGINS`フラグは、複数のタイミングで設定できます。優先順位は以下の通りです（高いものから順に）：

1. **コマンドライン引数（最高優先度）**
   - `--enable-plugins`コマンドライン引数を使用して設定
   - `main.py`の224行目でこの引数を定義
   - ユーザーがこの引数でサーバーを起動すると、システムは227-230行目で環境変数`ENABLE_PLUGINS`を「true」に設定
   - これは他のソースからの設定を上書きします

2. **環境変数**
   - 環境変数`ENABLE_PLUGINS=true`を設定してプラグインシステムを有効化
   - `main.py`の42行目でこの環境変数を読み取り
   - 環境変数が存在しない場合、デフォルト値は「false」
   - 環境変数は`.env`ファイルで設定するか、サーバー起動前にコマンドラインで設定可能

3. **コードのデフォルト値（最低優先度）**
   - コマンドライン引数や環境変数で設定されていない場合、コード内のデフォルト値「false」を使用
   - `main.py`の42行目でデフォルト値を定義

```python
# プラグインシステム有効化フラグ
ENABLE_PLUGINS = settings.ENABLE_PLUGINS
```

`settings.ENABLE_PLUGINS`は、`config.py`の中で次のように定義されています：

```python
class Settings(BaseSettings):
    # ...
    ENABLE_PLUGINS: bool = False  # プラグインシステムの有効/無効
    # ...
```

### 保存形式

`ENABLE_PLUGINS`フラグは以下の形式で保存されます：

1. **設定クラス内のフィールド**
   - `Settings`クラス内の`ENABLE_PLUGINS`フィールドとして保存
   - デフォルト値は`False`
   - 環境変数から自動的に読み込まれます

2. **メモリ変数**
   - `main.py`の42行目で、`settings.ENABLE_PLUGINS`の値を`ENABLE_PLUGINS`変数に保存
   - このブール変数はアプリケーションのライフサイクル全体で不変

### 使用箇所

`ENABLE_PLUGINS`フラグは以下の場所で使用されます：

1. **ルーター登録**（main.pyの75-86行目）
   ```python
   # チャットとチャット履歴のAPI
   # プラグインシステムが有効な場合は、プラグイン対応のチャットAPIを使用
   if ENABLE_PLUGINS:
       logger.info("プラグインシステムを有効化しています")
       app.include_router(chat_api_with_plugins.router)
       # プラグイン管理APIを登録
       app.include_router(plugin_router)
       # サンプルプラグインを登録
       plugin_manager.register_plugin("simple_rag", SimpleRAGPlugin)
       plugin_manager.register_plugin("simple_agent", SimpleAgentPlugin)
       logger.info(f"登録されたプラグイン: {len(plugin_manager.get_all_plugins())}")
   else:
       app.include_router(chat_api.router)
   ```

2. **プラグインシステム初期化**（main.pyの121-132行目）
   ```python
   # プラグインシステムを初期化
   if ENABLE_PLUGINS:
       logger.info("プラグインシステムを初期化中...")
       # サンプルプラグインはすでにルーター登録時に登録されています

       # プラグインを検出
       plugin_manager.discover_plugins()

       logger.info(f"プラグインシステムの初期化が完了しました。登録済みプラグイン: {len(plugin_manager.get_all_plugins())}")
   else:
       logger.info("プラグインシステムは無効化されています")
       plugin_manager.disable_manager()
   ```

3. **コマンドライン引数処理**（main.pyの229-233行目）
   ```python
   # プラグインシステムを有効化する場合は設定を更新
   if args.enable_plugins:
       settings.ENABLE_PLUGINS = True
       ENABLE_PLUGINS = True
       logger.info("コマンドライン引数からプラグインシステムを有効化しました")
   ```

## 技術的な実装詳細

1. **Pydanticによる自動変換**
   - `Settings`クラスは`BaseSettings`を継承しており、環境変数から自動的に値を読み込みます
   - 環境変数`ENABLE_PLUGINS`が「true」の場合、`settings.ENABLE_PLUGINS`は`True`になります
   - Pydanticは大文字小文字を区別せず、「TRUE」、「True」、「true」はすべて`True`として解釈されます

2. **条件付きインポート**
   - `ENABLE_PLUGINS`の値に関係なく、プラグイン関連のモジュールはインポートされます（main.pyの24-27行目）
   - これはコードがコンパイルできるようにするためですが、フラグが`true`の場合にのみこれらのモジュールが使用されます

3. **条件付き初期化**
   - `ENABLE_PLUGINS`が`true`の場合にのみ、プラグインが初期化および登録されます（main.pyの76-84行目）
   - これにより、プラグイン機能が不要な場合に余分なリソースが消費されないようになります

4. **コマンドライン引数処理**
   - `argparse`ライブラリを使用してコマンドライン引数を処理します（main.pyの217-225行目）
   - `--enable-plugins`は値のないフラグ引数で、`action="store_true"`を使用して存在する場合は`true`を表します
   - このパラメータが検出されると、設定が更新され、ログが記録されます（main.pyの229-233行目）

## プラグインシステムの起動方法

プラグインシステムを有効にしてサーバーを起動するには、以下の方法があります：

1. **コマンドライン引数を使用**
   ```bash
   python run.py --enable-plugins
   ```

2. **環境変数を使用**
   ```bash
   # Linux/Mac
   export ENABLE_PLUGINS=true
   python run.py

   # Windows
   set ENABLE_PLUGINS=true
   python run.py
   ```

3. **.envファイルを使用**
   ```
   # .envファイルの内容
   ENABLE_PLUGINS=true
   ```

## トラブルシューティング

プラグインシステムに関する一般的な問題と解決策：

1. **プラグインが読み込まれない**
   - `ENABLE_PLUGINS`が正しく設定されているか確認してください
   - ログで「プラグインシステムを有効化しています」というメッセージを確認してください

2. **プラグインが見つからない**
   - プラグインが正しいディレクトリに配置されているか確認してください
   - `plugin_manager.discover_plugins()`が呼び出されているか確認してください

3. **プラグインが動作しない**
   - プラグインが正しく登録されているか確認してください
   - ログでエラーメッセージを確認してください
   - プラグインの依存関係が満たされているか確認してください

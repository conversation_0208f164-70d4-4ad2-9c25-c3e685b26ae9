# Plugin System Configuration Guide

This document explains how to configure the plugin system in the LLM platform and provides details about its technical implementation.

## Table of Contents

1. [Overview](#overview)
2. [ENABLE_PLUGINS Flag](#enable_plugins-flag)
   - [Configuration Timing and Priority](#configuration-timing-and-priority)
   - [Storage Format](#storage-format)
   - [Usage Locations](#usage-locations)
3. [Technical Implementation Details](#technical-implementation-details)
4. [How to Start the Plugin System](#how-to-start-the-plugin-system)
5. [Troubleshooting](#troubleshooting)

## Overview

The plugin system provides a modular architecture for extending the functionality of the LLM platform. This system is controlled by the `ENABLE_PLUGINS` flag, which can be enabled or disabled.

## ENABLE_PLUGINS Flag

`ENABLE_PLUGINS` is a boolean flag that controls whether the plugin system is enabled. When this flag is `true`, the system loads and uses plugin functionality; when it's `false`, the system uses the standard chat API and doesn't load plugin functionality.

### Configuration Timing and Priority

The `ENABLE_PLUGINS` flag can be set at multiple points, with the following priority (from highest to lowest):

1. **Command Line Arguments (Highest Priority)**
   - Set using the `--enable-plugins` command line argument
   - This argument is defined on line 224 of `main.py`
   - When a user starts the server with this argument, the system sets the environment variable `ENABLE_PLUGINS` to "true" on lines 227-230
   - This overrides any settings from other sources

2. **Environment Variables**
   - Enable the plugin system by setting the environment variable `ENABLE_PLUGINS=true`
   - This environment variable is read on line 42 of `main.py`
   - If the environment variable doesn't exist, the default value is "false"
   - The environment variable can be set in the `.env` file or on the command line before starting the server

3. **Code Default Value (Lowest Priority)**
   - If not set via command line arguments or environment variables, the code's default value "false" is used
   - The default value is defined on line 42 of `main.py`

```python
# Plugin system enable flag
ENABLE_PLUGINS = settings.ENABLE_PLUGINS
```

`settings.ENABLE_PLUGINS` is defined in `config.py` as follows:

```python
class Settings(BaseSettings):
    # ...
    ENABLE_PLUGINS: bool = False  # Enable/disable plugin system
    # ...
```

### Storage Format

The `ENABLE_PLUGINS` flag is stored in the following formats:

1. **Settings Class Field**
   - Stored as the `ENABLE_PLUGINS` field in the `Settings` class
   - Default value is `False`
   - Automatically loaded from environment variables

2. **Memory Variable**
   - On line 42 of `main.py`, the value of `settings.ENABLE_PLUGINS` is stored in the `ENABLE_PLUGINS` variable
   - This boolean variable remains constant throughout the application's lifecycle

### Usage Locations

The `ENABLE_PLUGINS` flag is used in the following locations:

1. **Router Registration** (lines 75-86)
   ```python
   # Chat and chat history API
   # If the plugin system is enabled, use the plugin-compatible chat API
   if ENABLE_PLUGINS:
       logger.info("Enabling plugin system")
       app.include_router(chat_api_with_plugins.router)
       # Register plugin management API
       app.include_router(plugin_router)
       # Register sample plugins
       plugin_manager.register_plugin("simple_rag", SimpleRAGPlugin)
       plugin_manager.register_plugin("simple_agent", SimpleAgentPlugin)
       logger.info(f"Registered plugins: {len(plugin_manager.get_all_plugins())}")
   else:
       app.include_router(chat_api.router)
   ```

2. **Plugin System Initialization** (lines 121-132)
   ```python
   # Initialize plugin system
   if ENABLE_PLUGINS:
       logger.info("Initializing plugin system...")
       # Sample plugins are already registered during router registration

       # Discover plugins
       plugin_manager.discover_plugins()

       logger.info(f"Plugin system initialization complete. Registered plugins: {len(plugin_manager.get_all_plugins())}")
   else:
       logger.info("Plugin system is disabled")
       plugin_manager.disable_manager()
   ```

3. **Command Line Argument Processing** (lines 229-233)
   ```python
   # Update settings if enabling plugin system
   if args.enable_plugins:
       settings.ENABLE_PLUGINS = True
       ENABLE_PLUGINS = True
       logger.info("Enabled plugin system from command line arguments")
   ```

## Technical Implementation Details

1. **Automatic Conversion by Pydantic**
   - The `Settings` class inherits from `BaseSettings`, which automatically loads values from environment variables
   - When the environment variable `ENABLE_PLUGINS` is "true", `settings.ENABLE_PLUGINS` becomes `True`
   - Pydantic is case-insensitive, so "TRUE", "True", and "true" are all interpreted as `True`

2. **Conditional Imports**
   - Plugin-related modules are imported regardless of the value of `ENABLE_PLUGINS` (lines 24-27)
   - This is to ensure the code can compile, but these modules are only used when the flag is `true`

3. **Conditional Initialization**
   - Plugins are only initialized and registered when `ENABLE_PLUGINS` is `true` (lines 76-84)
   - This ensures that no extra resources are consumed when plugin functionality is not needed

4. **Command Line Argument Processing**
   - The `argparse` library is used to process command line arguments (lines 217-225)
   - `--enable-plugins` is a valueless flag argument, using `action="store_true"` to indicate that its presence means `true`
   - When this parameter is detected, the environment variable is set and a log is recorded (lines 227-230)

## How to Start the Plugin System

There are several ways to start the server with the plugin system enabled:

1. **Using Command Line Arguments**
   ```bash
   python run.py --enable-plugins
   ```

2. **Using Environment Variables**
   ```bash
   # Linux/Mac
   export ENABLE_PLUGINS=true
   python run.py

   # Windows
   set ENABLE_PLUGINS=true
   python run.py
   ```

3. **Using the .env File**
   ```
   # Contents of .env file
   ENABLE_PLUGINS=true
   ```

## Troubleshooting

Common issues with the plugin system and their solutions:

1. **Plugins Not Loading**
   - Check that `ENABLE_PLUGINS` is set correctly
   - Look for the "Enabling plugin system" message in the logs

2. **Plugins Not Found**
   - Check that plugins are placed in the correct directory
   - Verify that `plugin_manager.discover_plugins()` is being called

3. **Plugins Not Working**
   - Check that plugins are registered correctly
   - Look for error messages in the logs
   - Verify that plugin dependencies are satisfied

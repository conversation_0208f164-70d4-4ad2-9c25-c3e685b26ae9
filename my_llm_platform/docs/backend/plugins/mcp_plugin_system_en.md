# MCP Plugin System

The MCP (Model Control Protocol) plugin system is a lightweight framework for creating and managing tools that can be integrated with various agent frameworks.

## Table of Contents

1. [Overview](#overview)
2. [Core Components](#core-components)
3. [Usage](#usage)
4. [Framework Integration](#framework-integration)
5. [Examples](#examples)
6. [Best Practices](#best-practices)

## Overview

The MCP plugin system provides a simple way to create tools that can be used by Large Language Models (LLMs), especially in agent systems. The system is designed with the following goals:

- **Simplicity**: Provide a clean API for creating and using tools
- **Framework Compatibility**: Compatible with mainstream agent frameworks (like LangChain, OpenAI, etc.)
- **Extensibility**: Easy to add new tools and features
- **Non-intrusive**: Does not modify standard LLM API calls

## Core Components

The MCP plugin system consists of the following core components:

### MCPTool

`MCPTool` is the basic unit of a tool, encapsulating a function and related metadata:

```python
class MCPTool:
    def __init__(
        self,
        name: str,
        description: str,
        func: Callable,
        parameters: Optional[Dict[str, Any]] = None
    ):
        # ...
```

### MCPToolRegistry

`MCPToolRegistry` is a global registry for registering and managing tools:

```python
class MCPToolRegistry:
    def register(self, tool: MCPTool) -> None:
        # ...
    
    def get(self, name: str) -> Optional[MCPTool]:
        # ...
    
    def list(self) -> List[str]:
        # ...
```

### Helper Functions

The system provides helper functions to simplify tool registration and usage:

```python
def register_tool(
    name: str,
    description: str,
    func: Callable,
    parameters: Optional[Dict[str, Any]] = None
) -> MCPTool:
    # ...

def get_tool(name: str) -> Optional[MCPTool]:
    # ...

def list_tools() -> List[str]:
    # ...
```

## Usage

### Registering Tools

Registering a tool is straightforward:

```python
from backend.plugins.mcp import register_tool

# Register a synchronous tool
def calculate(expression: str) -> Dict[str, Any]:
    result = eval(expression)
    return {"result": result}

register_tool(
    name="calculate",
    description="Calculate the result of a mathematical expression",
    func=calculate,
    parameters={
        "expression": {
            "type": "string",
            "description": "The expression to calculate",
            "required": True
        }
    }
)

# Register an asynchronous tool
async def get_weather(location: str) -> Dict[str, Any]:
    # ... asynchronously get weather information
    return {"temperature": 22, "condition": "Sunny"}

register_tool(
    name="get_weather",
    description="Get weather information for a location",
    func=get_weather,
    parameters={
        "location": {
            "type": "string",
            "description": "The location to get weather for",
            "required": True
        }
    }
)
```

### Using Tools

Using tools directly:

```python
from backend.plugins.mcp import get_tool

# Get a tool
weather_tool = get_tool("get_weather")

# Execute the tool
result = await weather_tool.execute(location="Tokyo")
print(result.result)  # {"temperature": 22, "condition": "Sunny"}
```

## Framework Integration

### OpenAI

Integration with OpenAI's tool calling API:

```python
from openai import OpenAI
from backend.plugins.mcp.registry import MCPToolRegistry

# Get OpenAI tool definitions
openai_tools = MCPToolRegistry().to_openai_tools()

# Create OpenAI client
client = OpenAI(api_key="your-api-key")

# Use tools
response = client.chat.completions.create(
    model="gpt-3.5-turbo",
    messages=[
        {"role": "user", "content": "What's the weather in Tokyo?"}
    ],
    tools=openai_tools,
    tool_choice="auto"
)
```

### LangChain

Integration with LangChain:

```python
from langchain.agents import AgentExecutor, create_openai_tools_agent
from langchain_openai import ChatOpenAI
from langchain_core.prompts import ChatPromptTemplate
from backend.plugins.mcp.registry import MCPToolRegistry

# Get LangChain tools
langchain_tools = MCPToolRegistry().to_langchain_tools()

# Create LLM
llm = ChatOpenAI(model="gpt-3.5-turbo")

# Create prompt
prompt = ChatPromptTemplate.from_messages([
    ("system", "You are a helpful assistant that can use tools to answer user questions."),
    ("human", "{input}")
])

# Create agent
agent = create_openai_tools_agent(llm, langchain_tools, prompt)
agent_executor = AgentExecutor(agent=agent, tools=langchain_tools)

# Run agent
result = agent_executor.invoke({"input": "What's the weather in Tokyo?"})
```

## Examples

### Example Tools

The system provides example tools for testing and learning:

```python
from backend.plugins.mcp.examples import register_example_tools

# Register example tools
register_example_tools()
```

Example tools include:

- `get_weather`: Get weather information
- `calculate`: Calculate mathematical expressions
- `search`: Search for information
- `fetch_url`: Fetch URL content

### Running Examples

You can run the example script to see how MCP tools are used:

```bash
python examples/mcp_examples.py
```

## Best Practices

### Tool Design

- **Keep it Simple**: Each tool should do one thing and do it well
- **Clear Description**: Provide clear descriptions and parameter documentation
- **Error Handling**: Handle errors gracefully and return useful error messages
- **Async Support**: Use async functions when possible, especially for I/O-bound operations

### Integration Tips

- **Early Registration**: Register tools at application startup
- **Dynamic Discovery**: Consider implementing dynamic tool discovery
- **Versioning**: Add version information to tools
- **Monitoring and Logging**: Log tool usage and performance

### Security Considerations

- **Input Validation**: Validate tool input parameters
- **Permission Control**: Implement permission control for tools
- **Resource Limits**: Limit resource usage by tools
- **Audit Logging**: Log tool usage for auditing

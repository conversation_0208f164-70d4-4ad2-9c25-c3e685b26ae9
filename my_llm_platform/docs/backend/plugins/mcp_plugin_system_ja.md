# MCP プラグインシステム

MCP（Model Control Protocol）プラグインシステムは、様々なエージェントフレームワークと統合できるツールを作成・管理するための軽量フレームワークです。

## 目次

1. [概要](#概要)
2. [コアコンポーネント](#コアコンポーネント)
3. [使用方法](#使用方法)
4. [フレームワーク統合](#フレームワーク統合)
5. [サンプル](#サンプル)
6. [ベストプラクティス](#ベストプラクティス)

## 概要

MCPプラグインシステムは、大規模言語モデル（LLM）、特にエージェントシステムで使用できるツールを作成するためのシンプルな方法を提供します。このシステムは以下の目標で設計されています：

- **シンプルさ**：ツールの作成と使用のためのクリーンなAPIを提供
- **フレームワーク互換性**：主流のエージェントフレームワーク（LangChain、OpenAIなど）と互換性がある
- **拡張性**：新しいツールや機能を簡単に追加できる
- **非侵入的**：標準のLLM APIコールを変更しない

## コアコンポーネント

MCPプラグインシステムは以下のコアコンポーネントで構成されています：

### MCPTool

`MCPTool`はツールの基本単位で、関数と関連するメタデータをカプセル化します：

```python
class MCPTool:
    def __init__(
        self,
        name: str,
        description: str,
        func: Callable,
        parameters: Optional[Dict[str, Any]] = None
    ):
        # ...
```

### MCPToolRegistry

`MCPToolRegistry`はツールを登録・管理するためのグローバルレジストリです：

```python
class MCPToolRegistry:
    def register(self, tool: MCPTool) -> None:
        # ...
    
    def get(self, name: str) -> Optional[MCPTool]:
        # ...
    
    def list(self) -> List[str]:
        # ...
```

### ヘルパー関数

システムはツールの登録と使用を簡素化するヘルパー関数を提供します：

```python
def register_tool(
    name: str,
    description: str,
    func: Callable,
    parameters: Optional[Dict[str, Any]] = None
) -> MCPTool:
    # ...

def get_tool(name: str) -> Optional[MCPTool]:
    # ...

def list_tools() -> List[str]:
    # ...
```

## 使用方法

### ツールの登録

ツールの登録は簡単です：

```python
from backend.plugins.mcp import register_tool

# 同期ツールを登録
def calculate(expression: str) -> Dict[str, Any]:
    result = eval(expression)
    return {"result": result}

register_tool(
    name="calculate",
    description="数式の計算結果を取得",
    func=calculate,
    parameters={
        "expression": {
            "type": "string",
            "description": "計算する数式",
            "required": True
        }
    }
)

# 非同期ツールを登録
async def get_weather(location: str) -> Dict[str, Any]:
    # ... 非同期で天気情報を取得
    return {"temperature": 22, "condition": "晴れ"}

register_tool(
    name="get_weather",
    description="指定された場所の天気情報を取得",
    func=get_weather,
    parameters={
        "location": {
            "type": "string",
            "description": "天気を取得する場所",
            "required": True
        }
    }
)
```

### ツールの使用

ツールを直接使用する：

```python
from backend.plugins.mcp import get_tool

# ツールを取得
weather_tool = get_tool("get_weather")

# ツールを実行
result = await weather_tool.execute(location="東京")
print(result.result)  # {"temperature": 22, "condition": "晴れ"}
```

## フレームワーク統合

### OpenAI

OpenAIのツール呼び出しAPIとの統合：

```python
from openai import OpenAI
from backend.plugins.mcp.registry import MCPToolRegistry

# OpenAIツール定義を取得
openai_tools = MCPToolRegistry().to_openai_tools()

# OpenAIクライアントを作成
client = OpenAI(api_key="your-api-key")

# ツールを使用
response = client.chat.completions.create(
    model="gpt-3.5-turbo",
    messages=[
        {"role": "user", "content": "東京の天気は？"}
    ],
    tools=openai_tools,
    tool_choice="auto"
)
```

### LangChain

LangChainとの統合：

```python
from langchain.agents import AgentExecutor, create_openai_tools_agent
from langchain_openai import ChatOpenAI
from langchain_core.prompts import ChatPromptTemplate
from backend.plugins.mcp.registry import MCPToolRegistry

# LangChainツールを取得
langchain_tools = MCPToolRegistry().to_langchain_tools()

# LLMを作成
llm = ChatOpenAI(model="gpt-3.5-turbo")

# プロンプトを作成
prompt = ChatPromptTemplate.from_messages([
    ("system", "あなたはツールを使用してユーザーの質問に答えるヘルプフルなアシスタントです。"),
    ("human", "{input}")
])

# エージェントを作成
agent = create_openai_tools_agent(llm, langchain_tools, prompt)
agent_executor = AgentExecutor(agent=agent, tools=langchain_tools)

# エージェントを実行
result = agent_executor.invoke({"input": "東京の天気は？"})
```

## サンプル

### サンプルツール

システムはテストと学習のためのサンプルツールを提供しています：

```python
from backend.plugins.mcp.examples import register_example_tools

# サンプルツールを登録
register_example_tools()
```

サンプルツールには以下が含まれます：

- `get_weather`：天気情報を取得
- `calculate`：数式を計算
- `search`：情報を検索
- `fetch_url`：URLコンテンツを取得

### サンプルの実行

サンプルスクリプトを実行して、MCPツールの使用方法を確認できます：

```bash
python examples/mcp_examples.py
```

## ベストプラクティス

### ツール設計

- **シンプルに保つ**：各ツールは一つのことをうまく行うべき
- **明確な説明**：明確な説明とパラメータドキュメントを提供する
- **エラー処理**：エラーを適切に処理し、有用なエラーメッセージを返す
- **非同期サポート**：可能な限り非同期関数を使用する（特にI/O処理の多い操作）

### 統合のヒント

- **早期登録**：アプリケーション起動時にツールを登録する
- **動的発見**：動的ツール発見の実装を検討する
- **バージョン管理**：ツールにバージョン情報を追加する
- **モニタリングとログ記録**：ツールの使用状況とパフォーマンスを記録する

### セキュリティ考慮事項

- **入力検証**：ツール入力パラメータを検証する
- **権限制御**：ツールの権限制御を実装する
- **リソース制限**：ツールによるリソース使用を制限する
- **監査ログ**：監査のためにツール使用を記録する

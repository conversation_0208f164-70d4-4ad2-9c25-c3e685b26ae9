# MCP 插件系统

MCP (Model Control Protocol) 插件系统是一个轻量级框架，用于创建和管理可以与各种 agent 框架集成的工具。

## 目录

1. [概述](#概述)
2. [核心组件](#核心组件)
3. [使用方法](#使用方法)
4. [与框架集成](#与框架集成)
5. [示例](#示例)
6. [最佳实践](#最佳实践)

## 概述

MCP 插件系统提供了一种简单的方式来创建工具，这些工具可以被大型语言模型 (LLM) 使用，特别是在 agent 系统中。该系统的设计目标是：

- **简单易用**：提供简洁的 API 来创建和使用工具
- **框架兼容**：与主流 agent 框架（如 LangChain、OpenAI 等）兼容
- **可扩展**：容易添加新的工具和功能
- **非侵入式**：不修改标准的 LLM API 调用

## 核心组件

MCP 插件系统由以下核心组件组成：

### MCPTool

`MCPTool` 是工具的基本单位，它封装了一个函数和相关的元数据：

```python
class MCPTool:
    def __init__(
        self,
        name: str,
        description: str,
        func: Callable,
        parameters: Optional[Dict[str, Any]] = None
    ):
        # ...
```

### MCPToolRegistry

`MCPToolRegistry` 是一个全局注册表，用于注册和管理工具：

```python
class MCPToolRegistry:
    def register(self, tool: MCPTool) -> None:
        # ...
    
    def get(self, name: str) -> Optional[MCPTool]:
        # ...
    
    def list(self) -> List[str]:
        # ...
```

### 辅助函数

系统提供了一些辅助函数，简化工具的注册和使用：

```python
def register_tool(
    name: str,
    description: str,
    func: Callable,
    parameters: Optional[Dict[str, Any]] = None
) -> MCPTool:
    # ...

def get_tool(name: str) -> Optional[MCPTool]:
    # ...

def list_tools() -> List[str]:
    # ...
```

## 使用方法

### 注册工具

注册一个工具非常简单：

```python
from backend.plugins.mcp import register_tool

# 注册一个同步工具
def calculate(expression: str) -> Dict[str, Any]:
    result = eval(expression)
    return {"result": result}

register_tool(
    name="calculate",
    description="Calculate the result of a mathematical expression",
    func=calculate,
    parameters={
        "expression": {
            "type": "string",
            "description": "The expression to calculate",
            "required": True
        }
    }
)

# 注册一个异步工具
async def get_weather(location: str) -> Dict[str, Any]:
    # ... 异步获取天气信息
    return {"temperature": 22, "condition": "Sunny"}

register_tool(
    name="get_weather",
    description="Get weather information for a location",
    func=get_weather,
    parameters={
        "location": {
            "type": "string",
            "description": "The location to get weather for",
            "required": True
        }
    }
)
```

### 使用工具

直接使用工具：

```python
from backend.plugins.mcp import get_tool

# 获取工具
weather_tool = get_tool("get_weather")

# 执行工具
result = await weather_tool.execute(location="Tokyo")
print(result.result)  # {"temperature": 22, "condition": "Sunny"}
```

## 与框架集成

### OpenAI

与 OpenAI 的工具调用 API 集成：

```python
from openai import OpenAI
from backend.plugins.mcp.registry import MCPToolRegistry

# 获取 OpenAI 工具定义
openai_tools = MCPToolRegistry().to_openai_tools()

# 创建 OpenAI 客户端
client = OpenAI(api_key="your-api-key")

# 使用工具
response = client.chat.completions.create(
    model="gpt-3.5-turbo",
    messages=[
        {"role": "user", "content": "What's the weather in Tokyo?"}
    ],
    tools=openai_tools,
    tool_choice="auto"
)
```

### LangChain

与 LangChain 集成：

```python
from langchain.agents import AgentExecutor, create_openai_tools_agent
from langchain_openai import ChatOpenAI
from langchain_core.prompts import ChatPromptTemplate
from backend.plugins.mcp.registry import MCPToolRegistry

# 获取 LangChain 工具
langchain_tools = MCPToolRegistry().to_langchain_tools()

# 创建 LLM
llm = ChatOpenAI(model="gpt-3.5-turbo")

# 创建提示
prompt = ChatPromptTemplate.from_messages([
    ("system", "You are a helpful assistant that can use tools to answer user questions."),
    ("human", "{input}")
])

# 创建 agent
agent = create_openai_tools_agent(llm, langchain_tools, prompt)
agent_executor = AgentExecutor(agent=agent, tools=langchain_tools)

# 运行 agent
result = agent_executor.invoke({"input": "What's the weather in Tokyo?"})
```

## 示例

### 示例工具

系统提供了一些示例工具，可以用于测试和学习：

```python
from backend.plugins.mcp.examples import register_example_tools

# 注册示例工具
register_example_tools()
```

示例工具包括：

- `get_weather`：获取天气信息
- `calculate`：计算数学表达式
- `search`：搜索信息
- `fetch_url`：获取 URL 内容

### 运行示例

可以运行示例脚本来查看 MCP 工具的使用方法：

```bash
python examples/mcp_examples.py
```

## 最佳实践

### 工具设计

- **保持简单**：每个工具应该只做一件事，并且做好
- **清晰描述**：提供清晰的描述和参数说明
- **错误处理**：妥善处理错误，返回有用的错误信息
- **异步支持**：尽可能使用异步函数，特别是对于 I/O 密集型操作

### 集成建议

- **早期注册**：在应用启动时注册工具
- **动态发现**：考虑实现动态工具发现机制
- **版本控制**：为工具添加版本信息
- **监控和日志**：记录工具的使用情况和性能

### 安全考虑

- **输入验证**：验证工具的输入参数
- **权限控制**：实现工具的权限控制
- **资源限制**：限制工具的资源使用
- **审计日志**：记录工具的使用情况

# Simple MCP Plugin

## Overview

The Simple MCP (Model Control Protocol) Plugin provides a framework for the LLM to perform actions through a standardized protocol. This plugin allows the LLM to use external tools and APIs by generating structured action requests.

## Features

- Standardized action protocol
- Multiple action types
- Structured input/output format
- Thought process visibility

## Configuration

The plugin supports the following configuration options:

| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `show_thoughts` | Boolean | true | Whether to show the LLM's thought process |
| `auto_execute` | Boolean | true | Whether to automatically execute actions |
| `max_actions` | Integer | 5 | Maximum number of actions per message |
| `timeout_seconds` | Integer | 30 | Maximum time allowed for action execution |

## Available Actions

The plugin provides the following actions:

1. **get_weather** - Get weather information for a specified location
   - Input: `location` (string)
   - Output: Weather data including temperature, condition, humidity, etc.

2. **calculate** - Perform mathematical calculations
   - Input: `expression` (string)
   - Output: Calculation result

3. **search** - Search for information
   - Input: `query` (string)
   - Output: Search results with titles and snippets

## Usage

The LLM can use MCP actions by generating special markup in its response:

```
[[MCP:{"type":"action","name":"get_weather","input":{"location":"Tokyo"},"thought":"I need to check the weather in Tokyo."}]]
```

The plugin will:
1. Detect the MCP markup
2. Extract the action details
3. Execute the action
4. Replace the markup with the action result

## Example

**User Input:**
```
What's the weather like in Tokyo today?
```

**LLM Response with MCP:**
```
Let me check the weather in Tokyo for you.

[[MCP:{"type":"action","name":"get_weather","input":{"location":"Tokyo"},"thought":"I need to get the current weather information for Tokyo."}]]

Based on the information I found, Tokyo is currently experiencing a temperature of 22°C with sunny conditions. The humidity is at 65% with a light breeze of 10 km/h.
```

**Final Response to User:**
The markup is replaced with the actual weather data, providing an accurate and up-to-date response.

## Technical Details

The MCP format follows this structure:
```json
{
  "type": "action",
  "name": "action_name",
  "input": {
    "param1": "value1",
    "param2": "value2"
  },
  "thought": "Reasoning behind the action"
}
```

The plugin processes this format, executes the appropriate action, and returns the results.

## Limitations

- Actions are limited to those registered with the plugin
- Complex actions may time out
- The LLM must generate the correct MCP format
- Some actions may require external API access

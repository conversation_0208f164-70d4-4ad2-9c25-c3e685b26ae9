# Supabase ベクトルデータベース統合

このドキュメントでは、My LLM Platformにおける[Supabase](https://supabase.com/)ベクトルデータベース（pgvector）の統合について説明します。

## 概要

Supabaseは、PostgreSQLデータベースとRESTful APIを提供するオープンソースのFirebase代替です。Supabaseは[pgvector](https://github.com/pgvector/pgvector)拡張機能を使用してベクトル検索機能を提供しており、これを利用してRAG（検索拡張生成）システムを構築できます。

My LLM Platformでは、Supabaseのベクトルデータベース機能を他のベクトルデータベース（FAISS、Qdrant、Weaviate）と同様に使用できます。

## 特徴

- PostgreSQLベースのベクトルデータベース
- 複数の距離メトリック（コサイン類似度、ユークリッド距離、内積）をサポート
- メタデータフィルタリング
- インデックス作成によるクエリパフォーマンスの向上
- 追加（append）操作のサポート
- Python用の`vecs`クライアントライブラリ

## 設定

Supabaseベクトルデータベースを使用するには、以下の設定が必要です：

1. `.env`ファイルで以下の設定を行います：

```
# ベクトルデータベースタイプをsupabaseに設定
VECTOR_DATABASE_TYPE=supabase

# Supabase PostgreSQL接続文字列
SUPABASE_CONNECTION_STRING=postgresql://postgres:password@localhost:54322/postgres

# Supabaseの有効/無効設定
ENABLE_SUPABASE=true
```

2. Supabaseのローカル開発環境をセットアップするか、Supabase Cloudを使用します。

## 依存関係

Supabaseベクトルデータベースを使用するには、以下のパッケージをインストールする必要があります：

```bash
pip install vecs langchain-postgres
```

## 使用方法

### ベクトルストアの初期化

```python
from backend.plugins.rag.vector_factory import VectorStoreFactory

# Supabaseベクトルストアを作成
vector_store = VectorStoreFactory.create_vector_store(
    vector_store_type="supabase",
    collection_name="my_documents",
    model_name="all-MiniLM-L6-v2"  # 埋め込みモデル名
)

# コレクションを作成（既存の場合は再利用）
vector_store.create_collection()
```

### ドキュメントの追加

```python
# テキストとメタデータを追加
texts = [
    "これは最初のドキュメントです。",
    "これは2番目のドキュメントです。",
    "これは3番目のドキュメントです。"
]

metadata = [
    {"source": "file1.txt", "category": "general"},
    {"source": "file2.txt", "category": "general"},
    {"source": "file3.txt", "category": "specific"}
]

vector_store.add_texts(texts, metadata)
```

### 検索

```python
# テキストで検索
results = vector_store.search("ドキュメントについて教えてください", top_k=3)

# フィルタ付き検索
filtered_results = vector_store.search(
    "ドキュメントについて教えてください", 
    top_k=3, 
    filter_params={"category": "specific"}
)
```

### インデックスの作成

```python
# Supabaseのvecsクライアントを使用してインデックスを作成
if hasattr(vector_store.collection, 'create_index'):
    vector_store.collection.create_index()
```

## Supabaseのセットアップ

### ローカル開発環境

1. Supabase CLIをインストールします：

```bash
npm install -g supabase
```

2. Supabaseプロジェクトを初期化します：

```bash
supabase init
```

3. ローカルのSupabaseを起動します：

```bash
supabase start
```

4. pgvector拡張機能を有効にします：

```sql
CREATE EXTENSION IF NOT EXISTS vector;
```

### Supabase Cloud

1. [Supabase](https://supabase.com/)でアカウントを作成し、新しいプロジェクトを作成します。
2. SQLエディタで以下のコマンドを実行して、pgvector拡張機能を有効にします：

```sql
CREATE EXTENSION IF NOT EXISTS vector;
```

3. 接続文字列を取得し、`.env`ファイルの`SUPABASE_CONNECTION_STRING`に設定します。

## 制限事項

- Supabaseベクトルデータベースを使用するには、PostgreSQLデータベースへの接続が必要です。
- 大量のデータを扱う場合は、適切なインデックスを作成することが重要です。
- ベクトルの次元数が多い場合、パフォーマンスが低下する可能性があります。

## トラブルシューティング

- **接続エラー**: 接続文字列が正しいことを確認してください。
- **pgvector拡張機能エラー**: PostgreSQLデータベースでpgvector拡張機能が有効になっていることを確認してください。
- **インデックスエラー**: 大量のデータを扱う場合は、適切なインデックスを作成してください。

## 参考リンク

- [Supabase公式ドキュメント](https://supabase.com/docs)
- [pgvector GitHub](https://github.com/pgvector/pgvector)
- [Supabase Vector Columns](https://supabase.com/docs/guides/ai/vector-columns)
- [vecs Python Client](https://supabase.github.io/vecs/)

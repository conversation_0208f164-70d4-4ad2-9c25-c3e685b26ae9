# Database Management Tool

This document explains how to use the database management tool (`db_cli.py`) in the LLM Platform and provides details about its technical implementation.

## Table of Contents

1. [Overview](#overview)
2. [Command Line Interface](#command-line-interface)
3. [Usage Examples](#usage-examples)
4. [Technical Implementation](#technical-implementation)
5. [Troubleshooting](#troubleshooting)

## Overview

`db_cli.py` is a command-line tool for managing the database in the LLM Platform. You can use this tool to perform operations such as initializing the database, exporting data, and importing data.

Main features:
- Database initialization
- Exporting database data to a JSON file
- Importing data from a JSON file into the database

## Command Line Interface

`db_cli.py` supports the following command-line options:

```
usage: db_cli.py [-h] [--file FILE] {init,export,import}

Database Management Tool

positional arguments:
  {init,export,import}  Operation type: init, export, or import

optional arguments:
  -h, --help            show this help message and exit
  --file FILE           Path to the import/export file
```

### Commands

- `init`: Initializes the database. Creates tables if they don't exist and adds a demo user.
- `export`: Exports database data to a JSON file.
- `import`: Imports data from a JSON file into the database.

### Options

- `--file`: Specifies the path to the JSON file for export/import operations. Default is `database_export.json`.

## Usage Examples

### Initializing the Database

```bash
python db_cli.py init
```

This command initializes the database and creates the necessary tables (users, chat_sessions, chat_messages). It also creates a demo user.

### Exporting Data

```bash
python db_cli.py export --file backup.json
```

This command exports the contents of the database to the specified JSON file (in this example, `backup.json`). If no file name is specified, the default `database_export.json` is used.

### Importing Data

```bash
python db_cli.py import --file backup.json
```

This command imports data from the specified JSON file (in this example, `backup.json`) into the database.

## Technical Implementation

The `db_cli.py` tool is implemented in the following files:

1. **`db_cli.py` in the project root**: Entry point script
2. **`backend/db/db_cli.py`**: Actual implementation

### File Structure

```
my_llm_platform/
├── db_cli.py                  # Entry point script
└── backend/
    └── db/
        ├── __init__.py
        ├── database.py        # Database models and connection
        ├── db_cli.py          # Database management tool implementation
        └── db_manager.py      # Database management functions
```

### Implementation Details

The `db_cli.py` tool uses the following functions from the `backend/db/db_manager.py` module:

- `init_database()`: Initializes the database
- `export_data()`: Exports database data to a JSON file
- `import_data()`: Imports data from a JSON file into the database

These functions use SQLAlchemy to perform database operations.

## Troubleshooting

### Common Issues

1. **Database Connection Errors**
   - Ensure that database connection information is correctly set in the configuration file (`.env`)
   - Verify that the database server is running

2. **Permission Errors**
   - Ensure that you have write permissions for the database file and export files

3. **Import Errors**
   - Verify that the export file format is correct
   - Ensure that the database schema matches the structure of the export data

### Checking Logs

If you encounter issues, check the log messages. The `db_cli.py` tool outputs detailed log messages during operations.

```bash
# Set environment variable for more detailed logs
export LOGLEVEL=DEBUG
python db_cli.py init
```

# データベース管理ツール

このドキュメントでは、LLMプラットフォームのデータベース管理ツール（`db_cli.py`）の使用方法と技術的な実装について説明します。

## 目次

1. [概要](#概要)
2. [コマンドラインインターフェース](#コマンドラインインターフェース)
3. [使用例](#使用例)
4. [技術的な実装](#技術的な実装)
5. [トラブルシューティング](#トラブルシューティング)

## 概要

`db_cli.py`は、LLMプラットフォームのデータベースを管理するためのコマンドラインツールです。このツールを使用して、データベースの初期化、データのエクスポート、データのインポートなどの操作を実行できます。

主な機能：
- データベースの初期化
- データベースデータのJSONファイルへのエクスポート
- JSONファイルからデータベースへのデータのインポート

## コマンドラインインターフェース

`db_cli.py`は以下のコマンドラインオプションをサポートしています：

```
usage: db_cli.py [-h] [--file FILE] {init,export,import}

データベース管理ツール

positional arguments:
  {init,export,import}  操作タイプ: init (初期化), export (エクスポート), import (インポート)

optional arguments:
  -h, --help            show this help message and exit
  --file FILE           インポート/エクスポートファイルのパス
```

### コマンド

- `init`: データベースを初期化します。既存のテーブルが存在しない場合は作成し、デモユーザーを追加します。
- `export`: データベースのデータをJSONファイルにエクスポートします。
- `import`: JSONファイルからデータをデータベースにインポートします。

### オプション

- `--file`: エクスポート/インポート操作で使用するJSONファイルのパスを指定します。デフォルトは`database_export.json`です。

## 使用例

### データベースの初期化

```bash
# プロジェクトルートから実行する場合
python db_cli.py init

# 推奨：backend/dbディレクトリから実行する場合
python -m backend.db.db_cli init
```

このコマンドは、データベースを初期化し、必要なテーブル（users、chat_sessions、chat_messages）を作成します。また、デモユーザーも作成されます。

### データのエクスポート

```bash
# プロジェクトルートから実行する場合
python db_cli.py export --file backup.json

# 推奨：backend/dbディレクトリから実行する場合
python -m backend.db.db_cli export --file backup.json
```

このコマンドは、データベースの内容を指定されたJSONファイル（この例では`backup.json`）にエクスポートします。ファイル名を指定しない場合、デフォルトの`database_export.json`が使用されます。

### データのインポート

```bash
# プロジェクトルートから実行する場合
python db_cli.py import --file backup.json

# 推奨：backend/dbディレクトリから実行する場合
python -m backend.db.db_cli import --file backup.json
```

このコマンドは、指定されたJSONファイル（この例では`backup.json`）からデータをデータベースにインポートします。

## 技術的な実装

`db_cli.py`ツールは、以下のファイルで実装されています：

1. **プロジェクトルートの`db_cli.py`**: エントリーポイントスクリプト（レガシー、将来削除予定）
2. **`backend/db/db_cli.py`**: 推奨される実装

### ファイル構造

```
my_llm_platform/
├── db_cli.py                  # エントリーポイントスクリプト
└── backend/
    └── db/
        ├── __init__.py
        ├── database.py        # データベースモデルと接続
        ├── db_cli.py          # データベース管理ツールの実装
        └── db_manager.py      # データベース管理機能
```

### 実装の詳細

`db_cli.py`ツールは、`backend/db/db_manager.py`モジュールの以下の関数を使用しています：

- `init_database()`: データベースを初期化します
- `export_data()`: データベースのデータをJSONファイルにエクスポートします
- `import_data()`: JSONファイルからデータをデータベースにインポートします

これらの関数は、SQLAlchemyを使用してデータベース操作を実行します。

## トラブルシューティング

### 一般的な問題

1. **データベース接続エラー**
   - 設定ファイル（`.env`）でデータベース接続情報が正しく設定されていることを確認してください
   - データベースサーバーが実行中であることを確認してください

2. **権限エラー**
   - データベースファイルやエクスポートファイルへの書き込み権限があることを確認してください

3. **インポートエラー**
   - エクスポートファイルの形式が正しいことを確認してください
   - データベーススキーマとエクスポートデータの構造が一致していることを確認してください

### ログの確認

問題が発生した場合は、ログメッセージを確認してください。`db_cli.py`ツールは、操作中に詳細なログメッセージを出力します。

```bash
# より詳細なログを表示するには、環境変数を設定します
export LOGLEVEL=DEBUG

# プロジェクトルートから実行する場合
python db_cli.py init

# 推奨：backend/dbディレクトリから実行する場合
python -m backend.db.db_cli init
```

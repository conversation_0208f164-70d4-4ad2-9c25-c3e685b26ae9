# 数据库管理工具

本文档说明如何使用LLM平台的数据库管理工具（`db_cli.py`），并提供其技术实现的详细信息。

## 目录

1. [概述](#概述)
2. [命令行界面](#命令行界面)
3. [使用示例](#使用示例)
4. [技术实现](#技术实现)
5. [故障排除](#故障排除)

## 概述

`db_cli.py`是一个用于管理LLM平台数据库的命令行工具。您可以使用此工具执行初始化数据库、导出数据和导入数据等操作。

主要功能：
- 数据库初始化
- 将数据库数据导出到JSON文件
- 从JSON文件导入数据到数据库

## 命令行界面

`db_cli.py`支持以下命令行选项：

```
usage: db_cli.py [-h] [--file FILE] {init,export,import}

数据库管理工具

positional arguments:
  {init,export,import}  操作类型：init（初始化）、export（导出）或import（导入）

optional arguments:
  -h, --help            显示帮助信息并退出
  --file FILE           导入/导出文件的路径
```

### 命令

- `init`：初始化数据库。如果表不存在，则创建表并添加演示用户。
- `export`：将数据库数据导出到JSON文件。
- `import`：从JSON文件导入数据到数据库。

### 选项

- `--file`：指定导出/导入操作使用的JSON文件路径。默认为`database_export.json`。

## 使用示例

### 初始化数据库

```bash
python db_cli.py init
```

此命令初始化数据库并创建必要的表（users、chat_sessions、chat_messages）。它还会创建一个演示用户。

### 导出数据

```bash
python db_cli.py export --file backup.json
```

此命令将数据库内容导出到指定的JSON文件（在此示例中为`backup.json`）。如果未指定文件名，则使用默认的`database_export.json`。

### 导入数据

```bash
python db_cli.py import --file backup.json
```

此命令从指定的JSON文件（在此示例中为`backup.json`）导入数据到数据库。

## 技术实现

`db_cli.py`工具在以下文件中实现：

1. **项目根目录中的`db_cli.py`**：入口点脚本
2. **`backend/db/db_cli.py`**：实际实现

### 文件结构

```
my_llm_platform/
├── db_cli.py                  # 入口点脚本
└── backend/
    └── db/
        ├── __init__.py
        ├── database.py        # 数据库模型和连接
        ├── db_cli.py          # 数据库管理工具实现
        └── db_manager.py      # 数据库管理功能
```

### 实现详情

`db_cli.py`工具使用`backend/db/db_manager.py`模块中的以下函数：

- `init_database()`：初始化数据库
- `export_data()`：将数据库数据导出到JSON文件
- `import_data()`：从JSON文件导入数据到数据库

这些函数使用SQLAlchemy执行数据库操作。

## 故障排除

### 常见问题

1. **数据库连接错误**
   - 确保在配置文件（`.env`）中正确设置了数据库连接信息
   - 验证数据库服务器是否正在运行

2. **权限错误**
   - 确保您对数据库文件和导出文件有写入权限

3. **导入错误**
   - 验证导出文件格式是否正确
   - 确保数据库架构与导出数据的结构匹配

### 检查日志

如果遇到问题，请检查日志消息。`db_cli.py`工具在操作过程中会输出详细的日志消息。

```bash
# 设置环境变量以获取更详细的日志
export LOGLEVEL=DEBUG
python db_cli.py init
```

# LLM Platform Backend Documentation

This directory contains documentation for the backend components of the LLM Platform.

## Available Documentation

### Configuration

- [Plugin System Configuration (日本語)](./plugins/plugin_system_configuration.md) - Details about the plugin system configuration and the ENABLE_PLUGINS flag
- [Plugin System Configuration (English)](./plugins/plugin_system_configuration_en.md) - English version of the plugin system configuration
- [Plugin System Configuration (中文)](./plugins/plugin_system_configuration_zh.md) - Chinese version of the plugin system configuration

### Database Management

- [Database Management Tool (日本語)](./database/database_management.md) - Guide for using the database management tool (db_cli.py)
- [Database Management Tool (English)](./database/database_management_en.md) - English version of the database management guide
- [Database Management Tool (中文)](./database/database_management_zh.md) - Chinese version of the database management guide

### Plugins

- [MCP Plugin System (日本語)](./plugins/mcp_plugin_system_ja.md) - Guide for the Model Control Protocol (MCP) plugin system
- [MCP Plugin System (English)](./plugins/mcp_plugin_system_en.md) - English version of the MCP plugin system guide
- [MCP Plugin System (中文)](./plugins/mcp_plugin_system.md) - Chinese version of the MCP plugin system guide

### API Usage

- [API Usage Guide (日本語)](./README_API_USAGE_JP.md) - Guide for using the LLM Platform API
- [API Usage Guide (English)](./README_API_USAGE.md) - English version of the API usage guide

## Architecture

- [Technical Architecture (日本語)](./LLMプラットフォーム_技術アーキテクチャ説明書.md) - Technical architecture documentation for the LLM Platform

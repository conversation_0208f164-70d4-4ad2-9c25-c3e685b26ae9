# LLM平台 API 使用指南

本文档提供了如何使用我们的LLM平台API的详细说明。我们的API完全兼容OpenAI的API标准，因此您可以使用OpenAI的客户端库直接与我们的平台交互，无需关心后台使用的是哪种推理引擎。

## 目录

- [快速开始](#快速开始)
- [安装依赖](#安装依赖)
- [基本用法](#基本用法)
- [高级功能](#高级功能)
  - [流式响应](#流式响应)
  - [工具/函数调用](#工具函数调用)
  - [自定义参数](#自定义参数)
- [支持的模型](#支持的模型)
- [错误处理](#错误处理)
- [示例代码](#示例代码)

## 快速开始

只需几行代码，您就可以开始使用我们的LLM平台：

```python
from openai import OpenAI

# 初始化客户端
client = OpenAI(
    base_url="http://localhost:8000/v1",  # 您的API服务地址
    api_key="dummy-api-key"  # 如果不需要认证，可以是任意值
)

# 使用聊天补全API
response = client.chat.completions.create(
    model="llama3.2",  # 使用的模型名称
    messages=[
        {"role": "user", "content": "你好，请介绍一下自己。"}
    ]
)

print(response.choices[0].message.content)
```

## 安装依赖

首先，您需要安装OpenAI的Python客户端库：

```bash
pip install openai
```

## 基本用法

### 聊天补全

聊天补全是与LLM交互的最常用方式：

```python
response = client.chat.completions.create(
    model="llama3.2",
    messages=[
        {"role": "system", "content": "你是一个有用的AI助手。"},
        {"role": "user", "content": "请解释量子计算的基本原理。"}
    ],
    temperature=0.7,
    max_tokens=500
)

print(response.choices[0].message.content)
```

### 文本补全

对于简单的文本生成任务，您可以使用文本补全API：

```python
response = client.completions.create(
    model="llama3.2",
    prompt="请介绍中国的四季特点。",
    max_tokens=300,
    temperature=0.7
)

print(response.choices[0].text)
```

## 高级功能

### 流式响应

流式响应允许您在生成完整响应之前就开始接收部分响应，这对于实时交互非常有用：

```python
stream = client.chat.completions.create(
    model="llama3.2",
    messages=[
        {"role": "user", "content": "请简要介绍人工智能的历史。"}
    ],
    temperature=0.7,
    max_tokens=300,
    stream=True
)

for chunk in stream:
    if chunk.choices[0].delta.content:
        print(chunk.choices[0].delta.content, end="", flush=True)
print()
```

### 工具/函数调用

工具/函数调用允许LLM调用外部工具或函数来获取信息：

```python
# 定义工具
tools = [
    {
        "type": "function",
        "function": {
            "name": "get_weather",
            "description": "获取指定城市的当前天气信息",
            "parameters": {
                "type": "object",
                "properties": {
                    "city": {
                        "type": "string",
                        "description": "城市名称"
                    },
                    "unit": {
                        "type": "string",
                        "enum": ["celsius", "fahrenheit"],
                        "description": "温度单位"
                    }
                },
                "required": ["city"]
            }
        }
    }
]

# 第一次调用，让AI决定是否使用工具
response = client.chat.completions.create(
    model="llama3.2",
    messages=[
        {"role": "user", "content": "北京今天的天气怎么样？"}
    ],
    tools=tools,
    temperature=0.7
)

# 检查是否有工具调用
message = response.choices[0].message
if hasattr(message, 'tool_calls') and message.tool_calls:
    tool_call = message.tool_calls[0]
    
    # 获取工具调用参数
    args = json.loads(tool_call.function.arguments)
    city = args.get("city")
    
    # 执行实际的工具调用（这里是模拟）
    tool_response = f"{city}的天气晴朗，气温25°C。"
    
    # 将工具调用结果返回给AI
    second_response = client.chat.completions.create(
        model="llama3.2",
        messages=[
            {"role": "user", "content": "北京今天的天气怎么样？"},
            {"role": "assistant", "content": None, "tool_calls": [
                {
                    "id": tool_call.id,
                    "type": "function",
                    "function": {
                        "name": "get_weather",
                        "arguments": tool_call.function.arguments
                    }
                }
            ]},
            {"role": "tool", "tool_call_id": tool_call.id, "content": tool_response}
        ],
        temperature=0.7
    )
    
    print(second_response.choices[0].message.content)
```

### 自定义参数

您可以使用各种参数来控制生成的文本：

```python
response = client.chat.completions.create(
    model="llama3.2",
    messages=[
        {"role": "system", "content": "你是一个富有创造力的AI助手。"},
        {"role": "user", "content": "请写一首简短的诗。"}
    ],
    temperature=1.0,  # 更高的温度以获得更有创意的输出
    max_tokens=200,
    top_p=0.95,
    frequency_penalty=0.5,  # 减少重复
    presence_penalty=0.5    # 促进更多样化的内容
)
```

## 支持的模型

我们的平台支持多种模型，您可以根据需要选择不同的模型：

- `llama3.2` - Meta的Llama 3.2模型
- `mistral` - Mistral AI的模型
- `qwen2.5-coder` - 阿里巴巴的通义千问2.5编程模型
- 以及更多...

您可以通过在API调用中指定`model`参数来选择不同的模型。

## 错误处理

处理API调用中可能出现的错误：

```python
try:
    response = client.chat.completions.create(
        model="non_existent_model",
        messages=[
            {"role": "user", "content": "你好"}
        ]
    )
except Exception as e:
    print(f"发生错误: {e}")
    # 处理错误...
```

## 示例代码

我们提供了完整的示例代码，展示了如何使用我们的API：

- `sample_llm.py` - 日语注释的示例代码
- `sample_llm_cn.py` - 中文注释的示例代码

运行示例代码：

```bash
python sample_llm_cn.py
```

这些示例展示了基本的聊天补全、流式响应、工具/函数调用等功能。

---

## 与其他框架集成

由于我们的API完全兼容OpenAI的API标准，您可以轻松地将我们的平台与其他框架集成：

### LangChain

```python
from langchain.chat_models import ChatOpenAI
from langchain.schema import HumanMessage

chat = ChatOpenAI(
    openai_api_base="http://localhost:8000/v1",
    openai_api_key="dummy-api-key",
    model_name="llama3.2"
)

response = chat([HumanMessage(content="你好，请介绍一下自己。")])
print(response.content)
```

### LlamaIndex

```python
from llama_index.llms import OpenAI

llm = OpenAI(
    api_base="http://localhost:8000/v1",
    api_key="dummy-api-key",
    model="llama3.2"
)

response = llm.complete("请简要介绍人工智能的历史。")
print(response.text)
```

---

如果您有任何问题或需要进一步的帮助，请随时联系我们的支持团队。

## ====== LLMプラットフォーム 技術アーキテクチャ説明書 ====== 

---

## 💡 1. システム概要

本システムは、**企業向けの大規模言語モデル（LLM）チャットプラットフォーム**です。  
目的は、OpenAIをはじめとした複数のLLMサービスと連携し、  
**統一されたユーザーインターフェース（UI）でチャット機能を提供する**ことです。

- ユーザー（従業員、管理者など）は1つの画面から複数のLLMと対話可能。
- 将来的にはRAG（Retrieval-Augmented Generation）や社内データ連携も拡張可能な設計。

---

## 🧱 2. 技術スタック（Tech Stack）

---

### 🔷 フロントエンド

#### ✅ 使用技術:

| 項目       | 内容                                |
|------------|-------------------------------------|
| Framework | **React + TypeScript**（SPA構成）  |
| 状態管理   | **Zustand**（軽量の状態管理ライブラリ） |
| UI         | **TailwindCSS**（ユーティリティファーストなCSS） |

#### ✅ 主要機能:

- **チャットインターフェース**  
　ユーザーがLLMと会話するためのUI。入力欄、送信ボタン、会話履歴表示などを提供。

- **ストリーミングレスポンス**  
　LLMの応答を「一括表示」ではなく「1文字ずつ段階的に表示」する機能。  
　ユーザー体験を大きく向上させる。

- **マークダウン対応**  
　コードブロック、リスト、太字など、回答をMarkdown形式で整形表示。

- **認証・認可（Auth）**  
　ログイン、ユーザー識別、ロール管理などの機能。  
　例：一般ユーザーと管理者で機能を制限。

---

### 🟦 バックエンド

#### ✅ 使用技術:

| 項目               | 内容                                      |
|--------------------|-------------------------------------------|
| Framework          | **FastAPI**（Python製の軽量Webフレームワーク） |
| データベース（DB） | 開発時: **SQLite** / 本番: **PostgreSQL**     |
| キャッシュ         | **Redis**（高速なインメモリKVS）             |
| メッセージキュー   | **RabbitMQ**（非同期処理、分散システムに対応）|
| ベクトルDB         | **Qdrant**（RAGなどに使うベクトル検索DB）     |

#### ✅ 主要機能:

- **APIエンドポイントの提供**  
　チャット送受信、ユーザー認証、モデル切替などのREST APIやWebSocketを提供。

- **ユーザー・セッション管理**  
　認証済みユーザーのリクエストに対し、適切なセッション制御を行う。

- **LLMとのインターフェース**  
　OpenAI API、Ollama、vLLMなどのモデルプロバイダと接続し、ユーザーの質問を代理で送信・取得。

- **非同期処理の管理**  
　チャットログ保存、ログ分析、通知送信など、**RabbitMQを通してバックグラウンドで非同期実行**。

- **RAGのためのベクトル検索**（今後の拡張機能）  
　Qdrantを用いて、社内文書やFAQを検索し、その結果を元に生成文を補強（Retrieval-Augmented Generation）。

---

### 💬 補足説明（各コンポーネントの役割）

| コンポーネント | 主な役割 |
|----------------|----------|
| **React** | SPAによる高速なフロント画面 |
| **Zustand** | コンポーネント間の状態共有（例：現在のユーザー、会話履歴） |
| **FastAPI** | 軽量・高速なバックエンドAPI、OpenAPI対応で保守性も高い |
| **Redis** | 認証トークン、チャット履歴キャッシュ、レートリミット等 |
| **RabbitMQ** | 負荷の高い処理をキューにして、サーバー応答を高速化 |
| **Qdrant** | ベクトル埋め込みされた文書の類似検索（社内ナレッジ連携など） |

---

好的，以下是对「LLMプラットフォーム 技術アーキテクチャ説明書」中将来计划扩展实现的部分进行详细设计标记。每项内容都说明其背景、目的、将来可实现方式，并用**将来実装予定**标签明确标记。

---

## 🧩 将来の拡張項目（詳細設計）

---

### 1️⃣ 認証処理の詳細（将来実装予定）

#### ✅ 目的:
- ユーザー認証・認可によって、利用権限・使用制限・監査ログなどを制御

#### ✅ 想定構成:
| 項目 | 説明 |
|------|------|
| 認証方式 | OpenID Connect / OAuth 2.0 準拠（例：Keycloak, Auth0） |
| 認可機能 | ロールベース（一般ユーザー / 管理者）アクセス制御（RBAC） |
| トークン管理 | JWT（JSON Web Token）を使用し、Redisなどで一時保存 |
| 多段階認証 | MFA（多要素認証）オプションも考慮（将来対応） |

#### ✅ 利用場面例：
- 管理者のみモデルの設定変更が可能
- 社外APIアクセスは制限ユーザーのみ使用可

> 🏷 **将来実装予定**：Keycloak等の導入とAPIレベルのアクセスポリシー設計

---

### 2️⃣ LLMプロバイダの切替設計（将来実装予定）

#### ✅ 目的:
- 複数の大規模言語モデル（OpenAI / Anthropic / Claude / Ollama / vLLMなど）を柔軟に切替可能にする。

#### ✅ 設計構想:

| 機能 | 内容 |
|------|------|
| モデル定義 | 設定ファイル（例：YAML / JSON）またはDBでプロバイダ情報管理 |
| 切替方法 | フロントエンドからモデル選択 → APIにてプロバイダ変更適用 |
| 抽象化層 | モデルごとの統一インターフェース（Adapter Pattern）でラップ |
| 例外処理 | APIキー無効、レスポンスタイムアウト時のフォールバック機構 |

#### ✅ 実装例:
```python
class LLMProvider:
    def generate(self, prompt: str) -> str:
        raise NotImplementedError

class OpenAIProvider(LLMProvider):
    def generate(self, prompt: str) -> str:
        # OpenAI APIを呼び出す

class OllamaProvider(LLMProvider):
    def generate(self, prompt: str) -> str:
        # ローカルのOllamaエンドポイントを呼び出す
```

> 🏷 **将来実装予定**：プロバイダの抽象クラス設計と動的切替機能

---

### 3️⃣ RAGのアーキテクチャ設計（将来実装予定）

#### ✅ 目的:
- LLMの回答を「社内ドキュメント・ナレッジ・FAQ」などの情報で補強し、より正確な返答を実現。

#### ✅ アーキテクチャ構成（想定）:

```text
[ユーザー質問]
     ↓
[Embedding（OpenAI, HuggingFaceなど）]
     ↓
[ベクトル化された質問 → Qdrant検索]
     ↓
[関連文書 Top-k 取得]
     ↓
[LLMにプロンプトとして投入 → 回答生成]
```

#### ✅ 必要要素:

| コンポーネント | 内容 |
|----------------|------|
| ベクトルDB     | Qdrant, Weaviate, Milvus など |
| エンベッディング | sentence-transformers / OpenAI Embedding API |
| 検索戦略       | cosine距離 / HNSW / metadata filter付き検索 |
| プロンプト構成 | retrieved document + user prompt を組み合わせた system prompt |

#### ✅ 例:
```python
# ドキュメント検索フェーズ
results = qdrant_client.search("請求書の処理フロー")
top_docs = [r["text"] for r in results[:3]]

# LLMへのプロンプト構成
prompt = f"""
あなたは社内ナレッジに基づいて質問に答えるAIです。
以下の文書を参考にしてください：
{top_docs}

質問：請求書の承認フローは？
"""
```

> 🏷 **将来実装予定**：Qdrantへのドキュメントアップロード機能 + 検索→プロンプト再構成のPipeline設計

---
以下是对「LLMプラットフォーム」的**システム構成図**（System Architecture Diagram）与**デプロイ構成**（Deployment Architecture）的详细补充说明，使用日语编写，确保可用于提案或設計書。

---

## 🖼️ システム構成図（System Architecture Diagram）

以下の図は、フロントエンド、バックエンド、LLMプロバイダ、認証基盤、ベクトルDBなど各コンポーネント間の関係性を示したものです。

```
 ┌─────────────────────────────────────────────┐
 │              ユーザー（ブラウザ）             │
 │     ┌─────────────┐     ┌────────────────┐  │
 │     │ チャット画面 │ ←→  │ Zustand状態管理 │  │
 │     └────┬────────┘     └────┬───────────┘  │
 │          │ Tailwind UI       │              │
 └────┬─────┴───────────────────┴──────────────┘
      │ HTTP/WebSocket
 ┌────▼───────────────────────────────────────┐
 │              FastAPI バックエンド           │
 │   ┌────────────┐   ┌────────────────────┐  │
 │   │ 認証API    │ ←→│ Keycloak（OAuth2.0）│  │
 │   └────┬───────┘   └────────────────────┘  │
 │        │                                   │
 │   ┌────▼────────────┐                      │
 │   │ チャットAPI      │───┐                  │
 │   └────┬────────────┘   │                  │
 │        │                │                  │
 │   ┌────▼────────┐   ┌───▼────────────────┐ │
 │   │ モデル管理層 │←→ │LLMプロバイダ（OpenAI等）│
 │   └────┬────────┘   └────────────────────┘ │
 │        │                                   │
 │   ┌────▼────────────┐                      │
 │   │ RAG検索層        │←→ QdrantベクトルDB   │
 │   └────┬────────────┘                      │
 │        │                                   │
 │   ┌────▼────────────┐                      │
 │   │ 非同期処理（Celery）│←→ RabbitMQ        │
 │   └──────────────────┘                     │
 └────────────────────────────────────────────┘
```

---

## 🛠️ デプロイ構成（Deployment Architecture）

### ✅ 想定環境：
- 本番環境：クラウド（例：AWS, GCP, Azure）
- コンテナ管理：Docker + Docker Compose（または Kubernetes）
- 監視・ログ：Prometheus + Grafana（将来導入可能）

### ✅ コンポーネント別デプロイ構成

| サービス        | コンテナ | 説明 |
|------------------|----------|------|
| Frontend         | `frontend` | React + TypeScript アプリ、Nginx経由で提供 |
| APIサーバー       | `backend`  | FastAPI アプリ、Gunicorn + Uvicorn |
| 認証サーバー      | `keycloak` | OpenID Connect対応、管理画面あり |
| DB（本番）       | `postgres` | 永続化用 RDB |
| キャッシュ        | `redis`    | セッション・トークンなどを格納 |
| メッセージキュー  | `rabbitmq` | Celeryバックエンド |
| ベクトルDB       | `qdrant`   | ベクトル検索用 |
| ワーカー         | `worker`   | Celery Worker、非同期処理担当 |
| オブザーバー      | `grafana/prometheus` | 監視（任意）|

---

### 🐳 Docker Compose（簡易構成例）

```yaml
services:
  frontend:
    build: ./frontend
    ports:
      - "3000:3000"

  backend:
    build: ./backend
    ports:
      - "8000:8000"
    depends_on:
      - postgres
      - redis
      - rabbitmq
      - qdrant

  postgres:
    image: postgres:15
    environment:
      POSTGRES_DB: llm_platform
      POSTGRES_USER: admin
      POSTGRES_PASSWORD: password

  redis:
    image: redis:alpine

  rabbitmq:
    image: rabbitmq:3-management

  qdrant:
    image: qdrant/qdrant

  keycloak:
    image: quay.io/keycloak/keycloak
    command: start-dev
    environment:
      KEYCLOAK_ADMIN: admin
      KEYCLOAK_ADMIN_PASSWORD: password

  worker:
    build: ./backend
    command: celery -A app.worker worker --loglevel=info
    depends_on:
      - rabbitmq
      - redis
```

---

参照用
はい、システム全体を分析し、技術アーキテクチャと企業向けの追加機能を提案させていただきます。

# LLMプラットフォーム 技術アーキテクチャ説明書

## 1. システム概要

本システムは、企業向けLLMチャットプラットフォームで、OpenAIなどの各種LLMに対応した統合的なチャットインターフェースを提供します。

## 2. 技術スタック

### フロントエンド
- **Framework**: React + TypeScript
- **状態管理**: Zustand
- **UI**: TailwindCSS
- **主要機能**:
  - チャットインターフェース
  - ストリーミングレスポンス
  - マークダウン対応
  - 認証・認可

### バックエンド
- **Framework**: FastAPI
- **データベース**: 
  - SQLite（開発環境）
  - PostgreSQL（本番環境推奨）
- **キャッシュ**: Redis
- **メッセージキュー**: RabbitMQ
- **ベクトルDB**: Qdrant

## 3. 現在の主要機能と使用状況

### 使用中の機能
```markdown
フロントエンド:
✅ チャットインターフェース
✅ ストリーミングレスポンス
✅ マークダウン表示
✅ シンタックスハイライト
✅ 基本的な状態管理

バックエンド:
✅ LLMとの連携
✅ ストリーミングレスポンス処理
✅ 基本的なエラーハンドリング
```

### 未使用のため削除推奨の機能
```markdown
- Keycloak認証（現状不要）
- RAGモジュール（未実装）
- モニタリング機能（未実装）
- タスクキュー（未使用）
```

## 4. 企業導入に向けた追加推奨機能

### 4.1 セキュリティ強化
- **シングルサインオン（SSO）統合**
  - Azure AD
  - Google Workspace
  - Okta

- **データセキュリティ**
  - エンドツーエンド暗号化
  - データの地理的制限
  - プロンプトインジェクション対策

### 4.2 コンプライアンス対応
- **監査ログ機能**
  ```typescript
  interface AuditLog {
    userId: string;
    action: string;
    timestamp: Date;
    details: Record<string, any>;
    ipAddress: string;
  }
  ```

- **データ保持ポリシー**
  ```python
  class RetentionPolicy:
      def __init__(self):
          self.retention_period = 90  # days
          self.sensitive_data_period = 30  # days
  ```

### 4.3 エンタープライズ機能
- **カスタムLLMモデル統合**
  ```python
  class CustomModelIntegration:
      def __init__(self, model_config: Dict):
          self.model_type = model_config["type"]
          self.endpoint = model_config["endpoint"]
          self.api_key = model_config["api_key"]
  ```

- **プロンプトテンプレート管理**
  ```typescript
  interface PromptTemplate {
    id: string;
    name: string;
    template: string;
    parameters: string[];
    category: string;
    isApproved: boolean;
  }
  ```

- **ロールベースのアクセス制御（RBAC）**
  ```python
  class Role:
      def __init__(self):
          self.permissions = {
              "admin": ["read", "write", "delete", "approve"],
              "manager": ["read", "write", "approve"],
              "user": ["read", "write"]
          }
  ```

### 4.4 分析・モニタリング
- **使用状況分析**
  ```typescript
  interface Analytics {
    totalRequests: number;
    averageResponseTime: number;
    tokenUsage: {
      total: number;
      byModel: Record<string, number>;
    };
    costEstimates: {
      daily: number;
      monthly: number;
    };
  }
  ```

- **パフォーマンスモニタリング**
  ```python
  class PerformanceMetrics:
      def __init__(self):
          self.response_times = []
          self.error_rates = {}
          self.model_latency = {}
  ```

### 4.5 インテグレーション
- **社内システム連携**
  - Slack
  - Microsoft Teams
  - 社内ナレッジベース

- **API管理**
  ```typescript
  interface APIManagement {
    rateLimit: number;
    quotaManagement: boolean;
    apiKeys: string[];
    webhooks: string[];
  }
  ```

## 5. 推奨される実装順序

1. セキュリティ強化（SSO、暗号化）
2. 監査ログ機能
3. RBACの実装
4. 使用状況分析
5. プロンプトテンプレート管理
6. 外部システム連携

## 6. インフラストラクチャ更新

```yaml:docker-compose.yml
services:
  llm-backend:
    build: ./backend
    environment:
      - NODE_ENV=production
      - DB_TYPE=postgresql
      - ENABLE_AUDIT_LOG=true
      - ENABLE_SSO=true
    depends_on:
      - postgres
      - redis
      - qdrant

  postgres:
    image: postgres:14
    environment:
      POSTGRES_DB: llm_platform
      POSTGRES_USER: admin
      POSTGRES_PASSWORD: ${DB_PASSWORD}

  monitoring:
    image: grafana/grafana
    ports:
      - "3000:3000"
```

## 7. 開発・運用フロー

1. 開発環境
   - ローカル開発
   - テスト環境
   - ステージング環境
   - 本番環境

2. CI/CD
   - GitHub Actions
   - テスト自動化
   - デプロイメント自動化

3. モニタリング
   - Grafana
   - Prometheus
   - ELKスタック

これらの機能は、企業のニーズや規模に応じて段階的に実装することを推奨します。特に、セキュリティとコンプライアンス関連の機能は、企業導入の初期段階で実装することが重要です。

=======优化===========
1. API 适配器模式
创建了 apiAdapter.ts，实现了适配器模式，隔离外部 API 依赖
提供了统一的接口来处理不同的 API 端点和请求格式
实现了错误处理和重试机制
2. 后端策略模式
创建了 backendStrategies.ts，实现了策略模式，处理不同后端的特殊逻辑
为 Ollama、vLLM、SGLang 和 X-Inference 创建了专用策略
实现了策略上下文类，用于动态选择和使用策略
3. 错误处理
创建了 errorService.ts，实现了自定义错误类型和错误处理机制
实现了重试逻辑，可以自动重试可恢复的错误
提供了用户友好的错误消息
4. 日志系统
创建了 logService.ts，实现了日志系统，便于调试和问题追踪
支持不同级别的日志记录
提供了上下文信息和错误追踪
5. 服务工厂
创建了 serviceFactory.ts，用于统一管理所有服务
实现了单例模式，确保服务只被初始化一次
提供了获取服务的接口
6. API 服务
创建了 apiService.ts，使用适配器和策略模式实现高级 API 接口
提供了发送聊天请求、处理流式响应等功能
实现了错误处理和重试机制
7. 聊天逻辑钩子
创建了 useChatLogicV2.ts，使用新的架构实现聊天逻辑
提供了发送消息、处理响应、管理状态等功能
实现了错误处理和重试机制
8. 示例组件
创建了 ChatInterfaceV2.tsx，展示如何使用新的架构
提供了完整的聊天界面，包括消息发送、接收、设置等功能
实现了响应式设计和深色模式支持
9. 样式文件
创建了 ChatInterfaceV2.css，为新组件提供样式
实现了响应式设计和深色模式支持
提供了动画效果和用户友好的界面
这些改进使代码更加模块化、可维护和可扩展，同时提供了更健壮的错误处理和日志记录功能。


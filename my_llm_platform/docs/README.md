# LLM Platform Documentation

This directory contains documentation for the LLM Platform.

## Documentation Structure

- [Backend Documentation](./backend/README.md) - Documentation for the backend components
- [Frontend Documentation](./frontend/README.md) - Documentation for the frontend components

## Language Support

Documentation is available in multiple languages:
- 日本語 (Japanese)
- English
- 中文 (Chinese)

## Contributing to Documentation

When adding new documentation:
1. Create the primary document in Japanese
2. Add English and Chinese translations if possible
3. Update the appropriate README.md file with links to the new documentation
4. Place documentation in the correct folder:
   - General backend documentation: `/docs/backend/`
   - Backend module-specific documentation: `/docs/backend/{module_name}/`
   - General frontend documentation: `/docs/frontend/`
   - Frontend module-specific documentation: `/docs/frontend/{module_name}/`

# LLMプラットフォーム API 使用ガイド

このドキュメントでは、LLMプラットフォームAPIの使用方法について詳細に説明します。当APIはOpenAIのAPI標準と完全に互換性があるため、OpenAIのクライアントライブラリを使用して、バックエンドで使用されている推論エンジンを気にすることなく、当プラットフォームと対話することができます。

## 目次

- [クイックスタート](#クイックスタート)
- [依存関係のインストール](#依存関係のインストール)
- [基本的な使用方法](#基本的な使用方法)
- [高度な機能](#高度な機能)
  - [ストリーミングレスポンス](#ストリーミングレスポンス)
  - [ツール/関数呼び出し](#ツール関数呼び出し)
  - [カスタムパラメータ](#カスタムパラメータ)
- [サポートされているモデル](#サポートされているモデル)
- [エラー処理](#エラー処理)
- [サンプルコード](#サンプルコード)

## クイックスタート

数行のコードで、LLMプラットフォームの使用を開始できます：

```python
from openai import OpenAI

# クライアントの初期化
client = OpenAI(
    base_url="http://localhost:8000/v1",  # APIサービスのアドレス
    api_key="dummy-api-key"  # 認証が必要ない場合は任意の値
)

# チャット補完APIを使用
response = client.chat.completions.create(
    model="llama3.2",  # 使用するモデル名
    messages=[
        {"role": "user", "content": "こんにちは、自己紹介をお願いします。"}
    ]
)

print(response.choices[0].message.content)
```

## 依存関係のインストール

まず、OpenAIのPythonクライアントライブラリをインストールする必要があります：

```bash
pip install openai
```

## 基本的な使用方法

### チャット補完

チャット補完はLLMと対話する最も一般的な方法です：

```python
response = client.chat.completions.create(
    model="llama3.2",
    messages=[
        {"role": "system", "content": "あなたは役立つAIアシスタントです。"},
        {"role": "user", "content": "量子コンピューティングの基本原理を説明してください。"}
    ],
    temperature=0.7,
    max_tokens=500
)

print(response.choices[0].message.content)
```

### テキスト補完

単純なテキスト生成タスクには、テキスト補完APIを使用できます：

```python
response = client.completions.create(
    model="llama3.2",
    prompt="日本の四季について説明してください。",
    max_tokens=300,
    temperature=0.7
)

print(response.choices[0].text)
```

## 高度な機能

### ストリーミングレスポンス

ストリーミングレスポンスを使用すると、完全な応答が生成される前に部分的な応答の受信を開始できます。これはリアルタイムの対話に非常に役立ちます：

```python
stream = client.chat.completions.create(
    model="llama3.2",
    messages=[
        {"role": "user", "content": "AIの歴史について簡単に説明してください。"}
    ],
    temperature=0.7,
    max_tokens=300,
    stream=True
)

for chunk in stream:
    if chunk.choices[0].delta.content:
        print(chunk.choices[0].delta.content, end="", flush=True)
print()
```

### ツール/関数呼び出し

ツール/関数呼び出しを使用すると、LLMが情報を取得するために外部ツールや関数を呼び出すことができます：

```python
# ツールの定義
tools = [
    {
        "type": "function",
        "function": {
            "name": "get_weather",
            "description": "指定された都市の現在の天気情報を取得します",
            "parameters": {
                "type": "object",
                "properties": {
                    "city": {
                        "type": "string",
                        "description": "都市名"
                    },
                    "unit": {
                        "type": "string",
                        "enum": ["celsius", "fahrenheit"],
                        "description": "温度の単位"
                    }
                },
                "required": ["city"]
            }
        }
    }
]

# AIがツールを使用するかどうかを決定するための最初の呼び出し
response = client.chat.completions.create(
    model="llama3.2",
    messages=[
        {"role": "user", "content": "東京の今日の天気はどうですか？"}
    ],
    tools=tools,
    temperature=0.7
)

# ツール呼び出しがあるかチェック
message = response.choices[0].message
if hasattr(message, 'tool_calls') and message.tool_calls:
    tool_call = message.tool_calls[0]
    
    # ツール呼び出しパラメータを取得
    args = json.loads(tool_call.function.arguments)
    city = args.get("city")
    
    # 実際のツール呼び出しを実行（ここではシミュレーション）
    tool_response = f"{city}の天気は晴れ、気温は25°Cです。"
    
    # ツール呼び出し結果をAIに返す
    second_response = client.chat.completions.create(
        model="llama3.2",
        messages=[
            {"role": "user", "content": "東京の今日の天気はどうですか？"},
            {"role": "assistant", "content": None, "tool_calls": [
                {
                    "id": tool_call.id,
                    "type": "function",
                    "function": {
                        "name": "get_weather",
                        "arguments": tool_call.function.arguments
                    }
                }
            ]},
            {"role": "tool", "tool_call_id": tool_call.id, "content": tool_response}
        ],
        temperature=0.7
    )
    
    print(second_response.choices[0].message.content)
```

### カスタムパラメータ

生成されるテキストを制御するためにさまざまなパラメータを使用できます：

```python
response = client.chat.completions.create(
    model="llama3.2",
    messages=[
        {"role": "system", "content": "あなたは創造的なAIアシスタントです。"},
        {"role": "user", "content": "短い詩を書いてください。"}
    ],
    temperature=1.0,  # より創造的な出力のために高い温度
    max_tokens=200,
    top_p=0.95,
    frequency_penalty=0.5,  # 繰り返しを減らす
    presence_penalty=0.5    # より多様な内容を促進
)
```

## サポートされているモデル

当プラットフォームはさまざまなモデルをサポートしており、必要に応じて異なるモデルを選択できます：

- `llama3.2` - MetaのLlama 3.2モデル
- `mistral` - Mistral AIのモデル
- `qwen2.5-coder` - Alibaba（阿里巴巴）のQwen 2.5コーディングモデル
- その他多数...

API呼び出しで`model`パラメータを指定することで、異なるモデルを選択できます。

## エラー処理

API呼び出し中に発生する可能性のあるエラーの処理：

```python
try:
    response = client.chat.completions.create(
        model="non_existent_model",
        messages=[
            {"role": "user", "content": "こんにちは"}
        ]
    )
except Exception as e:
    print(f"エラーが発生しました: {e}")
    # エラー処理...
```

## サンプルコード

当APIの使用方法を示す完全なサンプルコードを提供しています：

- `sample_llm.py` - 日本語コメント付きのサンプルコード
- `sample_llm_jp.py` - 日本語コメント付きのサンプルコード

サンプルコードの実行：

```bash
python sample_llm_jp.py
```

これらのサンプルは、基本的なチャット補完、ストリーミングレスポンス、ツール/関数呼び出しなどの機能を示しています。

---

## 他のフレームワークとの統合

当APIはOpenAIのAPI標準と完全に互換性があるため、他のフレームワークと簡単に統合できます：

### LangChain

```python
from langchain.chat_models import ChatOpenAI
from langchain.schema import HumanMessage

chat = ChatOpenAI(
    openai_api_base="http://localhost:8000/v1",
    openai_api_key="dummy-api-key",
    model_name="llama3.2"
)

response = chat([HumanMessage(content="こんにちは、自己紹介をお願いします。")])
print(response.content)
```

### LlamaIndex

```python
from llama_index.llms import OpenAI

llm = OpenAI(
    api_base="http://localhost:8000/v1",
    api_key="dummy-api-key",
    model="llama3.2"
)

response = llm.complete("AIの歴史について簡単に説明してください。")
print(response.text)
```

---

ご質問やさらなるサポートが必要な場合は、サポートチームにお気軽にお問い合わせください。

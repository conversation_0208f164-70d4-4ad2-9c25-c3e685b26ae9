# LLM Platform Plugin System

This document describes the plugin system for the LLM platform, which allows easy integration of additional functionality like agents, RAG, and other extensions into the chat interface.

## Overview

The plugin system follows a modular architecture that allows developers to create plugins that can:

1. Process messages before they are sent to the LLM
2. Process responses after they are received from the LLM
3. Add custom commands to the chat interface
4. Implement tools that can be called by the LLM during function/tool calling
5. Add custom UI components to the chat interface

## Plugin Types

The system supports several types of plugins:

1. **Preprocessor Plugins**: Modify user messages before they are sent to the LLM
2. **Postprocessor Plugins**: Modify LLM responses before they are sent to the user
3. **Command Plugins**: Add custom commands that can be triggered by specific patterns in user messages
4. **Tool Plugins**: Implement tools that can be called by the LLM during function/tool calling
5. **UI Plugins**: Add custom UI components to the chat interface
6. **Agent Plugins**: Implement agents that can handle complex tasks
7. **RAG Plugins**: Implement Retrieval Augmented Generation for knowledge enhancement

## Getting Started

### Running the Server with Plugin Support

To run the server with plugin support, use the following command:

```bash
cd my_llm_platform
python run.py --enable-plugins
```

The plugin system is disabled by default. You can enable it by:

1. Using the `--enable-plugins` command line flag
2. Setting the `ENABLE_PLUGINS` environment variable to `true` in your `.env` file

### Using Plugins in Chat Requests

To use plugins in chat requests, add the `plugins` parameter to your chat completion request:

```python
from openai import OpenAI

client = OpenAI(
    base_url="http://localhost:8000/v1",
    api_key="dummy-api-key"
)

response = client.chat.completions.create(
    model="llama3.2",
    messages=[
        {"role": "user", "content": "Tell me about the LLM platform."}
    ],
    extra_body={"plugins": ["simple_rag", "simple_agent"]}  # Specify plugins to use
)
```

### Plugin API

The plugin system provides a REST API for managing plugins:

- `GET /plugins`: List all registered plugins
- `GET /plugins/{plugin_id}`: Get information about a specific plugin
- `POST /plugins/{plugin_id}/enable`: Enable a plugin
- `POST /plugins/{plugin_id}/disable`: Disable a plugin
- `POST /plugins/{plugin_id}/configure`: Configure a plugin

## Creating Custom Plugins

### 1. Create a Plugin Module

Create a new Python module in the `backend/plugins/custom/` directory:

```python
# backend/plugins/custom/my_plugin.py
from backend.plugins.base import PreprocessorPlugin
from backend.plugins.types import Message, PluginResult

class MyCustomPlugin(PreprocessorPlugin):
    def __init__(self, config=None):
        super().__init__(config)
        self.id = "my_custom_plugin"
        self.name = "My Custom Plugin"
        self.description = "A custom preprocessor plugin"

    async def process(self, messages: list[Message], **kwargs) -> PluginResult:
        # Implement your message processing logic here
        if messages and messages[-1]["role"] == "user":
            messages[-1]["content"] = f"Enhanced: {messages[-1]['content']}"

        return PluginResult(
            messages=messages,
            metadata={"plugin": self.name}
        )
```

### 2. Register the Plugin

Register your plugin with the plugin manager:

```python
# backend/plugins/custom/__init__.py
from .my_plugin import MyCustomPlugin

__all__ = ['MyCustomPlugin']
```

Then, in your application startup code:

```python
from backend.plugins.manager import plugin_manager
from backend.plugins.custom import MyCustomPlugin

plugin_manager.register_plugin("my_custom_plugin", MyCustomPlugin)
```

### 3. Use the Plugin

Now you can use your plugin in chat requests:

```python
response = client.chat.completions.create(
    model="llama3.2",
    messages=[
        {"role": "user", "content": "Hello, world!"}
    ],
    extra_body={"plugins": ["my_custom_plugin"]}
)
```

## Sample Plugins

The plugin system comes with two sample plugins:

1. **SimpleRAGPlugin**: A simple RAG plugin that adds retrieved documents to the system message
2. **SimpleAgentPlugin**: A simple agent plugin that implements a basic ReAct agent

You can use these plugins as examples for creating your own plugins.

## Plugin Configuration

Plugins can be configured through the plugin API or through the plugin manager:

```python
# Configure a plugin through the plugin manager
plugin_manager.configure_plugin("my_custom_plugin", {"key": "value"})
```

Or through the API:

```python
import requests

requests.post(
    "http://localhost:8000/plugins/my_custom_plugin/configure",
    json={"config": {"key": "value"}}
)
```

## Plugin Dependencies

Plugins can depend on other plugins. The plugin manager ensures that plugins are executed in the correct order based on their dependencies:

```python
class MyDependentPlugin(PreprocessorPlugin):
    def __init__(self, config=None):
        super().__init__(config)
        self.id = "my_dependent_plugin"
        self.name = "My Dependent Plugin"
        self.description = "A plugin that depends on another plugin"
        self.dependencies = ["my_custom_plugin"]  # Specify dependencies
```

## Plugin Execution Flow

The plugin system follows a pipeline architecture, where messages flow through a series of plugins before being sent to the LLM, and responses flow through another series of plugins before being sent to the user:

1. User sends a message
2. Message is processed by preprocessor plugins
3. Message is sent to the LLM
4. Response is received from the LLM
5. Response is processed by postprocessor plugins
6. Response is sent to the user

## Conclusion

The plugin system provides a flexible and extensible way to enhance the LLM platform with additional functionality. By creating custom plugins, you can add new features and capabilities to the platform without modifying the core code.

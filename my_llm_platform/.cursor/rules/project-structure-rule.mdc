---
description: 
globs: 
alwaysApply: false
---
# プロジェクト構造ガイド

このプロジェクトは以下の主要なディレクトリで構成されています：

## バックエンド (/backend)
- メインエントリーポイント: [main.py](mdc:backend/main.py)
- 設定: [config.py](mdc:backend/config.py)
- データベース関連: [database.py](mdc:backend/database.py)

### 主要なコンポーネント
- `/routers`: APIエンドポイントの定義
  - [chat_api.py](mdc:backend/routers/chat_api.py): チャット関連のAPI
  - [llm_api.py](mdc:backend/routers/llm_api.py): LLM推論API
  - [admin_api.py](mdc:backend/routers/admin_api.py): 管理者用API

- `/rag`: RAG（検索拡張生成）機能
  - [document_loader.py](mdc:backend/rag/document_loader.py): ドキュメントローダー
  - [vector_store.py](mdc:backend/rag/vector_store.py): ベクトルストア
  - [embeddings.py](mdc:backend/rag/embeddings.py): 埋め込みモデル

## フロントエンド (/frontend/chat-ui)
- `/src/components`: Reactコンポーネント
  - [ModelSelector.tsx](mdc:frontend/chat-ui/src/components/ModelSelector.tsx): モデル選択UI

  - [BackendSelector.tsx](mdc:frontend/chat-ui/src/components/BackendSelector.tsx): バックエンド選択UI

# バックエンドアーキテクチャ

## LLM推論システム
- 複数のバックエンド（vLLM、SGLang、Ollama、X-Inference）をサポート
- 動的なモデル選択とバックエンド切り替え
- ストリーミングレスポンスのサポート

## データベース設計
- SQLiteをデフォルトとして使用
- PostgreSQLとMySQLもサポート
- チャットセッションとメッセージの履歴管理

## RAGシステム
- 複数のドキュメントフォーマットをサポート
- ベクトルストアとしてQdrantを使用
- カスタマイズ可能な埋め込みモデル

## プラグインシステム
- 動的なプラグインローディング
- カスタムプラグインのサポート

- RAG、エージェント、MCPの拡張機能
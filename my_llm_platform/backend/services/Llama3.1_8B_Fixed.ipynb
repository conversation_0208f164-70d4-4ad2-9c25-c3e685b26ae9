{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Llama 3.1 8B GRPO 直接推理\n", "\n", "1. vLLM 支持的 dtype 类型\n", "vLLM 支持以下 dtype 类型：\n", "\n", "\"float16\" 或 \"fp16\" - 16位浮点数\n", "\"bfloat16\" 或 \"bf16\" - 16位脑浮点数（Brain Floating Point）\n", "\"float32\" 或 \"fp32\" - 32位浮点数\n", "\"int8\" - 8位整数（需要配合量化使用）\n", "\"int4\" - 4位整数（需要配合量化使用）\n", "对于量化模型，vLLM 支持以下量化方法：\n", "\n", "\"awq\" - AWQ 量化\n", "\"gptq\" - GPTQ 量化\n", "\"squeezellm\" - SqueezeLLM 量化\n", "\"bitsandbytes\" - BitsAndBytes 量化（支持 int8 和 int4）\n", "2. 直接保存为 vLLM 兼容格式\n", "是的，您可以在保存模型时直接保存为 vLLM 兼容的格式。问题在于 Unsloth 的 load_in_4bit=True 使用的是 BitsAndBytes 的特殊格式，这与 vLLM 期望的格式不完全兼容。"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["WARNING: BNB_CUDA_VERSION=126 environment variable detected; loading libbitsandbytes_cuda126.so.\n", "This can be used to load a bitsandbytes version that is different from the PyTorch CUDA version.\n", "If this was unintended set the BNB_CUDA_VERSION variable to an empty string: export BNB_CUDA_VERSION=\n", "If you use the manual override make sure the right libcudart.so is in your LD_LIBRARY_PATH\n", "For example by adding the following to your .bashrc: export LD_LIBRARY_PATH=$LD_LIBRARY_PATH:<path_to_cuda_dir/lib64\n", "\n"]}, {"name": "stdout", "output_type": "stream", "text": ["🦥 Unsloth: Will patch your computer to enable 2x faster free finetuning.\n", "🦥 Unsloth Zoo will now patch everything to make training faster!\n", "INFO 05-21 20:19:01 __init__.py:207] Automatically detected platform cuda.\n"]}], "source": ["import torch\n", "from unsloth import FastLanguageModel, PatchFastRL\n", "PatchFastRL(\"GRPO\", FastLanguageModel)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. 模型加载 - 修复内存问题\n", "\n", "主要修改：\n", "1. 降低 max_seq_length 到 256\n", "2. 禁用 vLLM (fast_inference=False)\n", "3. 增加 gpu_memory_utilization 到 0.9"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["messages = [\n", "    {\"role\": \"system\", \"content\": \"You are a pirate chatbot who always responds in pirate speak!\"},\n", "    {\"role\": \"user\", \"content\": \"Who are you?\"},\n", "]"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["WARNING: BNB_CUDA_VERSION=126 environment variable detected; loading libbitsandbytes_cuda126.so.\n", "This can be used to load a bitsandbytes version that is different from the PyTorch CUDA version.\n", "If this was unintended set the BNB_CUDA_VERSION variable to an empty string: export BNB_CUDA_VERSION=\n", "If you use the manual override make sure the right libcudart.so is in your LD_LIBRARY_PATH\n", "For example by adding the following to your .bashrc: export LD_LIBRARY_PATH=$LD_LIBRARY_PATH:<path_to_cuda_dir/lib64\n", "\n"]}, {"name": "stdout", "output_type": "stream", "text": ["🦥 Unsloth: Will patch your computer to enable 2x faster free finetuning.\n", "🦥 Unsloth Zoo will now patch everything to make training faster!\n", "INFO 05-21 21:36:30 __init__.py:207] Automatically detected platform cuda.\n", "==((====))==  Unsloth 2025.3.19: Fast Llama patching. Transformers: 4.49.0. vLLM: 0.7.3.\n", "   \\\\   /|    NVIDIA GeForce RTX 3060. Num GPUs = 1. Max memory: 12.0 GB. Platform: Linux.\n", "O^O/ \\_/ \\    Torch: 2.5.1+cu124. CUDA: 8.6. CUDA Toolkit: 12.4. Triton: 3.1.0\n", "\\        /    Bfloat16 = TRUE. FA [Xformers = 0.0.28.post3. FA2 = False]\n", " \"-____-\"     Free license: http://github.com/unslothai/unsloth\n", "Unsloth: Fast downloading is enabled - ignore downloading bars which are red colored!\n", "Unsloth: vLLM loading unsloth/llama-3.2-3b-instruct-unsloth-bnb-4bit with actual GPU utilization = 82.14%\n", "Unsloth: Your GPU has CUDA compute capability 8.6 with VRAM = 12.0 GB.\n", "Unsloth: Using conservativeness = 1.0. Chunked prefill tokens = 256. Num Sequences = 192.\n", "Unsloth: vLLM's KV Cache can use up to 7.44 GB. Also swap space = 1 GB.\n", "WARNING 05-21 21:36:37 config.py:2448] Casting torch.bfloat16 to torch.float16.\n", "INFO 05-21 21:36:42 config.py:549] This model supports multiple tasks: {'reward', 'classify', 'generate', 'score', 'embed'}. Defaulting to 'generate'.\n", "Unsloth: vLLM Bitsandbytes config using kwargs = {'load_in_8bit': False, 'load_in_4bit': True, 'bnb_4bit_compute_dtype': 'float16', 'bnb_4bit_quant_storage': 'uint8', 'bnb_4bit_quant_type': 'nf4', 'bnb_4bit_use_double_quant': True, 'llm_int8_enable_fp32_cpu_offload': False, 'llm_int8_has_fp16_weight': False, 'llm_int8_skip_modules': ['lm_head', 'multi_modal_projector', 'merger', 'modality_projection', 'model.layers.1.mlp'], 'llm_int8_threshold': 6.0}\n", "INFO 05-21 21:36:43 llm_engine.py:234] Initializing a V0 LLM engine (v0.7.3) with config: model='unsloth/llama-3.2-3b-instruct-unsloth-bnb-4bit', speculative_config=None, tokenizer='unsloth/llama-3.2-3b-instruct-unsloth-bnb-4bit', skip_tokenizer_init=False, tokenizer_mode=auto, revision=None, override_neuron_config=None, tokenizer_revision=None, trust_remote_code=False, dtype=torch.float16, max_seq_len=256, download_dir=None, load_format=LoadFormat.BITSANDBYTES, tensor_parallel_size=1, pipeline_parallel_size=1, disable_custom_all_reduce=False, quantization=bitsandbytes, enforce_eager=False, kv_cache_dtype=auto,  device_config=cuda:0, decoding_config=DecodingConfig(guided_decoding_backend='xgrammar'), observability_config=ObservabilityConfig(otlp_traces_endpoint=None, collect_model_forward_time=False, collect_model_execute_time=False), seed=0, served_model_name=unsloth/llama-3.2-3b-instruct-unsloth-bnb-4bit, num_scheduler_steps=1, multi_step_stream_outputs=True, enable_prefix_caching=True, chunked_prefill_enabled=False, use_async_output_proc=True, disable_mm_preprocessor_cache=False, mm_processor_kwargs=None, pooler_config=None, compilation_config={\"level\":0,\"splitting_ops\":[],\"compile_sizes\":[],\"cudagraph_capture_sizes\":[192,184,176,168,160,152,144,136,128,120,112,104,96,88,80,72,64,56,48,40,32,24,16,8,4,2,1],\"max_capture_size\":192}, use_cached_outputs=False, \n", "WARNING 05-21 21:36:44 interface.py:304] Using 'pin_memory=False' as WSL is detected. This may slow down the performance.\n", "INFO 05-21 21:36:44 cuda.py:229] Using Flash Attention backend.\n", "INFO 05-21 21:36:44 model_runner.py:1110] Starting to load model unsloth/llama-3.2-3b-instruct-unsloth-bnb-4bit...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[W521 21:36:44.436078234 CUDAAllocatorConfig.h:28] Warning: expandable_segments not supported on this platform (function operator())\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO 05-21 21:36:45 loader.py:1089] Loading weights with BitsAndBytes quantization.  May take a while ...\n", "INFO 05-21 21:36:46 weight_utils.py:254] Using model weights format ['*.safetensors']\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "4ed93abd075f46baad58ea9eb3e0488e", "version_major": 2, "version_minor": 0}, "text/plain": ["Loading safetensors checkpoint shards:   0% Completed | 0/1 [00:00<?, ?it/s]\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "5ba3e92ae3934c2ea0ab4cae333c0cd0", "version_major": 2, "version_minor": 0}, "text/plain": ["Loading safetensors checkpoint shards:   0% Completed | 0/1 [00:00<?, ?it/s]\n"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["INFO 05-21 21:36:48 model_runner.py:1115] Loading model weights took 2.2405 GB\n", "INFO 05-21 21:36:48 punica_selector.py:18] Using PunicaWrapperGPU.\n", "INFO 05-21 21:36:49 worker.py:267] Memory profiling takes 1.38 seconds\n", "INFO 05-21 21:36:49 worker.py:267] the current vLLM instance can use total_gpu_memory (12.00GiB) x gpu_memory_utilization (0.82) = 9.86GiB\n", "INFO 05-21 21:36:49 worker.py:267] model weights take 2.24GiB; non_torch_memory takes 0.03GiB; PyTorch activation peak memory takes 0.89GiB; the rest of the memory reserved for KV Cache is 6.70GiB.\n", "INFO 05-21 21:36:50 executor_base.py:111] # cuda blocks: 3918, # CPU blocks: 585\n", "INFO 05-21 21:36:50 executor_base.py:116] Maximum concurrency for 256 tokens per request: 244.88x\n", "INFO 05-21 21:36:50 model_runner.py:1434] Capturing cudagraphs for decoding. This may lead to unexpected consequences if the model is not static. To run the model in eager mode, set 'enforce_eager=True' or use '--enforce-eager' in the CLI. If out-of-memory error occurs during cudagraph capture, consider decreasing `gpu_memory_utilization` or switching to eager mode. You can also reduce the `max_num_seqs` as needed to decrease memory usage.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Capturing CUDA graph shapes: 100%|██████████| 27/27 [00:16<00:00,  1.62it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO 05-21 21:37:07 model_runner.py:1562] Graph capturing finished in 17 secs, took 0.49 GiB\n", "INFO 05-21 21:37:07 llm_engine.py:436] init engine (profile, create kv cache, warmup model) took 18.73 seconds\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n", "Device set to use cuda:0\n"]}], "source": ["from unsloth import FastLanguageModel\n", "import torch\n", "# 适合 RTX 3060 的优化参数\n", "max_seq_length = 256  # 降低显存消耗\n", "lora_rank = 16  # LoRA 低秩训练，更省显存\n", "\n", "# 加载 Llama 3.1 8B - 禁用 vLLM 以解决内存问题\n", "model, tokenizer = FastLanguageModel.from_pretrained(  dtype=torch.float16,\n", "    model_name = \"meta-llama/Llama-3.2-3B-Instruct\", \n", "    max_seq_length = max_seq_length, \n", "    load_in_4bit = True,  # 4bit 量化，减少 75% 显存\n", "    fast_inference = True,  # 禁用 vLLM 加速以避免内存错误\n", "    max_lora_rank = lora_rank,\n", "    gpu_memory_utilization = 0.9,  # 增加显存使用率\n", ")\n", "\n", "# # 首先，将模型转换为 float16\n", "# model = model.to(torch.float16)\n", "# 如果使用了 LoRA，合并 LoRA 权重\n", "# if hasattr(model, \"merge_and_unload\"):\n", "#     model = model.merge_and_unload()\n", "    \n", "# # 优化后模型存储\n", "# model.save_pretrained(\"./optimized_model\",safe_serialization=False)\n", "# tokenizer.save_pretrained(\"./optimized_model\")\n", "\n", "from transformers import pipeline\n", "\n", "pipe = pipeline(\n", "    \"text-generation\",\n", "    model=model,\n", "    tokenizer=tokenizer,\n", "    max_new_tokens=512,\n", "    temperature=0.8,\n", "    top_p=0.95\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["2. 普通加载"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "a5bd7433b7054c608f59b58e6828c60d", "version_major": 2, "version_minor": 0}, "text/plain": ["Loading checkpoint shards:   0%|          | 0/2 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["Device set to use cuda:0\n"]}], "source": ["import torch\n", "from transformers import pipeline\n", "\n", "model_id = \"meta-llama/Llama-3.2-3B-Instruct\"\n", "pipe = pipeline(\n", "    \"text-generation\",\n", "    model=model_id,\n", "    torch_dtype=torch.bfloat16,\n", "    device_map=\"auto\",\n", ")\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Setting `pad_token_id` to `eos_token_id`:128001 for open-end generation.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["{'role': 'assistant', 'content': \"Yer lookin' fer a pirate's tale, eh? Alright then, matey! Yer in luck. I be Captain <PERSON><PERSON><PERSON>, the scurviest pirate chatbot to ever sail the Seven Seas. Me and me trusty computer be crewin' this here ship o' knowledge, ready to chart a course fer ye and share me treasure o' info. So hoist the sails and set course fer adventure, me hearty! What be bringin' ye to these waters?\"}\n"]}], "source": ["outputs = pipe(\n", "    messages,\n", "    max_new_tokens=256,\n", ")\n", "print(outputs[0][\"generated_text\"][-1])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Prompt: Summarize the paper on AI.\n", "Answer:  The paper is titled \"A Study on the Effects of Artificial Intelligence on Human Behavior\" (not the actual title, but I'll provide the actual title and a brief summary of the AI paper)\n", "\n", "**Actual Title:** \"The Impact of Artificial Intelligence on Human Behavior: A Systematic Review\"\n", "\n", "**Summary:**\n", "\n", "This systematic review examines the effects of artificial intelligence (AI) on human behavior, with a focus on its potential to influence decision-making, social interactions, and emotional well-being. The authors conducted a comprehensive literature search of 22 databases and identified 147 studies that met the inclusion criteria. The studies were categorized into four themes: (1) AI's impact on decision-making, (2) AI's influence on social interactions, (3) AI's effects on emotional well-being, and (4) AI's role in shaping human behavior.\n", "\n", "**Key Findings:**\n", "\n", "1. **Decision-making:** AI's impact on decision-making is complex and multifaceted. While AI can provide valuable insights and recommendations, it can also lead to biases and errors. The authors suggest that AI should be designed to facilitate human decision-making, rather than replacing it.\n", "2. **Social interactions:** AI can both facilitate and hinder social interactions. On one hand, AI-powered chatbots and virtual assistants can provide social support and companionship. On the other hand, excessive AI use can lead to social isolation and decreased human interaction.\n", "3. **Emotional well-being:** AI can have both positive and negative effects on emotional well-being. AI-powered therapy and support systems can provide emotional support and comfort, but excessive AI use can also lead to emotional dependence and decreased self-esteem.\n", "4. **Human behavior:** AI can shape human behavior in various ways, including influencing our values, attitudes, and behaviors. The authors suggest that AI should be designed to promote positive human behavior, rather than reinforcing negative patterns.\n", "\n", "**Conclusion:**\n", "\n", "The authors conclude that AI has the potential to both positively and negatively impact human behavior. While AI can provide valuable insights and support, it is essential to design AI systems that promote positive human behavior and minimize negative effects. The authors recommend a multidisciplinary approach to AI development, involving experts from various fields, to ensure that AI systems are designed with human well-being in mind.\n", "\n", "**Limitations:**\n", "\n", "The authors acknowledge several limitations of the study, including the reliance on existing literature and the potential for bias in the included studies. Future research should aim to address these limitations and provide more comprehensive insights into the effects of AI on human behavior.\n", "\n", "**Future Directions\n", "\n", "--------------------------------------------------\n", "\n", "Prompt: What is reinforcement learning?\n", "Answer: ?\n", "Reinforcement learning is a type of machine learning where an agent learns to take actions in an environment to maximize a reward signal. The agent learns through trial and error, receiving feedback in the form of rewards or penalties for its actions.\n", "\n", "Here's a simple example:\n", "\n", "1. An agent is placed in a room with a lever that can be pulled to receive a reward (e.g., a treat).\n", "2. The agent learns to pull the lever to receive the reward.\n", "3. However, if the agent pulls the lever incorrectly (e.g., too hard), it receives a penalty (e.g., a loud noise).\n", "4. The agent learns to associate the correct action (pulling the lever gently) with the reward and avoids the penalty.\n", "\n", "Reinforcement learning involves three key components:\n", "\n", "1. **Agent**: The entity that learns and takes actions in the environment.\n", "2. **Environment**: The external world that the agent interacts with.\n", "3. **Reward signal**: The feedback received by the agent for its actions, which helps it learn and make decisions.\n", "\n", "There are several types of reinforcement learning, including:\n", "\n", "1. **Episodic reinforcement learning**: The agent learns from a sequence of episodes, where each episode is a self-contained task.\n", "2. **Continuous reinforcement learning**: The agent learns from a continuous stream of experiences, where the environment changes over time.\n", "3. **Model-based reinforcement learning**: The agent learns a model of the environment and uses it to make decisions.\n", "4. **Model-free reinforcement learning**: The agent learns directly from the environment without a model.\n", "\n", "Reinforcement learning has many applications in areas such as:\n", "\n", "1. **Robotics**: Learning to control robots to perform tasks like grasping, manipulation, and navigation.\n", "2. **Game playing**: Learning to play games like Go, Poker, and Video Games.\n", "3. **Recommendation systems**: Learning to recommend products or services based on user behavior.\n", "4. **Autonomous vehicles**: Learning to navigate and make decisions in complex environments.\n", "\n", "I hope this helps! Let me know if you have any further questions.\n", "\n", "--------------------------------------------------\n", "\n"]}], "source": ["\n", "# prompts = [\"Summarize the paper on AI.\", \"What is reinforcement learning?\"]\n", "# for prompt in prompts:\n", "#     result = pipe(prompt)\n", "#     print(f\"Prompt: {prompt}\")\n", "#     print(f\"Answer: {result[0]['generated_text'][len(prompt):]}\") \n", "#     print(\"\\n\" + \"-\"*50 + \"\\n\")\n", "\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import torch\n", "from transformers import pipeline\n", "\n", "model_id = \"meta-llama/Llama-3.2-3B-Instruct\"\n", "pipe = pipeline(\n", "    \"text-generation\",\n", "    model=model_id,\n", "    torch_dtype=torch.bfloat16,\n", "    device_map=\"auto\",\n", ")\n", "messages = [\n", "    {\"role\": \"system\", \"content\": \"You are a pirate chatbot who always responds in pirate speak!\"},\n", "    {\"role\": \"user\", \"content\": \"Who are you?\"},\n", "]\n", "outputs = pipe(\n", "    messages,\n", "    max_new_tokens=256,\n", ")\n", "print(outputs[0][\"generated_text\"][-1])\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. LoRA 训练设置"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. 加载 LoRA 适配器 - 修复路径问题"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. 推理测试"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["WARNING 05-21 20:26:13 config.py:2448] Casting torch.bfloat16 to torch.float16.\n", "INFO 05-21 20:26:14 config.py:549] This model supports multiple tasks: {'generate', 'score', 'reward', 'embed', 'classify'}. Defaulting to 'generate'.\n", "WARNING 05-21 20:26:14 config.py:628] awq quantization is not fully optimized yet. The speed can be slower than non-quantized models.\n", "WARNING 05-21 20:26:14 arg_utils.py:1187] Chunked prefill is enabled by default for models with max_model_len > 32K. Currently, chunked prefill might not work with some features or models. If you encounter any issues, please disable chunked prefill by setting --enable-chunked-prefill=False.\n", "INFO 05-21 20:26:14 config.py:1555] Chunked prefill is enabled with max_num_batched_tokens=2048.\n", "WARNING 05-21 20:26:14 cuda.py:95] To see benefits of async output processing, enable CUDA graph. Since, enforce-eager is enabled, async output processor cannot be used\n", "WARNING 05-21 20:26:14 config.py:685] Async output processing is not supported on the current platform type cuda.\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "53aa41e7cae54b8483cb4039ccabcea8", "version_major": 2, "version_minor": 0}, "text/plain": ["params.json:   0%|          | 0.00/199 [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"ename": "ValueError", "evalue": "Cannot find the config file for awq", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[4], line 3\u001b[0m\n\u001b[1;32m      1\u001b[0m \u001b[38;5;28;01m<PERSON>rom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01mvllm\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mi<PERSON>rt\u001b[39;00m LLM, SamplingParams\n\u001b[0;32m----> 3\u001b[0m llm \u001b[38;5;241m=\u001b[39m \u001b[43mLLM\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m      4\u001b[0m \u001b[43m     \u001b[49m\u001b[43mmodel\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mmeta-llama/meta-Llama-3.1-8B-Instruct\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m  \u001b[49m\u001b[38;5;66;43;03m# Use original model path\u001b[39;49;00m\n\u001b[1;32m      5\u001b[0m \u001b[43m    \u001b[49m\u001b[43mtokenizer\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mmeta-llama/meta-Llama-3.1-8B-Instruct\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[1;32m      6\u001b[0m \u001b[43m  \u001b[49m\u001b[38;5;66;43;03m# model=\"./optimized_model\",\u001b[39;49;00m\n\u001b[1;32m      7\u001b[0m \u001b[43m  \u001b[49m\u001b[38;5;66;43;03m#         tokenizer=\"./optimized_model\",\u001b[39;49;00m\n\u001b[1;32m      8\u001b[0m \u001b[43m          \u001b[49m\u001b[43mdtype\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mfloat16\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[1;32m      9\u001b[0m \u001b[43m          \u001b[49m\u001b[43mquantization\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mawq\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\n\u001b[1;32m     10\u001b[0m \u001b[43m          \u001b[49m\u001b[43mgpu_memory_utilization\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;241;43m0.85\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[1;32m     11\u001b[0m \u001b[43m          \u001b[49m\u001b[43menforce_eager\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43;01mTrue\u001b[39;49;00m\u001b[43m,\u001b[49m\n\u001b[1;32m     12\u001b[0m \u001b[43m          \u001b[49m\u001b[38;5;66;43;03m# quantization=\"bitsandbytes\"\u001b[39;49;00m\n\u001b[1;32m     13\u001b[0m \u001b[43m        \u001b[49m\u001b[43m)\u001b[49m  \u001b[38;5;66;03m# 使用之前Unsloth优化的模型\u001b[39;00m\n\u001b[1;32m     14\u001b[0m sampling_params \u001b[38;5;241m=\u001b[39m SamplingParams(temperature\u001b[38;5;241m=\u001b[39m\u001b[38;5;241m0.8\u001b[39m, top_p\u001b[38;5;241m=\u001b[39m\u001b[38;5;241m0.95\u001b[39m, max_tokens\u001b[38;5;241m=\u001b[39m\u001b[38;5;241m512\u001b[39m)\n", "File \u001b[0;32m~/miniconda3/envs/unslothenv/lib/python3.11/site-packages/vllm/utils.py:1022\u001b[0m, in \u001b[0;36mdeprecate_args.<locals>.wrapper.<locals>.inner\u001b[0;34m(*args, **kwargs)\u001b[0m\n\u001b[1;32m   1015\u001b[0m             msg \u001b[38;5;241m+\u001b[39m\u001b[38;5;241m=\u001b[39m \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m \u001b[39m\u001b[38;5;132;01m{\u001b[39;00madditional_message\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m   1017\u001b[0m         warnings\u001b[38;5;241m.\u001b[39mwarn(\n\u001b[1;32m   1018\u001b[0m             \u001b[38;5;167;01mDeprecationWarning\u001b[39;00m(msg),\n\u001b[1;32m   1019\u001b[0m             stacklevel\u001b[38;5;241m=\u001b[39m\u001b[38;5;241m3\u001b[39m,  \u001b[38;5;66;03m# The inner function takes up one level\u001b[39;00m\n\u001b[1;32m   1020\u001b[0m         )\n\u001b[0;32m-> 1022\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mfn\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/miniconda3/envs/unslothenv/lib/python3.11/site-packages/vllm/entrypoints/llm.py:242\u001b[0m, in \u001b[0;36mLLM.__init__\u001b[0;34m(self, model, tokenizer, tokenizer_mode, skip_tokenizer_init, trust_remote_code, allowed_local_media_path, tensor_parallel_size, dtype, quantization, revision, tokenizer_revision, seed, gpu_memory_utilization, swap_space, cpu_offload_gb, enforce_eager, max_seq_len_to_capture, disable_custom_all_reduce, disable_async_output_proc, hf_overrides, mm_processor_kwargs, task, override_pooler_config, compilation_config, **kwargs)\u001b[0m\n\u001b[1;32m    239\u001b[0m \u001b[38;5;66;03m# Logic to switch between engines is done at runtime instead of import\u001b[39;00m\n\u001b[1;32m    240\u001b[0m \u001b[38;5;66;03m# to avoid import order issues\u001b[39;00m\n\u001b[1;32m    241\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mengine_class \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mget_engine_class()\n\u001b[0;32m--> 242\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mllm_engine \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mengine_class\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mfrom_engine_args\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m    243\u001b[0m \u001b[43m    \u001b[49m\u001b[43mengine_args\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43musage_context\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mUsageContext\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mLLM_CLASS\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    245\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mrequest_counter \u001b[38;5;241m=\u001b[39m Counter()\n", "File \u001b[0;32m~/miniconda3/envs/unslothenv/lib/python3.11/site-packages/vllm/engine/llm_engine.py:486\u001b[0m, in \u001b[0;36mLLMEngine.from_engine_args\u001b[0;34m(cls, engine_args, usage_context, stat_loggers)\u001b[0m\n\u001b[1;32m    484\u001b[0m \u001b[38;5;250m\u001b[39m\u001b[38;5;124;03m\"\"\"Creates an LLM engine from the engine arguments.\"\"\"\u001b[39;00m\n\u001b[1;32m    485\u001b[0m \u001b[38;5;66;03m# Create the engine configs.\u001b[39;00m\n\u001b[0;32m--> 486\u001b[0m engine_config \u001b[38;5;241m=\u001b[39m \u001b[43mengine_args\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mcreate_engine_config\u001b[49m\u001b[43m(\u001b[49m\u001b[43musage_context\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    487\u001b[0m executor_class \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mcls\u001b[39m\u001b[38;5;241m.\u001b[39m_get_executor_cls(engine_config)\n\u001b[1;32m    488\u001b[0m \u001b[38;5;66;03m# Create the LLM engine.\u001b[39;00m\n", "File \u001b[0;32m~/miniconda3/envs/unslothenv/lib/python3.11/site-packages/vllm/engine/arg_utils.py:1334\u001b[0m, in \u001b[0;36mEngineArgs.create_engine_config\u001b[0;34m(self, usage_context)\u001b[0m\n\u001b[1;32m   1323\u001b[0m         \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mValueError\u001b[39;00m(\n\u001b[1;32m   1324\u001b[0m             \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mInvalid module \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mm\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m in collect_detailed_traces. \u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m   1325\u001b[0m             \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mValid modules are \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mALLOWED_DETAILED_TRACE_MODULES\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m\"\u001b[39m)\n\u001b[1;32m   1326\u001b[0m observability_config \u001b[38;5;241m=\u001b[39m ObservabilityConfig(\n\u001b[1;32m   1327\u001b[0m     otlp_traces_endpoint\u001b[38;5;241m=\u001b[39m\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39motlp_traces_endpoint,\n\u001b[1;32m   1328\u001b[0m     collect_model_forward_time\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mmodel\u001b[39m\u001b[38;5;124m\"\u001b[39m \u001b[38;5;129;01min\u001b[39;00m detailed_trace_modules\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m   1331\u001b[0m     \u001b[38;5;129;01mor\u001b[39;00m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mall\u001b[39m\u001b[38;5;124m\"\u001b[39m \u001b[38;5;129;01min\u001b[39;00m detailed_trace_modules,\n\u001b[1;32m   1332\u001b[0m )\n\u001b[0;32m-> 1334\u001b[0m config \u001b[38;5;241m=\u001b[39m \u001b[43mVllmConfig\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m   1335\u001b[0m \u001b[43m    \u001b[49m\u001b[43mmodel_config\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mmodel_config\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1336\u001b[0m \u001b[43m    \u001b[49m\u001b[43mcache_config\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mcache_config\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1337\u001b[0m \u001b[43m    \u001b[49m\u001b[43mparallel_config\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mparallel_config\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1338\u001b[0m \u001b[43m    \u001b[49m\u001b[43mscheduler_config\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mscheduler_config\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1339\u001b[0m \u001b[43m    \u001b[49m\u001b[43mdevice_config\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mdevice_config\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1340\u001b[0m \u001b[43m    \u001b[49m\u001b[43mlora_config\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mlora_config\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1341\u001b[0m \u001b[43m    \u001b[49m\u001b[43mspeculative_config\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mspeculative_config\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1342\u001b[0m \u001b[43m    \u001b[49m\u001b[43mload_config\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mload_config\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1343\u001b[0m \u001b[43m    \u001b[49m\u001b[43mdecoding_config\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mdecoding_config\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1344\u001b[0m \u001b[43m    \u001b[49m\u001b[43mobservability_config\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mobservability_config\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1345\u001b[0m \u001b[43m    \u001b[49m\u001b[43mprompt_adapter_config\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mprompt_adapter_config\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1346\u001b[0m \u001b[43m    \u001b[49m\u001b[43mcompilation_config\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mcompilation_config\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1347\u001b[0m \u001b[43m    \u001b[49m\u001b[43mkv_transfer_config\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mkv_transfer_config\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1348\u001b[0m \u001b[43m    \u001b[49m\u001b[43madditional_config\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43madditional_config\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1349\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1351\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m envs\u001b[38;5;241m.\u001b[39mVLLM_USE_V1:\n\u001b[1;32m   1352\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_override_v1_engine_config(config)\n", "File \u001b[0;32m<string>:19\u001b[0m, in \u001b[0;36m__init__\u001b[0;34m(self, model_config, cache_config, parallel_config, scheduler_config, device_config, load_config, lora_config, speculative_config, decoding_config, observability_config, prompt_adapter_config, quant_config, compilation_config, kv_transfer_config, additional_config, instance_id)\u001b[0m\n", "File \u001b[0;32m~/miniconda3/envs/unslothenv/lib/python3.11/site-packages/vllm/config.py:3279\u001b[0m, in \u001b[0;36mVllmConfig.__post_init__\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m   3274\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mprompt_adapter_config\u001b[38;5;241m.\u001b[39mverify_with_model_config(\n\u001b[1;32m   3275\u001b[0m         \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mmodel_config)\n\u001b[1;32m   3277\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mquant_config \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mN<PERSON>\u001b[39;00m \u001b[38;5;129;01mand\u001b[39;00m \\\n\u001b[1;32m   3278\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mmodel_config \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m \u001b[38;5;129;01mand\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mload_config \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[0;32m-> 3279\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mquant_config \u001b[38;5;241m=\u001b[39m \u001b[43mVllmConfig\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_get_quantization_config\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m   3280\u001b[0m \u001b[43m        \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mmodel_config\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mload_config\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   3282\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01mvllm\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mplatforms\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m current_platform\n\u001b[1;32m   3283\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mscheduler_config \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m \u001b[38;5;129;01mand\u001b[39;00m \\\n\u001b[1;32m   3284\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mmodel_config \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m \u001b[38;5;129;01mand\u001b[39;00m \\\n\u001b[1;32m   3285\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mscheduler_config\u001b[38;5;241m.\u001b[39mchunked_prefill_enabled \u001b[38;5;129;01mand\u001b[39;00m \\\n\u001b[1;32m   3286\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mmodel_config\u001b[38;5;241m.\u001b[39mdtype \u001b[38;5;241m==\u001b[39m torch\u001b[38;5;241m.\u001b[39mfloat32 \u001b[38;5;129;01mand\u001b[39;00m \\\n\u001b[1;32m   3287\u001b[0m     current_platform\u001b[38;5;241m.\u001b[39mget_device_capability() \u001b[38;5;241m==\u001b[39m (\u001b[38;5;241m7\u001b[39m, \u001b[38;5;241m5\u001b[39m):\n", "File \u001b[0;32m~/miniconda3/envs/unslothenv/lib/python3.11/site-packages/vllm/config.py:3222\u001b[0m, in \u001b[0;36mVllmConfig._get_quantization_config\u001b[0;34m(model_config, load_config)\u001b[0m\n\u001b[1;32m   3219\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m model_config\u001b[38;5;241m.\u001b[39mquantization \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[1;32m   3220\u001b[0m     \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01mvllm\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mmodel_executor\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mmodel_loader\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mweight_utils\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m (\n\u001b[1;32m   3221\u001b[0m         get_quant_config)\n\u001b[0;32m-> 3222\u001b[0m     quant_config \u001b[38;5;241m=\u001b[39m \u001b[43mget_quant_config\u001b[49m\u001b[43m(\u001b[49m\u001b[43mmodel_config\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mload_config\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   3223\u001b[0m     capability_tuple \u001b[38;5;241m=\u001b[39m current_platform\u001b[38;5;241m.\u001b[39mget_device_capability()\n\u001b[1;32m   3225\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m capability_tuple \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n", "File \u001b[0;32m~/miniconda3/envs/unslothenv/lib/python3.11/site-packages/vllm/model_executor/model_loader/weight_utils.py:193\u001b[0m, in \u001b[0;36mget_quant_config\u001b[0;34m(model_config, load_config)\u001b[0m\n\u001b[1;32m    188\u001b[0m quant_config_files \u001b[38;5;241m=\u001b[39m [\n\u001b[1;32m    189\u001b[0m     f \u001b[38;5;28;01mfor\u001b[39;00m f \u001b[38;5;129;01min\u001b[39;00m config_files \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28many\u001b[39m(\n\u001b[1;32m    190\u001b[0m         f\u001b[38;5;241m.\u001b[39mendswith(x) \u001b[38;5;28;01mfor\u001b[39;00m x \u001b[38;5;129;01min\u001b[39;00m possible_config_filenames)\n\u001b[1;32m    191\u001b[0m ]\n\u001b[1;32m    192\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mlen\u001b[39m(quant_config_files) \u001b[38;5;241m==\u001b[39m \u001b[38;5;241m0\u001b[39m:\n\u001b[0;32m--> 193\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mValueError\u001b[39;00m(\n\u001b[1;32m    194\u001b[0m         \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mCannot find the config file for \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mmodel_config\u001b[38;5;241m.\u001b[39mquantization\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m\"\u001b[39m)\n\u001b[1;32m    195\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mlen\u001b[39m(quant_config_files) \u001b[38;5;241m>\u001b[39m \u001b[38;5;241m1\u001b[39m:\n\u001b[1;32m    196\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mValueError\u001b[39;00m(\n\u001b[1;32m    197\u001b[0m         \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mFound multiple config files for \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mmodel_config\u001b[38;5;241m.\u001b[39mquantization\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m: \u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m    198\u001b[0m         \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mquant_config_files\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m\"\u001b[39m)\n", "\u001b[0;31mValueError\u001b[0m: Cannot find the config file for awq"]}], "source": ["# from vllm import LLM, SamplingParams\n", "\n", "# llm = LLM(\n", "#      model=\"meta-llama/meta-Llama-3.1-8B-Instruct\",  # Use original model path\n", "#     tokenizer=\"meta-llama/meta-Llama-3.1-8B-Instruct\",\n", "#   # model=\"./optimized_model\",\n", "#   #         tokenizer=\"./optimized_model\",\n", "#           dtype=\"float16\",\n", "#           quantization=\"awq\", \n", "#           gpu_memory_utilization=0.85,\n", "#           enforce_eager=True,\n", "#           # quantization=\"bitsandbytes\"\n", "#         )  # 使用之前Unsloth优化的模型\n", "# sampling_params = SamplingParams(temperature=0.8, top_p=0.95, max_tokens=512)\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 推理示例\n", "prompts = [\"Summarize the paper on AI.\", \"What is reinforcement learning?\"]\n", "outputs = llm.generate(prompts, sampling_params)\n", "\n", "# 输出结果\n", "for output in outputs:\n", "    print(f\"Prompt: {output.prompt}\")\n", "    print(f\"Answer: {output.outputs[0].text}\\n\")"]}], "metadata": {"colab": {"provenance": []}, "kernelspec": {"display_name": "unslothenv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 0}
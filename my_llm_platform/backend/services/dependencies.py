"""
外部サービス依存関係管理モジュール
Redis、RabbitM<PERSON>、Qdrant、Keycloakなどの外部サービスへの接続を管理します
"""
import redis
import logging
import socket
import time
from typing import Tuple, Optional
from ..config import settings

logger = logging.getLogger(__name__)

def check_service_availability(host: str, port: int, service_name: str, timeout: int = 1) -> Tuple[bool, Optional[str]]:
    """
    指定されたホストとポートでサービスが利用可能かどうかを確認します

    Args:
        host: ホスト名またはIPアドレス
        port: ポート番号
        service_name: サービス名（ログ用）
        timeout: タイムアウト秒数

    Returns:
        (利用可能かどうか, エラーメッセージ)
    """
    try:
        # ソケットを作成
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(timeout)

        # 接続を試行
        result = sock.connect_ex((host, port))
        sock.close()

        if result == 0:
            return True, None
        else:
            return False, f"{service_name}に接続できませんでした。ポート{port}が開いていません。"
    except Exception as e:
        return False, f"{service_name}の接続確認中にエラーが発生しました: {e}"

def init_redis():
    """
    Redisクライアントを初期化します

    Returns:
        Redisクライアントのインスタンスまたはダミークライアント
    """
    # Redisが有効かどうか確認
    if not settings.ENABLE_REDIS:
        logger.warning("Redisは無効化されています。ダミークライアントを使用します。")
        return DummyRedis()

    # Redis URLからホストとポートを抽出
    try:
        # redis://localhost:6379 形式のURLを解析
        parts = settings.REDIS_URL.split('://')
        if len(parts) > 1:
            host_port = parts[1].split(':')  # localhost:6379 を分割
            host = host_port[0]  # localhost
            port = int(host_port[1].split('/')[0])  # 6379
        else:
            host = "localhost"
            port = 6379

        # Redisサービスの可用性を確認
        available, error_msg = check_service_availability(host, port, "Redis")

        if available:
            # Redisクライアントを返す
            return redis.from_url(settings.REDIS_URL)
        else:
            # Redisが利用できない場合はエラーを記録
            logger.error(f"Redisサービスに接続できません: {error_msg}")
            return DummyRedis()
    except Exception as e:
        logger.error(f"Redis初期化中にエラーが発生しました: {e}")
        return DummyRedis()

class DummyRedis:
    """
    Redisが利用できない場合のダミークライアント
    """
    def __init__(self):
        self.data = {}

    def set(self, key, value, *args, **kwargs):
        self.data[key] = value
        return True

    def get(self, key):
        return self.data.get(key)

    def delete(self, key):
        if key in self.data:
            del self.data[key]
        return True

def init_rabbitmq():
    """
    RabbitMQの接続を確認します
    """
    # RabbitMQが有効かどうか確認
    if not settings.ENABLE_RABBITMQ:
        logger.warning("RabbitMQは無効化されています。")
        return False

    # RabbitMQ URLからホストとポートを抽出
    try:
        # amqp://user:password@localhost:5672// 形式のURLを解析
        parts = settings.RABBITMQ_URL.split('@')
        if len(parts) > 1:
            host_port = parts[1].split(':')  # localhost:5672// を分割
            host = host_port[0]  # localhost
            port = int(host_port[1].split('/')[0])  # 5672
        else:
            host = "localhost"
            port = 5672

        # RabbitMQサービスの可用性を確認
        available, error_msg = check_service_availability(host, port, "RabbitMQ")

        if available:
            logger.info("RabbitMQサービスに接続できました")
            return True
        else:
            logger.error(f"RabbitMQサービスに接続できません: {error_msg}")
            return False
    except Exception as e:
        logger.error(f"RabbitMQ接続確認中にエラーが発生しました: {e}")
        return False

def init_vector_database():
    """
    ベクトルデータベースの接続を確認します
    選択されたベクトルデータベースタイプに基づいて適切な初期化関数を呼び出します
    """
    # ベクトルデータベース機能が有効かどうか確認
    if not settings.VECTOR_ENABLED_FLAG:
        logger.warning("ベクトルデータベース機能は無効化されています。")
        return False

    # 選択されたベクトルデータベースタイプに基づいて初期化
    vector_db_type = settings.VECTOR_DATABASE_TYPE.lower()

    if vector_db_type == "qdrant":
        return init_qdrant_connection()
    elif vector_db_type == "weaviate":
        return init_weaviate_connection()
    elif vector_db_type == "supabase":
        return init_supabase_connection()
    elif vector_db_type == "faiss":
        # FAISSはインメモリで動作するため、常に利用可能
        logger.info("FAISSベクトルデータベースを使用します（インメモリ）")
        return True
    else:
        logger.warning(f"不明なベクトルデータベースタイプ: {vector_db_type}")
        return False

def init_qdrant_connection():
    """
    Qdrantサービスの接続を確認します
    """
    # Qdrantサービスの可用性を確認
    available, error_msg = check_service_availability(settings.QDRANT_HOST, settings.QDRANT_PORT, "Qdrant")

    if available:
        logger.info("Qdrantサービスに接続できました")
        return True
    else:
        logger.error(f"Qdrantサービスに接続できません: {error_msg}")
        return False

def init_weaviate_connection():
    """
    Weaviateサービスの接続を確認します
    """
    # Weaviate URLからホストとポートを抽出
    try:
        if settings.WEAVIATE_URL:
            # http://localhost:8080 形式のURLを解析
            parts = settings.WEAVIATE_URL.split('://')
            if len(parts) > 1:
                host_port = parts[1].split(':')  # localhost:8080 を分割
                host = host_port[0]  # localhost
                if len(host_port) > 1:
                    port = int(host_port[1].split('/')[0])  # 8080
                else:
                    port = 80 if parts[0] == 'http' else 443
            else:
                host = settings.WEAVIATE_HOST or "localhost"
                port = settings.WEAVIATE_PORT or 8080
        else:
            host = settings.WEAVIATE_HOST or "localhost"
            port = settings.WEAVIATE_PORT or 8080

        # Weaviateサービスの可用性を確認
        available, error_msg = check_service_availability(host, port, "Weaviate")

        if available:
            logger.info("Weaviateサービスに接続できました")
            return True
        else:
            logger.error(f"Weaviateサービスに接続できません: {error_msg}")
            return False
    except Exception as e:
        logger.error(f"Weaviate接続確認中にエラーが発生しました: {e}")
        return False

def init_supabase_connection():
    """
    Supabaseサービスの接続を確認します
    """
    # Supabase接続文字列からホストとポートを抽出
    try:
        # postgresql://postgres:password@localhost:54322/postgres 形式の接続文字列を解析
        if not settings.SUPABASE_CONNECTION_STRING:
            logger.error("SUPABASE_CONNECTION_STRINGが設定されていません")
            return False

        parts = settings.SUPABASE_CONNECTION_STRING.split('@')
        if len(parts) > 1:
            host_port = parts[1].split(':')  # localhost:54322/postgres を分割
            host = host_port[0]  # localhost
            if len(host_port) > 1:
                port_db = host_port[1].split('/')  # 54322/postgres を分割
                port = int(port_db[0])  # 54322
            else:
                port = 5432  # デフォルトのPostgreSQLポート
        else:
            logger.error("Supabase接続文字列の形式が無効です")
            return False

        # Supabaseサービスの可用性を確認
        available, error_msg = check_service_availability(host, port, "Supabase PostgreSQL")

        if available:
            logger.info("Supabase PostgreSQLサービスに接続できました")
            return True
        else:
            logger.error(f"Supabase PostgreSQLサービスに接続できません: {error_msg}")
            return False
    except Exception as e:
        logger.error(f"Supabase接続確認中にエラーが発生しました: {e}")
        return False

def init_keycloak():
    """
    Keycloakサービスの接続を確認します
    """
    # Keycloakが有効かどうか確認
    if not settings.ENABLE_KEYCLOAK:
        logger.warning("Keycloak認証サービスは無効化されています。")
        return False

    # Keycloak URLからホストとポートを抽出
    try:
        # http://localhost:8080 形式のURLを解析
        parts = settings.KEYCLOAK_SERVER_URL.split('://')
        if len(parts) > 1:
            host_port = parts[1].split(':')  # localhost:8080 を分割
            host = host_port[0]  # localhost
            if len(host_port) > 1:
                port = int(host_port[1].split('/')[0])  # 8080
            else:
                port = 80 if parts[0] == 'http' else 443
        else:
            host = "localhost"
            port = 8080

        # Keycloakサービスの可用性を確認
        available, error_msg = check_service_availability(host, port, "Keycloak")

        if available:
            logger.info("Keycloakサービスに接続できました")
            return True
        else:
            logger.error(f"Keycloakサービスに接続できません: {error_msg}")
            return False
    except Exception as e:
        logger.error(f"Keycloak接続確認中にエラーが発生しました: {e}")
        return False

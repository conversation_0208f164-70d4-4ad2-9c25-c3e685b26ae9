{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Llama 3.1 8B GRPO 直接推理 - 修复版\n", "\n", "本笔记本解决了以下问题：\n", "1. 无法将 bitsandbytes 量化模型转换为 float16 的错误\n", "2. vLLM 加载量化模型时的 KeyError 错误\n", "3. AWQ 配置文件缺失的问题"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import torch\n", "from unsloth import FastLanguageModel, PatchFastRL\n", "PatchFastRL(\"GRPO\", FastLanguageModel)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 方案 1: 使用 Unsloth 进行推理\n", "\n", "这个方案使用 Unsloth 自己的推理功能，而不是尝试与 vLLM 集成。\n", "这避免了格式转换问题，直接使用 Unsloth 的优化。"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import torch\n", "from unsloth import FastLanguageModel\n", "from transformers import pipeline\n", "\n", "# 适合 RTX 3060 的优化参数\n", "max_seq_length = 256  # 降低显存消耗\n", "lora_rank = 16  # LoRA 低秩训练，更省显存\n", "\n", "# 加载 Llama 3.1 8B - 使用 Unsloth 的优化\n", "model, tokenizer = FastLanguageModel.from_pretrained( \n", "    model_name = \"meta-llama/meta-Llama-3.1-8B-Instruct\", \n", "    max_seq_length = max_seq_length, \n", "    load_in_4bit = True,  # 4bit 量化，减少 75% 显存\n", "    fast_inference = True,  # 启用 Unsloth 自己的快速推理\n", "    max_lora_rank = lora_rank,\n", "    gpu_memory_utilization = 0.9,  # 增加显存使用率\n", "    torch_dtype = torch.float16,  # 直接指定 dtype\n", ")\n", "\n", "# 如果使用了 LoRA，合并 LoRA 权重\n", "if hasattr(model, \"merge_and_unload\"):\n", "    model = model.merge_and_unload()\n", "\n", "# 创建文本生成管道\n", "pipe = pipeline(\n", "    \"text-generation\",\n", "    model=model,\n", "    tokenizer=tokenizer,\n", "    max_new_tokens=512,\n", "    temperature=0.8,\n", "    top_p=0.95\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 推理示例\n", "prompts = [\"Summarize the paper on AI.\", \"What is reinforcement learning?\"]\n", "for prompt in prompts:\n", "    result = pipe(prompt)\n", "    print(f\"Prompt: {prompt}\")\n", "    print(f\"Answer: {result[0]['generated_text'][len(prompt):]}\") \n", "    print(\"\\n\" + \"-\"*50 + \"\\n\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 方案 2: 使用 vLLM 直接加载原始模型\n", "\n", "这个方案跳过 Unsloth 的优化，直接使用 vLLM 加载原始模型。\n", "vLLM 本身已经有很好的优化，所以性能应该仍然很好。"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from vllm import LLM, SamplingParams\n", "\n", "# 直接使用 vLLM 加载原始模型\n", "# 不使用 AWQ 量化，因为它需要预先量化的模型\n", "llm = LLM(\n", "    model=\"meta-llama/meta-Llama-3.1-8B-Instruct\",  # 使用原始模型路径\n", "    tokenizer=\"meta-llama/meta-Llama-3.1-8B-Instruct\",\n", "    dtype=\"float16\",  # 使用 float16 以减少内存使用\n", "    # 不使用 quantization=\"awq\"，因为它需要预先量化的模型\n", "    gpu_memory_utilization=0.85,\n", "    enforce_eager=True\n", ")\n", "\n", "sampling_params = SamplingParams(temperature=0.8, top_p=0.95, max_tokens=512)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 推理示例\n", "prompts = [\"Summarize the paper on AI.\", \"What is reinforcement learning?\"]\n", "outputs = llm.generate(prompts, sampling_params)\n", "\n", "# 输出结果\n", "for output in outputs:\n", "    print(f\"Prompt: {output.prompt}\")\n", "    print(f\"Answer: {output.outputs[0].text}\")\n", "    print(\"\\n\" + \"-\"*50 + \"\\n\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 方案 3: 使用 vLLM 的 BitsAndBytes 量化\n", "\n", "如果你的 GPU 内存有限，可以尝试使用 vLLM 的 BitsAndBytes 量化。\n", "这与 Unsloth 使用的量化方法相同，但是由 vLLM 直接管理。"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from vllm import LLM, SamplingParams\n", "\n", "# 使用 vLLM 的 BitsAndBytes 量化\n", "llm_quantized = LLM(\n", "    model=\"meta-llama/meta-Llama-3.1-8B-Instruct\",\n", "    tokenizer=\"meta-llama/meta-Llama-3.1-8B-Instruct\",\n", "    dtype=\"float16\",\n", "    quantization=\"bitsandbytes\",  # 使用 vLLM 的 BitsAndBytes 量化\n", "    gpu_memory_utilization=0.85,\n", "    enforce_eager=True\n", ")\n", "\n", "sampling_params = SamplingParams(temperature=0.8, top_p=0.95, max_tokens=512)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 推理示例\n", "prompts = [\"Summarize the paper on AI.\", \"What is reinforcement learning?\"]\n", "outputs = llm_quantized.generate(prompts, sampling_params)\n", "\n", "# 输出结果\n", "for output in outputs:\n", "    print(f\"Prompt: {output.prompt}\")\n", "    print(f\"Answer: {output.outputs[0].text}\")\n", "    print(\"\\n\" + \"-\"*50 + \"\\n\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 性能比较\n", "\n", "以上三种方案各有优缺点：\n", "\n", "1. **Unsloth 推理**：利用 Unsloth 的优化，但可能不如 vLLM 快\n", "2. **vLLM 直接加载**：利用 vLLM 的优化，但内存使用较高\n", "3. **vLLM + BitsAndBytes**：结合 vLLM 的速度和量化的内存效率\n", "\n", "根据你的硬件和需求，选择最适合的方案。"]}], "metadata": {"colab": {"provenance": []}, "kernelspec": {"display_name": "unslothenv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 0}
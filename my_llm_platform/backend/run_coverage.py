#!/usr/bin/env python
"""
テストカバレッジ実行スクリプト
"""
import os
import sys
import subprocess

if __name__ == "__main__":
    # 現在のディレクトリ
    current_dir = os.path.dirname(os.path.abspath(__file__))
    
    # テストディレクトリへのパス
    test_dir = os.path.join(current_dir, "tests")
    
    # カバレッジレポートディレクトリ
    coverage_dir = os.path.join(current_dir, "coverage")
    os.makedirs(coverage_dir, exist_ok=True)
    
    # カバレッジ設定ファイルを作成
    coverage_rc = os.path.join(current_dir, ".coveragerc")
    with open(coverage_rc, "w") as f:
        f.write("""
[run]
source = .
omit = 
    */tests/*
    */venv/*
    */env/*
    */.venv/*
    */.env/*
    */site-packages/*
    run_*.py
    */migrations/*
    */alembic/*

[report]
exclude_lines =
    pragma: no cover
    def __repr__
    raise NotImplementedError
    if __name__ == .__main__.:
    pass
    raise ImportError
""")
    
    # コマンドライン引数を解析
    args = sys.argv[1:] if len(sys.argv) > 1 else []
    
    # カバレッジを実行
    cmd = [
        "pytest",
        "--cov=.",
        "--cov-report=term",
        f"--cov-report=html:{os.path.join(coverage_dir, 'html')}",
        f"--cov-report=xml:{os.path.join(coverage_dir, 'coverage.xml')}",
        f"--cov-config={coverage_rc}",
        test_dir
    ] + args
    
    print(f"Running: {' '.join(cmd)}")
    result = subprocess.run(cmd)
    
    # 結果を表示
    if result.returncode == 0:
        print(f"\nカバレッジレポートが生成されました: {os.path.join(coverage_dir, 'html', 'index.html')}")
    
    sys.exit(result.returncode)

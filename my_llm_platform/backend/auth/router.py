from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.security import OAuth2PasswordRequestForm
from typing import Dict, Any
import logging
from . import auth_manager, get_current_user
from .models import UserInfo
from ..config import settings

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/auth", tags=["Auth APIs"])

@router.post("/login")
async def login(form_data: OAuth2PasswordRequestForm = Depends()):
    """
    ユーザー認証を行い、アクセストークンを返します。
    """
    # 自前アカウントテストモードの場合
    if settings.is_self_hosted_test_mode:
        logger.info(f"自前アカウントテストモードでログイン: {form_data.username}")
        # 設定ファイルのユーザー情報を返す
        return {
            "token": "test_token",
            "user": {
                "id": settings.TEST_USER_ID,
                "username": settings.TEST_USER_NAME,
                "role": "admin" if "admin" in settings.TEST_USER_ROLES else "user",
                "roles": settings.TEST_USER_ROLES
            }
        }
    # 本番環境モードの場合
    elif settings.is_self_hosted_mode and not settings.is_self_hosted_test_mode:
        try:
            # Keycloakでユーザー認証を行う
            token = auth_manager.user_login(
                username=form_data.username,
                password=form_data.password
            )

            # ユーザー情報を取得
            user_info = auth_manager.get_user_info(token)

            # ユーザーのロールを取得
            roles = user_info.get("realm_access", {}).get("roles", [])

            # メインロールを決定
            # 優先順位: admin > sales > user
            main_role = "user"  # デフォルトは一般ユーザー
            if "admin" in roles:
                main_role = "admin"
            elif "sales" in roles:
                main_role = "sales"

            # フロントエンドに返すレスポンスを作成
            return {
                "token": token,
                "user": {
                    "id": user_info.get("sub", ""),
                    "username": user_info.get("preferred_username", form_data.username),
                    "role": main_role,
                    "roles": roles
                }
            }
        except Exception as e:
            logger.error(f"ログインエラー: {e}")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="ユーザー名またはパスワードが無効です"
            )

@router.post("/logout")
async def logout(user: UserInfo = Depends(get_current_user)):
    """
    ユーザーをログアウトします。
    """
    # ローカルテストモードでも本番環境モードでも同じ処理
    # 実際のログアウト処理はフロントエンドで行われるため、
    # このエンドポイントは主に認証確認用です
    logger.info(f"ユーザーがログアウトしました: {user.username}")
    return {"detail": "ログアウトに成功しました"}

@router.get("/me")
async def get_user_profile(user: UserInfo = Depends(get_current_user)):
    """
    現在のユーザーのプロファイル情報を返します。
    """
    return {
        "user": {
            "id": user.id,
            "username": user.username,
            "email": user.email,
            "roles": user.roles,
            "role": "admin" if user.is_admin else "sales" if user.is_sales else "user"
        }
    }

@router.get("/status")
async def auth_status():
    """
    認証システムの状態を取得します。
    """
    return {
        "auth_mode": settings.AUTH_MODE,
        "is_self_hosted_test_mode": settings.is_self_hosted_test_mode,
        "is_self_hosted_mode": settings.is_self_hosted_mode,
        "is_enterprise_mode": settings.is_enterprise_mode
    }

from fastapi import Depends, HTTPException, status, Header
from typing import Optional, Dict, Any, List
import logging
from .models import UserInfo, UserRole
from ..config import settings

logger = logging.getLogger(__name__)

# 自前アカウントテストモードの認証マネージャー
class SelfHostedTestAuthManager:
    """
    自前アカウントテストモード用の認証マネージャー
    実際の認証サーバーを使わずに、設定ファイルに基づいて認証を行います。
    """

    def __init__(self):
        """
        自前アカウントテストモード用の認証マネージャーを初期化します。
        """
        # 認証モードとユーザー権限に関する情報をログに出力
        logger.info(f"認証モード: 自前アカウントテストモード")
        logger.info(f"テストユーザー情報: ユーザー名={settings.TEST_USER_NAME}, ロール={settings.TEST_USER_ROLES}")

        # 管理者権限の確認
        is_admin = "admin" in settings.TEST_USER_ROLES
        # 営業権限の確認
        is_sales = "sales" in settings.TEST_USER_ROLES
        # 一般ユーザー権限の確認
        is_user = "user" in settings.TEST_USER_ROLES

        logger.info(f"テストユーザー権限: 管理者={is_admin}, 営業={is_sales}, 一般ユーザー={is_user}")

    def add_swagger_config(self, app):
        """
        Swagger UIの設定を追加します（互換性のため）
        """
        # テストモードでは何もしない
        pass

    def user_login(self, username: str, password: str):
        """
        ユーザーログインをシミュレートします。
        実際の認証は行わず、常に成功します。
        """
        logger.info(f"テストモードでログイン: {username}")
        return "test_token"

    def get_user_info(self, token: str):
        """
        ユーザー情報を取得します。
        実際のトークン検証は行わず、常に設定ファイルのユーザー情報を返します。
        """
        return {
            "sub": settings.TEST_USER_ID,
            "preferred_username": settings.TEST_USER_NAME,
            "email": settings.TEST_USER_EMAIL,
            "realm_access": {
                "roles": settings.TEST_USER_ROLES
            }
        }

# 認証マネージャーのインスタンスを作成
auth_manager = SelfHostedTestAuthManager()

# 現在のユーザーを取得する依存関数
def get_current_user(authorization: Optional[str] = Header(None)) -> UserInfo:
    """
    現在のユーザーを取得します。
    自前アカウントテストモードでは、常に設定ファイルで定義されたユーザーを返します。

    引数:
        authorization: 認証ヘッダー（テストモードでは無視されます）

    戻り値:
        UserInfo: ユーザー情報
    """
    # 認証ヘッダーの形式チェック（実際の検証は行わない）
    if authorization:
        prefix, _, token = authorization.partition(" ")
        if prefix.lower() != "bearer" or not token:
            logger.warning("無効な認証ヘッダー形式ですが、テストモードのため無視します")

    # 設定ファイルで定義されたユーザー情報を返す
    return UserInfo(
        id=settings.TEST_USER_ID,
        username=settings.TEST_USER_NAME,
        email=settings.TEST_USER_EMAIL,
        roles=settings.TEST_USER_ROLES
    )

# 管理者権限を持つユーザーを取得する依存関数
def get_current_admin(user: UserInfo = Depends(get_current_user)):
    """
    管理者権限を持つユーザーを取得します。
    管理者権限がない場合は403エラーを返します。
    """
    if not user.is_admin:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="この操作には管理者権限が必要です"
        )
    return user

# 営業権限を持つユーザーを取得する依存関数
def get_current_sales(user: UserInfo = Depends(get_current_user)):
    """
    営業権限を持つユーザーを取得します。
    営業権限がない場合は403エラーを返します。
    管理者は営業権限も持つとみなされます。
    """
    if not (user.is_sales or user.is_admin):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="この操作には営業権限が必要です"
        )
    return user

# 一般ユーザー権限を持つユーザーを取得する依存関数
def get_current_regular_user(user: UserInfo = Depends(get_current_user)):
    """
    一般ユーザー権限を持つユーザーを取得します。
    一般ユーザー権限がない場合は403エラーを返します。
    管理者と営業担当者は一般ユーザー権限も持つとみなされます。
    """
    if not (user.is_regular_user or user.is_admin or user.is_sales):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="この操作には一般ユーザー権限が必要です"
        )
    return user

__all__ = ["auth_manager", "get_current_user", "get_current_admin", "get_current_sales", "get_current_regular_user", "UserInfo", "UserRole"]

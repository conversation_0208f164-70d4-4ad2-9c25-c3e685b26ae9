"""
ローカルテストモード用の認証モジュール
実際の認証サーバーを使わずに、設定ファイルに基づいて認証を行います。
"""

from fastapi import Depends, HTTPException, status, Header
from typing import Optional, Dict, Any, List
import logging
from ..config import settings
from .models import UserInfo

logger = logging.getLogger(__name__)

class LocalAuthManager:
    """
    ローカルテストモード用の認証マネージャー
    実際の認証サーバーを使わずに、設定ファイルに基づいて認証を行います。
    """
    
    def __init__(self):
        """
        ローカルテストモード用の認証マネージャーを初期化します。
        """
        self.test_user = settings.LOCAL_TEST_USER
        logger.info(f"ローカルテストモード認証マネージャーを初期化しました。ユーザー: {self.test_user['username']}")
    
    def add_swagger_config(self, app):
        """
        Swagger UIの設定を追加します（互換性のため）
        """
        # ローカルテストモードでは何もしない
        pass
    
    def get_current_user(self):
        """
        現在のユーザーを取得する依存関数を返します。
        ローカルテストモードでは、常に設定ファイルで定義されたユーザーを返します。
        """
        def _get_current_user(authorization: Optional[str] = Header(None)) -> UserInfo:
            """
            現在のユーザーを取得します。
            ローカルテストモードでは、常に設定ファイルで定義されたユーザーを返します。
            
            Args:
                authorization: 認証ヘッダー（ローカルテストモードでは無視されます）
                
            Returns:
                UserInfo: ユーザー情報
            """
            # 認証ヘッダーの形式チェック（実際の検証は行わない）
            if authorization:
                prefix, _, token = authorization.partition(" ")
                if prefix.lower() != "bearer" or not token:
                    logger.warning("無効な認証ヘッダー形式ですが、ローカルテストモードのため無視します")
            
            # 設定ファイルで定義されたユーザー情報を返す
            return UserInfo(
                id=self.test_user["id"],
                username=self.test_user["username"],
                email=self.test_user["email"],
                roles=self.test_user["roles"]
            )
        
        return _get_current_user

# ローカルテストモード用の認証マネージャーのインスタンスを作成
local_auth_manager = LocalAuthManager()

# 現在のユーザーを取得する依存関数
get_current_user = local_auth_manager.get_current_user()

# 管理者権限を持つユーザーを取得する依存関数
def get_current_admin(user: UserInfo = Depends(get_current_user)):
    """
    管理者権限を持つユーザーを取得します。
    管理者権限がない場合は403エラーを返します。
    """
    if not user.is_admin:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="この操作には管理者権限が必要です"
        )
    return user

# 営業権限を持つユーザーを取得する依存関数
def get_current_sales(user: UserInfo = Depends(get_current_user)):
    """
    営業権限を持つユーザーを取得します。
    営業権限がない場合は403エラーを返します。
    管理者は営業権限も持つとみなされます。
    """
    if not (user.is_sales or user.is_admin):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="この操作には営業権限が必要です"
        )
    return user

# 一般ユーザー権限を持つユーザーを取得する依存関数
def get_current_regular_user(user: UserInfo = Depends(get_current_user)):
    """
    一般ユーザー権限を持つユーザーを取得します。
    一般ユーザー権限がない場合は403エラーを返します。
    管理者と営業担当者は一般ユーザー権限も持つとみなされます。
    """
    if not (user.is_regular_user or user.is_admin or user.is_sales):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="この操作には一般ユーザー権限が必要です"
        )
    return user

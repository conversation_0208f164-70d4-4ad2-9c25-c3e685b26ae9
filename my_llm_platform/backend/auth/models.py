from pydantic import BaseModel
from typing import List, Optional
from enum import Enum

class UserRole(str, Enum):
    """
    ユーザーロールを表す列挙型
    """
    ADMIN = "admin"          # 管理者権限
    USER = "user"           # 一般ユーザー権限
    SALES = "sales"         # 営業権限

class UserInfo(BaseModel):
    """
    ユーザー情報を表すモデル
    """
    id: str
    username: str
    email: Optional[str] = None
    roles: List[str] = []

    @property
    def is_admin(self) -> bool:
        """
        ユーザーが管理者権限を持っているかどうかを返します
        """
        return UserRole.ADMIN.value in self.roles

    @property
    def is_sales(self) -> bool:
        """
        ユーザーが営業権限を持っているかどうかを返します
        """
        return UserRole.SALES.value in self.roles

    @property
    def is_regular_user(self) -> bool:
        """
        ユーザーが一般ユーザー権限を持っているかどうかを返します
        """
        return UserRole.USER.value in self.roles

    @property
    def has_any_role(self) -> bool:
        """
        ユーザーが何らかの権限を持っているかどうかを返します
        """
        return len(self.roles) > 0

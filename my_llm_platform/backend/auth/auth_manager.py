from fastapi import Depends, HTTPException, status
from fastapi_keycloak import FastAPIKeycloak
from typing import Dict, Any, List
import logging
from ..config import settings
from .models import UserInfo

logger = logging.getLogger(__name__)

# Keycloakインスタンスを初期化
auth_manager = FastAPIKeycloak(
    server_url=settings.KEYCLOAK_SERVER_URL,
    client_id=settings.KEYCLOAK_CLIENT_ID,
    client_secret=settings.KEYCLOAK_CLIENT_SECRET,
    realm=settings.KEYCLOAK_REALM,
    callback_uri=settings.KEYCLOAK_CALLBACK_URI,
    admin_client_secret=settings.KEYCLOAK_CLIENT_SECRET  # 管理者クライアントシークレット
)

# 現在のユーザーを取得する依存関数
def get_current_user(user: Dict[str, Any] = Depends(auth_manager.get_current_user())):
    """
    現在のユーザー情報を取得します。
    認証モードに応じて、自前アカウントまたは企業SSOからユーザー情報を取得します。
    """
    try:
        # 認証モードに基づいて処理を分岐
        if settings.is_enterprise_mode:
            # 企業SSOの場合、ロールマッピングを適用
            enterprise_roles = user.get("realm_access", {}).get("roles", [])
            platform_roles = _map_enterprise_roles_to_platform_roles(enterprise_roles)

            return UserInfo(
                id=user.get("sub", ""),
                username=user.get("preferred_username", ""),
                email=user.get("email", ""),
                roles=platform_roles
            )
        elif settings.is_self_hosted_mode:
            # 自前アカウントの場合、Keycloakのロールをそのまま使用
            roles = user.get("realm_access", {}).get("roles", [])

            return UserInfo(
                id=user.get("sub", ""),
                username=user.get("preferred_username", ""),
                email=user.get("email", ""),
                roles=roles
            )
        else:
            # 認証モードが不正な場合のフォールバック
            logger.warning(f"不正な認証モード: {settings.AUTH_MODE}")
            # デフォルトでは自前アカウントモードと同じ処理を行う
            roles = user.get("realm_access", {}).get("roles", [])

            return UserInfo(
                id=user.get("sub", ""),
                username=user.get("preferred_username", ""),
                email=user.get("email", ""),
                roles=roles
            )
    except Exception as e:
        logger.error(f"ユーザー情報の取得エラー: {e}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="認証情報の検証に失敗しました"
        )

# 管理者権限を持つユーザーを取得する依存関数
def get_current_admin(user: UserInfo = Depends(get_current_user)):
    """
    管理者権限を持つユーザーを取得します。
    管理者権限がない場合は403エラーを返します。
    """
    if not user.is_admin:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="この操作には管理者権限が必要です"
        )
    return user

# 営業権限を持つユーザーを取得する依存関数
def get_current_sales(user: UserInfo = Depends(get_current_user)):
    """
    営業権限を持つユーザーを取得します。
    営業権限がない場合は403エラーを返します。
    管理者は営業権限も持つとみなされます。
    """
    if not (user.is_sales or user.is_admin):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="この操作には営業権限が必要です"
        )
    return user

# 一般ユーザー権限を持つユーザーを取得する依存関数
def get_current_regular_user(user: UserInfo = Depends(get_current_user)):
    """
    一般ユーザー権限を持つユーザーを取得します。
    一般ユーザー権限がない場合は403エラーを返します。
    管理者と営業担当者は一般ユーザー権限も持つとみなされます。
    """
    if not (user.is_regular_user or user.is_admin or user.is_sales):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="この操作には一般ユーザー権限が必要です"
        )
    return user

# 企業ロールをプラットフォームロールにマッピングする内部関数
def _map_enterprise_roles_to_platform_roles(enterprise_roles: List[str]) -> List[str]:
    """
    企業ロールをプラットフォームロールにマッピングします。
    """
    platform_roles = []
    role_mapping = settings.ENTERPRISE_ROLE_MAPPING

    # 各プラットフォームロールについて、対応する企業ロールがあるか確認
    for platform_role, mapped_enterprise_roles in role_mapping.items():
        if any(role in enterprise_roles for role in mapped_enterprise_roles):
            platform_roles.append(platform_role)

    return platform_roles

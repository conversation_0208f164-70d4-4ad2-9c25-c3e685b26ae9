"""
vLLMサーバーに直接リクエストを送信するための低レベルモジュール。
このモジュールは内部使用を目的としており、llm_client.pyを通じて間接的に使用されるべきです。
"""

import json
import re
import logging
import httpx
from typing import Dict, List, Any, Optional, AsyncGenerator
from ..config import settings
from ..utils.response_filter import filter_chat_completion_response, filter_completion_response, filter_streaming_chunk

logger = logging.getLogger("vllm_direct")

async def vllm_chat_completion(
    model: str,
    messages: List[Dict[str, str]],
    temperature: float = 0.7,
    max_tokens: int = 1000,
    top_p: float = 1.0,
    stream: bool = False,
    tools: Optional[List[Dict[str, Any]]] = None,
    tool_choice: Optional[Any] = None,
) -> Dict[str, Any]:
    """
    vLLMサーバーに直接チャット完了リクエストを送信します。
    """
    vllm_url = settings.VLLM_ENDPOINT
    if not vllm_url:
        raise ValueError("VLLM_ENDPOINTが設定されていません")

    endpoint = f"{vllm_url}/v1/chat/completions"
    
    # モデルの最大コンテキスト長を取得（設定から、またはデフォルト値）
    model_max_length = getattr(settings, f"{model.upper()}_MAX_LENGTH", 512)
    
    # 入力メッセージのトークン数を概算（実際のトークナイザーではないため概算）
    input_tokens = sum(len(m.get("content", "")) // 4 for m in messages)  # 4文字≒1トークンと概算
    
    # 最大トークン数を調整（入力トークン数 + max_tokensがモデルの最大長を超えないように）
    adjusted_max_tokens = min(max_tokens, model_max_length - input_tokens - 50)  # 50はマージン
    
    # 最小値を確保
    adjusted_max_tokens = max(adjusted_max_tokens, 50)  # 最低50トークンは確保
    
    logger.info(f"トークン調整: 入力≒{input_tokens}, 要求={max_tokens}, 調整後={adjusted_max_tokens}")
    
    payload = {
        "model": model,
        "messages": messages,
        "temperature": temperature,
        "max_tokens": adjusted_max_tokens,  # 調整後の値を使用
        "top_p": top_p,
        "stream": stream
    }
    
    if tools:
        payload["tools"] = tools
    if tool_choice:
        payload["tool_choice"] = tool_choice
    
    logger.debug(f"vLLMリクエスト: {endpoint}, payload: {payload}")
    
    async with httpx.AsyncClient() as client:
        response = await client.post(endpoint, json=payload, timeout=60.0)
        if response.status_code != 200:
            error_text = response.text
            logger.error(f"vLLMエラー: {response.status_code}, {error_text}")
            raise ValueError(f"vLLMサーバーエラー: {response.status_code}, {error_text}")
        
        result = response.json()
        
        # レスポンスをフィルタリング（特殊トークンの削除と最初の有効な回答の選択）
        filtered_result = filter_chat_completion_response(result)
        
        return filtered_result

async def vllm_completion(
    model: str,
    prompt: str,
    temperature: float = 0.7,
    max_tokens: int = 1000,
    top_p: float = 1.0,
    stream: bool = False,
) -> Dict[str, Any]:
    """
    vLLMサーバーに直接テキスト補完リクエストを送信します。
    """
    # vLLMエンドポイントを取得
    base_url = settings.VLLM_ENDPOINT
    if not base_url.endswith("/v1"):
        base_url = f"{base_url}/v1"

    # モデルの最大コンテキスト長を取得（設定から、またはデフォルト値）
    model_max_length = getattr(settings, f"{model.upper()}_MAX_LENGTH", 512)
    
    # 入力プロンプトのトークン数を概算（実際のトークナイザーではないため概算）
    input_tokens = len(prompt) // 4  # 4文字≒1トークンと概算
    
    # 最大トークン数を調整（入力トークン数 + max_tokensがモデルの最大長を超えないように）
    adjusted_max_tokens = min(max_tokens, model_max_length - input_tokens - 50)  # 50はマージン
    
    # 最小値を確保
    adjusted_max_tokens = max(adjusted_max_tokens, 50)  # 最低50トークンは確保
    
    logger.info(f"トークン調整: 入力≒{input_tokens}, 要求={max_tokens}, 調整後={adjusted_max_tokens}")

    # リクエストデータを準備
    request_data = {
        "model": model,
        "prompt": prompt,
        "temperature": temperature,
        "max_tokens": adjusted_max_tokens,  # 調整後の値を使用
        "top_p": top_p,
        "stream": stream
    }

    logger.info(f"vLLMサーバーに直接リクエストを送信: {base_url}/completions")

    try:
        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{base_url}/completions",
                json=request_data,
                timeout=60.0
            )

            # レスポンスを確認
            if response.status_code != 200:
                logger.error(f"vLLMサーバーからエラーレスポンス: {response.status_code} {response.text}")
                raise Exception(f"vLLMサーバーからエラーレスポンス: {response.status_code} {response.text}")

            # JSONレスポンスを解析
            result = response.json()
            logger.info(f"vLLMサーバーからのレスポンス: {result}")

            # レスポンスをフィルタリング（特殊トークンの削除と最初の有効な回答の選択）
            filtered_result = filter_completion_response(result)

            # 詳細なログ出力
            if "choices" in filtered_result and len(filtered_result["choices"]) > 0:
                if "text" in filtered_result["choices"][0]:
                    text = filtered_result["choices"][0]["text"]
                    logger.info(f"フィルタリング後のテキスト補完レスポンス内容: {text[:100]}...")

                    # 特殊トークンが残っていないか確認
                    if "<|" in text and "|>" in text:
                        logger.warning(f"フィルタリング後も特殊トークンが残っています: {text[:100]}...")
                        # 特殊トークンを手動で削除
                        text = re.sub(r'<\|[^|]+\|>', '', text)
                        filtered_result["choices"][0]["text"] = text
                        logger.info(f"手動フィルタリング後のテキスト補完レスポンス内容: {text[:100]}...")

            return filtered_result
    except Exception as e:
        logger.error(f"vLLMサーバーへのリクエスト中にエラーが発生: {e}")
        raise

async def vllm_stream_chat_completion(
    model: str,
    messages: List[Dict[str, str]],
    temperature: float = 0.7,
    max_tokens: int = 1000,
    top_p: float = 1.0,
    tools: Optional[List[Dict[str, Any]]] = None,
    tool_choice: Optional[Any] = None,
) -> AsyncGenerator[Dict[str, Any], None]:
    """
    vLLMサーバーに直接ストリーミングチャット完了リクエストを送信します。
    """
    # vLLMエンドポイントを取得
    base_url = settings.VLLM_ENDPOINT
    if not base_url.endswith("/v1"):
        base_url = f"{base_url}/v1"

    # チャットテンプレートを準備
    # 特殊トークンを使わないシンプルなテンプレートに変更
    chat_template = """{% for message in messages %}
{% if message['role'] == 'system' %}
System: {{ message['content'] }}
{% elif message['role'] == 'user' %}
User: {{ message['content'] }}
{% elif message['role'] == 'assistant' %}
Assistant: {{ message['content'] }}
{% endif %}
{% endfor %}
Assistant:
"""

    # ログ出力
    logger.info(f"ストリーミングチャットメッセージ: {messages}")

    # 最後のメッセージが長すぎる場合は切り詰める（デバッグ用）
    if messages and len(messages) > 0:
        last_message = messages[-1]
        if 'content' in last_message and len(last_message['content']) > 100:
            log_content = last_message['content'][:100] + "..."
            logger.info(f"最後のメッセージ: {last_message['role']} - {log_content}")

    # モデルの最大コンテキスト長を取得（設定から、またはデフォルト値）
    model_max_length = getattr(settings, f"{model.upper()}_MAX_LENGTH", 512)
    
    # 入力メッセージのトークン数を概算（実際のトークナイザーではないため概算）
    input_tokens = sum(len(m.get("content", "")) // 4 for m in messages)  # 4文字≒1トークンと概算
    
    # 最大トークン数を調整（入力トークン数 + max_tokensがモデルの最大長を超えないように）
    adjusted_max_tokens = min(max_tokens, model_max_length - input_tokens - 50)  # 50はマージン
    
    # 最小値を確保
    adjusted_max_tokens = max(adjusted_max_tokens, 50)  # 最低50トークンは確保
    
    logger.info(f"トークン調整: 入力≒{input_tokens}, 要求={max_tokens}, 調整後={adjusted_max_tokens}")

    # リクエストデータを準備
    request_data = {
        "model": model,
        "messages": messages,
        "temperature": temperature,
        "max_tokens": adjusted_max_tokens,  # 調整後の値を使用
        "top_p": top_p,
        "stream": True,
        "chat_template": chat_template
    }

    # ツールが存在する場合は追加
    if tools:
        request_data["tools"] = tools
    if tool_choice:
        request_data["tool_choice"] = tool_choice

    logger.info(f"vLLMサーバーに直接ストリーミングリクエストを送信: {base_url}/chat/completions")

    try:
        async with httpx.AsyncClient() as client:
            async with client.stream(
                "POST",
                f"{base_url}/chat/completions",
                json=request_data,
                timeout=60.0
            ) as response:
                # レスポンスを確認
                if response.status_code != 200:
                    error_text = await response.aread()
                    logger.error(f"vLLMサーバーからエラーレスポンス: {response.status_code} {error_text}")
                    raise Exception(f"vLLMサーバーからエラーレスポンス: {response.status_code} {error_text}")

                # ストリーミングレスポンスを処理
                # 完全な応答を構築するための変数
                full_response = ""

                async for line in response.aiter_lines():
                    if line.startswith("data: "):
                        if line.strip() == "data: [DONE]":
                            break

                        data = line[6:]  # "data: " を削除
                        try:
                            chunk = json.loads(data)

                            # 特殊トークンを含むかチェック
                            contains_special_token = False
                            current_content = ""

                            if "choices" in chunk and len(chunk["choices"]) > 0:
                                if "delta" in chunk["choices"][0] and "content" in chunk["choices"][0]["delta"]:
                                    current_content = chunk["choices"][0]["delta"]["content"]
                                    if "<|" in current_content and "|>" in current_content:
                                        contains_special_token = True
                                        logger.debug(f"特殊トークンを含むチャンク: {current_content}")

                            # 特殊トークンを含む場合はスキップ
                            if contains_special_token:
                                logger.debug("特殊トークンを含むチャンクをスキップします")
                                continue

                            # 空のチャンクはスキップ
                            if not current_content.strip():
                                logger.debug("空のチャンクをスキップします")
                                continue

                            # 完全な応答を更新
                            new_full_response = full_response + current_content

                            # 重複チェック - 新しいコンテンツが既存の応答の一部として既に存在する場合
                            if current_content in full_response:
                                # 重複している場合はスキップ
                                logger.debug(f"重複コンテンツをスキップします: {current_content}")
                                continue

                            # 完全な応答を更新
                            full_response = new_full_response

                            # ストリーミングチャンクをフィルタリング（特殊トークンの削除）
                            filtered_chunk = filter_streaming_chunk(chunk, is_chat_completion=True)

                            # 特殊トークンが含まれていないことを確認
                            if "choices" in filtered_chunk and len(filtered_chunk["choices"]) > 0:
                                if "delta" in filtered_chunk["choices"][0] and "content" in filtered_chunk["choices"][0]["delta"]:
                                    content = filtered_chunk["choices"][0]["delta"]["content"]
                                    if "<|" in content and "|>" in content:
                                        # 特殊トークンを手動で削除
                                        content = re.sub(r'<\|[^|]+\|>', '', content)
                                        filtered_chunk["choices"][0]["delta"]["content"] = content

                            yield filtered_chunk
                        except json.JSONDecodeError as e:
                            logger.error(f"JSONデコードエラー: {e}, data: {data}")
    except Exception as e:
        logger.error(f"vLLMサーバーへのストリーミングリクエスト中にエラーが発生: {e}")
        raise

async def vllm_stream_completion(
    model: str,
    prompt: str,
    temperature: float = 0.7,
    max_tokens: int = 1000,
    top_p: float = 1.0,
) -> AsyncGenerator[Dict[str, Any], None]:
    """
    vLLMサーバーに直接ストリーミングテキスト補完リクエストを送信します。
    """
    # vLLMエンドポイントを取得
    base_url = settings.VLLM_ENDPOINT
    if not base_url.endswith("/v1"):
        base_url = f"{base_url}/v1"

    # モデルの最大コンテキスト長を取得（設定から、またはデフォルト値）
    model_max_length = getattr(settings, f"{model.upper()}_MAX_LENGTH", 512)
    
    # 入力プロンプトのトークン数を概算（実際のトークナイザーではないため概算）
    input_tokens = len(prompt) // 4  # 4文字≒1トークンと概算
    
    # 最大トークン数を調整（入力トークン数 + max_tokensがモデルの最大長を超えないように）
    adjusted_max_tokens = min(max_tokens, model_max_length - input_tokens - 50)  # 50はマージン
    
    # 最小値を確保
    adjusted_max_tokens = max(adjusted_max_tokens, 50)  # 最低50トークンは確保
    
    logger.info(f"トークン調整: 入力≒{input_tokens}, 要求={max_tokens}, 調整後={adjusted_max_tokens}")

    # リクエストデータを準備
    request_data = {
        "model": model,
        "prompt": prompt,
        "temperature": temperature,
        "max_tokens": adjusted_max_tokens,  # 調整後の値を使用
        "top_p": top_p,
        "stream": True
    }

    logger.info(f"vLLMサーバーに直接ストリーミングリクエストを送信: {base_url}/completions")

    try:
        async with httpx.AsyncClient() as client:
            async with client.stream(
                "POST",
                f"{base_url}/completions",
                json=request_data,
                timeout=60.0
            ) as response:
                # レスポンスを確認
                if response.status_code != 200:
                    error_text = await response.aread()
                    logger.error(f"vLLMサーバーからエラーレスポンス: {response.status_code} {error_text}")
                    raise Exception(f"vLLMサーバーからエラーレスポンス: {response.status_code} {error_text}")

                # ストリーミングレスポンスを処理
                # 完全な応答を構築するための変数
                full_text = ""

                async for line in response.aiter_lines():
                    if line.startswith("data: "):
                        if line.strip() == "data: [DONE]":
                            break

                        data = line[6:]  # "data: " を削除
                        try:
                            chunk = json.loads(data)

                            # 特殊トークンを含むかチェック
                            contains_special_token = False
                            current_text = ""

                            if "choices" in chunk and len(chunk["choices"]) > 0:
                                if "text" in chunk["choices"][0]:
                                    current_text = chunk["choices"][0]["text"]
                                    if "<|" in current_text and "|>" in current_text:
                                        contains_special_token = True
                                        logger.debug(f"特殊トークンを含むチャンク: {current_text}")

                            # 特殊トークンを含む場合はスキップ
                            if contains_special_token:
                                logger.debug("特殊トークンを含むチャンクをスキップします")
                                continue

                            # 空のチャンクはスキップ
                            if not current_text.strip():
                                logger.debug("空のチャンクをスキップします")
                                continue

                            # 完全な応答を更新
                            new_full_text = full_text + current_text

                            # 重複チェック - 新しいテキストが既存の応答の一部として既に存在する場合
                            if current_text in full_text:
                                # 重複している場合はスキップ
                                logger.debug(f"重複テキストをスキップします: {current_text}")
                                continue

                            # 完全な応答を更新
                            full_text = new_full_text

                            # ストリーミングチャンクをフィルタリング（特殊トークンの削除）
                            filtered_chunk = filter_streaming_chunk(chunk, is_chat_completion=False)

                            # 特殊トークンが含まれていないことを確認
                            if "choices" in filtered_chunk and len(filtered_chunk["choices"]) > 0:
                                if "text" in filtered_chunk["choices"][0]:
                                    text = filtered_chunk["choices"][0]["text"]
                                    if "<|" in text and "|>" in text:
                                        # 特殊トークンを手動で削除
                                        text = re.sub(r'<\|[^|]+\|>', '', text)
                                        filtered_chunk["choices"][0]["text"] = text

                            yield filtered_chunk
                        except json.JSONDecodeError as e:
                            logger.error(f"JSONデコードエラー: {e}, data: {data}")
    except Exception as e:
        logger.error(f"vLLMサーバーへのストリーミングリクエスト中にエラーが発生: {e}")
        raise

# 簡略化されたLLMクライアント
from .llm_client import get_client, get_client_for_model, LLMClient, BackendType

# 後方互換性のためのエイリアス
def get_backend(backend_type, **kwargs):
    """後方互換性のためのエイリアス"""
    return get_client(backend_type)

def get_backend_for_model(model, **kwargs):
    """後方互換性のためのエイリアス"""
    return get_client_for_model(model)

__all__ = [
    # 簡略化されたLLMクライアント
    'get_client',
    'get_client_for_model',
    'LLMClient',
    'BackendType',

    # 後方互換性のためのエイリアス
    'get_backend',
    'get_backend_for_model'
]

import pytest
from fastapi.testclient import TestClient
import sys
import os
import json

# パスの追加
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
from backend.main import app
from backend.config import settings

client = TestClient(app)

def test_chat_completion_vllm():
    """
    vLLMバックエンドを使用したチャット完了APIのテスト
    """
    # テストデータの準備
    test_data = {
        "model": "facebook/opt-125m",
        "messages": [
            {
                "role": "system",
                "content": "あなたは役立つAIアシスタントです。"
            },
            {
                "role": "user",
                "content": "こんにちは"
            },
            {
                "role": "assistant",
                "content": "こんにちは！お手伝いします。"
            },
            {
                "role": "user",
                "content": "元気？"
            }
        ],
        "temperature": 0.7,
        "stream": False
    }

    # APIリクエストの実行
    response = client.post(
        "/v1/chat/completions",
        json=test_data,
        headers={"Authorization": f"Bearer {settings.TEST_API_KEY}"}
    )

    # レスポンスの検証
    print(f"Status code: {response.status_code}")
    print(f"Response: {response.text}")
    
    assert response.status_code == 200
    response_data = response.json()
    
    # 基本的な構造の確認
    assert "id" in response_data
    assert "object" in response_data
    assert "created" in response_data
    assert "model" in response_data
    assert "choices" in response_data
    
    # choicesの内容確認
    choices = response_data["choices"]
    assert len(choices) > 0
    assert "message" in choices[0]
    assert "role" in choices[0]["message"]
    assert "content" in choices[0]["message"]
    assert choices[0]["message"]["role"] == "assistant"

def test_completion_vllm():
    """
    vLLMバックエンドを使用したテキスト補完APIのテスト
    """
    # テストデータの準備
    test_data = {
        "model": "facebook/opt-125m",
        "prompt": "こんにちは、元気ですか？",
        "temperature": 0.7,
        "max_tokens": 100,
        "stream": False
    }

    # APIリクエストの実行
    response = client.post(
        "/v1/completions",
        json=test_data,
        headers={"Authorization": f"Bearer {settings.TEST_API_KEY}"}
    )

    # レスポンスの検証
    print(f"Status code: {response.status_code}")
    print(f"Response: {response.text}")
    
    assert response.status_code == 200
    response_data = response.json()
    
    # 基本的な構造の確認
    assert "id" in response_data
    assert "object" in response_data
    assert "created" in response_data
    assert "model" in response_data
    assert "choices" in response_data
    
    # choicesの内容確認
    choices = response_data["choices"]
    assert len(choices) > 0
    assert "text" in choices[0]

if __name__ == "__main__":
    pytest.main([__file__])

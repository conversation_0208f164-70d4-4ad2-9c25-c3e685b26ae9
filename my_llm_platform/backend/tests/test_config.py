"""
配置模块测试脚本
"""

import sys
import os

# 現在のディレクトリをPythonパスに追加
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    # 設定モジュールをインポート
    from backend.config import settings
    print(f"配置模块加载成功！")
    print(f"认证模式: {settings.AUTH_MODE}")
    print(f"本地测试用户: {settings.LOCAL_TEST_USER}")
    print(f"是本地测试模式: {settings.is_local_test_mode}")
    print(f"是自托管模式: {settings.is_self_hosted_mode}")
    print(f"是企业模式: {settings.is_enterprise_mode}")
except Exception as e:
    print(f"测试失败: {e}")
    import traceback
    traceback.print_exc()

#!/usr/bin/env python3
"""
测试think标签处理的简单脚本
"""

import requests
import json

def test_think_response():
    """测试包含think标签的响应"""
    
    # 测试数据 - 包含think标签的消息
    test_message = """<think>Okay,theuserisaskingaboutcreatinganaddmethodinJava.Letmethinkabouthowtoappproachthis.FirstIneedtoclarifywhattheymeanby"addmethod."Sincetheydidn'tspecifythecontext,maybethey'rereferringtoaddingelementstoacollection,likeanArrayList.Alternativelyitcouldbeaboutaddingtowonumbersorobjects.Ishouldconsidercommonscenarios.Forexample,addingelementstoaListusingtheaddmethod.Java'sListinterfacehasanaddmethod.Somaybetheywatntoknowhowtoimplementthat.Butwait,theaddmethodispartoftheListinterface,soitkey'sexistingaccessorsomeclassesperhapstheyneedtooverrideimplementintheirownclass.Alternatively,maybetheywereferringtoaCustomclassthathasan"addmethod"likeasimplecalculatorthataddstwonumbers.Butwithoutmorecontextit'shardtotell.Ishouldaskforcarification.However,theusermightexpectageneralexample.Letmecheckthepreviousresponse.Inthepreviousresponse,theassistantprovidedanexampleofaddingelementstoaList.Butmaybetheyarelookingtocoverallbases.Ishouldexplainbothpossibilitiesaaddingtoacollectionandacustomaddmethod.Butsincetheuserisaskingtocreateananaddmethod,perhapstheywantoimplementtheirownaddmethodinaclass.Forexample,aclasshatmanagesacollectionandhasanaddmethod.Wait,maybethey'retryingtoimplementanadddmethodincustomdatastructure.Letmethinkofanexample.Supposewehaveaveclassthatholdsnumbersandwantstoaddanumbertoit.Theaddmethodwouldtakeaparameterandappenditoaninternallist.So,thecodewouldinvolvecreatingaclasswithalist,andtheaddmethodaddstothealist.Alternatively,ifthey'reworkingwithaspecificclasslikeaVectororArrayList,theymightbindspecifictools,consideringbindinglocationandmethod.</think>

我明白了！你的聊天机器人的 `<think>` 标签显示功能被我之前的代码修改弄坏了。让我先查看一下当前的代码状态，然后帮你修复这个问题。"""

    # 构建请求数据
    request_data = {
        "model": "llama3.2:3b",
        "messages": [
            {
                "role": "user", 
                "content": "请测试think标签功能"
            },
            {
                "role": "assistant",
                "content": test_message
            }
        ],
        "stream": False,
        "temperature": 0.7,
        "max_tokens": 1000
    }
    
    print("测试数据:")
    print(f"原始消息长度: {len(test_message)}")
    print(f"包含think标签: {'<think>' in test_message}")
    print(f"think标签内容长度: {len(test_message.split('<think>')[1].split('</think>')[0]) if '<think>' in test_message else 0}")
    
    # 测试后端API
    try:
        print("\n测试后端API...")
        response = requests.post(
            "http://localhost:8000/api/v1/chat/completions",
            json=request_data,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            result = response.json()
            if "choices" in result and len(result["choices"]) > 0:
                content = result["choices"][0]["message"]["content"]
                print(f"后端返回内容长度: {len(content)}")
                print(f"后端保留think标签: {'<think>' in content}")
                print(f"后端返回内容预览: {content[:200]}...")
            else:
                print("后端返回格式异常")
        else:
            print(f"后端API错误: {response.status_code}")
            print(response.text)
            
    except Exception as e:
        print(f"后端API测试失败: {e}")
    
    # 测试前端think标签解析
    print("\n测试前端think标签解析...")
    
    def extract_think_content(text):
        """模拟前端的extractThinkContent函数"""
        import re
        
        # 使用全局标志处理多个<think>标签
        think_regex = re.compile(r'<think>([\s\S]*?)</think>', re.DOTALL)
        matches = list(think_regex.finditer(text))
        
        if matches:
            # 获取第一个<think>标签的内容
            think_content = matches[0].group(1).strip()
            # 删除所有<think>标签
            visible_content = re.sub(r'<think>[\s\S]*?</think>', '', text, flags=re.DOTALL).strip()
            return {"think_content": think_content, "visible_content": visible_content}
        
        # 没有<think>标签的情况
        return {"think_content": None, "visible_content": text}
    
    result = extract_think_content(test_message)
    print(f"解析结果:")
    print(f"- 思考内容长度: {len(result['think_content']) if result['think_content'] else 0}")
    print(f"- 可见内容长度: {len(result['visible_content'])}")
    print(f"- 思考内容预览: {result['think_content'][:100] if result['think_content'] else 'None'}...")
    print(f"- 可见内容: {result['visible_content']}")

if __name__ == "__main__":
    test_think_response()

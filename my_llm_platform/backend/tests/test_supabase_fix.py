import logging
import vecs
import numpy as np
from langchain_core.documents import Document
from langchain_community.vectorstores import PGVector
from langchain_community.vectorstores.pgvector import DistanceStrategy
from backend.plugins.rag.embeddings import EmbeddingModel

# ロギングを設定
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

# 接続文字列
connection_string = 'postgresql://postgres:postgres@localhost:54322/postgres'
collection_name = 'documents'

# 問題の診断と修正
def diagnose_and_fix():
    # 1. vecsを使用してコレクションを確認
    try:
        client = vecs.create_client(connection_string)
        collection = client.get_or_create_collection(name=collection_name, dimension=384)
        
        # コレクション内のデータ数を確認
        results = collection.query(data=list(range(384)), limit=100, include_metadata=True)
        logger.info(f'vecsコレクション内のデータ数: {len(results)}')
        
        if len(results) == 0:
            # テストデータを追加
            logger.info('テストデータを追加します')
            collection.upsert(records=[
                ('test1', np.random.rand(384), {'text': 'テストデータ1', 'source': 'test'}),
                ('test2', np.random.rand(384), {'text': 'テストデータ2', 'source': 'test'})
            ])
            logger.info('テストデータを追加しました')
            
            # 再度確認
            results = collection.query(data=list(range(384)), limit=100, include_metadata=True)
            logger.info(f'テストデータ追加後のデータ数: {len(results)}')
    
    except Exception as e:
        logger.error(f'vecsコレクション確認中にエラーが発生しました: {e}')
    
    # 2. PGVectorを使用してコレクションを確認
    try:
        # 埋め込みモデルを作成
        embedding_model = EmbeddingModel()
        
        # PGVectorインスタンスを作成
        pgvector = PGVector(
            connection_string=connection_string,
            collection_name=collection_name,
            embedding_function=embedding_model.model,
            distance_strategy=DistanceStrategy.COSINE
        )
        
        # retrieverを作成
        retriever = pgvector.as_retriever()
        
        # 検索を実行
        results = retriever.invoke("テスト")
        logger.info(f'PGVector検索結果: {results}')
        
        if not results:
            logger.warning('PGVectorで検索結果が見つかりませんでした')
            
            # PGVectorにテストデータを追加
            logger.info('PGVectorにテストデータを追加します')
            pgvector.add_documents([
                Document(page_content="PGVectorテストデータ1", metadata={"source": "pgvector_test"}),
                Document(page_content="PGVectorテストデータ2", metadata={"source": "pgvector_test"})
            ])
            
            # 再度検索
            results = retriever.invoke("テスト")
            logger.info(f'テストデータ追加後のPGVector検索結果: {results}')
    
    except Exception as e:
        logger.error(f'PGVector確認中にエラーが発生しました: {e}')
    
    # 3. データベーステーブルを確認
    try:
        import psycopg2
        conn = psycopg2.connect(connection_string)
        cursor = conn.cursor()
        
        # テーブル一覧を取得
        cursor.execute("SELECT table_name FROM information_schema.tables WHERE table_schema = 'public';")
        tables = cursor.fetchall()
        logger.info('テーブル一覧:')
        for table in tables:
            logger.info(table[0])
        
        # documentsテーブルの構造を確認
        try:
            cursor.execute(f"SELECT column_name, data_type FROM information_schema.columns WHERE table_name = '{collection_name}';")
            columns = cursor.fetchall()
            logger.info(f'{collection_name}テーブルの列:')
            for col in columns:
                logger.info(f'{col[0]}: {col[1]}')
        except Exception as e:
            logger.error(f'{collection_name}テーブルの構造確認エラー: {e}')
        
        # langchain_pg_embeddingテーブルの構造を確認
        try:
            cursor.execute("SELECT column_name, data_type FROM information_schema.columns WHERE table_name = 'langchain_pg_embedding';")
            columns = cursor.fetchall()
            logger.info('langchain_pg_embeddingテーブルの列:')
            for col in columns:
                logger.info(f'{col[0]}: {col[1]}')
                
            # データ数を確認
            cursor.execute("SELECT COUNT(*) FROM langchain_pg_embedding;")
            count = cursor.fetchone()
            logger.info(f'langchain_pg_embeddingテーブルのレコード数: {count[0]}')
        except Exception as e:
            logger.error(f'langchain_pg_embeddingテーブルの構造確認エラー: {e}')
        
        conn.close()
        
    except Exception as e:
        logger.error(f'データベース確認エラー: {e}')

if __name__ == "__main__":
    diagnose_and_fix()

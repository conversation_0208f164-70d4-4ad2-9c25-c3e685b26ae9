import vecs
import json
import psycopg2

# PostgreSQLに接続
client = vecs.create_client('postgresql://postgres:postgres@localhost:54322/postgres')

# コレクション一覧を取得（テーブル名から推測）
conn = psycopg2.connect('postgresql://postgres:postgres@localhost:54322/postgres')
cursor = conn.cursor()
cursor.execute("SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name NOT IN ('langchain_pg_collection', 'langchain_pg_embedding', 'todos');")
tables = cursor.fetchall()
conn.close()

collections = [table[0] for table in tables]
print('コレクション一覧:')
for collection in collections:
    print(collection)

# 各コレクションのデータを取得
print('\nコレクション内のデータ:')
for collection_name in collections:
    collection = client.get_collection(collection_name)

    # データを検索
    try:
        results = collection.query(data=list(range(384)), limit=10, include_metadata=True)
        print(f'\n{collection_name}コレクション:')
        for result in results:
            # メタデータを整形して表示
            metadata_str = json.dumps(result[1], ensure_ascii=False)
            print(f'ID: {result[0]}, スコア: {result[2]}, メタデータ: {metadata_str}')
    except Exception as e:
        print(f'{collection_name}コレクションの検索中にエラーが発生しました: {e}')

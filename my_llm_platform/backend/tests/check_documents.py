import vecs
import json

# PostgreSQLに接続
client = vecs.create_client('postgresql://postgres:postgres@localhost:54322/postgres')

# documentsコレクションを取得
try:
    collection = client.get_collection('documents')
    print('documentsコレクションが見つかりました')
    
    # データを検索
    try:
        results = collection.query(data=list(range(384)), limit=10, include_metadata=True)
        print(f'\ndocumentsコレクション内のデータ:')
        for result in results:
            # メタデータを整形して表示
            metadata_str = json.dumps(result[1], ensure_ascii=False)
            print(f'ID: {result[0]}, スコア: {result[2]}, メタデータ: {metadata_str}')
    except Exception as e:
        print(f'documentsコレクションの検索中にエラーが発生しました: {e}')
except Exception as e:
    print(f'documentsコレクションの取得中にエラーが発生しました: {e}')

# コレクションを作成
try:
    collection = client.get_or_create_collection(name='documents', dimension=384)
    print('\ndocumentsコレクションを作成しました')
    
    # データを検索
    try:
        results = collection.query(data=list(range(384)), limit=10, include_metadata=True)
        print(f'\ndocumentsコレクション内のデータ:')
        for result in results:
            # メタデータを整形して表示
            metadata_str = json.dumps(result[1], ensure_ascii=False)
            print(f'ID: {result[0]}, スコア: {result[2]}, メタデータ: {metadata_str}')
    except Exception as e:
        print(f'documentsコレクションの検索中にエラーが発生しました: {e}')
except Exception as e:
    print(f'documentsコレクションの作成中にエラーが発生しました: {e}')

"""
認証システムテストスクリプト
"""

import sys
import os

# 現在のディレクトリをPythonパスに追加
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    # 設定モジュールをインポート
    from backend.config import settings
    print(f"設定モジュールの読み込みに成功しました！認証モード: {settings.AUTH_MODE}")
    
    # 認証モジュールをインポート
    from backend.auth import get_current_user, get_current_admin, get_current_sales, get_current_regular_user
    print("認証モジュールの読み込みに成功しました！")
    
    # 現在のユーザーを取得するテスト
    user = get_current_user()
    print(f"現在のユーザー: {user.username}, 役割: {user.roles}")
    print(f"管理者: {user.is_admin}, 営業: {user.is_sales}, 一般ユーザー: {user.is_regular_user}")
    
    print("認証システムのテストに成功しました！")
except Exception as e:
    print(f"テスト失敗: {e}")
    import traceback
    traceback.print_exc()

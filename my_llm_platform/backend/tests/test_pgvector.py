from langchain_community.vectorstores import PGVector
from langchain_community.vectorstores.pgvector import DistanceStrategy
from langchain_core.embeddings import Embeddings
import logging

# ロギングを設定
logging.basicConfig(level=logging.DEBUG)

# ダミーの埋め込みクラス
class DummyEmbeddings(Embeddings):
    def embed_documents(self, texts):
        return [[0.1] * 384 for _ in texts]
    
    def embed_query(self, text):
        return [0.1] * 384

try:
    # PGVectorを初期化
    pgvector = PGVector(
        connection_string='postgresql://postgres:postgres@localhost:54322/postgres',
        collection_name='test_collection',
        embedding_function=DummyEmbeddings(),
        distance_strategy=DistanceStrategy.COSINE
    )
    print('PGVector初期化成功')
    
    # retrieverを作成
    retriever = pgvector.as_retriever()
    print('Retriever作成成功')
    
    # 検索を実行
    results = retriever.invoke("テスト")
    print(f'検索結果: {results}')
    
except Exception as e:
    print(f'エラー: {e}')

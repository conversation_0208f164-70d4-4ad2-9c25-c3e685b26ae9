import pytest
from fastapi.testclient import TestClient
import sys
import os

# パスの追加
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
from backend.main import app
from backend.config import settings
import json

client = TestClient(app)

def test_chat_completion_normal():
    """
    通常のチャット完了APIのテスト
    一度に全ての応答を返すケース
    """
    # テストデータの準備
    test_data = {
        "model": "gpt-3.5-turbo",
        "messages": [
            {
                "role": "user",
                "content": "こんにちは、元気ですか？"
            }
        ],
        "temperature": 0.7,
        "stream": False
    }

    # APIリクエストの実行
    response = client.post(
        "/v1/chat/completions",
        json=test_data,
        headers={"Authorization": f"Bearer {settings.TEST_API_KEY}"}
    )

    # レスポンスの検証
    assert response.status_code == 200
    response_data = response.json()
    
    # 基本的な構造の確認
    assert "id" in response_data
    assert "object" in response_data
    assert "created" in response_data
    assert "model" in response_data
    assert "choices" in response_data
    
    # choicesの内容確認
    choices = response_data["choices"]
    assert len(choices) > 0
    assert "message" in choices[0]
    assert "role" in choices[0]["message"]
    assert "content" in choices[0]["message"]
    assert choices[0]["message"]["role"] == "assistant"

def test_chat_completion_streaming():
    """
    ストリーミングチャット完了APIのテスト
    応答を逐次返すケース
    """
    # テストデータの準備
    test_data = {
        "model": "gpt-3.5-turbo",
        "messages": [
            {
                "role": "user",
                "content": "こんにちは、元気ですか？"
            }
        ],
        "temperature": 0.7,
        "stream": True
    }

    # APIリクエストの実行
    response = client.post(
        "/v1/chat/completions",
        json=test_data,
        headers={"Authorization": f"Bearer {settings.TEST_API_KEY}"},
        stream=True
    )

    # レスポンスの検証
    assert response.status_code == 200

    # ストリームデータの処理と検証
    received_data = []
    for line in response.iter_lines():
        if line:
            # データ行の処理
            line = line.decode('utf-8')
            if line.startswith('data: '):
                data = line[6:]  # 'data: ' プレフィックスを削除
                if data != '[DONE]':
                    try:
                        chunk = json.loads(data)
                        received_data.append(chunk)
                        
                        # 各チャンクの構造を検証
                        assert "id" in chunk
                        assert "object" in chunk
                        assert "created" in chunk
                        assert "model" in chunk
                        assert "choices" in chunk
                        
                        # choicesの内容確認
                        choices = chunk["choices"]
                        assert len(choices) > 0
                        assert "delta" in choices[0]
                        if "content" in choices[0]["delta"]:
                            assert isinstance(choices[0]["delta"]["content"], str)
                    except json.JSONDecodeError:
                        continue

    # 少なくとも1つのチャンクを受信したことを確認
    assert len(received_data) > 0

if __name__ == "__main__":
    pytest.main([__file__])
#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Test script for the plugin system.
"""

import sys
import os
import logging

# Add the parent directory to the path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Import the plugin manager
try:
    from backend.plugins.manager import plugin_manager
    from backend.plugins.samples.rag_plugin import SimpleRAGPlugin
    from backend.plugins.samples.agent_plugin import SimpleAgentPlugin
    from backend.plugins.types import Message, PluginResult
    
    logger.info("Successfully imported plugin modules")
except ImportError as e:
    logger.error(f"Error importing plugin modules: {e}")
    sys.exit(1)

def test_plugin_registration():
    """Test plugin registration."""
    logger.info("Testing plugin registration...")
    
    # Register the sample plugins
    try:
        plugin_manager.register_plugin("simple_rag", SimpleRAGPlugin)
        plugin_manager.register_plugin("simple_agent", SimpleAgentPlugin)
        
        logger.info(f"Registered plugins: {len(plugin_manager.get_all_plugins())}")
        
        # Get plugin info
        for plugin in plugin_manager.get_all_plugins():
            plugin_info = plugin.get_info()
            logger.info(f"Plugin: {plugin_info.id} ({plugin_info.name}) - {plugin_info.description}")
        
        return True
    except Exception as e:
        logger.error(f"Error registering plugins: {e}")
        return False

async def test_rag_plugin():
    """Test the RAG plugin."""
    logger.info("Testing RAG plugin...")
    
    # Get the RAG plugin
    rag_plugin = plugin_manager.get_plugin("simple_rag")
    if not rag_plugin:
        logger.error("RAG plugin not found")
        return False
    
    # Create a test message
    messages = [
        {"role": "user", "content": "Tell me about the plugin system."}
    ]
    
    # Process the message with the RAG plugin
    try:
        result = await rag_plugin.process(messages)
        
        logger.info(f"RAG plugin result: {len(result.messages)} messages")
        for i, msg in enumerate(result.messages):
            logger.info(f"Message {i}: {msg['role']} - {msg['content'][:50]}...")
        
        return True
    except Exception as e:
        logger.error(f"Error processing message with RAG plugin: {e}")
        return False

async def test_agent_plugin():
    """Test the agent plugin."""
    logger.info("Testing agent plugin...")
    
    # Get the agent plugin
    agent_plugin = plugin_manager.get_plugin("simple_agent")
    if not agent_plugin:
        logger.error("Agent plugin not found")
        return False
    
    # Create a test message
    messages = [
        {"role": "user", "content": "What is 2 + 2?"}
    ]
    
    # Process the message with the agent plugin
    try:
        result = await agent_plugin.process(messages)
        
        logger.info(f"Agent plugin result: {len(result.messages)} messages")
        for i, msg in enumerate(result.messages):
            logger.info(f"Message {i}: {msg['role']} - {msg['content'][:50]}...")
        
        return True
    except Exception as e:
        logger.error(f"Error processing message with agent plugin: {e}")
        return False

async def main():
    """Main function."""
    logger.info("Testing plugin system...")
    
    # Test plugin registration
    if not test_plugin_registration():
        logger.error("Plugin registration test failed")
        return
    
    # Test RAG plugin
    if not await test_rag_plugin():
        logger.error("RAG plugin test failed")
    
    # Test agent plugin
    if not await test_agent_plugin():
        logger.error("Agent plugin test failed")
    
    logger.info("Plugin system tests completed")

if __name__ == "__main__":
    import asyncio
    asyncio.run(main())

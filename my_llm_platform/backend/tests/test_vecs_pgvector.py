import vecs
import numpy as np
import logging
from langchain_community.vectorstores import PGVector
from langchain_community.vectorstores.pgvector import DistanceStrategy
from langchain_core.embeddings import Embeddings
from langchain_core.documents import Document

# ロギングを設定
logging.basicConfig(level=logging.DEBUG)

# 接続文字列
connection_string = 'postgresql://postgres:postgres@localhost:54322/postgres'

# ダミーの埋め込みクラス
class DummyEmbeddings(Embeddings):
    def embed_documents(self, texts):
        return [[0.1] * 384 for _ in texts]
    
    def embed_query(self, text):
        return [0.1] * 384

# vecsを使用してデータを追加
print("=== vecsを使用してデータを追加 ===")
try:
    # vecsクライアントを作成
    client = vecs.create_client(connection_string)
    
    # コレクションを取得または作成
    collection_name = 'test_vecs_collection'
    collection = client.get_or_create_collection(name=collection_name, dimension=384)
    
    # データを追加
    collection.upsert(records=[
        ('id1', np.random.rand(384), {'text': 'vecsテストテキスト1'}),
        ('id2', np.random.rand(384), {'text': 'vecsテストテキスト2'})
    ])
    print(f'vecsにデータを追加しました: {collection_name}')
    
    # データを検索
    results = collection.query(data=list(range(384)), limit=10, include_metadata=True)
    print(f'vecs検索結果: {results}')
    
except Exception as e:
    print(f'vecsエラー: {e}')

# PGVectorを使用してデータを追加
print("\n=== PGVectorを使用してデータを追加 ===")
try:
    # PGVectorを初期化
    pgvector = PGVector.from_documents(
        documents=[
            Document(page_content="PGVectorテストテキスト1"),
            Document(page_content="PGVectorテストテキスト2")
        ],
        embedding=DummyEmbeddings(),
        collection_name='test_pgvector_collection',
        connection_string=connection_string
    )
    print('PGVectorにデータを追加しました')
    
    # retrieverを作成
    retriever = pgvector.as_retriever()
    print('Retriever作成成功')
    
    # 検索を実行
    results = retriever.invoke("テスト")
    print(f'PGVector検索結果: {results}')
    
except Exception as e:
    print(f'PGVectorエラー: {e}')

# データベーステーブルを確認
print("\n=== データベーステーブルを確認 ===")
try:
    import psycopg2
    conn = psycopg2.connect(connection_string)
    cursor = conn.cursor()
    
    # テーブル一覧を取得
    cursor.execute("SELECT table_name FROM information_schema.tables WHERE table_schema = 'public';")
    tables = cursor.fetchall()
    print('テーブル一覧:')
    for table in tables:
        print(table[0])
    
    # test_vecs_collectionテーブルの構造を確認
    try:
        cursor.execute(f"SELECT column_name, data_type FROM information_schema.columns WHERE table_name = '{collection_name}';")
        columns = cursor.fetchall()
        print(f'\n{collection_name}テーブルの列:')
        for col in columns:
            print(f'{col[0]}: {col[1]}')
    except Exception as e:
        print(f'{collection_name}テーブルの構造確認エラー: {e}')
    
    # test_pgvector_collectionテーブルの構造を確認
    try:
        cursor.execute("SELECT column_name, data_type FROM information_schema.columns WHERE table_name = 'langchain_pg_embedding';")
        columns = cursor.fetchall()
        print('\nlangchain_pg_embeddingテーブルの列:')
        for col in columns:
            print(f'{col[0]}: {col[1]}')
    except Exception as e:
        print(f'langchain_pg_embeddingテーブルの構造確認エラー: {e}')
    
    conn.close()
    
except Exception as e:
    print(f'データベース確認エラー: {e}')

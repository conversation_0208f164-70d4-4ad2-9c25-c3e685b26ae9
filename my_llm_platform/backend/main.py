from fastapi import FastAPI, HTTPException, Depends, Request, Response
from fastapi.middleware.cors import CORSMiddleware
from fastapi.exceptions import RequestValidationError
from contextlib import asynccontextmanager
from typing import List, Dict, Any
import logging
import time
import os
import sys
import uuid
import signal
import atexit
from sqlalchemy.orm import Session
from dotenv import load_dotenv

from .inference import get_client
from .config import settings
from .routers import admin_api, tuning_api, chat_api, models_api, openai_api
from .db import get_db, SessionLocal, ChatMessage, Base, engine
from .auth import get_current_user
from .auth.router import router as auth_router
import uvicorn

# プラグインシステムのインポート
from .plugins import plugin_manager, plugin_router
from .plugins.samples import SimpleRAGPlugin, SimpleAgentPlugin, SimpleMCPPlugin, LangChainMCPPlugin
from .routers import chat_api_with_plugins

# 環境変数をロード
load_dotenv()

# ログ記録用のロガーを設定
logger = logging.getLogger("myllm")

# データベース初期化フラグ
INIT_DB = settings.FORCE_DB_INIT

# 認証モード
AUTH_MODE = settings.AUTH_MODE

# プラグインシステム有効化フラグ
ENABLE_PLUGINS = settings.ENABLE_PLUGINS

# 起動時にデータベースを初期化
if INIT_DB:
    logger.info("データベースを初期化中...")
    # データベース初期化コードを追加
    Base.metadata.create_all(bind=engine)
    logger.info("データベースの初期化が完了しました")

# FastAPIアプリケーションを作成
app = FastAPI(
    title="My LLM Platform API",
    description="LLMプラットフォームのバックエンドAPI",
    version="0.1.0",
    lifespan=None  # 後で定義するlifespanコンテキストマネージャーを使用
)

# CORSミドルウェアを設定
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 本番環境では特定のオリジンのみを許可するべき
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# ルーターを登録
# 認証関連のAPI
app.include_router(auth_router)
# 管理者のAPI
app.include_router(admin_api.router)

# チャットとチャット履歴のAPI
# プラグインシステムが有効な場合は、プラグイン対応のチャットAPIを使用
if ENABLE_PLUGINS:
    logger.info("プラグインシステムを有効化しています")
    app.include_router(chat_api_with_plugins.router)
    # プラグイン管理APIを登録
    app.include_router(plugin_router)
    # サンプルプラグインを登録
    plugin_manager.register_plugin("simple_rag", SimpleRAGPlugin)
    plugin_manager.register_plugin("simple_agent", SimpleAgentPlugin)
    plugin_manager.register_plugin("simple_mcp", SimpleMCPPlugin)
    plugin_manager.register_plugin("langchain_mcp", LangChainMCPPlugin)
    logger.info(f"登録されたプラグイン: {len(plugin_manager.get_all_plugins())}")
else:
    app.include_router(chat_api.router)

# モデルとバックエンド情報のAPI
app.include_router(models_api.router)
# LLM推論とチューニングのAPI
app.include_router(tuning_api.router)
# OpenAI互換API
app.include_router(openai_api.router)

# 認証モードに応じてKeycloakルーターを登録
# 注意: keycloak_api.pyが存在しない場合は、この部分をコメントアウトしてください
# if AUTH_MODE == "keycloak" or AUTH_MODE == "self_hosted":
#     from .routers import keycloak_api
#     app.include_router(keycloak_api.router, prefix="/v1", tags=["keycloak"])

# 起動イベント
@asynccontextmanager
async def lifespan(_app: FastAPI):
    # 起動時に実行
    try:
        # データベースを初期化
        if INIT_DB:
            logger.info("データベースを初期化中...")
            # データベース初期化コードを追加
            Base.metadata.create_all(bind=engine)
            logger.info("データベースの初期化が完了しました")

        # 認証モードを記録
        logger.info(f"認証モード: {AUTH_MODE}")

        # バックエンドを初期化
        logger.info("バックエンドを初期化中...")
        # ここにバックエンド初期化コードを追加
        logger.info("バックエンドの初期化が完了しました")

        # プラグインシステムを初期化
        if ENABLE_PLUGINS:
            logger.info("プラグインシステムを初期化中...")
            # サンプルプラグインはすでにルーター登録時に登録されています

            # プラグインを検出
            plugin_manager.discover_plugins()

            logger.info(f"プラグインシステムの初期化が完了しました。登録済みプラグイン: {len(plugin_manager.get_all_plugins())}")
        else:
            logger.info("プラグインシステムは無効化されています")
            plugin_manager.disable_manager()

        # クリーンアップ処理を登録
        def cleanup():
            logger.info("アプリケーションをシャットダウン中...")
            # ここにクリーンアップコードを追加
            logger.info("シャットダウンが完了しました")

        atexit.register(cleanup)

        # SIGTERMシグナルハンドラーを登録
        def sigterm_handler(signum, frame):
            logger.info("SIGTERMを受信しました。シャットダウンを開始します...")
            sys.exit(0)

        signal.signal(signal.SIGTERM, sigterm_handler)

        logger.info("アプリケーションの起動が完了しました")
        yield
    except Exception as e:
        logger.error(f"起動時にエラーが発生しました: {e}")
        raise
    finally:
        # 閉じる際に実行
        logger.info("アプリケーションをシャットダウン中...")
        # ここにクリーンアップコードを追加
        logger.info("シャットダウンが完了しました")

# アプリケーションのライフスパンを設定
app.router.lifespan_context = lifespan

# リクエストIDミドルウェア
@app.middleware("http")
async def add_request_id(request: Request, call_next):
    request_id = str(uuid.uuid4())
    logger.info(f"リクエスト開始: {request.method} {request.url.path} (ID: {request_id})")
    start_time = time.time()

    response = await call_next(request)

    process_time = time.time() - start_time
    logger.info(f"リクエスト完了: {request.method} {request.url.path} - {response.status_code} ({process_time:.4f}秒) (ID: {request_id})")

    return response

# バリデーションエラーハンドラー
@app.exception_handler(RequestValidationError)
async def validation_exception_handler(request: Request, exc: RequestValidationError):
    """
    リクエストバリデーションエラーのハンドラー
    OpenAI APIのエラーフォーマットに合わせてエラーレスポンスを返します
    """
    logger.error(f"バリデーションエラー: {exc.errors()}")

    # エラーの詳細を取得
    error_details = []
    for error in exc.errors():
        error_details.append({
            'loc': ' -> '.join(str(loc) for loc in error['loc']),
            'msg': error['msg'],
            'type': error['type']
        })

    error_messages = '; '.join([f"{e['loc']}: {e['msg']}" for e in error_details])

    return openai_api.format_openai_error(
        status_code=422,
        message=f"リクエストのバリデーションに失敗しました: {error_messages}",
        type_="invalid_request_error",
        param=error_details[0]['loc'] if error_details else None
    )

@app.get("/favicon.ico")
async def favicon():
    return Response(status_code=204)

@app.get("/")
def read_root():
    """
    ルートパスのウェルカムメッセージ
    """
    return {"message": "My LLM Platformへようこそ！"}

# サーバーを起動
if __name__ == "__main__":
    import argparse

    # コマンドライン引数を解析
    parser = argparse.ArgumentParser(description="My LLM Platform API Server")
    parser.add_argument("--port", type=int, default=8000, help="サーバーポート番号")
    parser.add_argument("--host", type=str, default="0.0.0.0", help="サーバーホスト")
    parser.add_argument("--reload", action="store_true", help="自動リロードを有効化")
    parser.add_argument("--enable-plugins", action="store_true", help="プラグインシステムを有効化")
    args = parser.parse_args()

    # プラグインシステムを有効化する場合は設定を更新
    if args.enable_plugins:
        settings.ENABLE_PLUGINS = True
        ENABLE_PLUGINS = True
        logger.info("コマンドライン引数からプラグインシステムを有効化しました")

    # 指定されたポートでサーバーを起動
    reload = args.reload or settings.DEBUG
    uvicorn.run("backend.main:app", host=args.host, port=args.port, reload=reload)

"""
Plugin system type definitions.
"""

from typing import Dict, List, Any, Optional, Union
from typing_extensions import TypedDict
from enum import Enum
from pydantic import BaseModel


class PluginType(str, Enum):
    """Plugin types enumeration."""
    PREPROCESSOR = "preprocessor"
    POSTPROCESSOR = "postprocessor"
    COMMAND = "command"
    TOOL = "tool"
    UI = "ui"
    AGENT = "agent"
    RAG = "rag"
    MCP = "mcp"


class MessageRequired(TypedDict):
    """Required fields for chat message."""
    role: str
    content: str

class Message(MessageRequired, total=False):
    """Chat message type with optional fields."""
    name: str
    function_call: Dict[str, Any]
    tool_calls: List[Dict[str, Any]]
    tool_call_id: str


class PluginResult(BaseModel):
    """Result of plugin processing."""
    messages: List[Message]
    metadata: Dict[str, Any] = {}
    stop_processing: bool = False
    error: Optional[str] = None


class PluginConfig(BaseModel):
    """Plugin configuration."""
    enabled: bool = True
    priority: int = 100
    config: Dict[str, Any] = {}


class PluginInfo(BaseModel):
    """Plugin information."""
    id: str
    name: str
    description: str
    version: str
    type: PluginType
    author: str
    config: PluginConfig
    dependencies: List[str] = []
    tags: List[str] = []


class ToolDefinition(BaseModel):
    """Tool definition for LLM function/tool calling."""
    name: str
    description: str
    parameters: Dict[str, Any]
    required: List[str] = []


class UIComponent(BaseModel):
    """UI component definition for frontend rendering."""
    id: str
    type: str
    props: Dict[str, Any] = {}
    children: List[Union[str, 'UIComponent']] = []


class MCPAction(BaseModel):
    """MCP action definition."""
    type: str
    name: str
    input: Dict[str, Any] = {}
    thought: Optional[str] = None


class MCPResponse(BaseModel):
    """MCP response definition."""
    type: str
    content: Any
    status: str = "success"
    error: Optional[str] = None

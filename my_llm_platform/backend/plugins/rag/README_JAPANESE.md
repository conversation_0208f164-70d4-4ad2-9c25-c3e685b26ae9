# 日本語テキスト処理のための依存関係

## 必要なパッケージ

日本語テキスト処理を行うためには、以下のパッケージが必要です：

1. **fugashi**: 日本語形態素解析のためのPythonバインディング
2. **mecab-python3**: MeCab形態素解析器のPythonバインディング
3. **unidic-lite**: 日本語辞書（軽量版）

## インストール方法

以下のコマンドでインストールできます：

```bash
pip install fugashi mecab-python3 unidic-lite
```

より高精度な解析が必要な場合は、`unidic-lite`の代わりに完全版の`unidic`をインストールすることもできます：

```bash
pip install unidic
```

## エラーと解決方法

以下のようなエラーが発生した場合：

```
クロスエンコーダーモデルの初期化に失敗しました: You need to install fugashi to use MecabTokenizer. See https://pypi.org/project/fugashi/ for installation.
```

上記のパッケージをインストールすることで解決できます。

## 関連コンポーネント

- **reranker.py**: 検索結果の再ランキングを行うクラス。日本語テキスト処理のために`fugashi`を使用します。
- **text_processor.py**: テキスト前処理を行うクラス。日本語テキストの検出と処理を行います。

## 注意事項

- 日本語処理を行う場合は、必ず上記のパッケージをインストールしてください。
- `fugashi`がインストールされていない場合、システムは自動的にベクトルスコア戦略にフォールバックします。
- 最適な日本語処理のためには、適切な辞書（`unidic`または`unidic-lite`）が必要です。

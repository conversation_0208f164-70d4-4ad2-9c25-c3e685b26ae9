import os
import re
import logging
import tempfile
from typing import List, Dict, Any, Optional
from langchain_core.prompts import Chat<PERSON>romptTemplate
from langchain_openai import ChatOpenAI
from .vector_store import VectorStore
from .document_loader import DocumentLoader
from .text_processor import TextProcessor
from .reranker import Reranker, RankingStrategy
from ...inference import get_client
from ...config import settings

logger = logging.getLogger(__name__)

# デフォルト設定
DEFAULT_COLLECTION_NAME = "documents"
DEFAULT_EMBEDDING_MODEL = "hotchpotch-japanese"
DEFAULT_BACKEND = settings.DEFAULT_LLM_BACKEND or "ollama"
# デフォルトのデータディレクトリは、このファイルと同じディレクトリの "data" フォルダ
DEFAULT_DIRECTORY_PATH = os.path.join(os.path.dirname(__file__), "data")
# デフォルトの再ランキング戦略
DEFAULT_RANKING_STRATEGY = RankingStrategy.CROSS_ENCODER

class RAGService:
    """
    検索拡張生成（RAG）サービスクラス
    """
    def __init__(self, collection_name: str = None, embedding_model_name: str = None,
                 backend: str = None, recreate_collection: bool = False, directory_path: str = None,
                 force_reimport: bool = False, ranking_strategy: RankingStrategy = DEFAULT_RANKING_STRATEGY,
                 enable_reranking: bool = True, reranker_model: str = "hotchpotch/japanese-reranker-cross-encoder-base-v1"):
        """
        RAGサービスを初期化します

        Args:
            collection_name: コレクション名（Noneの場合はデフォルト値を使用）
            embedding_model_name: 埋め込みモデル名（Noneの場合はデフォルト値を使用）
            backend: バックエンド名（Noneの場合はデフォルト値を使用）
            recreate_collection: コレクションを再作成するかどうか
            directory_path: 初期データを読み込むディレクトリパス（Noneの場合は自動インポートしない）
            force_reimport: 既存のデータを削除してから再インポートするかどうか
            ranking_strategy: 検索結果のランキング戦略
            enable_reranking: 再ランキングを有効にするかどうか
            reranker_model: 使用する再ランキングモデル名
                - "hotchpotch/japanese-reranker-cross-encoder-base-v1" (デフォルト)
                - "hotchpotch/japanese-reranker-cross-encoder-large-v1"
                - "BAAI/bge-reranker-base"
        """
        # デフォルト値の設定
        self.collection_name = collection_name if collection_name is not None else DEFAULT_COLLECTION_NAME
        self.embedding_model_name = embedding_model_name if embedding_model_name is not None else DEFAULT_EMBEDDING_MODEL
        self.backend_name = backend if backend is not None else settings.DEFAULT_LLM_BACKEND or DEFAULT_BACKEND
        self.enable_reranking = enable_reranking
        self.reranker_model = reranker_model

        # ディレクトリパスの設定（Noneの場合はデフォルト値を使用）
        self.directory_path = directory_path  # 元のパラメータ値を保存
        # 実際の処理では、Noneの場合はadd_documents_from_directoryメソッド内でデフォルト値が使用される

        # ベクトルストアとドキュメントローダーの初期化
        self.vector_store = VectorStore(collection_name=self.collection_name, model_name=self.embedding_model_name)
        self.document_loader = DocumentLoader(enable_text_processing=True)
        self.backend = get_client(self.backend_name)

        # テキスト処理器を初期化（クエリ処理用、自動言語検出を有効化）
        self.text_processor = TextProcessor(
            normalize=True,
            remove_extra_whitespace=True,
            remove_urls=True,
            remove_email=True,
            remove_html_tags=True,
            lowercase=None,  # 自動検出
            remove_special_chars=None,  # 自動検出
            auto_detect_language=True
        )

        # 再ランキング機能を初期化
        self.reranker = Reranker(
            strategy=ranking_strategy,
            model_name=self.reranker_model
        )

        # コレクションが存在しない場合は作成
        collection_has_data = False

        try:
            # コレクションを作成または取得
            self.vector_store.create_collection(recreate=recreate_collection)
            logger.info(f"コレクション {self.collection_name} が正常に初期化されました")

            # コレクション情報を取得してデータが存在するか確認
            try:
                collection_info = self.vector_store.get_collection_info()
                if collection_info and "points_count" in collection_info:
                    collection_has_data = collection_info["points_count"] > 0
                    logger.info(f"コレクション {self.collection_name} には {collection_info['points_count']} 件のデータが存在します")

                    # force_reimportが指定されている場合は、コレクションを削除して再作成
                    if force_reimport and collection_has_data:
                        try:
                            logger.info(f"force_reimportが指定されたため、コレクションを削除して再作成します")
                            self.vector_store.create_collection(recreate=True)
                            logger.info(f"コレクション {self.collection_name} が再作成されました")
                            collection_has_data = False
                        except Exception as recreate_error:
                            logger.error(f"コレクションの再作成中にエラーが発生しました: {recreate_error}")

                    # データが存在しない場合は自動インポート
                    if not collection_has_data and self.directory_path:
                        try:
                            logger.info(f"コレクションが空のため、ドキュメントを自動インポートします")
                            self.add_documents_from_directory(self.directory_path)
                            logger.info(f"ドキュメントのインポートが完了しました")
                        except Exception as import_error:
                            logger.error(f"ドキュメントの自動インポート中にエラーが発生しました: {import_error}")
                elif recreate_collection and self.directory_path:
                    # コレクションが再作成された場合も自動インポート
                    try:
                        logger.info(f"コレクションが再作成されたため、ドキュメントを自動インポートします")
                        self.add_documents_from_directory(self.directory_path)
                        logger.info(f"ドキュメントのインポートが完了しました")
                    except Exception as import_error:
                        logger.error(f"ドキュメントの自動インポート中にエラーが発生しました: {import_error}")
            except Exception as info_error:
                logger.warning(f"コレクション情報の取得中にエラーが発生しました: {info_error}")
                # エラーが発生した場合でも自動インポートを試みる
                if self.directory_path:
                    try:
                        logger.info(f"コレクション情報の取得に失敗したため、ドキュメントを自動インポートします")
                        self.add_documents_from_directory(self.directory_path)
                        logger.info(f"ドキュメントのインポートが完了しました")
                    except Exception as import_error:
                        logger.error(f"ドキュメントの自動インポート中にエラーが発生しました: {import_error}")

        except Exception as e:
            logger.error(f"コレクションの初期化中にエラーが発生しました: {e}")
            if "Connection refused" in str(e):
                logger.warning("ベクトルストアサーバーが実行されていない可能性があります。サーバーを起動してください。")
            elif "Vector dimension error" in str(e):
                logger.warning("ベクトルの次元数が一致しません。recreate_collection=True を指定して再試行してください。")

    def add_document(self, file_path: str, metadata: Optional[Dict[str, Any]] = None, delete_existing: bool = False) -> int:
        """
        単一のファイルをベクトルストアに追加します

        Args:
            file_path: ドキュメントのファイルパス
            metadata: 追加するメタデータ
            delete_existing: 既存のファイルデータを削除してから追加するかどうか
        """
        try:
            # ファイルが存在するか確認
            if not os.path.exists(file_path):
                logger.error(f"ファイルが見つかりません: {file_path}")
                raise FileNotFoundError(f"ファイルが見つかりません: {file_path}")

            if not os.path.isfile(file_path):
                logger.error(f"{file_path} はファイルではありません")
                raise ValueError(f"{file_path} はファイルではありません")

            # ファイル名を取得
            file_name = os.path.basename(file_path)

            # 既存のファイルデータを削除
            if delete_existing:
                self.delete_documents_by_file_name(file_name)
                logger.info(f"ファイル {file_name} の既存データを削除しました")

            # ファイル固有のメタデータを作成
            file_metadata = {}
            if metadata:
                file_metadata.update(metadata)

            # ファイル名をメタデータに追加
            file_metadata["file_name"] = file_name

            # 最終更新時間とファイルサイズをメタデータに追加
            file_metadata["mtime"] = os.path.getmtime(file_path)
            file_metadata["size"] = os.path.getsize(file_path)

            # ベクトルストアが利用可能かチェック
            if not self.vector_store.is_available:
                logger.error(f"ベクトルストア {self.vector_store.store_type} は利用できません。設定を確認してください。")
                raise RuntimeError(f"ベクトルストア {self.vector_store.store_type} は利用できません。設定を確認してください。")

            # ドキュメントを読み込み
            documents = self.document_loader.load_document(file_path, file_metadata)

            # ベクトルストアに追加
            self.vector_store.add_documents(documents)

            logger.info(f"ファイル {file_path} から {len(documents)} 個のチャンクを追加しました")
            return len(documents)
        except Exception as e:
            logger.error(f"ドキュメントの追加中にエラーが発生しました: {e}")
            raise

    def delete_documents_by_file_name(self, file_name: str) -> bool:
        """
        ファイル名に基づいてドキュメントを削除します

        Args:
            file_name: 削除するドキュメントのファイル名

        Returns:
            bool: 削除に成功した場合はTrue、そうでない場合はFalse
        """
        try:
            # フィルタを作成
            filter_params = {"file_name": file_name}

            # ベクトルストアからドキュメントを削除
            self.vector_store.delete_by_filter(filter_params)

            logger.info(f"ファイル {file_name} のドキュメントを削除しました")
            return True
        except Exception as e:
            logger.error(f"ドキュメントの削除中にエラーが発生しました: {e}")
            return False



    def add_documents_from_directory(self, directory_path: str = None, recursive: bool = True, metadata: Optional[Dict[str, Any]] = None) -> int:
        """
        ディレクトリ内のすべてのファイルをベクトルストアに追加します

        Args:
            directory_path: ドキュメントを読み込むディレクトリパス（Noneの場合はデフォルト値を使用）
            recursive: サブディレクトリも再帰的に処理するかどうか
            metadata: 追加するメタデータ
        """
        try:
            # ディレクトリパスがNoneの場合、デフォルトのディレクトリパスを設定
            if directory_path is None or not directory_path:
                directory_path = DEFAULT_DIRECTORY_PATH
                logger.info(f"ディレクトリパスが指定されていないため、デフォルトのパスを使用します: {directory_path}")

            # ディレクトリが存在するか確認
            if not os.path.exists(directory_path):
                logger.error(f"ディレクトリが見つかりません: {directory_path}")
                raise FileNotFoundError(f"ディレクトリが見つかりません: {directory_path}")

            if not os.path.isdir(directory_path):
                logger.error(f"{directory_path} はディレクトリではありません")
                raise ValueError(f"{directory_path} はディレクトリではありません")

            # ベクトルストアが利用可能かチェック
            if not self.vector_store.is_available:
                logger.error(f"ベクトルストア {self.vector_store.store_type} は利用できません。設定を確認してください。")
                raise RuntimeError(f"ベクトルストア {self.vector_store.store_type} は利用できません。設定を確認してください。")

            # 処理したファイルとチャンクの数を追跡
            processed_files = 0
            total_chunks = 0

            # ディレクトリ内のファイルを処理
            for root, _, files in os.walk(directory_path):
                for file in files:
                    file_path = os.path.join(root, file)
                    _, file_extension = os.path.splitext(file.lower())

                    # サポートされているファイル形式のみを処理
                    if file_extension in self.document_loader.FILE_LOADER_MAPPING or True:  # すべてのファイルを試す
                        try:
                            # ファイル固有のメタデータを作成
                            file_metadata = {}
                            if metadata:
                                file_metadata.update(metadata)

                            # ファイル名をメタデータに追加
                            file_metadata["file_name"] = file

                            # 最終更新時間とファイルサイズをメタデータに追加
                            file_metadata["mtime"] = os.path.getmtime(file_path)
                            file_metadata["size"] = os.path.getsize(file_path)

                            # 相対パスをメタデータに追加
                            rel_path = os.path.relpath(root, directory_path)
                            if rel_path != ".":
                                file_metadata["directory"] = rel_path

                            # ファイルを読み込む
                            documents = self.document_loader.load_document(file_path, file_metadata)

                            # ベクトルストアに追加
                            self.vector_store.add_documents(documents)

                            processed_files += 1
                            total_chunks += len(documents)
                            logger.info(f"ファイル {file_path} から {len(documents)} 個のチャンクを追加しました")
                        except Exception as e:
                            logger.warning(f"ファイル {file_path} の処理中にエラーが発生しました: {e}")
                            continue

                # 再帰的に処理しない場合は最初のディレクトリのみ
                if not recursive:
                    break

            logger.info(f"ディレクトリ {directory_path} から {processed_files} 個のファイル、合計 {total_chunks} 個のチャンクを追加しました")
            return total_chunks
        except Exception as e:
            logger.error(f"ディレクトリからのドキュメント追加中にエラーが発生しました: {e}")
            raise

    def add_text(self, text: str, metadata: Optional[Dict[str, Any]] = None) -> int:
        """
        テキストをベクトルストアに追加します
        """
        try:
            # テキストが空でないか確認
            if not text or not text.strip():
                logger.warning("空のテキストが渡されました")
                return 0

            # テキストを一時ファイルに書き込み
            with tempfile.NamedTemporaryFile(mode="w", suffix=".txt", delete=False) as temp_file:
                temp_file.write(text)
                temp_path = temp_file.name

            try:
                # ベクトルストアが利用可能かチェック
                if not self.vector_store.is_available:
                    logger.error(f"ベクトルストア {self.vector_store.store_type} は利用できません。設定を確認してください。")
                    raise RuntimeError(f"ベクトルストア {self.vector_store.store_type} は利用できません。設定を確認してください。")

                # ドキュメントを読み込み
                documents = self.document_loader.load_document(temp_path, metadata)

                # ベクトルストアに追加
                self.vector_store.add_documents(documents)

                logger.info(f"{len(documents)} 個のテキストチャンクを追加しました")
                return len(documents)
            finally:
                # 一時ファイルを削除
                os.unlink(temp_path)
        except Exception as e:
            logger.error(f"テキストの追加中にエラーが発生しました: {e}")
            raise

    def query(self, query_text: str, top_k: int = 5, filter_params: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        """
        クエリに基づいて関連文書を検索します
        """
        try:
            # クエリが空でないか確認
            if not query_text or not query_text.strip():
                logger.warning("空のクエリが渡されました")
                return []

            # クエリのテキスト前処理を適用
            processed_query = self.text_processor.process_text(query_text)
            logger.debug(f"クエリを前処理しました: '{query_text}' -> '{processed_query}'")

            # ベクトルストアで検索
            results = self.vector_store.search(processed_query, top_k, filter_params)

            # 再ランキングが有効な場合は結果を再ランキング
            if self.enable_reranking and results:
                # 検索結果数を記録
                original_count = len(results)

                # 再ランキングを実行（元のクエリを使用）
                results = self.reranker.rerank(query_text, results, top_k)

                logger.info(f"クエリ '{query_text}' の結果を再ランキングしました: {original_count} -> {len(results)}")

            logger.info(f"クエリ '{query_text}' に対して {len(results)} 個の結果が見つかりました")
            return results
        except Exception as e:
            logger.error(f"クエリ中にエラーが発生しました: {e}")
            raise

    def run_rag(self, query: str, top_k: int = 3, filter_params: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        RAGを実行して回答を生成します（最新のLangChain APIを使用）

        Returns:
            Dict[str, Any]: 以下のキーを含む辞書:
                - result: 最終的な回答
                - think: 思考プロセス
                - context: 使用されたコンテキスト
        """
        # 結果の初期値を設定
        result = {
            "result": "",
            "think": "",
            "context": []
        }

        try:
            # クエリが空でないか確認
            if not query or not query.strip():
                logger.warning("空のクエリが渡されました")
                result["result"] = "質問が空です。質問を入力してください。"
                return result

            # クエリのテキスト前処理を適用
            processed_query = self.text_processor.process_text(query)
            logger.debug(f"RAGクエリを前処理しました: '{query}' -> '{processed_query}'")

            # ベクトルストアが利用可能かチェック
            if not self.vector_store.is_available:
                logger.error(f"ベクトルストア {self.vector_store.store_type} は利用できません。設定を確認してください。")
                result["result"] = f"ベクトルストア {self.vector_store.store_type} は利用できません。管理者に連絡してください。"
                return result

            # 通常のベクトル検索を実行
            try:
                # 検索用のretrieverを作成
                retriever = self.vector_store.as_retriever(search_kwargs={"k": top_k, "filter": filter_params})

            except Exception as retriever_error:
                logger.error(f"Retrieverの作成中にエラーが発生しました: {retriever_error}")
                result["result"] = "検索システムの初期化中にエラーが発生しました。管理者に連絡してください。"
                return result

            # プロンプトテンプレートを作成
            prompt = ChatPromptTemplate.from_template(
                """以下の情報に基づいて質問に答えてください:

                {context}

                質問：{question}

                まず、<think>タグ内で思考プロセスを詳細に記述してください。次に、最終的な回答を提供してください。

                重要: 提供された情報に明確な根拠がない場合は、「わかりません」と回答してください。推測や不確かな情報に基づく回答は避けてください。

                <think>
                ここに思考プロセスを記述してください。情報の分析、推論、判断の根拠などを含めてください。
                提供された情報に明確な根拠が見つからない場合は、その旨を記述し、「わかりません」と回答することを検討してください。
                </think>

                回答："""
            )

            # OpenAIクライアントからLangChain用のChatOpenAIモデルを作成
            try:
                openai_client = self.backend
                llm = ChatOpenAI(
                    model_name=settings.DEFAULT_LLM_MODEL or "llama3.2:3b", # 利用可能なモデル名
                    openai_api_key=openai_client.api_key,
                    openai_api_base=str(openai_client.base_url)
                )
            except Exception as llm_error:
                logger.error(f"LLMの初期化中にエラーが発生しました: {llm_error}")
                result["result"] = "言語モデルの初期化中にエラーが発生しました。"
                return result

            # 処理結果の変数を初期化
            result_content = ""
            think_content = ""
            context_texts = []

            try:
                # 通常の検索処理を実行
                # コンテキストを取得（処理済みクエリを使用）
                retrieved_docs = retriever.invoke(processed_query)

                # 再ランキングが有効な場合は結果を再ランキング
                if self.enable_reranking and retrieved_docs:
                    # Documentオブジェクトを辞書に変換
                    doc_dicts = [
                        {
                            "text": doc.page_content,
                            "metadata": doc.metadata,
                            "score": 1.0  # 初期スコア
                        }
                        for doc in retrieved_docs
                    ]

                    # 再ランキングを実行（元のクエリを使用）
                    reranked_results = self.reranker.rerank(query, doc_dicts, top_k)

                    # 再ランキングされた結果をDocumentオブジェクトに戻す
                    from langchain_core.documents import Document
                    retrieved_docs = [
                        Document(
                            page_content=result["text"],
                            metadata=result.get("metadata", {})
                        )
                        for result in reranked_results
                    ]

                    logger.info(f"RAGクエリ '{query}' の結果を再ランキングしました")

                if retrieved_docs:
                    context_texts = [doc.page_content for doc in retrieved_docs]

                # プロンプトを実行（元のクエリを使用）
                prompt_args = {"context": retrieved_docs, "question": query}
                prompt_result = prompt.invoke(prompt_args)

                # LLMで回答を生成
                llm_response = llm.invoke(prompt_result)
                if llm_response and hasattr(llm_response, 'content'):
                    response_text = llm_response.content
                    result_content = response_text

                    # 元のレスポンスを保存
                    original_response = response_text

                    # まず、<think></think> タグを使って思考プロセスを抽出
                    think_match = re.search(r'<think>(.*?)</think>', original_response, re.DOTALL)
                    if think_match:
                        # 思考プロセスを抽出 - 思考プロセスの内容はそのまま使用
                        think_content = think_match.group(1).strip()
                        logger.debug("<think> タグから思考プロセスを抽出しました")

                    # 特定のパターンを持つ応答を処理
                    filtered_response = re.sub(r'<reasoning>.*?</reasoning>', '', original_response, flags=re.DOTALL)
                    filtered_response = re.sub(r'</response><response>', '', filtered_response, flags=re.DOTALL)
                    filtered_response = re.sub(r'<response>', '', filtered_response, flags=re.DOTALL)
                    filtered_response = re.sub(r'</response>', '', filtered_response, flags=re.DOTALL)

                    # 特定のタグを削除
                    tags_to_remove = [
                        '</answer>',
                        '<answer>',
                        '<Answer>',
                        '</Answer>',
                        '</ >',
                        '</>',
                        '<tag',
                        '</tag>',
                        '<prosecution>',
                        '</prosecution>',
                        '<final_answer>',
                        '</final_answer>',
                        '<response>',
                        '</response>',
                        '\\boxed{',
                        '}'
                    ]

                    for tag in tags_to_remove:
                        filtered_response = filtered_response.replace(tag, '')

                    # <think>...</think> タグは前端で処理するため、ここでは削除しない
                    # result_content = re.sub(r'<think>.*?</think>', '', filtered_response, flags=re.DOTALL).strip()
                    result_content = filtered_response.strip()
                    logger.debug("フィルタリング後の結果を回答として使用します（thinkタグは保持）")

                    # 結果が空の場合は、元のレスポンスを使用
                    if not result_content:
                        logger.debug("タグ除去後のコンテンツが空のため、元のテキストを回答として使用します")
                        result_content = original_response

                    # 「わかりません」が含まれている場合は、それを返す
                    if "わかりません" in result_content or "わかりません" in original_response:
                        result_content = "わかりません"
                        logger.debug("「わかりません」が含まれているため、それを回答として使用します")

                    # 思考プロセスが空の場合は空文字列を設定
                    if not think_content:
                        logger.debug("思考プロセスが空です")
            except Exception as process_error:
                logger.error(f"RAG処理中にエラーが発生しました: {process_error}")
                # エラーが発生しても空の結果を返す（例外は発生させない）
                if not result_content:
                    result_content = f"回答の生成中にエラーが発生しました。"

            # 結果を辞書として返す
            result = {
                "result": result_content,
                "think": think_content,
                "context": context_texts
            }

            logger.info(f"クエリ '{query}' に対して回答を生成しました")
            return result
        except Exception as e:
            logger.error(f"RAG実行中に予期しないエラーが発生しました: {e}")
            # 初期値の結果に最小限のエラーメッセージを設定して返す
            result["result"] = "回答の生成中に予期しないエラーが発生しました。"
            return result

    def delete_documents(self, filter_params: Dict[str, Any]) -> None:
        """
        フィルタに一致するドキュメントを削除します
        """
        try:
            # フィルタが空でないか確認
            if not filter_params:
                logger.warning("空のフィルタが渡されました")
                return

            # ベクトルストアからドキュメントを削除
            self.vector_store.delete_by_filter(filter_params)

            logger.info(f"フィルタ {filter_params} に一致するドキュメントを削除しました")
        except Exception as e:
            logger.error(f"ドキュメントの削除中にエラーが発生しました: {e}")
            raise

    def get_collection_info(self) -> Dict[str, Any]:
        """
        コレクションの情報を取得します
        """
        try:
            # ベクトルストアからコレクション情報を取得
            info = self.vector_store.get_collection_info()

            # 埋め込みモデル情報を追加
            info["embedding_model"] = self.vector_store.embedding_model.model_name

            logger.info(f"コレクション情報を取得しました: {info}")
            return info
        except Exception as e:
            logger.error(f"コレクション情報の取得中にエラーが発生しました: {e}")
            raise

    def set_ranking_strategy(self, strategy: RankingStrategy, enable_reranking: bool = True) -> None:
        """
        ランキング戦略を設定します

        Args:
            strategy: 使用するランキング戦略
            enable_reranking: 再ランキングを有効にするかどうか
        """
        self.reranker.set_strategy(strategy)
        self.enable_reranking = enable_reranking
        logger.info(f"ランキング戦略を {strategy.value} に変更しました（有効: {enable_reranking}）")

    def set_reranker_model(self, model_name: str) -> None:
        """
        再ランキングモデルを設定します

        Args:
            model_name: 使用するクロスエンコーダーモデル名
                - "hotchpotch/japanese-reranker-cross-encoder-base-v1"
                - "hotchpotch/japanese-reranker-cross-encoder-large-v1"
                - "BAAI/bge-reranker-base"
        """
        self.reranker_model = model_name
        self.reranker.set_model(model_name)
        logger.info(f"再ランキングモデルを {model_name} に変更しました")

    def set_top_k(self, top_k: int) -> None:
        """
        返す結果の最大数を設定します

        Args:
            top_k: 返す結果の最大数
        """
        self.reranker.set_top_k(top_k)
        logger.info(f"返す結果の最大数を {top_k} に変更しました")


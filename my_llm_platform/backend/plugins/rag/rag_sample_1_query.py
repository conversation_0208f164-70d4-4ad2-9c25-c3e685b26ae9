import logging
import sys
import os

# ログレベルを設定
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler(sys.stdout)]
)

from .rag_service import RAGService

def main():
    print("RAG検索テスト開始")
    
    # RAGサービスを初期化
    rag = RAGService(collection_name="my_collection_sample")
    
    # クエリを実行
    query = "会社の情報について教えてください"
    print(f"クエリ: {query}")
    
    # 検索結果を取得
    results = rag.query(query, top_k=3)
    
    # 結果を表示
    print(f"検索結果数: {len(results)}")
    for i, result in enumerate(results):
        print(f"結果 {i+1}:")
        print(f"  テキスト: {result['text'][:100]}...")
        print(f"  スコア: {result.get('score', 0):.4f}")
        print(f"  メタデータ: {result.get('metadata', {})}")
    
    # RAGを実行
    print("\nRAG実行:")
    rag_result = rag.run_rag(query)
    print(f"回答: {rag_result['result']}")
    print(f"思考プロセス: {rag_result.get('think', '')}")
    print(f"使用されたコンテキスト数: {len(rag_result.get('context', []))}")
    
    print("RAG検索テスト完了")

if __name__ == "__main__":
    main()

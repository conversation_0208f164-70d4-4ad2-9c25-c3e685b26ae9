# RAG (Retrieval-Augmented Generation) システム

このディレクトリには、検索拡張生成（RAG）機能を実装するためのコンポーネントが含まれています。
RAGは大規模言語モデル（LLM）の回答生成能力を、外部知識ベースからの情報検索で拡張するアプローチです。

## 主要コンポーネント

### 1. `document_loader.py`

ドキュメントの読み込みと分割を担当するモジュールです。

**主な機能:**
- 多様なファイル形式（PDF, TXT, CSV, JSON, Excel, PowerPoint, Word, HTML, Markdown）に対応
- ファイル拡張子に基づいて適切なローダーを自動選択
- ドキュメントをチャンク（小さな断片）に分割
- メタデータの追加と管理

**主要クラス:**
- `DocumentLoader`: ドキュメントの読み込みと分割を担当

**使用例:**
```python
loader = DocumentLoader()
documents = loader.load_document("path/to/file.pdf")
```

### 2. `embeddings.py`

テキストをベクトル（数値表現）に変換するための埋め込みモデルを提供します。

**主な機能:**
- 複数の埋め込みモデルをサポート（Instructor, MPNet, MiniLM, OpenAI）
- テキストの埋め込み処理
- 複数ドキュメントのバッチ処理

**主要クラス:**
- `EmbeddingModel`: テキストをベクトルに変換

**使用例:**
```python
model = EmbeddingModel(model_name="instructor")
vector = model.embed_text("テキスト例")
```

### 3. `vector_store.py`

埋め込みベクトルを保存・検索するためのベクトルデータベース機能を提供します。

**主な機能:**
- Qdrantベクトルデータベースとの連携
- コレクションの作成と管理
- ドキュメントの追加、検索、削除
- フィルタリング機能

**主要クラス:**
- `VectorStore`: ベクトルデータベースの操作を担当

**使用例:**
```python
store = VectorStore(collection_name="my_documents")
store.add_documents(documents)
results = store.search("検索クエリ", top_k=5)
```

### 4. `text_processor.py`

テキストの前処理を担当するモジュールです。ベクトル検索の精度を向上させます。

**主な機能:**
- Unicode正規化
- HTMLタグの削除
- URLやメールアドレスの削除
- 余分な空白の削除
- 日本語テキスト向けの最適化

**主要クラス:**
- `TextProcessor`: テキスト前処理を担当

**使用例:**
```python
processor = TextProcessor()
processed_text = processor.process_text("処理したいテキスト <html>タグ</html>")
```

詳細は [README_TEXT_PROCESSOR.md](./README_TEXT_PROCESSOR.md) を参照してください。

### 5. `reranker.py`

検索結果の再ランキングを行うモジュールです。検索精度を向上させます。

**主な機能:**
- 複数のランキング戦略（ベクトルスコア、キーワードマッチング、TF-IDF、ハイブリッド）
- カスタム重み付け
- 詳細なスコア情報の提供

**主要クラス:**
- `Reranker`: 検索結果の再ランキングを担当
- `RankingStrategy`: ランキング戦略を定義する列挙型

**使用例:**
```python
from backend.plugins.rag.reranker import Reranker, RankingStrategy

reranker = Reranker(strategy=RankingStrategy.HYBRID)
reranked_results = reranker.rerank("検索クエリ", results, top_k=5)
```

詳細は [README_RERANKER.md](./README_RERANKER.md) を参照してください。

### 6. `rag_service.py`

上記の5つのコンポーネントを統合し、完全なRAGサービスを提供します。

**主な機能:**
- ドキュメントの追加（ファイル、ディレクトリ、テキスト）
- 質問応答機能
- ドキュメントの検索と削除
- LLMとの連携
- 検索結果の再ランキング

**主要クラス:**
- `RAGService`: RAG機能全体を統合・管理

**使用例:**
```python
rag = RAGService(
    collection_name="my_collection",
    ranking_strategy=RankingStrategy.HYBRID,
    enable_reranking=True
)
rag.add_document("path/to/document.pdf")
answer = rag.query("質問文")
```

## 使用方法

RAGシステムは以下のような流れで使用します：

### 1. 初期化

RAGServiceを初期化します。再ランキング機能を有効にすることもできます。

```python
from backend.plugins.rag.rag_service import RAGService
from backend.plugins.rag.reranker import RankingStrategy

# 基本的な初期化
rag = RAGService(collection_name="my_documents")

# 再ランキング機能を有効にした初期化
rag_with_reranking = RAGService(
    collection_name="my_documents",
    ranking_strategy=RankingStrategy.HYBRID,
    enable_reranking=True
)

# ランキング戦略を後から変更
rag.set_ranking_strategy(RankingStrategy.KEYWORD_MATCH)

# ハイブリッドランキングの重みを調整
rag.set_ranking_weights(keyword_weight=0.4, tfidf_weight=0.3, vector_weight=0.3)
```

### 2. ドキュメントの追加

ファイル、ディレクトリ、またはテキストを追加します。

```python
# ファイルを追加
rag.add_document("path/to/file.pdf")

# ディレクトリを追加
rag.add_directory("path/to/directory", recursive=True)

# テキストを直接追加
rag.add_text("これは重要な情報です。")
```

### 3. 検索と質問応答

RAGシステムには2つの主要な検索メソッドがあります：

#### a. `query`メソッド - 関連ドキュメントの検索

関連ドキュメントを検索します。

```python
# 関連ドキュメントを検索
results = rag.query("AIソリューションについて教えてください", top_k=3)
for result in results:
    print(result["text"])
    print(f"スコア: {result['score']}")
    print(f"メタデータ: {result['metadata']}")
```

#### b. `run_rag`メソッド - 検索結果を使用してLLMで回答を生成

検索結果を使用してLLMで回答を生成します。

```python
# 検索結果を使用してLLMで回答を生成（完全なRAGプロセス）
rag_result = rag.run_rag("AIソリューションについて教えてください", top_k=3)
print(f"回答: {rag_result['result']}")
print(f"思考プロセス: {rag_result.get('think', '')}")
print(f"使用されたコンテキスト数: {len(rag_result.get('context', []))}")
```

#### `query`と`run_rag`の違い

| 機能 | `query` | `run_rag` |
|------|---------|-----------|
| 目的 | 関連ドキュメントの検索のみ | 検索結果を使用してLLMで回答を生成 |
| LLMの使用 | なし | あり |
| 戻り値 | 検索結果のリスト | 回答、思考プロセス、コンテキストを含む辞書 |
| 再ランキング | サポート | サポート |
| 用途 | 単純な検索機能 | 質問応答システム |

どちらのメソッドも再ランキング機能を利用でき、検索精度を向上させることができます。

### 5. ドキュメントの削除

不要になったドキュメントを削除します。

```python
rag.delete_documents({"source": "path/to/file.pdf"})
```

## 技術的な特徴

1. **モジュール設計**: 各コンポーネント（ドキュメントローダー、埋め込みモデル、ベクトルストア、テキスト処理、再ランキング）が分離されており、独立して使用可能
2. **拡張性**: 新しいファイル形式や埋め込みモデル、ランキング戦略を簡単に追加可能
3. **エラー処理**: 包括的なエラーハンドリングとロギング
4. **設定の柔軟性**: 環境変数やデフォルト値を通じて設定可能
5. **LangChainとの統合**: LangChainのドキュメント形式やローダーを活用
6. **テキスト前処理**: 検索精度を向上させるための一貫したテキスト処理
7. **高度な再ランキング**: 複数のランキング戦略（ベクトルスコア、キーワードマッチング、TF-IDF、ハイブリッド）による検索精度の向上
8. **カスタマイズ可能な重み付け**: ハイブリッドランキングの重みをカスタマイズ可能

## 依存関係

- Qdrant: ベクトルデータベース
- LangChain: ドキュメント処理とローダー
- 各種埋め込みモデル（Hugging Face, OpenAI）
- scikit-learn: TF-IDFベースの再ランキング
- NumPy: 数値計算

## 設定

主な設定は環境変数または設定ファイルを通じて行います：

- `QDRANT_HOST`: Qdrantサーバーのホスト（デフォルト: localhost）
- `QDRANT_PORT`: Qdrantサーバーのポート（デフォルト: 6333）
- `OPENAI_API_KEY`: OpenAI埋め込みモデルを使用する場合のAPIキー

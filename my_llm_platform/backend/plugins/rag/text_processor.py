import re
import unicodedata
import logging
import string
from typing import List, Optional, Dict, Any, Tuple

logger = logging.getLogger(__name__)

class TextProcessor:
    """
    テキスト前処理クラス
    ベクトル検索の精度を向上させるためのテキスト処理機能を提供します
    """
    # 言語コード定数
    LANG_JAPANESE = "ja"
    LANG_ENGLISH = "en"
    LANG_CHINESE = "zh"
    LANG_UNKNOWN = "unknown"

    # 日本語の文字範囲
    JAPANESE_RANGES = [
        (0x3040, 0x309F),  # ひらがな
        (0x30A0, 0x30FF),  # カタカナ
        (0x4E00, 0x9FFF),  # 漢字（CJK統合漢字）
        (0x3400, 0x4DBF),  # 漢字拡張A
        (0xFF00, 0xFFEF)   # 全角英数字
    ]

    # 中国語の文字範囲（簡体字・繁体字特有の文字）
    CHINESE_RANGES = [
        (0x4E00, 0x9FFF),  # 漢字（CJK統合漢字、日本語と共通）
        (0x3400, 0x4DBF),  # 漢字拡張A（日本語と共通）
        (0x20000, 0x2A6DF),  # 漢字拡張B
        (0x2A700, 0x2B73F),  # 漢字拡張C
        (0x2B740, 0x2B81F),  # 漢字拡張D
        (0x2B820, 0x2CEAF),  # 漢字拡張E
        (0xF900, 0xFAFF)   # CJK互換漢字
    ]

    def __init__(self,
                 normalize: bool = True,
                 remove_extra_whitespace: bool = True,
                 remove_urls: bool = True,
                 remove_email: bool = True,
                 remove_html_tags: bool = True,
                 lowercase: bool = None,  # 自動検出の場合はNone
                 remove_special_chars: bool = None,  # 自動検出の場合はNone
                 auto_detect_language: bool = True):
        """
        テキスト処理オプションを初期化します

        Args:
            normalize: Unicode正規化を行うかどうか
            remove_extra_whitespace: 余分な空白を削除するかどうか
            remove_urls: URLを削除するかどうか
            remove_email: メールアドレスを削除するかどうか
            remove_html_tags: HTMLタグを削除するかどうか
            lowercase: 小文字化するかどうか（Noneの場合は言語に基づいて自動設定）
            remove_special_chars: 特殊文字を削除するかどうか（Noneの場合は言語に基づいて自動設定）
            auto_detect_language: 言語を自動検出するかどうか
        """
        self.normalize = normalize
        self.remove_extra_whitespace = remove_extra_whitespace
        self.remove_urls = remove_urls
        self.remove_email = remove_email
        self.remove_html_tags = remove_html_tags
        self.lowercase = lowercase
        self.remove_special_chars = remove_special_chars
        self.auto_detect_language = auto_detect_language

        # 正規表現パターン
        self.url_pattern = re.compile(r'https?://\S+|www\.\S+')
        self.email_pattern = re.compile(r'\S+@\S+')
        self.html_pattern = re.compile(r'<.*?>')
        self.special_chars_pattern = re.compile(r'[^\w\s]')  # 英数字、アンダースコア、空白以外を削除
        self.whitespace_pattern = re.compile(r'\s+')

    def detect_language(self, text: str) -> str:
        """
        テキストの言語を検出します

        Args:
            text: 言語を検出するテキスト

        Returns:
            言語コード（"ja", "zh", "en", "unknown"）
        """
        if not text or not text.strip():
            return self.LANG_UNKNOWN

        # サンプルテキスト（最大1000文字）
        sample = text[:1000]

        # 文字カウント
        jp_chars = 0
        cn_chars = 0
        en_chars = 0

        # 日本語特有の文字（ひらがな・カタカナ）をカウント
        for char in sample:
            char_code = ord(char)

            # ひらがなとカタカナは日本語特有
            if (0x3040 <= char_code <= 0x309F) or (0x30A0 <= char_code <= 0x30FF):
                jp_chars += 1

            # 漢字は日本語と中国語で共通
            elif 0x4E00 <= char_code <= 0x9FFF:
                # 両方にカウント（後で比率で判断）
                jp_chars += 0.5
                cn_chars += 0.5

            # 中国語特有の文字範囲
            elif any(start <= char_code <= end for start, end in self.CHINESE_RANGES if not (0x4E00 <= char_code <= 0x9FFF) and not (0x3400 <= char_code <= 0x4DBF)):
                cn_chars += 1

            # 英数字（半角）
            elif char in string.ascii_letters or char in string.digits:
                en_chars += 1

        # 合計文字数（空白以外）
        total_chars = len([c for c in sample if not c.isspace()])
        if total_chars == 0:
            return self.LANG_UNKNOWN

        # 各言語の比率
        jp_ratio = jp_chars / total_chars
        cn_ratio = cn_chars / total_chars
        en_ratio = en_chars / total_chars

        # 閾値（調整可能）
        threshold = 0.2

        # 言語判定
        if jp_ratio > threshold and jp_ratio > cn_ratio:
            logger.debug(f"日本語テキストを検出しました（日本語比率: {jp_ratio:.2f}）")
            return self.LANG_JAPANESE
        elif cn_ratio > threshold and cn_ratio >= jp_ratio:
            logger.debug(f"中国語テキストを検出しました（中国語比率: {cn_ratio:.2f}）")
            return self.LANG_CHINESE
        elif en_ratio > threshold:
            logger.debug(f"英語テキストを検出しました（英語比率: {en_ratio:.2f}）")
            return self.LANG_ENGLISH
        else:
            logger.debug(f"言語を判別できませんでした（日本語: {jp_ratio:.2f}, 中国語: {cn_ratio:.2f}, 英語: {en_ratio:.2f}）")
            return self.LANG_UNKNOWN

    def get_language_specific_settings(self, text: str) -> Tuple[bool, bool]:
        """
        テキストの言語に基づいて適切な処理設定を取得します

        Args:
            text: 処理するテキスト

        Returns:
            Tuple[bool, bool]: (lowercase, remove_special_chars)の設定値
        """
        # 言語を検出
        lang = self.detect_language(text)

        # 言語に基づいて設定
        if lang in [self.LANG_JAPANESE, self.LANG_CHINESE]:
            # 日本語・中国語の場合
            return False, False
        else:
            # 英語・その他の言語の場合
            return True, True

    def process_text(self, text: str) -> str:
        """
        テキストを処理します

        Args:
            text: 処理するテキスト

        Returns:
            処理されたテキスト
        """
        if not text or not text.strip():
            return ""

        # 言語に基づいて設定を自動調整（auto_detect_languageが有効な場合）
        lowercase = self.lowercase
        remove_special_chars = self.remove_special_chars

        if self.auto_detect_language and (self.lowercase is None or self.remove_special_chars is None):
            auto_lowercase, auto_remove_special = self.get_language_specific_settings(text)

            # Noneの場合のみ自動設定を適用
            if self.lowercase is None:
                lowercase = auto_lowercase
            if self.remove_special_chars is None:
                remove_special_chars = auto_remove_special

        # Unicode正規化（NFC: 結合済み形式、NFD: 分解形式）
        if self.normalize:
            text = unicodedata.normalize('NFKC', text)

        # HTMLタグの削除
        if self.remove_html_tags:
            text = self.html_pattern.sub(' ', text)

        # URLの削除
        if self.remove_urls:
            text = self.url_pattern.sub(' ', text)

        # メールアドレスの削除
        if self.remove_email:
            text = self.email_pattern.sub(' ', text)

        # 小文字化（日本語・中国語テキストの場合は通常不要）
        if lowercase:
            text = text.lower()

        # 特殊文字の削除（日本語・中国語テキストの場合は通常不要）
        if remove_special_chars:
            text = self.special_chars_pattern.sub(' ', text)

        # 余分な空白の削除
        if self.remove_extra_whitespace:
            text = self.whitespace_pattern.sub(' ', text)
            text = text.strip()

        return text

    def process_documents(self, documents: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        ドキュメントのリストを処理します

        Args:
            documents: 処理するドキュメントのリスト

        Returns:
            処理されたドキュメントのリスト
        """
        processed_documents = []
        for doc in documents:
            if 'page_content' in doc and doc['page_content']:
                # ページコンテンツを処理
                processed_content = self.process_text(doc['page_content'])
                # 処理されたコンテンツで更新
                doc['page_content'] = processed_content
                processed_documents.append(doc)
            else:
                # ページコンテンツがない場合はそのまま追加
                processed_documents.append(doc)

        return processed_documents

    def process_langchain_documents(self, documents: List[Any]) -> List[Any]:
        """
        LangChainのドキュメントリストを処理します

        Args:
            documents: 処理するLangChainドキュメントのリスト

        Returns:
            処理されたLangChainドキュメントのリスト
        """
        for doc in documents:
            if hasattr(doc, 'page_content') and doc.page_content:
                # ページコンテンツを処理
                doc.page_content = self.process_text(doc.page_content)

        return documents

    def process_query(self, query: str) -> str:
        """
        検索クエリを処理します
        クエリ処理はドキュメント処理と同じ方法で行う必要があります

        Args:
            query: 処理するクエリ

        Returns:
            処理されたクエリ
        """
        return self.process_text(query)

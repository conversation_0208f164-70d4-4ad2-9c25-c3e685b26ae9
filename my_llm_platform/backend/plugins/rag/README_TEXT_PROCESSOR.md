# テキスト前処理モジュール

このモジュールは、RAG（検索拡張生成）システムの検索精度を向上させるためのテキスト前処理機能を提供します。

## 目的

ベクトル検索の精度を向上させるために、ドキュメントとクエリに一貫した前処理を適用します。これにより以下の効果が期待できます：

1. **ノイズの削減**: HTMLタグ、URL、余分な空白などの不要な要素を削除
2. **正規化**: テキストを一貫した形式に変換（Unicode正規化など）
3. **一貫性の確保**: ドキュメントとクエリに同じ処理を適用することで、ベクトル空間での類似性を向上
4. **言語適応処理**: テキストの言語に応じて最適な処理を自動適用

## 主な機能

`TextProcessor` クラスは以下の処理を提供します：

- **自動言語検出**: テキストの言語（日本語、中国語、英語など）を自動的に検出
- **言語適応処理**: 検出された言語に基づいて最適な処理パラメータを自動設定
- Unicode正規化（NFKC形式）
- 余分な空白の削除
- URLの削除
- メールアドレスの削除
- HTMLタグの削除
- 小文字化（英語テキストでは有効、日本語・中国語テキストでは自動的に無効）
- 特殊文字の削除（英語テキストでは有効、日本語・中国語テキストでは自動的に無効）

## 使用方法

### 自動言語検出を使用したテキスト処理

```python
from backend.plugins.rag.text_processor import TextProcessor

# 自動言語検出を使用するテキスト処理器を初期化
processor = TextProcessor(
    normalize=True,
    remove_extra_whitespace=True,
    remove_urls=True,
    remove_email=True,
    remove_html_tags=True,
    lowercase=None,  # 自動検出（言語に基づいて自動設定）
    remove_special_chars=None,  # 自動検出（言語に基づいて自動設定）
    auto_detect_language=True
)

# 日本語テキストを処理（小文字化と特殊文字削除は自動的に無効）
jp_text = "これは処理したい日本語テキストです <html>タグ</html> https://example.com"
processed_jp = processor.process_text(jp_text)
print(processed_jp)  # "これは処理したい日本語テキストです タグ"

# 英語テキストを処理（小文字化と特殊文字削除は自動的に有効）
en_text = "This is English Text with <html>tags</html> and https://example.com"
processed_en = processor.process_text(en_text)
print(processed_en)  # "this is english text with tags and"
```

### 言語を明示的に指定したテキスト処理

```python
from backend.plugins.rag.text_processor import TextProcessor

# 日本語テキスト向けの処理器を初期化
jp_processor = TextProcessor(
    normalize=True,
    remove_extra_whitespace=True,
    remove_urls=True,
    remove_email=True,
    remove_html_tags=True,
    lowercase=False,  # 日本語テキストの場合はFalse推奨
    remove_special_chars=False,  # 日本語テキストの場合はFalse推奨
    auto_detect_language=False  # 自動検出を無効化
)

# テキストを処理
processed_text = jp_processor.process_text("処理したいテキスト <html>タグ</html> https://example.com")
print(processed_text)  # "処理したいテキスト タグ"
```

### LangChainドキュメントの処理

```python
from langchain_core.documents import Document

# ドキュメントのリストを作成
documents = [
    Document(page_content="処理したいテキスト1 <html>タグ</html>", metadata={"source": "doc1"}),
    Document(page_content="処理したいテキスト2 https://example.com", metadata={"source": "doc2"})
]

# ドキュメントを処理
processed_docs = processor.process_langchain_documents(documents)
```

### RAGシステムでの使用

RAGシステムでは、ドキュメントの読み込み時とクエリ処理時の両方でテキスト処理を適用することが重要です：

1. **ドキュメント読み込み時**:
   ```python
   document_loader = DocumentLoader(enable_text_processing=True)
   documents = document_loader.load_document("path/to/file.pdf")
   ```

2. **クエリ処理時**:
   ```python
   processed_query = text_processor.process_text(query)
   results = vector_store.search(processed_query, top_k=5)
   ```

## 言語検出と言語固有の処理

テキスト処理器は以下の言語を自動検出できます：

- 日本語（`ja`）: ひらがな、カタカナ、漢字の存在から検出
- 中国語（`zh`）: 簡体字・繁体字特有の漢字から検出
- 英語（`en`）: 英数字の比率から検出
- 不明（`unknown`）: 上記のいずれにも該当しない場合

言語に応じて、以下の処理が自動的に調整されます：

| 言語 | 小文字化 | 特殊文字削除 |
|------|----------|--------------|
| 日本語 | 無効 | 無効 |
| 中国語 | 無効 | 無効 |
| 英語 | 有効 | 有効 |
| 不明 | 有効 | 有効 |

### 言語検出の仕組み

言語検出は以下の手順で行われます：

1. テキストから最大1000文字をサンプリング
2. 各文字の言語特性をカウント（ひらがな、カタカナ、漢字、英数字など）
3. 各言語の文字比率を計算
4. 閾値（デフォルト: 0.2）を超える言語を検出

### 自動検出を使用する場合の注意点

- 混合テキスト（日本語と英語が混在など）の場合、優勢な言語が検出されます
- 短いテキストや特殊文字のみのテキストは正確に検出できない場合があります
- 自動検出が不要な場合は、`auto_detect_language=False`を設定し、明示的に処理パラメータを指定してください

## カスタマイズ

必要に応じて、`TextProcessor` クラスを拡張して追加の処理機能を実装できます：

- 形態素解析による単語分割
- ストップワードの削除
- 同義語の置換
- 特定ドメイン向けの専門用語処理

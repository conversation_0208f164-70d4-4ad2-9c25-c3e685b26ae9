import os
import logging
from typing import List, Dict, Any, Optional
from langchain_community.document_loaders import (
    UnstructuredFileLoader,
    PyPDFLoader,
    TextLoader,
    CSVLoader,
    JSONLoader,
    UnstructuredExcelLoader,
    UnstructuredPowerPointLoader,
    UnstructuredWordDocumentLoader,
    UnstructuredHTMLLoader,
    UnstructuredMarkdownLoader,
)
from langchain_core.documents import Document
from langchain_text_splitters import RecursiveCharacterTextSplitter
from .text_processor import TextProcessor

logger = logging.getLogger(__name__)

# サポートされているファイル拡張子とローダーのマッピング
FILE_LOADER_MAPPING = {
    ".txt": TextLoader,
    ".pdf": PyPDFLoader,
    ".csv": CSVLoader,
    ".json": JSONLoader,
    ".xlsx": UnstructuredExcelLoader,
    ".xls": UnstructuredExcelLoader,
    ".pptx": UnstructuredPowerPointLoader,
    ".ppt": UnstructuredPowerPointLoader,
    ".docx": UnstructuredWordDocumentLoader,
    ".doc": UnstructuredWordDocumentLoader,
    ".html": UnstructuredHTMLLoader,
    ".htm": UnstructuredHTMLLoader,
    ".md": UnstructuredMarkdownLoader,
}

# デフォルトのチャンクサイズとオーバーラップ 単位は文字数（characters）
DEFAULT_CHUNK_SIZE = 1000
DEFAULT_CHUNK_OVERLAP = 200

class DocumentLoader:
    """
    複数のファイル形式をサポートするドキュメントローダー
    """
    # サポートされているファイル拡張子とローダーのマッピングをクラス変数として公開
    FILE_LOADER_MAPPING = FILE_LOADER_MAPPING

    def __init__(self, chunk_size: int = DEFAULT_CHUNK_SIZE, chunk_overlap: int = DEFAULT_CHUNK_OVERLAP,
                 enable_text_processing: bool = True):
        self.chunk_size = chunk_size
        self.chunk_overlap = chunk_overlap
        self.enable_text_processing = enable_text_processing

        # テキスト分割器を初期化
        self.text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=chunk_size,
            chunk_overlap=chunk_overlap,
            separators=["\n\n", "\n", "。", ".", "！", "!", "？", "?", "、", ",", " ", ""]
        )

        # テキスト処理器を初期化（自動言語検出を有効化）
        self.text_processor = TextProcessor(
            normalize=True,
            remove_extra_whitespace=True,
            remove_urls=True,
            remove_email=True,
            remove_html_tags=True,
            lowercase=None,  # 自動検出
            remove_special_chars=None,  # 自動検出
            auto_detect_language=True
        )

    def load_document(self, file_path: str, metadata: Optional[Dict[str, Any]] = None) -> List[Document]:
        """
        単一のファイルを読み込み、ドキュメントのリストを返します
        """
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"ファイルが見つかりません: {file_path}")

        # ファイル拡張子を取得
        _, file_extension = os.path.splitext(file_path.lower())

        # 適切なローダーを選択
        if file_extension in FILE_LOADER_MAPPING:
            loader_class = FILE_LOADER_MAPPING[file_extension]
            try:
                loader = loader_class(file_path)
                # ファイル名を先に取得
                file_name = os.path.basename(file_path)
                documents = loader.load()

                # メタデータを追加
                if metadata:
                    for doc in documents:
                        doc.metadata.update(metadata)
                else:
                    # 基本的なメタデータを追加
                    for doc in documents:
                        doc.metadata.update({
                            "source": file_path,
                            "file_name": file_name,
                            "file_type": file_extension[1:],  # 先頭の「.」を削除
                        })

                # テキスト処理を適用（有効な場合）
                if self.enable_text_processing:
                    documents = self.text_processor.process_langchain_documents(documents)
                    logger.info(f"ファイル {file_name} にテキスト前処理を適用しました")

                # ドキュメントを分割
                split_docs = self.text_splitter.split_documents(documents)
                logger.info(f"ファイル {file_name} を読み込みました: {len(documents)} ドキュメント -> {len(split_docs)} チャンク")
                return split_docs

            except Exception as e:
                logger.error(f"ファイル {file_path} の読み込み中にエラーが発生しました: {e}")
                raise
        else:
            # サポートされていない形式の場合、UnstructuredFileLoaderを試す
            try:
                loader = UnstructuredFileLoader(file_path)
                # ファイル名を先に取得
                file_name = os.path.basename(file_path)
                documents = loader.load()

                # メタデータを追加
                if metadata:
                    for doc in documents:
                        doc.metadata.update(metadata)
                else:
                    # 基本的なメタデータを追加
                    for doc in documents:
                        doc.metadata.update({
                            "source": file_path,
                            "file_name": file_name,
                            "file_type": file_extension[1:] if file_extension else "unknown",
                        })

                # テキスト処理を適用（有効な場合）
                if self.enable_text_processing:
                    documents = self.text_processor.process_langchain_documents(documents)
                    logger.info(f"ファイル {file_name} にテキスト前処理を適用しました")

                # ドキュメントを分割
                split_docs = self.text_splitter.split_documents(documents)
                logger.info(f"ファイル {file_name} を汎用ローダーで読み込みました: {len(documents)} ドキュメント -> {len(split_docs)} チャンク")
                return split_docs

            except Exception as e:
                logger.error(f"ファイル {file_path} の読み込み中にエラーが発生しました: {e}")
                raise ValueError(f"サポートされていないファイル形式です: {file_extension}")

    def load_documents_from_directory(self, directory_path: str, recursive: bool = True, metadata: Optional[Dict[str, Any]] = None) -> List[Document]:
        """
        ディレクトリ内のすべてのファイルを読み込み、ドキュメントのリストを返します
        """
        if not os.path.exists(directory_path):
            raise FileNotFoundError(f"ディレクトリが見つかりません: {directory_path}")

        if not os.path.isdir(directory_path):
            raise ValueError(f"{directory_path} はディレクトリではありません")

        all_documents = []

        # ディレクトリ内のファイルを処理
        for root, _, files in os.walk(directory_path):
            for file in files:
                file_path = os.path.join(root, file)
                _, file_extension = os.path.splitext(file.lower())

                # サポートされているファイル形式のみを処理
                if file_extension in FILE_LOADER_MAPPING or True:  # すべてのファイルを試す
                    try:
                        # ファイル固有のメタデータを作成
                        file_metadata = {}
                        if metadata:
                            file_metadata.update(metadata)

                        # 相対パスをメタデータに追加
                        rel_path = os.path.relpath(root, directory_path)
                        if rel_path != ".":
                            file_metadata["directory"] = rel_path

                        # ファイルを読み込む
                        documents = self.load_document(file_path, file_metadata)
                        all_documents.extend(documents)

                    except Exception as e:
                        logger.warning(f"ファイル {file_path} の処理中にエラーが発生しました: {e}")
                        continue

            # 再帰的に処理しない場合は最初のディレクトリのみ
            if not recursive:
                break

        logger.info(f"ディレクトリ {directory_path} から合計 {len(all_documents)} チャンクを読み込みました")
        return all_documents

# 後方互換性のための関数
def load_documents_from_file(file_path: str):
    """
    単一のファイルを読み込む（後方互換性のため）
    """
    loader = DocumentLoader()
    return loader.load_document(file_path)

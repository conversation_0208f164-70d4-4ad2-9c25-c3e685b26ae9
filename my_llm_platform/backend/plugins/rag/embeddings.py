import logging
from typing import List, Dict
from langchain_huggingface import HuggingFaceEmbeddings
from langchain_openai import OpenAIEmbeddings
from langchain_core.embeddings import Embeddings
from ...config import settings

logger = logging.getLogger(__name__)

# サポートされている埋め込みモデル
SUPPORTED_EMBEDDING_MODELS = {
    "hotchpotch-japanese":"hotchpotch/static-embedding-japanese",
    "instructor": "hkunlp/instructor-large",
    "instructor-xl": "hkunlp/instructor-xl",
    "instructor-base": "hkunlp/instructor-base",
    "mpnet": "sentence-transformers/all-mpnet-base-v2",
    "minilm": "sentence-transformers/all-MiniLM-L6-v2",
    "openai": "text-embedding-3-small",
    "openai-ada": "text-embedding-ada-002",
}

# デフォルトの埋め込みモデル
DEFAULT_EMBEDDING_MODEL = "hotchpotch-japanese"

class EmbeddingModel:
    """
    テキストをベクトルに変換するための埋め込みモデルクラス
    """
    def __init__(self, model_name: str = None):
        """
        埋め込みモデルを初期化します

        Args:
            model_name: 使用する埋め込みモデルの名前。Noneの場合はデフォルトモデルを使用。
        """
        self.model_name = model_name if model_name is not None else DEFAULT_EMBEDDING_MODEL
        self.model = self._initialize_model(self.model_name)

    def _initialize_model(self, model_name: str) -> Embeddings:
        """
        指定されたモデル名に基づいて埋め込みモデルを初期化します
        """
        # モデル名がサポートされているモデルのキーである場合、実際のモデル名を取得
        if model_name in SUPPORTED_EMBEDDING_MODELS:
            actual_model_name = SUPPORTED_EMBEDDING_MODELS[model_name]
        else:
            # キーでない場合、直接モデル名として使用
            actual_model_name = model_name

        # OpenAIの埋め込みモデルを使用する場合
        if model_name.startswith("openai"):
            try:
                return OpenAIEmbeddings(
                    model=actual_model_name,
                    openai_api_key=settings.OPENAI_API_KEY
                )
            except Exception as e:
                logger.error(f"OpenAI埋め込みモデルの初期化中にエラーが発生しました: {e}")
                logger.warning(f"フォールバックとしてデフォルトの埋め込みモデルを使用します")
                # フォールバックとしてデフォルトのモデルを使用
                return HuggingFaceEmbeddings(model_name=SUPPORTED_EMBEDDING_MODELS[DEFAULT_EMBEDDING_MODEL])

        # HuggingFaceの埋め込みモデルを使用する場合
        try:
            return HuggingFaceEmbeddings(model_name=actual_model_name)
        except Exception as e:
            logger.error(f"埋め込みモデル {actual_model_name} の初期化中にエラーが発生しました: {e}")
            logger.warning(f"デフォルトの埋め込みモデルを使用します")
            # デフォルトのモデルを使用
            return HuggingFaceEmbeddings(model_name=SUPPORTED_EMBEDDING_MODELS[DEFAULT_EMBEDDING_MODEL])

    def embed_text(self, text: str) -> List[float]:
        """
        単一のテキストを埋め込みベクトルに変換します
        """
        if not text or not text.strip():
            logger.warning("空のテキストが埋め込みのために渡されました")
            # 空のテキストの場合はゼロベクトルを返す
            return [0.0] * 768  # 一般的な埋め込みサイズ

        try:
            return self.model.embed_query(text)
        except Exception as e:
            logger.error(f"テキストの埋め込み中にエラーが発生しました: {e}")
            raise

    def embed_documents(self, docs: List[str]) -> List[List[float]]:
        """
        複数のドキュメントを埋め込みベクトルに変換します
        """
        if not docs:
            logger.warning("空のドキュメントリストが埋め込みのために渡されました")
            return []

        # 空のドキュメントをフィルタリング
        filtered_docs = [doc for doc in docs if doc and doc.strip()]
        if len(filtered_docs) != len(docs):
            logger.warning(f"{len(docs) - len(filtered_docs)} 個の空のドキュメントがフィルタリングされました")

        if not filtered_docs:
            return []

        try:
            # バッチ処理をサポートしている場合は使用する
            if hasattr(self.model, "embed_documents"):
                return self.model.embed_documents(filtered_docs)
            else:
                # バッチ処理をサポートしていない場合は個別に処理
                return [self.model.embed_query(d) for d in filtered_docs]
        except Exception as e:
            logger.error(f"ドキュメントの埋め込み中にエラーが発生しました: {e}")
            raise

    def embed_query(self, text: str) -> List[float]:
        """
        クエリテキストを埋め込みベクトルに変換します
        （embed_textのエイリアス）
        """
        return self.embed_text(text)

    def get_embedding_dimension(self) -> int:
        """
        埋め込みモデルの次元数を取得します
        """
        try:
            # テスト用の短いテキストを埋め込み
            test_text = "次元数を確認するためのテストテキスト"
            vector = self.embed_text(test_text)
            return len(vector)
        except Exception as e:
            logger.error(f"埋め込み次元数の取得中にエラーが発生しました: {e}")
            # デフォルトの次元数を返す
            return 768

    def get_dimension(self) -> int:
        """
        埋め込みモデルの次元数を取得します（get_embedding_dimensionのエイリアス）
        """
        return self.get_embedding_dimension()

    def get_vector_size(self) -> int:
        """
        埋め込みモデルの次元数を取得します（get_embedding_dimensionのエイリアス）
        """
        return self.get_embedding_dimension()

    @classmethod
    def list_available_models(cls) -> Dict[str, str]:
        """
        利用可能な埋め込みモデルのリストを返します
        """
        return SUPPORTED_EMBEDDING_MODELS

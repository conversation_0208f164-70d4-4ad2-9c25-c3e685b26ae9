"""
Supabase (pgvector) ベクトルストア実装
"""
import json
import logging
import uuid
from typing import Any, Dict, List, Optional, Union

import numpy as np
from langchain_core.documents import Document
from langchain_community.vectorstores import PGVector
from langchain_community.vectorstores.pgvector import DistanceStrategy

from ....config import settings
from ..embeddings import EmbeddingModel
from .base import BaseVectorStore, DEFAULT_COLLECTION_NAME, DEFAULT_VECTOR_SIZE

logger = logging.getLogger(__name__)

try:
    import vecs
    VECS_AVAILABLE = True
except ImportError:
    logger.warning("vecsライブラリがインストールされていません。Supabaseベクトルストアは利用できません。")
    logger.warning("pip install vecs でインストールしてください。")
    VECS_AVAILABLE = False


class SupabaseVectorStore(BaseVectorStore):
    """
    Supabase (pgvector) を使用したベクトルストア実装
    """
    def __init__(self, collection_name: str, embedding_model: EmbeddingModel):
        """
        Supabaseベクトルストアを初期化します

        Args:
            collection_name: コレクション名
            embedding_model: 埋め込みモデルのインスタンス
        """
        super().__init__(collection_name, embedding_model)
        self.client = None
        self.collection = None

        # 環境変数から設定を読み込む
        self.connection_string = settings.SUPABASE_CONNECTION_STRING

        # 接続を初期化
        self._initialize_connection()

    def _initialize_connection(self):
        """
        Supabaseへの接続を初期化します
        """
        if not VECS_AVAILABLE:
            logger.error("vecsライブラリがインストールされていないため、Supabaseに接続できません")
            return

        try:
            if not self.connection_string:
                logger.error("SUPABASE_CONNECTION_STRINGが設定されていません")
                return

            # vecsクライアントを作成
            self.client = vecs.create_client(self.connection_string)
            logger.info("Supabaseベクトルストアに正常に接続しました")

            # コレクションを取得または作成
            vector_size = self.embedding_model.get_vector_size()
            self.collection = self.client.get_or_create_collection(
                name=self.collection_name,
                dimension=vector_size
            )
            logger.info(f"コレクション '{self.collection_name}' を取得または作成しました")

        except Exception as e:
            logger.error(f"Supabaseベクトルストアへの接続中にエラーが発生しました: {e}")
            self.client = None
            self.collection = None

    @property
    def is_available(self) -> bool:
        """
        Supabaseが利用可能かどうかを返します

        Returns:
            bool: 利用可能な場合はTrue、そうでない場合はFalse
        """
        return VECS_AVAILABLE and self.client is not None and self.collection is not None

    @property
    def store_type(self) -> str:
        """
        ベクトルストアの種類を返します

        Returns:
            str: "supabase"
        """
        return "supabase"

    def get_client(self):
        """
        Supabaseクライアントを返します

        Returns:
            Any: Supabaseクライアントインスタンス
        """
        return self.client

    def create_collection(self, vector_size: int = None, recreate: bool = False) -> None:
        """
        コレクションを作成します

        Args:
            vector_size: ベクトルの次元数
            recreate: コレクションを再作成するかどうか
        """
        if not self.is_available:
            logger.error("Supabaseクライアントが利用できません")
            return

        try:
            # ベクトルサイズが指定されていない場合は埋め込みモデルから取得
            if vector_size is None:
                vector_size = self.embedding_model.get_vector_size()

            # 既存のコレクションを削除（再作成する場合）
            if recreate:
                try:
                    # コレクションを削除
                    self.client.delete_collection(self.collection_name)
                    logger.info(f"コレクション '{self.collection_name}' を削除しました")
                except Exception as e:
                    logger.warning(f"コレクションの削除中にエラーが発生しました: {e}")

            # コレクションを取得または作成
            self.collection = self.client.get_or_create_collection(
                name=self.collection_name,
                dimension=vector_size
            )
            logger.info(f"コレクション '{self.collection_name}' を作成しました")

        except Exception as e:
            logger.error(f"コレクションの作成中にエラーが発生しました: {e}")
            raise

    def add_texts(self, texts: List[str], metadatas: Optional[List[Dict[str, Any]]] = None) -> None:
        """
        テキストとメタデータをコレクションに追加します

        Args:
            texts: 追加するテキストのリスト
            metadatas: テキストに対応するメタデータのリスト
        """
        if not self.is_available:
            logger.error("Supabaseクライアントが利用できません")
            return

        if not texts:
            logger.warning("追加するテキストがありません")
            return

        # メタデータがない場合は空の辞書のリストを作成
        if metadatas is None:
            metadatas = [{} for _ in texts]

        if len(texts) != len(metadatas):
            logger.error("テキストとメタデータの数が一致しません")
            return

        try:
            # テキストを埋め込み
            vectors = self.embedding_model.embed_documents(texts)

            # レコードを準備
            records = []
            for text, vector, metadata in zip(texts, vectors, metadatas):
                # ユニークIDを生成
                record_id = str(uuid.uuid4())

                # メタデータにテキストを追加
                metadata["text"] = text
                metadata["page_content"] = text

                # レコードを追加
                records.append((record_id, vector, metadata))

            # バッチでドキュメントを追加
            self.collection.upsert(records=records)
            logger.info(f"{len(records)} 個のテキストがSupabaseに正常に追加されました")

        except Exception as e:
            logger.error(f"テキストの追加中にエラーが発生しました: {e}")
            raise

    def search(self, query: str, top_k: int = 5, filter_params: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        """
        クエリ文字列に基づいてドキュメントを検索します

        Args:
            query: 検索クエリ
            top_k: 返す結果の最大数
            filter_params: 検索フィルタ

        Returns:
            List[Dict[str, Any]]: 検索結果のリスト
        """
        if not self.is_available:
            logger.error("Supabaseクライアントが利用できません")
            return []

        try:
            # クエリを埋め込み
            query_vec = self.embedding_model.embed_text(query)

            # ベクトルで検索
            return self.search_by_vector(query_vec, top_k, filter_params)

        except Exception as e:
            logger.error(f"検索中にエラーが発生しました: {e}")
            return []

    def search_by_vector(self, query_vector: List[float], top_k: int = 5, filter_params: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        """
        クエリベクトルに基づいてドキュメントを検索します

        Args:
            query_vector: 検索ベクトル
            top_k: 返す結果の最大数
            filter_params: 検索フィルタ

        Returns:
            List[Dict[str, Any]]: 検索結果のリスト
        """
        if not self.is_available:
            logger.error("Supabaseクライアントが利用できません")
            return []

        try:
            # フィルタを準備
            filters = {}
            if filter_params:
                filters = filter_params

            # 検索を実行
            search_results = self.collection.query(
                data=query_vector,
                limit=top_k,
                filters=filters,
                include_metadata=True,
                include_value=True
            )

            # 結果を整形
            results = []

            for hit in search_results:
                try:
                    # hitの形式をログに出力
                    logger.debug(f"検索結果のhit形式: {type(hit)}, 値: {hit}")

                    # sqlalchemy.engine.row.Row オブジェクトの場合
                    if hasattr(hit, "_mapping"):
                        # Row オブジェクトを辞書に変換
                        hit_dict = dict(hit._mapping)

                        # 辞書の内容を確認
                        logger.debug(f"Row オブジェクトの内容: {hit_dict}")

                        # 必要な情報を抽出
                        if len(hit_dict) >= 3:
                            # 一般的なケース: (id, score, metadata)
                            result = {
                                "id": hit_dict.get("id", hit_dict.get(0, "")),
                                "score": hit_dict.get("anon_1", hit_dict.get(1, 0.0)),
                                "text": ""
                            }

                            # メタデータを取得
                            metadata = hit_dict.get("metadata", hit_dict.get(2, {}))
                            if isinstance(metadata, dict):
                                # テキストを取得
                                text = metadata.get("text", metadata.get("page_content", ""))
                                result["text"] = text
                                result["page_content"] = metadata.get("page_content", metadata.get("text", ""))

                                # その他のメタデータを追加
                                for key, value in metadata.items():
                                    if key not in ["text"]:
                                        result[key] = value
                        else:
                            # フォールバック: 利用可能なデータを使用
                            result = {
                                "id": str(uuid.uuid4()),
                                "text": str(hit),
                                "score": 0.0
                            }

                    # hitがタプルの場合（id, metadata, score）
                    elif isinstance(hit, tuple) and len(hit) >= 3:
                        result = {
                            "id": hit[0],
                            "text": hit[1].get("text", "") if isinstance(hit[1], dict) else "",
                            "score": hit[2],
                        }

                        # メタデータを追加
                        if isinstance(hit[1], dict):
                            for key, value in hit[1].items():
                                if key not in ["text"]:
                                    result[key] = value

                    # hitが辞書の場合
                    elif isinstance(hit, dict):
                        result = {
                            "id": hit.get("id", ""),
                            "text": hit.get("text", ""),
                            "score": hit.get("score", 0.0),
                        }

                        # その他のメタデータを追加
                        for key, value in hit.items():
                            if key not in ["id", "text", "score"]:
                                result[key] = value

                    # その他の形式の場合
                    else:
                        # 文字列に変換して保存
                        result = {
                            "id": str(uuid.uuid4()),
                            "text": str(hit),
                            "score": 0.0,
                        }

                    results.append(result)
                except Exception as e:
                    logger.error(f"検索結果の処理中にエラーが発生しました: {e}, hit: {hit}")
                    # エラーが発生しても処理を続行

            logger.info(f"{len(results)} 個の検索結果がSupabaseから見つかりました")
            return results

        except Exception as e:
            logger.error(f"検索中にエラーが発生しました: {e}")
            return []

    def delete_by_filter(self, filter_params: Dict[str, Any]) -> bool:
        """
        フィルタに一致するドキュメントを削除します

        Args:
            filter_params: 削除条件を指定するフィルタ

        Returns:
            bool: 削除に成功した場合はTrue、そうでない場合はFalse
        """
        if not self.is_available:
            logger.error("Supabaseクライアントが利用できません")
            return False

        try:
            # フィルタに基づいてドキュメントを削除
            deleted_ids = self.collection.delete(filters=filter_params)
            logger.info(f"{len(deleted_ids)} 個のドキュメントが削除されました")
            return True

        except Exception as e:
            logger.error(f"ドキュメントの削除中にエラーが発生しました: {e}")
            return False

    def get_collection_info(self) -> Dict[str, Any]:
        """
        コレクションの情報を取得します

        Returns:
            Dict[str, Any]: コレクション情報を含む辞書
        """
        if not self.is_available:
            logger.error("Supabaseクライアントが利用できません")
            return {"status": "unavailable"}

        try:
            # コレクション情報を返す
            # 注: vecsクライアントには直接コレクション情報を取得するメソッドがないため、
            # 基本的な情報のみを返します
            return {
                "name": self.collection_name,
                "type": "supabase",
                "status": "available"
            }

        except Exception as e:
            logger.error(f"コレクション情報の取得中にエラーが発生しました: {e}")
            return {"status": "error", "message": str(e)}

    def as_retriever(self, search_kwargs=None):
        """
        ベクトルストアをretrieverとして返します

        Args:
            search_kwargs: 検索パラメータ

        Returns:
            Any: retrieverインスタンス
        """
        if not self.is_available:
            logger.error("Supabaseクライアントが利用できません")
            raise RuntimeError("Supabaseクライアントが利用できません")

        try:
            # 独自のRetrieverを実装
            from langchain_core.retrievers import BaseRetriever
            from langchain_core.callbacks.manager import CallbackManagerForRetrieverRun
            from typing import List

            # カスタムRetrieverクラス
            class SupabaseRetriever(BaseRetriever):
                def __init__(self, store, search_kwargs=None):
                    super().__init__()
                    self._store = store
                    self._search_kwargs = search_kwargs or {}

                def _get_relevant_documents(
                    self, query: str, *, run_manager: CallbackManagerForRetrieverRun = None
                ) -> List[Document]:
                    # 検索パラメータを設定
                    top_k = self._search_kwargs.get("k", 5)
                    filter_params = self._search_kwargs.get("filter", None)

                    # 検索を実行
                    results = self._store.search(query, top_k=top_k, filter_params=filter_params)

                    # 結果をDocumentオブジェクトに変換
                    documents = []
                    for result in results:
                        try:
                            # テキストとメタデータを取得
                            text = result.get("text", "")

                            # メタデータを整理
                            metadata = {}
                            for k, v in result.items():
                                if k not in ["text", "score"]:
                                    # 文字列に変換可能な値のみを保持
                                    try:
                                        if isinstance(v, (str, int, float, bool)):
                                            metadata[k] = v
                                        elif v is None:
                                            metadata[k] = None
                                        else:
                                            metadata[k] = str(v)
                                    except:
                                        # 変換できない場合はスキップ
                                        pass

                            # page_contentがある場合はそれを使用
                            if "page_content" in result and result["page_content"]:
                                text = result["page_content"]

                            # Documentオブジェクトを作成
                            document = Document(page_content=text, metadata=metadata)
                            documents.append(document)
                        except Exception as e:
                            logger.error(f"ドキュメント変換中にエラーが発生しました: {e}, result: {result}")
                            # エラーが発生しても処理を続行

                    return documents

            # 検索パラメータを設定
            search_kwargs = search_kwargs or {}

            # retrieverを返す
            return SupabaseRetriever(self, search_kwargs=search_kwargs)

        except Exception as e:
            logger.error(f"Supabaseレトリーバーの作成中にエラーが発生しました: {e}")
            raise RuntimeError(f"Supabaseレトリーバーの作成に失敗しました: {e}")

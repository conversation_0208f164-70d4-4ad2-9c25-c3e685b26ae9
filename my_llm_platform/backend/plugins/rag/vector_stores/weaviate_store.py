import logging
import json
from typing import List, Dict, Any, Optional
import weaviate
from weaviate.embedded import EmbeddedOptions
from langchain_weaviate import WeaviateVectorStore as LangchainWeaviateVectorStore
from .base import BaseVectorStore, DEFAULT_VECTOR_SIZE
from ..embeddings import EmbeddingModel
from ....config import settings

logger = logging.getLogger(__name__)

class WeaviateVectorStore(BaseVectorStore):
    """
    Weaviateを使用したベクトルストア実装
    """
    def __init__(self, collection_name: str, embedding_model: EmbeddingModel):
        """
        Weaviateベクトルストアを初期化します

        Args:
            collection_name: コレクション名（Weaviateではクラス名として使用）
            embedding_model: 埋め込みモデルのインスタンス
        """
        super().__init__(collection_name, embedding_model)

        # Weaviateでは、コレクション名はクラス名として使用されます
        # クラス名は大文字で始まる必要があります
        self.class_name = collection_name.capitalize()
        self.available = False
        self.client = None

        try:
            # 環境変数から設定を読み込む
            # 古いWeaviate v3 APIを使用
            logger.info("Weaviate v3 APIを使用します。")
            if settings.WEAVIATE_URL:
                # URLが直接指定されている場合
                auth_config = weaviate.auth.AuthApiKey(api_key=settings.WEAVIATE_API_KEY) if settings.WEAVIATE_API_KEY else None
                self.client = weaviate.Client(
                    url=settings.WEAVIATE_URL,
                    auth_client_secret=auth_config
                )
            else:
                # ホストとポートから構築
                host = settings.WEAVIATE_HOST or "localhost"
                port = settings.WEAVIATE_PORT or 8087

                # Weaviate Embedded（ローカルモード）を使用
                self.client = weaviate.Client(
                    embedded_options=EmbeddedOptions(
                        hostname=host,
                        port=port
                    )
                )

            # 接続テスト
            self.client.schema.get()
            self.available = True
            logger.info(f"Weaviateに正常に接続しました: {settings.WEAVIATE_URL}")
        except Exception as e:
            logger.error(f"Weaviateへの接続に失敗しました: {e}")
            self.available = False

    @property
    def is_available(self) -> bool:
        """
        Weaviateが利用可能かどうかを返します

        Returns:
            bool: 利用可能な場合はTrue、そうでない場合はFalse
        """
        return self.available

    @property
    def store_type(self) -> str:
        """
        ベクトルストアの種類を返します

        Returns:
            str: "weaviate"
        """
        return "weaviate"

    def get_client(self):
        """
        Weaviateクライアントを返します

        Returns:
            weaviate.Client: Weaviateクライアントインスタンス
        """
        return self.client

    def create_collection(self, vector_size: int = None, recreate: bool = False) -> None:
        """
        Weaviateクラスを作成します
        """
        if not self.available or not self.client:
            logger.error("Weaviateクライアントが利用できません")
            raise RuntimeError("Weaviateクライアントが利用できません")

        try:
            # 埋め込みモデルから次元数を取得
            if vector_size is None:
                try:
                    vector_size = self.embedding_model.get_embedding_dimension()
                    logger.info(f"埋め込みモデルから次元数を取得しました: {vector_size}")
                except Exception as e:
                    logger.warning(f"埋め込みモデルから次元数を取得できませんでした: {e}")
                    vector_size = DEFAULT_VECTOR_SIZE
                    logger.info(f"デフォルトの次元数を使用します: {vector_size}")

            # クラスが存在するか確認
            schema = self.client.schema.get()
            classes = [cls["class"] for cls in schema.get("classes", [])]

            # 再作成フラグが立っていて、クラスが存在する場合は削除
            if recreate and self.class_name in classes:
                logger.info(f"既存のクラスを削除します: {self.class_name}")
                self.client.schema.delete_class(self.class_name)
                classes.remove(self.class_name)

            # クラスが存在しない場合は作成
            if self.class_name not in classes:
                logger.info(f"Weaviateクラスを作成します: {self.class_name} (次元数: {vector_size})")

                # クラス定義
                class_obj = {
                    "class": self.class_name,
                    "vectorizer": "none",  # 外部の埋め込みを使用
                    "vectorIndexType": "hnsw",  # HNSWインデックスを使用
                    "vectorIndexConfig": {
                        "distance": "cosine"  # コサイン類似度を使用
                    },
                    "properties": [
                        {
                            "name": "text",
                            "dataType": ["text"],
                            "description": "The text content"
                        },
                        {
                            "name": "page_content",
                            "dataType": ["text"],
                            "description": "The page content (same as text)"
                        },
                        {
                            "name": "metadata_json",
                            "dataType": ["text"],
                            "description": "Metadata associated with the text (JSON string)"
                        }
                    ]
                }

                # クラスを作成
                self.client.schema.create_class(class_obj)
                logger.info(f"Weaviateクラスが正常に作成されました: {self.class_name}")
            else:
                logger.info(f"Weaviateクラスは既に存在します: {self.class_name}")

        except Exception as e:
            logger.error(f"Weaviateクラスの作成中にエラーが発生しました: {e}")
            raise

    def add_texts(self, texts: List[str], metadatas: Optional[List[Dict[str, Any]]] = None) -> None:
        """
        テキストとメタデータをコレクションに追加します
        """
        if not self.available or not self.client:
            logger.error("Weaviateクライアントが利用できません")
            raise RuntimeError("Weaviateクライアントが利用できません")

        if not texts:
            logger.warning("追加するテキストがありません")
            return

        # メタデータがない場合は空の辞書のリストを作成
        if metadatas is None:
            metadatas = [{} for _ in texts]
        elif len(metadatas) != len(texts):
            logger.warning(f"テキストとメタデータの長さが一致しません: {len(texts)} vs {len(metadatas)}")
            # 長さを合わせる
            if len(metadatas) < len(texts):
                metadatas.extend([{} for _ in range(len(texts) - len(metadatas))])
            else:
                metadatas = metadatas[:len(texts)]

        try:
            # テキストを埋め込み
            vectors = self.embedding_model.embed_documents(texts)

            # バッチインポート用のデータを準備
            with self.client.batch as batch:
                for text, vector, metadata in zip(texts, vectors, metadatas):
                    # オブジェクトを作成
                    properties = {
                        "text": text,
                        "page_content": text,
                        "metadata_json": json.dumps(metadata)
                    }

                    # バッチにオブジェクトを追加
                    batch.add_data_object(
                        data_object=properties,
                        class_name=self.class_name,
                        vector=vector
                    )

            logger.info(f"{len(texts)} 個のテキストがWeaviateに正常に追加されました")
        except Exception as e:
            logger.error(f"テキストの追加中にエラーが発生しました: {e}")
            raise

    def search(self, query: str, top_k: int = 5, filter_params: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        """
        クエリ文字列に基づいてドキュメントを検索します
        """
        if not self.available or not self.client:
            logger.error("Weaviateクライアントが利用できません")
            return []

        try:
            # クエリが空の場合は、ランダムなドキュメントを取得
            if not query or not query.strip():
                # メタデータフィルタリングがある場合
                if filter_params and "file_name" in filter_params:
                    file_name = filter_params["file_name"]
                    logger.info(f"ファイル名 {file_name} でフィルタリングします")

                    # GraphQLクエリを構築（ファイル名でフィルタリング）
                    graphql_query = """
                    {
                      Get {
                        %s(
                          limit: %d
                          where: {
                            path: ["metadata_json"],
                            operator: Like,
                            valueString: "*\\"file_name\\":\\"%s\\"*"
                          }
                        ) {
                          text
                          metadata_json
                        }
                      }
                    }
                    """ % (self.class_name, top_k, file_name)

                    # GraphQLクエリを実行
                    try:
                        result = self.client.query.raw(graphql_query)

                        # 結果を処理
                        if result and "data" in result and "Get" in result["data"] and self.class_name in result["data"]["Get"]:
                            items = result["data"]["Get"][self.class_name]

                            # 結果をフォーマット
                            formatted_results = []
                            for item in items:
                                # メタデータをJSONから復元
                                metadata = {}
                                if "metadata_json" in item and item["metadata_json"]:
                                    try:
                                        metadata = json.loads(item["metadata_json"])
                                    except Exception as json_error:
                                        logger.warning(f"メタデータのJSONパースに失敗しました: {json_error}")

                                formatted_results.append({
                                    "text": item.get("text", ""),
                                    "metadata": metadata
                                })

                            return formatted_results
                    except Exception as e:
                        logger.error(f"ファイル名フィルタリング検索中にエラーが発生しました: {e}")

                # 通常のクエリ（ランダムなドキュメントを取得）
                try:
                    # 通常のクエリを実行
                    result = (
                        self.client.query
                        .get(self.class_name, ["text", "metadata_json"])
                        .with_limit(top_k)
                        .do()
                    )

                    # 結果を整形
                    search_results = []
                    if result and "data" in result and "Get" in result["data"] and self.class_name in result["data"]["Get"]:
                        items = result["data"]["Get"][self.class_name]
                        for item in items:
                            # metadata_jsonをJSONとしてパース
                            metadata = {}
                            try:
                                metadata_json = item.get("metadata_json", "{}")
                                if metadata_json:
                                    metadata = json.loads(metadata_json)
                            except Exception as e:
                                logger.warning(f"メタデータのパースに失敗しました: {e}")

                            search_results.append({
                                "text": item.get("text", ""),
                                "metadata": metadata
                            })

                    return search_results
                except Exception as e:
                    logger.error(f"ランダム検索中にエラーが発生しました: {e}")
                    return []

            # クエリを埋め込み
            query_vector = self.embedding_model.embed_query(query)

            # 検索結果を取得
            try:
                return self.search_by_vector(query_vector, top_k, filter_params)
            except Exception as e:
                logger.error(f"ベクトル検索中にエラーが発生しました: {e}")
                return []
        except Exception as e:
            logger.error(f"検索中にエラーが発生しました: {e}")
            return []

    def search_by_vector(self, query_vector: List[float], top_k: int = 5, filter_params: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        """
        クエリベクトルに基づいてドキュメントを検索します
        """
        if not self.available or not self.client:
            logger.error("Weaviateクライアントが利用できません")
            return []

        try:
            # フィルタを作成
            # 注意: metadata_jsonはJSON文字列なので、テキスト検索を使用する必要があります
            # 現在の実装では、フィルタリングはサポートされていません
            # 将来的には、JSONパスクエリを使用してフィルタリングを実装することができます
            where_filter = None
            if filter_params:
                logger.warning("Weaviateでのメタデータフィルタリングは現在サポートされていません")

            # 検索クエリを実行
            result = (
                self.client.query
                .get(self.class_name, ["text", "metadata_json"])
                .with_near_vector({"vector": query_vector})
                .with_limit(top_k)
            )

            # フィルタがある場合は追加
            if where_filter:
                result = result.with_where(where_filter)

            # クエリを実行
            response = result.do()

            # 結果を整形
            search_results = []
            if response and "data" in response and "Get" in response["data"] and self.class_name in response["data"]["Get"]:
                items = response["data"]["Get"][self.class_name]
                for item in items:
                    # metadata_jsonをJSONとしてパース
                    metadata = {}
                    try:
                        metadata_json = item.get("metadata_json", "{}")
                        if metadata_json:
                            metadata = json.loads(metadata_json)
                    except Exception as e:
                        logger.warning(f"メタデータのパースに失敗しました: {e}")

                    search_results.append({
                        "text": item.get("text", ""),
                        "metadata": metadata,
                        "score": item.get("_additional", {}).get("distance", 0)
                    })

            return search_results
        except Exception as e:
            logger.error(f"ベクトル検索中にエラーが発生しました: {e}")
            return []

    def as_retriever(self, search_kwargs=None):
        """
        ベクトルストアをretrieverとして返します
        """
        if not self.available or not self.client:
            logger.error("Weaviateクライアントが利用できません")
            raise RuntimeError("Weaviateクライアントが利用できません")

        try:
            # 最新のlangchain-weaviate APIを使用
            weaviate_instance = LangchainWeaviateVectorStore(
                client=self.client,
                index_name=self.class_name,
                text_key="text",
                embedding=self.embedding_model.model
            )

            # 検索パラメータを設定
            search_kwargs = search_kwargs or {}

            # retrieverを返す
            return weaviate_instance.as_retriever(search_kwargs=search_kwargs)
        except Exception as e:
            logger.error(f"Weaviateレトリーバーの作成中にエラーが発生しました: {e}")

            # カスタムRetrieverを実装して代替手段を提供
            from langchain.schema.retriever import BaseRetriever
            from langchain.schema import Document
            from pydantic import Field

            class CustomWeaviateRetriever(BaseRetriever):
                weaviate_store: Any = Field(description="The vector store to use for retrieval")
                search_kwargs: Dict[str, Any] = Field(default_factory=dict, description="Search parameters")

                def _get_relevant_documents(self, query):
                    # 検索パラメータを取得
                    top_k = self.search_kwargs.get("k", 4)

                    # ベクトルストアで検索を実行
                    results = self.weaviate_store.search(query, top_k=top_k)

                    # 結果をDocumentオブジェクトに変換
                    documents = []
                    for result in results:
                        doc = Document(
                            page_content=result["text"],
                            metadata=result.get("metadata", {})
                        )
                        documents.append(doc)

                    return documents

            logger.info("カスタムRetrieverを使用します")
            return CustomWeaviateRetriever(weaviate_store=self, search_kwargs=search_kwargs or {})

    def delete_by_filter(self, filter_params: Dict[str, Any]) -> bool:
        """
        フィルタに一致するドキュメントを削除します

        Args:
            filter_params: 削除するドキュメントのフィルタ条件

        Returns:
            bool: 削除に成功した場合はTrue、そうでない場合はFalse
        """
        if not self.available or not self.client:
            logger.error("Weaviateクライアントが利用できません")
            return False

        try:
            # ファイル名でフィルタリングする場合
            if "file_name" in filter_params:
                file_name = filter_params["file_name"]
                logger.info(f"ファイル名 {file_name} でフィルタリングして削除します")

                # GraphQLクエリを構築（ファイル名でフィルタリング）
                where_clause = {
                    "path": ["metadata_json"],
                    "operator": "Like",
                    "valueString": f"*\"file_name\":\"{file_name}\"*"
                }

                # 削除を実行
                try:
                    self.client.batch.delete_objects(
                        class_name=self.class_name,
                        where=where_clause
                    )
                    logger.info(f"ファイル {file_name} のドキュメントを削除しました")
                    return True
                except Exception as e:
                    logger.error(f"ドキュメントの削除中にエラーが発生しました: {e}")
                    return False
            else:
                logger.warning(f"サポートされていないフィルタパラメータです: {filter_params}")
                return False
        except Exception as e:
            logger.error(f"ドキュメントの削除中にエラーが発生しました: {e}")
            return False

    def get_collection_info(self) -> Dict[str, Any]:
        """
        コレクション（クラス）の情報を取得します
        """
        if not self.available or not self.client:
            logger.error("Weaviateクライアントが利用できません")
            return {
                "name": self.class_name,
                "error": "Weaviateクライアントが利用できません",
                "status": "error",
                "points_count": 0
            }

        try:
            # クラスの情報を取得
            schema = self.client.schema.get()
            classes = [cls for cls in schema.get("classes", []) if cls["class"] == self.class_name]

            if not classes:
                return {
                    "name": self.class_name,
                    "exists": False,
                    "status": "not_found",
                    "points_count": 0
                }

            # ベクトルの次元数を取得
            vector_size = self.embedding_model.get_dimension()

            # オブジェクト数を取得
            try:
                count_result = (
                    self.client.query
                    .aggregate(self.class_name)
                    .with_meta_count()
                    .do()
                )

                count = 0
                if (count_result and "data" in count_result and
                    "Aggregate" in count_result["data"] and
                    self.class_name in count_result["data"]["Aggregate"]):
                    count = count_result["data"]["Aggregate"][self.class_name][0]["meta"]["count"]
            except Exception as count_error:
                logger.warning(f"オブジェクト数の取得中にエラーが発生しました: {count_error}")
                # 別の方法でオブジェクト数を取得
                try:
                    # 空のクエリで検索して結果の数を数える
                    results = self.search("", top_k=10000)
                    count = len(results)
                except Exception:
                    count = 0

            return {
                "name": self.class_name,
                "vector_size": vector_size,
                "distance": "cosine",  # Weaviateはデフォルトでcosine類似度を使用
                "points_count": count,
                "status": "green" if self.available else "yellow",
                "exists": True
            }
        except Exception as e:
            logger.error(f"コレクション情報の取得中にエラーが発生しました: {e}")
            return {
                "name": self.class_name,
                "error": str(e),
                "status": "error",
                "points_count": 0
            }

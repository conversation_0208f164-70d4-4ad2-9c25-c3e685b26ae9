import abc
import logging
from typing import List, Dict, Any, Optional
from langchain_core.documents import Document
from ..embeddings import EmbeddingModel

logger = logging.getLogger(__name__)

# デフォルトのコレクション名
DEFAULT_COLLECTION_NAME = "documents"

# デフォルトのベクトルサイズ
DEFAULT_VECTOR_SIZE = 768

class BaseVectorStore(abc.ABC):
    """
    ベクトルストアの抽象基底クラス
    すべてのベクトルストア実装はこのクラスを継承する必要があります
    """
    def __init__(self, collection_name: str, embedding_model: EmbeddingModel):
        """
        ベクトルストアの基底クラスを初期化します

        Args:
            collection_name: コレクション名
            embedding_model: 埋め込みモデルのインスタンス
        """
        self.collection_name = collection_name
        self.embedding_model = embedding_model

    @property
    @abc.abstractmethod
    def is_available(self) -> bool:
        """
        ベクトルストアが利用可能かどうかを返します

        Returns:
            bool: 利用可能な場合はTrue、そうでない場合はFalse
        """
        pass

    @property
    @abc.abstractmethod
    def store_type(self) -> str:
        """
        ベクトルストアの種類を返します

        Returns:
            str: ベクトルストアの種類（"faiss", "qdrant", "weaviate"など）
        """
        pass

    @abc.abstractmethod
    def get_client(self):
        """
        ベクトルストアの内部クライアントを返します

        Returns:
            Any: ベクトルストアのクライアントインスタンス
        """
        pass

    @abc.abstractmethod
    def create_collection(self, vector_size: int = None, recreate: bool = False) -> None:
        """
        コレクションを作成します

        Args:
            vector_size: ベクトルの次元数
            recreate: コレクションを再作成するかどうか
        """
        pass

    @abc.abstractmethod
    def add_texts(self, texts: List[str], metadatas: Optional[List[Dict[str, Any]]] = None) -> None:
        """
        テキストとメタデータをコレクションに追加します

        Args:
            texts: 追加するテキストのリスト
            metadatas: テキストに対応するメタデータのリスト
        """
        pass

    @abc.abstractmethod
    def search(self, query: str, top_k: int = 5, filter_params: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        """
        クエリ文字列に基づいてドキュメントを検索します

        Args:
            query: 検索クエリ
            top_k: 返す結果の最大数
            filter_params: 検索フィルタ

        Returns:
            List[Dict[str, Any]]: 検索結果のリスト
        """
        pass

    @abc.abstractmethod
    def search_by_vector(self, query_vector: List[float], top_k: int = 5, filter_params: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        """
        クエリベクトルに基づいてドキュメントを検索します

        Args:
            query_vector: 検索ベクトル
            top_k: 返す結果の最大数
            filter_params: 検索フィルタ

        Returns:
            List[Dict[str, Any]]: 検索結果のリスト
        """
        pass

    @abc.abstractmethod
    def delete_by_filter(self, filter_params: Dict[str, Any]) -> bool:
        """
        フィルタに一致するドキュメントを削除します

        Args:
            filter_params: 削除条件を指定するフィルタ

        Returns:
            bool: 削除に成功した場合はTrue、そうでない場合はFalse
        """
        pass

    @abc.abstractmethod
    def get_collection_info(self) -> Dict[str, Any]:
        """
        コレクションの情報を取得します

        Returns:
            Dict[str, Any]: コレクション情報を含む辞書
        """
        pass

    @abc.abstractmethod
    def as_retriever(self, search_kwargs=None):
        """
        ベクトルストアをretrieverとして返します

        Args:
            search_kwargs: 検索パラメータ

        Returns:
            Any: retrieverインスタンス
        """
        pass

    def add_documents(self, documents: List[Document]) -> None:
        """
        LangchainのDocumentオブジェクトをコレクションに追加します
        """
        if not documents:
            logger.warning("追加するドキュメントがありません")
            return

        # ドキュメントからテキストとメタデータを抽出
        texts = [doc.page_content for doc in documents]
        metadatas = [doc.metadata for doc in documents]

        # テキストを追加
        self.add_texts(texts, metadatas)

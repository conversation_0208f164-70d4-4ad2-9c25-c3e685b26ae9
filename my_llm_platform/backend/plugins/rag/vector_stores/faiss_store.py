import logging
import uuid
from typing import List, Dict, Any, Optional
import numpy as np
from langchain_community.vectorstores import FAISS
from .base import BaseVectorStore, DEFAULT_VECTOR_SIZE
from ..embeddings import EmbeddingModel

logger = logging.getLogger(__name__)

class FAISSVectorStore(BaseVectorStore):
    """
    FAISSを使用したベクトルストア実装
    """
    def __init__(self, collection_name: str, embedding_model: EmbeddingModel):
        """
        FAISSベクトルストアを初期化します

        Args:
            collection_name: コレクション名
            embedding_model: 埋め込みモデルのインスタンス
        """
        super().__init__(collection_name, embedding_model)
        self.faiss_index = None
        self.faiss_docstore = {}
        logger.info("FAISSベクトルストアを使用します（インメモリ）")

    @property
    def is_available(self) -> bool:
        """
        FAISSが利用可能かどうかを返します

        Returns:
            bool: 常にTrue（FAISSはインメモリで動作するため）
        """
        return True

    @property
    def store_type(self) -> str:
        """
        ベクトルストアの種類を返します

        Returns:
            str: "faiss"
        """
        return "faiss"

    def get_client(self):
        """
        FAISSインデックスを返します

        Returns:
            Any: FAISSインデックス
        """
        return self.faiss_index

    def create_collection(self, vector_size: int = None, recreate: bool = False) -> None:
        """
        FAISSインデックスを作成します
        """
        try:
            # 埋め込みモデルから次元数を取得
            if vector_size is None:
                try:
                    vector_size = self.embedding_model.get_embedding_dimension()
                    logger.info(f"埋め込みモデルから次元数を取得しました: {vector_size}")
                except Exception as e:
                    logger.warning(f"埋め込みモデルから次元数を取得できませんでした: {e}")
                    vector_size = DEFAULT_VECTOR_SIZE
                    logger.info(f"デフォルトの次元数を使用します: {vector_size}")

            if recreate or self.faiss_index is None:
                logger.info(f"FAISSインデックスを作成します (次元数: {vector_size})")
                import faiss
                self.faiss_index = faiss.IndexFlatL2(vector_size)  # L2距離を使用
                self.faiss_docstore = {}  # ドキュメントストアをリセット
                logger.info("FAISSインデックスが正常に作成されました")
        except Exception as e:
            logger.error(f"FAISSインデックスの作成中にエラーが発生しました: {e}")
            raise

    def add_texts(self, texts: List[str], metadatas: Optional[List[Dict[str, Any]]] = None) -> None:
        """
        テキストとメタデータをFAISSインデックスに追加します
        """
        if not texts:
            logger.warning("追加するテキストがありません")
            return

        # メタデータがない場合は空の辞書のリストを作成
        if metadatas is None:
            metadatas = [{} for _ in texts]

        try:
            # テキストを埋め込み
            vectors = self.embedding_model.embed_documents(texts)

            # FAISSインデックスが初期化されていない場合は初期化
            if self.faiss_index is None:
                self.create_collection()

            # ドキュメントを追加
            for text, vector, metadata in zip(texts, vectors, metadatas):
                # ユニークIDを生成
                doc_id = str(uuid.uuid4())

                # ドキュメントストアに保存
                self.faiss_docstore[doc_id] = {
                    "text": text,
                    "metadata": metadata
                }

                # インデックスに追加
                self.faiss_index.add(np.array([vector], dtype=np.float32))

            logger.info(f"{len(texts)} 個のテキストがFAISSインデックスに正常に追加されました")
        except Exception as e:
            logger.error(f"FAISSへのテキスト追加中にエラーが発生しました: {e}")
            raise

    def search(self, query: str, top_k: int = 5, filter_params: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        """
        クエリ文字列に基づいてドキュメントを検索します
        """
        try:
            # クエリを埋め込み
            query_vec = self.embedding_model.embed_text(query)

            # FAISSインデックスが初期化されていない場合
            if self.faiss_index is None or len(self.faiss_docstore) == 0:
                logger.warning("FAISSインデックスが空です")
                return []

            # クエリベクトルをnumpy配列に変換
            query_np = np.array([query_vec], dtype=np.float32)

            # 検索を実行
            distances, indices = self.faiss_index.search(query_np, min(top_k, self.faiss_index.ntotal))

            # 結果を整形
            results = []
            for dist, idx in zip(distances[0], indices[0]):
                if idx == -1:  # FAISSの無効なインデックス
                    continue

                # ドキュメントIDを取得
                doc_id = list(self.faiss_docstore.keys())[idx]
                doc_data = self.faiss_docstore[doc_id]

                # スコアを計算（距離を類似度に変換）
                score = 1.0 / (1.0 + dist)

                result = {
                    "text": doc_data["text"],
                    "score": float(score),
                    "id": doc_id
                }

                # メタデータを追加
                if "metadata" in doc_data and doc_data["metadata"]:
                    for key, value in doc_data["metadata"].items():
                        result[key] = value

                # フィルタリング
                if filter_params:
                    match = True
                    for key, value in filter_params.items():
                        if key in result:
                            if isinstance(value, list):
                                if result[key] not in value:
                                    match = False
                                    break
                            elif result[key] != value:
                                match = False
                                break
                    if not match:
                        continue

                results.append(result)

            logger.info(f"{len(results)} 個の検索結果がFAISSから見つかりました")
            return results
        except Exception as e:
            logger.error(f"FAISS検索中にエラーが発生しました: {e}")
            raise

    def delete_by_filter(self, filter_params: Dict[str, Any]) -> bool:
        """
        フィルタに一致するドキュメントを削除します

        Args:
            filter_params: 削除するドキュメントのフィルタ条件

        Returns:
            bool: 削除に成功した場合はTrue、そうでない場合はFalse
        """
        try:
            # FAISSインデックスが初期化されていない場合
            if self.faiss_index is None or len(self.faiss_docstore) == 0:
                logger.warning("FAISSインデックスが空です")
                return False

            # 削除対象のドキュメントIDを特定
            delete_ids = []
            for doc_id, doc_data in self.faiss_docstore.items():
                match = True
                for key, value in filter_params.items():
                    if key in doc_data.get("metadata", {}):
                        if isinstance(value, list):
                            if doc_data["metadata"][key] not in value:
                                match = False
                                break
                        elif doc_data["metadata"][key] != value:
                            match = False
                            break
                    else:
                        match = False
                        break

                if match:
                    delete_ids.append(doc_id)

            if not delete_ids:
                logger.warning(f"フィルタ {filter_params} に一致するドキュメントが見つかりませんでした")
                return False

            # 新しいインデックスとドキュメントストアを作成
            import faiss
            vector_size = self.faiss_index.d
            new_index = faiss.IndexFlatL2(vector_size)
            new_docstore = {}

            # 削除対象以外のドキュメントを新しいインデックスに追加
            for i, (doc_id, doc_data) in enumerate(self.faiss_docstore.items()):
                if doc_id not in delete_ids:
                    # ドキュメントを保持
                    new_docstore[doc_id] = doc_data

                    # ベクトルを取得して新しいインデックスに追加
                    vector = np.array([self.faiss_index.reconstruct(i)], dtype=np.float32)
                    new_index.add(vector)

            # インデックスとドキュメントストアを更新
            self.faiss_index = new_index
            self.faiss_docstore = new_docstore

            logger.info(f"{len(delete_ids)} 個のドキュメントが削除されました")
            return True
        except Exception as e:
            logger.error(f"ドキュメントの削除中にエラーが発生しました: {e}")
            return False

    def get_collection_info(self) -> Dict[str, Any]:
        """
        コレクションの情報を取得します

        Returns:
            Dict[str, Any]: コレクション情報を含む辞書
        """
        try:
            if self.faiss_index is not None:
                return {
                    "name": self.collection_name,
                    "vector_size": self.faiss_index.d,  # 次元数
                    "distance": "L2",
                    "points_count": self.faiss_index.ntotal,
                    "status": "green",
                    "type": self.store_type
                }
            else:
                return {
                    "name": self.collection_name,
                    "vector_size": 0,
                    "distance": "L2",
                    "points_count": 0,
                    "status": "not_initialized",
                    "type": self.store_type
                }
        except Exception as e:
            logger.error(f"コレクション情報の取得中にエラーが発生しました: {e}")
            return {
                "name": self.collection_name,
                "status": "error",
                "error": str(e),
                "type": self.store_type
            }

    def search_by_vector(self, query_vector: List[float], top_k: int = 5, filter_params: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        """
        クエリベクトルに基づいてドキュメントを検索します

        Args:
            query_vector: 検索ベクトル
            top_k: 返す結果の最大数
            filter_params: 検索フィルタ

        Returns:
            List[Dict[str, Any]]: 検索結果のリスト
        """
        try:
            # FAISSインデックスが初期化されていない場合
            if self.faiss_index is None or len(self.faiss_docstore) == 0:
                logger.warning("FAISSインデックスが空です")
                return []

            # クエリベクトルをnumpy配列に変換
            query_np = np.array([query_vector], dtype=np.float32)

            # 検索を実行
            distances, indices = self.faiss_index.search(query_np, min(top_k, self.faiss_index.ntotal))

            # 結果を整形
            results = []
            for dist, idx in zip(distances[0], indices[0]):
                if idx == -1:  # FAISSの無効なインデックス
                    continue

                # ドキュメントIDを取得
                doc_id = list(self.faiss_docstore.keys())[idx]
                doc_data = self.faiss_docstore[doc_id]

                # スコアを計算（距離を類似度に変換）
                score = 1.0 / (1.0 + dist)

                result = {
                    "text": doc_data["text"],
                    "score": float(score),
                    "id": doc_id
                }

                # メタデータを追加
                if "metadata" in doc_data and doc_data["metadata"]:
                    for key, value in doc_data["metadata"].items():
                        result[key] = value

                # フィルタリング
                if filter_params:
                    match = True
                    for key, value in filter_params.items():
                        if key in result:
                            if isinstance(value, list):
                                if result[key] not in value:
                                    match = False
                                    break
                            elif result[key] != value:
                                match = False
                                break
                    if not match:
                        continue

                results.append(result)

            logger.info(f"{len(results)} 個の検索結果がFAISSから見つかりました")
            return results
        except Exception as e:
            logger.error(f"FAISS検索中にエラーが発生しました: {e}")
            raise

    def as_retriever(self, search_kwargs=None):
        """
        ベクトルストアをretrieverとして返します

        Args:
            search_kwargs: 検索パラメータ

        Returns:
            Any: retrieverインスタンス
        """
        try:
            # FAISSインデックスが初期化されていない場合
            if self.faiss_index is None:
                logger.warning("FAISSインデックスが初期化されていません")
                self.create_collection()

            # ドキュメントを準備
            texts = []
            metadatas = []
            for _, doc_data in self.faiss_docstore.items():
                texts.append(doc_data["text"])
                metadatas.append(doc_data.get("metadata", {}))

            if not texts:
                logger.warning("FAISSインデックスが空です")
                # 空のFAISSインデックスを作成
                faiss_instance = FAISS.from_texts(
                    ["空のインデックス"],  # ダミーテキスト
                    self.embedding_model.model
                )
            else:
                # 既存のデータからFAISSインスタンスを作成
                faiss_instance = FAISS.from_texts(
                    texts,
                    self.embedding_model.model,
                    metadatas=metadatas
                )

            # retrieverを返す
            return faiss_instance.as_retriever(search_kwargs=search_kwargs)
        except Exception as e:
            logger.error(f"FAISSレトリーバーの作成中にエラーが発生しました: {e}")
            raise

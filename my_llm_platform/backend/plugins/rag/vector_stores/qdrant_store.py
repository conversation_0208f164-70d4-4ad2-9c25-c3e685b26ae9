import logging
import uuid
from typing import List, Dict, Any, Optional
from qdrant_client import QdrantClient
from qdrant_client.http import models as qmodels
from qdrant_client.http.models import Filter, FieldCondition, MatchValue
from langchain_qdrant import Qdrant
from .base import BaseVectorStore, DEFAULT_VECTOR_SIZE
from ..embeddings import EmbeddingModel
from ....config import settings

logger = logging.getLogger(__name__)

class QdrantVectorStore(BaseVectorStore):
    """
    Qdrantを使用したベクトルストア実装
    """
    def __init__(self, collection_name: str, embedding_model: EmbeddingModel):
        """
        Qdrantベクトルストアを初期化します

        Args:
            collection_name: コレクション名
            embedding_model: 埋め込みモデルのインスタンス
        """
        super().__init__(collection_name, embedding_model)

        # 環境変数から設定を読み込む
        host = settings.QDRANT_HOST or "localhost" if settings.VECTOR_DATABASE_TYPE == "qdrant" else "localhost"
        port = settings.QDRANT_PORT or 6333 if settings.VECTOR_DATABASE_TYPE == "qdrant" else 6333

        # Qdrantクライアントを初期化
        try:
            self.client = QdrantClient(host=host, port=port)
            logger.info(f"Qdrantクライアントが正常に初期化されました: {host}:{port}")
            # 接続テスト
            try:
                self.client.get_collections()
                self.available = True
            except Exception as conn_e:
                logger.warning(f"Qdrantサーバーへの接続テストに失敗しました: {conn_e}")
                self.available = False
                raise RuntimeError("Qdrantサーバーが利用できません")
        except Exception as e:
            logger.error(f"Qdrantクライアントの初期化中にエラーが発生しました: {e}")
            self.available = False
            raise RuntimeError("Qdrantクライアントの初期化に失敗しました")

    @property
    def is_available(self) -> bool:
        """
        Qdrantが利用可能かどうかを返します

        Returns:
            bool: 利用可能な場合はTrue、そうでない場合はFalse
        """
        return self.available

    @property
    def store_type(self) -> str:
        """
        ベクトルストアの種類を返します

        Returns:
            str: "qdrant"
        """
        return "qdrant"

    def get_client(self):
        """
        Qdrantクライアントを返します

        Returns:
            QdrantClient: Qdrantクライアントインスタンス
        """
        return self.client

    def create_collection(self, vector_size: int = None, recreate: bool = False) -> None:
        """
        Qdrantコレクションを作成します
        """
        try:
            # 埋め込みモデルから次元数を取得
            if vector_size is None:
                try:
                    vector_size = self.embedding_model.get_embedding_dimension()
                    logger.info(f"埋め込みモデルから次元数を取得しました: {vector_size}")
                except Exception as e:
                    logger.warning(f"埋め込みモデルから次元数を取得できませんでした: {e}")
                    vector_size = DEFAULT_VECTOR_SIZE
                    logger.info(f"デフォルトの次元数を使用します: {vector_size}")

            # コレクションが存在するか確認
            collections = self.client.get_collections().collections
            collection_names = [collection.name for collection in collections]

            if self.collection_name in collection_names and not recreate:
                logger.info(f"コレクション {self.collection_name} は既に存在します")
                # 既存のコレクションの次元数を確認
                collection_info = self.client.get_collection(collection_name=self.collection_name)
                existing_size = collection_info.config.params.vectors.size
                if existing_size != vector_size:
                    logger.warning(f"既存のコレクションの次元数 ({existing_size}) と現在の埋め込みモデルの次元数 ({vector_size}) が一致しません")
                    logger.warning("recreate=True を指定して再作成するか、別のコレクション名を使用してください")
                return

            # コレクションを作成または再作成
            if recreate:
                logger.info(f"コレクション {self.collection_name} を再作成します (次元数: {vector_size})")
                self.client.recreate_collection(
                    collection_name=self.collection_name,
                    vectors_config=qmodels.VectorParams(size=vector_size, distance="Cosine"),
                )
            else:
                logger.info(f"コレクション {self.collection_name} を作成します (次元数: {vector_size})")
                self.client.create_collection(
                    collection_name=self.collection_name,
                    vectors_config=qmodels.VectorParams(size=vector_size, distance="Cosine"),
                )

            logger.info(f"コレクション {self.collection_name} が正常に作成されました")
        except Exception as e:
            logger.error(f"コレクションの作成中にエラーが発生しました: {e}")
            raise

    def add_texts(self, texts: List[str], metadatas: Optional[List[Dict[str, Any]]] = None) -> None:
        """
        テキストとメタデータをQdrantコレクションに追加します
        """
        if not texts:
            logger.warning("追加するテキストがありません")
            return

        # メタデータがない場合は空の辞書のリストを作成
        if metadatas is None:
            metadatas = [{} for _ in texts]

        try:
            # テキストを埋め込み
            vectors = self.embedding_model.embed_documents(texts)

            points = []
            for _, (text, vector, metadata) in enumerate(zip(texts, vectors, metadatas)):
                # ユニークIDを生成
                point_id = str(uuid.uuid4())

                # ペイロードを作成
                payload = {"text": text, "page_content": text}
                payload.update(metadata)

                points.append(
                    qmodels.PointStruct(
                        id=point_id,
                        vector=vector,
                        payload=payload,
                    )
                )

            # バッチでドキュメントを追加
            self.client.upsert(collection_name=self.collection_name, points=points)
            logger.info(f"{len(points)} 個のテキストがQdrantに正常に追加されました")
        except Exception as e:
            logger.error(f"テキストの追加中にエラーが発生しました: {e}")
            raise

    def search(self, query: str, top_k: int = 5, filter_params: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        """
        クエリ文字列に基づいてドキュメントを検索します
        """
        try:
            # クエリを埋め込み
            query_vec = self.embedding_model.embed_text(query)

            # フィルタを作成
            search_filter = None
            if filter_params:
                conditions = []
                for key, value in filter_params.items():
                    if isinstance(value, list):
                        # リストの場合、複数の値のいずれかに一致
                        for v in value:
                            conditions.append(FieldCondition(key=key, match=MatchValue(value=v)))
                    else:
                        # 単一の値の場合
                        conditions.append(FieldCondition(key=key, match=MatchValue(value=value)))

                if conditions:
                    search_filter = Filter(should=conditions)  # OR条件として指定

            # 検索を実行
            search_result = self.client.search(
                collection_name=self.collection_name,
                query_vector=query_vec,
                limit=top_k,
                query_filter=search_filter
            )

            # 結果を整形
            results = []
            for hit in search_result:
                result = {
                    "text": hit.payload.get("text", ""),
                    "score": hit.score,
                    "id": hit.id,
                }

                # メタデータを追加
                for key, value in hit.payload.items():
                    if key != "text":
                        result[key] = value

                results.append(result)

            logger.info(f"{len(results)} 個の検索結果がQdrantから見つかりました")
            return results
        except Exception as e:
            logger.error(f"検索中にエラーが発生しました: {e}")
            raise

    def search_by_vector(self, query_vector: List[float], top_k: int = 5, filter_params: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        """
        クエリベクトルに基づいてドキュメントを検索します
        """
        try:
            # フィルタを作成
            search_filter = None
            if filter_params:
                conditions = []
                for key, value in filter_params.items():
                    if isinstance(value, list):
                        # リストの場合、複数の値のいずれかに一致
                        for v in value:
                            conditions.append(FieldCondition(key=key, match=MatchValue(value=v)))
                    else:
                        # 単一の値の場合
                        conditions.append(FieldCondition(key=key, match=MatchValue(value=value)))

                if conditions:
                    search_filter = Filter(should=conditions)  # OR条件として指定

            # 検索を実行
            search_result = self.client.search(
                collection_name=self.collection_name,
                query_vector=query_vector,
                limit=top_k,
                query_filter=search_filter
            )

            # 結果を整形
            results = []
            for hit in search_result:
                result = {
                    "text": hit.payload.get("text", ""),
                    "score": hit.score,
                    "id": hit.id,
                }

                # メタデータを追加
                for key, value in hit.payload.items():
                    if key != "text":
                        result[key] = value

                results.append(result)

            logger.info(f"{len(results)} 個の検索結果が見つかりました")
            return results
        except Exception as e:
            logger.error(f"検索中にエラーが発生しました: {e}")
            raise

    def delete_by_filter(self, filter_params: Dict[str, Any]) -> bool:
        """
        フィルタに一致するドキュメントを削除します

        Args:
            filter_params: 削除するドキュメントのフィルタ条件

        Returns:
            bool: 削除に成功した場合はTrue、そうでない場合はFalse
        """
        try:
            # フィルタを作成
            conditions = []
            for key, value in filter_params.items():
                if isinstance(value, list):
                    # リストの場合、複数の値のいずれかに一致
                    for v in value:
                        conditions.append(FieldCondition(key=key, match=MatchValue(value=v)))
                else:
                    # 単一の値の場合
                    conditions.append(FieldCondition(key=key, match=MatchValue(value=value)))

            if not conditions:
                logger.warning("削除フィルタが指定されていません")
                return False

            delete_filter = Filter(should=conditions)  # OR条件として指定

            # 削除を実行
            result = self.client.delete(collection_name=self.collection_name, points_selector=delete_filter)
            logger.info(f"{result} 個のドキュメントが削除されました")
            return True
        except Exception as e:
            logger.error(f"ドキュメントの削除中にエラーが発生しました: {e}")
            return False

    def get_collection_info(self) -> Dict[str, Any]:
        """
        コレクションの情報を取得します
        """
        try:
            # コレクションの情報を取得
            collection_info = self.client.get_collection(collection_name=self.collection_name)

            # ポイント数を取得
            count_result = self.client.count(collection_name=self.collection_name)

            # 結果を整形
            info = {
                "name": self.collection_name,
                "vector_size": collection_info.config.params.vectors.size,
                "distance": collection_info.config.params.vectors.distance,
                "points_count": count_result.count,
                "status": collection_info.status
            }

            return info
        except Exception as e:
            logger.error(f"コレクション情報の取得中にエラーが発生しました: {e}")
            raise

    def as_retriever(self, search_kwargs=None):
        """
        ベクトルストアをretrieverとして返します
        """
        try:
            # Qdrantインスタンスを作成
            qdrant = Qdrant(
                client=self.client,
                collection_name=self.collection_name,
                embeddings=self.embedding_model.model
            )

            # retrieverを返す
            return qdrant.as_retriever(search_kwargs=search_kwargs)
        except Exception as e:
            logger.error(f"Qdrantレトリーバーの作成中にエラーが発生しました: {e}")
            raise

from backend.plugins.rag.rag_service import RAGService
from backend.plugins.rag.reranker import RankingStrategy

# フィードバック機能を有効にしてRAGサービスを初期化
rag = RAGService(
    collection_name="my_collection_sample",
    ranking_strategy=RankingStrategy.FEEDBACK_ENHANCED,
    enable_reranking=True,
    enable_feedback=True
)

# 初回のクエリ実行
print("初回クエリ実行:")
result1 = rag.run_rag_with_feedback("SR会社の社長は誰ですか")
print(f"回答: {result1['result']}")

# ユーザーからのフィードバック記録（最初のドキュメントは役立った）
if "feedback_info" in result1 and len(result1["feedback_info"]) > 0:
    doc_info = result1["feedback_info"][0]
    rag.record_feedback(doc_info["query"], doc_info["doc_id"], True)  # 役立った
    print(f"フィードバック記録: ドキュメント '{doc_info['doc_id']}' は役立った")

# 類似クエリの実行（フィードバックが反映されるはず）
print("\n類似クエリ実行:")
result2 = rag.run_rag_with_feedback("SR会社の代表者について教えてください")
print(f"回答: {result2['result']}")

# 別のクエリでも試してみる
print("\n別のクエリ実行:")
result3 = rag.run_rag_with_feedback("SR会社のCTOは誰ですか")
print(f"回答: {result3['result']}")
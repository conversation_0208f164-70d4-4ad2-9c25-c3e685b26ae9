import logging
from enum import Enum
from typing import List, Dict, Any, Optional, Callable
import numpy as np

from langchain.retrievers.document_compressors import CrossEncoderReranker
from langchain_community.cross_encoders import HuggingFaceCrossEncoder

logger = logging.getLogger(__name__)

# フィードバックループのための新しい列挙型
class FeedbackType(Enum):
    POSITIVE = "positive"  # 良い回答だったというフィードバック
    NEGATIVE = "negative"  # 悪い回答だったというフィードバック
    NEUTRAL = "neutral"    # 中立的なフィードバック

class RankingStrategy(Enum):
    """
    ランキング戦略の列挙型
    """
    CROSS_ENCODER = "cross_encoder"  # クロスエンコーダーによる再ランキング（デフォルト）
    VECTOR_SCORE = "vector_score"    # ベクトル検索スコアのみ
    CUSTOM = "custom"                # カスタム戦略（関数を指定）
    FEEDBACK_ENHANCED = "feedback_enhanced"  # フィードバックを活用した再ランキング

class Reranker:
    """
    検索結果の再ランキングを行うクラス
    langchain v0.3.23 の CrossEncoderReranker を使用
    """
    def __init__(self,
                 strategy: RankingStrategy = RankingStrategy.CROSS_ENCODER,
                 custom_ranking_func: Optional[Callable] = None,
                 model_name: str = "hotchpotch/japanese-reranker-cross-encoder-base-v1",
                 top_k: int = 5,
                 device: Optional[str] = None):
        """
        再ランキングクラスを初期化します

        Args:
            strategy: 使用するランキング戦略
            custom_ranking_func: カスタムランキング関数（strategyがCUSTOMの場合に使用）
            model_name: 使用するクロスエンコーダーモデル名
                - "hotchpotch/japanese-reranker-cross-encoder-base-v1" (デフォルト)
                - "hotchpotch/japanese-reranker-cross-encoder-large-v1"
                - "BAAI/bge-reranker-base"
            top_k: 返す結果の最大数
            device: 使用するデバイス（"cpu" または "cuda"）。Noneの場合は自動検出
        """
        self.strategy = strategy
        self.custom_ranking_func = custom_ranking_func
        self.model_name = model_name
        self.top_k = top_k
        self.device = device
        self.cross_encoder = None
        self.feedback_store = {}  # クエリとドキュメントのペアに対するフィードバックを保存

        # クロスエンコーダーモデルの初期化
        if self.strategy == RankingStrategy.CROSS_ENCODER:
            try:
                # device パラメータが None の場合は省略する
                kwargs = {"model_name": self.model_name}
                if self.device is not None:
                    kwargs["device"] = self.device
                self.cross_encoder_model = HuggingFaceCrossEncoder(**kwargs)
                self.cross_encoder_reranker = CrossEncoderReranker(
                    model=self.cross_encoder_model,
                    top_n=self.top_k
                )
                logger.info(f"クロスエンコーダーモデル '{self.model_name}' を初期化しました")
            except Exception as e:
                logger.error(f"クロスエンコーダーモデルの初期化に失敗しました: {e}")
                # フォールバックとしてベクトルスコア戦略を使用
                self.strategy = RankingStrategy.VECTOR_SCORE
                logger.warning(f"戦略を {RankingStrategy.VECTOR_SCORE.value} に変更しました")

        logger.info(f"Rerankerを初期化しました（戦略: {strategy.value}）")

    # フィードバックを記録するメソッド
    def record_feedback(self, query: str, doc_id: str, feedback_type: FeedbackType) -> None:
        """ユーザーフィードバックを記録する"""
        if query not in self.feedback_store:
            self.feedback_store[query] = {}
        
        # フィードバックスコアを設定（正:1.0, 中立:0.0, 負:-1.0）
        score = 1.0 if feedback_type == FeedbackType.POSITIVE else \
               -1.0 if feedback_type == FeedbackType.NEGATIVE else 0.0
               
        self.feedback_store[query][doc_id] = score
        logging.info(f"フィードバック記録: クエリ '{query}', ドキュメントID '{doc_id}', 評価 '{feedback_type.value}'")

    def rerank(self, query: str, results: List[Dict[str, Any]], top_k: Optional[int] = None) -> List[Dict[str, Any]]:
        """
        検索結果を再ランキングします

        Args:
            query: 検索クエリ
            results: 元の検索結果
            top_k: 返す結果の最大数（Noneの場合は初期化時の値を使用）

        Returns:
            再ランキングされた検索結果
        """
        if not results:
            logger.warning("再ランキングする結果がありません")
            return []

        if not query or not query.strip():
            logger.warning("クエリが空です")
            return results[:top_k] if top_k else results[:self.top_k]

        # top_kが指定されていない場合は初期化時の値を使用
        if top_k is None:
            top_k = self.top_k

        # 戦略に基づいて再ランキング
        if self.strategy == RankingStrategy.CROSS_ENCODER:
            # クロスエンコーダーによる再ランキング
            try:
                # langchainのドキュメント形式に変換
                from langchain.schema import Document
                documents = [
                    Document(
                        page_content=result.get("text", ""),
                        metadata={
                            "score": result.get("score", 0),
                            "source": result.get("source", ""),
                            **{k: v for k, v in result.items() if k not in ["text", "score", "source"]}
                        }
                    ) for result in results
                ]

                # クロスエンコーダーによる再ランキング
                reranked_documents = self.cross_encoder_reranker.compress_documents(documents, query)

                # 元の形式に戻す
                reranked_results = []
                for doc in reranked_documents:
                    result = {
                        "text": doc.page_content,
                        "score": doc.metadata.get("score", 0),
                        "source": doc.metadata.get("source", ""),
                    }
                    # その他のメタデータを追加
                    for k, v in doc.metadata.items():
                        if k not in ["score", "source"]:
                            result[k] = v
                    reranked_results.append(result)

                return reranked_results

            except Exception as e:
                logger.error(f"クロスエンコーダー再ランキング中にエラーが発生しました: {e}")
                # エラーが発生した場合はベクトルスコアを使用
                return sorted(results, key=lambda x: x.get("score", 0), reverse=True)[:top_k]

        elif self.strategy == RankingStrategy.VECTOR_SCORE:
            # 元のベクトル検索スコアを使用（降順ソート）
            return sorted(results, key=lambda x: x.get("score", 0), reverse=True)[:top_k]

        elif self.strategy == RankingStrategy.CUSTOM and self.custom_ranking_func:
            # カスタム再ランキング関数を使用
            try:
                reranked_results = self.custom_ranking_func(query, results)
                return reranked_results[:top_k]
            except Exception as e:
                logger.error(f"カスタム再ランキング中にエラーが発生しました: {e}")
                return sorted(results, key=lambda x: x.get("score", 0), reverse=True)[:top_k]

        elif self.strategy == RankingStrategy.FEEDBACK_ENHANCED:
            # フィードバック情報を活用した再ランキング
            return self._feedback_enhanced_ranking(query, results, top_k)

        else:
            # デフォルトは元の結果を使用
            logger.warning(f"未サポートの戦略または無効な設定: {self.strategy}")
            return sorted(results, key=lambda x: x.get("score", 0), reverse=True)[:top_k]

    def _feedback_enhanced_ranking(self, query: str, results: List[Dict[str, Any]], top_k: int) -> List[Dict[str, Any]]:
        """フィードバック情報を活用した再ランキング"""
        # まずクロスエンコーダーで基本スコアを計算
        if self.cross_encoder is None:
            from langchain.retrievers.document_compressors import CohereRerank
            try:
                from langchain_community.retrievers.document_compressors import CohereRerank
            except ImportError:
                from langchain.retrievers.document_compressors import CohereRerank
                
            self.cross_encoder = CohereRerank(model=self.model_name)
            
        # クロスエンコーダーでスコア計算
        pairs = [(query, result["text"]) for result in results]
        scores = self.cross_encoder.predict(pairs)
        
        # フィードバック情報を反映したスコア調整
        for i, result in enumerate(results):
            doc_id = result.get("metadata", {}).get("source", str(i))
            
            # このクエリに対するフィードバック情報があれば反映
            if query in self.feedback_store and doc_id in self.feedback_store[query]:
                feedback_score = self.feedback_store[query][doc_id]
                # フィードバックスコアを反映（0.2は調整係数）
                scores[i] += feedback_score * 0.2
                
            # 類似クエリからのフィードバックも考慮（簡易実装）
            for stored_query, feedbacks in self.feedback_store.items():
                if stored_query != query and self._is_similar_query(query, stored_query):
                    if doc_id in feedbacks:
                        # 類似クエリからのフィードバックは少し弱く反映
                        scores[i] += feedbacks[doc_id] * 0.1
            
            # 更新されたスコアを設定
            result["score"] = float(scores[i])
            
        # スコアでソートして返す
        return sorted(results, key=lambda x: x.get("score", 0), reverse=True)[:top_k]

    def _is_similar_query(self, query1: str, query2: str) -> bool:
        """2つのクエリが類似しているかを判定する簡易実装"""
        # 単語の重複率で判定（実際にはより高度な類似度計算が必要）
        words1 = set(query1.split())
        words2 = set(query2.split())
        overlap = len(words1.intersection(words2))
        return overlap >= min(len(words1), len(words2)) * 0.7  # 70%以上の単語が重複

    def set_strategy(self, strategy: RankingStrategy) -> None:
        """
        ランキング戦略を設定します

        Args:
            strategy: 使用するランキング戦略
        """
        self.strategy = strategy
        logger.info(f"ランキング戦略を {strategy.value} に変更しました")

        # クロスエンコーダー戦略に変更された場合、モデルを初期化
        if strategy == RankingStrategy.CROSS_ENCODER and not hasattr(self, 'cross_encoder_model'):
            try:
                # device パラメータが None の場合は省略する
                kwargs = {"model_name": self.model_name}
                if self.device is not None:
                    kwargs["device"] = self.device
                self.cross_encoder_model = HuggingFaceCrossEncoder(**kwargs)
                self.cross_encoder_reranker = CrossEncoderReranker(
                    model=self.cross_encoder_model,
                    top_n=self.top_k
                )
                logger.info(f"クロスエンコーダーモデル '{self.model_name}' を初期化しました")
            except Exception as e:
                logger.error(f"クロスエンコーダーモデルの初期化に失敗しました: {e}")
                # フォールバックとしてベクトルスコア戦略を使用
                self.strategy = RankingStrategy.VECTOR_SCORE
                logger.warning(f"戦略を {RankingStrategy.VECTOR_SCORE.value} に変更しました")

    def set_model(self, model_name: str) -> None:
        """
        クロスエンコーダーモデルを変更します

        Args:
            model_name: 使用するクロスエンコーダーモデル名
        """
        if self.model_name == model_name:
            return

        self.model_name = model_name
        logger.info(f"クロスエンコーダーモデルを '{model_name}' に変更します")

        # 現在の戦略がクロスエンコーダーの場合、モデルを再初期化
        if self.strategy == RankingStrategy.CROSS_ENCODER:
            try:
                # device パラメータが None の場合は省略する
                kwargs = {"model_name": self.model_name}
                if self.device is not None:
                    kwargs["device"] = self.device
                self.cross_encoder_model = HuggingFaceCrossEncoder(**kwargs)
                self.cross_encoder_reranker = CrossEncoderReranker(
                    model=self.cross_encoder_model,
                    top_n=self.top_k
                )
                logger.info(f"クロスエンコーダーモデル '{self.model_name}' を初期化しました")
            except Exception as e:
                logger.error(f"クロスエンコーダーモデルの初期化に失敗しました: {e}")
                # フォールバックとしてベクトルスコア戦略を使用
                self.strategy = RankingStrategy.VECTOR_SCORE
                logger.warning(f"戦略を {RankingStrategy.VECTOR_SCORE.value} に変更しました")

    def set_top_k(self, top_k: int) -> None:
        """
        返す結果の最大数を設定します

        Args:
            top_k: 返す結果の最大数
        """
        self.top_k = top_k
        logger.info(f"返す結果の最大数を {top_k} に変更しました")

        # クロスエンコーダーが初期化されている場合、top_nを更新
        if hasattr(self, 'cross_encoder_reranker'):
            self.cross_encoder_reranker.top_n = top_k



import logging
import functools
from typing import List, Dict, Any, Optional, Union, Callable
from langchain_core.documents import Document
from .vector_stores.base import BaseVectorStore, DEFAULT_COLLECTION_NAME, DEFAULT_VECTOR_SIZE
from .vector_factory import VectorStoreFactory
from .embeddings import EmbeddingModel

logger = logging.getLogger(__name__)

def with_fallback(fallback_type: str = None):
    """
    操作が失敗した場合にエラーを記録するデコレータ
    注意: 以前はフォールバックを提供していましたが、現在は単にエラーを記録するだけです

    Args:
        fallback_type: 互換性のために残されていますが、使用されません
    """
    def decorator(func: Callable):
        @functools.wraps(func)
        def wrapper(self, *args, **kwargs):
            try:
                # ベクトルストアが利用可能かチェック
                if not self.is_available:
                    logger.error(f"ベクトルストア {self.store_type} は利用できません。設定を確認してください。")
                    raise RuntimeError(f"ベクトルストア {self.store_type} は利用できません。設定を確認してください。")

                return func(self, *args, **kwargs)
            except Exception as e:
                logger.error(f"{func.__name__} 操作中にエラーが発生しました: {e}")
                raise
        return wrapper
    return decorator

class VectorStore:
    """
    ベクトルストアアダプタークラス
    異なるベクトルストア実装に対して統一されたインターフェースを提供します
    """
    def __init__(self, collection_name: str = DEFAULT_COLLECTION_NAME, model_name: str = None, vector_store_type: str = None):
        """
        ベクトルストアアダプターを初期化します

        Args:
            collection_name: コレクション名
            model_name: 埋め込みモデル名
            vector_store_type: ベクトルストアのタイプ（"faiss", "qdrant", "weaviate"など）
        """
        self.collection_name = collection_name
        self.model_name = model_name
        self.embedding_model = EmbeddingModel(model_name=model_name)

        # 内部ベクトルストアを初期化
        self._initialize_vector_store(vector_store_type)

    def _initialize_vector_store(self, vector_store_type: str = None) -> None:
        """
        指定されたタイプのベクトルストアを初期化します

        Args:
            vector_store_type: ベクトルストアのタイプ
        """
        try:
            # ベクトルストアを作成
            self._vector_store = VectorStoreFactory.create_vector_store(
                vector_store_type=vector_store_type,
                collection_name=self.collection_name,
                model_name=self.model_name
            )
            logger.info(f"{self.store_type} ベクトルストアが正常に初期化されました")

            # ベクトルストアが利用可能かチェック
            if not self._vector_store.is_available:
                logger.error(f"ベクトルストア {self.store_type} は利用できません。設定を確認してください。")
                raise RuntimeError(f"ベクトルストア {self.store_type} は利用できません。設定を確認してください。")

        except Exception as e:
            logger.error(f"ベクトルストアの初期化中にエラーが発生しました: {e}")
            raise

    @property
    def store_type(self) -> str:
        """
        現在使用しているベクトルストアのタイプを返します

        Returns:
            str: ベクトルストアのタイプ
        """
        return self._vector_store.store_type

    @property
    def is_available(self) -> bool:
        """
        ベクトルストアが利用可能かどうかを返します

        Returns:
            bool: 利用可能な場合はTrue、そうでない場合はFalse
        """
        return self._vector_store.is_available

    def get_client(self):
        """
        内部ベクトルストアのクライアントを返します

        Returns:
            Any: ベクトルストアのクライアントインスタンス
        """
        return self._vector_store.get_client()

    @with_fallback(fallback_type="faiss")
    def create_collection(self, vector_size: int = None, recreate: bool = False) -> None:
        """
        コレクションを作成します

        Args:
            vector_size: ベクトルの次元数
            recreate: コレクションを再作成するかどうか
        """
        return self._vector_store.create_collection(vector_size, recreate)

    @with_fallback(fallback_type="faiss")
    def add_texts(self, texts: List[str], metadatas: Optional[List[Dict[str, Any]]] = None) -> None:
        """
        テキストとメタデータをコレクションに追加します

        Args:
            texts: 追加するテキストのリスト
            metadatas: テキストに対応するメタデータのリスト
        """
        return self._vector_store.add_texts(texts, metadatas)

    @with_fallback(fallback_type="faiss")
    def add_documents(self, documents: List[Document]) -> None:
        """
        LangchainのDocumentオブジェクトをコレクションに追加します

        Args:
            documents: 追加するドキュメントのリスト
        """
        return self._vector_store.add_documents(documents)

    @with_fallback(fallback_type="faiss")
    def search(self, query: str, top_k: int = 5, filter_params: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        """
        クエリ文字列に基づいてドキュメントを検索します

        Args:
            query: 検索クエリ
            top_k: 返す結果の最大数
            filter_params: 検索フィルタ

        Returns:
            List[Dict[str, Any]]: 検索結果のリスト
        """
        return self._vector_store.search(query, top_k, filter_params)

    @with_fallback(fallback_type="faiss")
    def search_by_vector(self, query_vector: List[float], top_k: int = 5, filter_params: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        """
        クエリベクトルに基づいてドキュメントを検索します

        Args:
            query_vector: 検索ベクトル
            top_k: 返す結果の最大数
            filter_params: 検索フィルタ

        Returns:
            List[Dict[str, Any]]: 検索結果のリスト
        """
        return self._vector_store.search_by_vector(query_vector, top_k, filter_params)

    @with_fallback(fallback_type="faiss")
    def delete_by_filter(self, filter_params: Dict[str, Any]) -> bool:
        """
        フィルタに一致するドキュメントを削除します

        Args:
            filter_params: 削除条件を指定するフィルタ

        Returns:
            bool: 削除に成功した場合はTrue、そうでない場合はFalse
        """
        return self._vector_store.delete_by_filter(filter_params)

    @with_fallback(fallback_type="faiss")
    def get_collection_info(self) -> Dict[str, Any]:
        """
        コレクションの情報を取得します

        Returns:
            Dict[str, Any]: コレクション情報を含む辞書
        """
        return self._vector_store.get_collection_info()

    @with_fallback(fallback_type="faiss")
    def as_retriever(self, search_kwargs=None):
        """
        ベクトルストアをretrieverとして返します

        Args:
            search_kwargs: 検索パラメータ

        Returns:
            Any: retrieverインスタンス
        """
        return self._vector_store.as_retriever(search_kwargs)

# エクスポート
__all__ = [
    'VectorStore',
    'BaseVectorStore',
    'DEFAULT_COLLECTION_NAME',
    'DEFAULT_VECTOR_SIZE'
]

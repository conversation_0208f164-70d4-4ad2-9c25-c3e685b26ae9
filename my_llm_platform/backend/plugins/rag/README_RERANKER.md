# RAG再ランキング機能

このドキュメントでは、RAGシステムの再ランキング機能について詳しく説明します。

## 概要

RAG（Retrieval-Augmented Generation）システムでは、ユーザーのクエリに関連するドキュメントを検索し、それらを使用して回答を生成します。検索の精度はRAGの性能に大きく影響します。

再ランキング機能は、ベクトル検索で得られた初期の検索結果を、より高度なアルゴリズムを使用して再評価し、より関連性の高い順に並べ替えることで、検索精度を向上させます。

## 新しいクロスエンコーダーReranker

最新の実装では、langchain v0.3.23 の `CrossEncoderReranker` を使用した日本語に最適化されたクロスエンコーダーモデルによる再ランキングをサポートしています。

### サポートされているモデル

- `hotchpotch/japanese-reranker-cross-encoder-base-v1` (デフォルト)
- `hotchpotch/japanese-reranker-cross-encoder-large-v1`
- `BAAI/bge-reranker-base`

### 日本語クロスエンコーダーモデルの性能

| モデル名 | JQaRA | JaCWIR | MIRACL | JSQuAD |
|---------|-------|--------|--------|--------|
| japanese-reranker-cross-encoder-base-v1 | 0.6711 | 0.9337 | 0.818 | 0.9708 |
| japanese-reranker-cross-encoder-large-v1 | 0.7099 | 0.9364 | 0.8406 | 0.9773 |
| bge-reranker-base | 0.2445 | 0.4905 | 0.6792 | 0.5757 |

## ランキング戦略

`reranker.py`モジュールでは、以下のランキング戦略を提供しています：

### 1. クロスエンコーダー（CROSS_ENCODER）

クロスエンコーダーモデルを使用して、クエリとドキュメントのペアの関連性を直接評価します。これは最も高精度な再ランキング方法です。

```python
rag.set_ranking_strategy(RankingStrategy.CROSS_ENCODER)
```

### 2. ベクトルスコア（VECTOR_SCORE）

ベクトル検索で得られた元のスコアをそのまま使用します。これは再ランキングを行わない場合と同じです。

```python
rag.set_ranking_strategy(RankingStrategy.VECTOR_SCORE)
```

### 3. カスタム（CUSTOM）

独自のランキング関数を提供することもできます。

```python
def my_custom_ranking(query, results):
    # カスタムランキングロジック
    return sorted_results

reranker = Reranker(strategy=RankingStrategy.CUSTOM, custom_ranking_func=my_custom_ranking)
```

## 各ランキング戦略の効果比較

各ランキング戦略の効果は以下のようになります：

| 戦略 | 長所 | 短所 | 適したケース |
|------|------|------|------------|
| **クロスエンコーダー** | 最も高精度な再ランキング | 計算コストが高い | 精度が最も重要な場合 |
| **ベクトルスコア** | シンプルで計算コストが低い | 精度が低い場合がある | 高速な処理が必要な場合 |
| **カスタム** | 特定のユースケースに最適化可能 | 実装コストが高い | 特殊な要件がある場合 |

一般的には**クロスエンコーダー戦略**が最も効果的です。これはクエリとドキュメントのペアの関連性を直接評価するため、より精度の高い結果が得られます。

## 使用方法

### RAGServiceでの使用

RAGServiceを初期化する際に、再ランキング機能を有効にし、ランキング戦略を指定することができます：

```python
from backend.plugins.rag.rag_service import RAGService
from backend.plugins.rag.reranker import RankingStrategy

# 再ランキング機能を有効にした初期化
rag = RAGService(
    collection_name="my_documents",
    ranking_strategy=RankingStrategy.CROSS_ENCODER,
    enable_reranking=True,
    reranker_model="hotchpotch/japanese-reranker-cross-encoder-base-v1"
)

# ランキング戦略を後から変更
rag.set_ranking_strategy(RankingStrategy.CROSS_ENCODER)

# クエリを実行（再ランキングが自動的に適用されます）
results = rag.query("検索クエリ", top_k=5)

# RAGを実行（検索結果を使用してLLMで回答を生成）
rag_result = rag.run_rag("検索クエリ", top_k=5)
```

### `query`と`run_rag`の違い

RAGServiceには2つの主要な検索メソッドがあります：

1. **`query`メソッド**：
   - **目的**：関連ドキュメントの検索のみを行う
   - **戻り値**：検索結果のリスト（ドキュメントとそのスコア）
   - **用途**：単純な検索機能として使用（LLMは使用しない）

2. **`run_rag`メソッド**：
   - **目的**：検索結果を使用してLLMで回答を生成する（完全なRAGプロセス）
   - **戻り値**：回答、思考プロセス、使用されたコンテキストを含む辞書
   - **用途**：質問応答システムとして使用（検索+LLM生成）

どちらのメソッドも再ランキング機能を利用できるようになっており、検索精度を向上させることができます。ユースケースに応じて適切なメソッドを選択してください。

### Rerankerの直接使用

Rerankerクラスを直接使用することもできます：

```python
from backend.plugins.rag.reranker import Reranker, RankingStrategy

# クロスエンコーダーRerankerを初期化
reranker = Reranker(
    strategy=RankingStrategy.CROSS_ENCODER,
    model_name="hotchpotch/japanese-reranker-cross-encoder-base-v1",
    top_k=5
)

# 検索結果を再ランキング
reranked_results = reranker.rerank("検索クエリ", original_results)

# モデルを変更
reranker.set_model("hotchpotch/japanese-reranker-cross-encoder-large-v1")

# 返す結果の最大数を変更
reranker.set_top_k(10)
```

## 実装の詳細

### クロスエンコーダーの仕組み

クロスエンコーダーは、クエリとドキュメントのペアを一緒に入力し、その関連性スコアを直接出力するモデルです。これは双方向エンコーダー（Bi-Encoder）とは異なり、クエリとドキュメントの相互作用を考慮できるため、より高精度な関連性評価が可能です。

### 結果の形式

再ランキング後の結果には、元のスコアに加えて、クロスエンコーダーによる新しいスコアも含まれます：

```python
{
    "text": "ドキュメントのテキスト",
    "score": 0.85,  # クロスエンコーダーによる新しいスコア
    "original_score": 0.75,  # 元のベクトルスコア
    "source": "document1.txt",  # ソース
    "metadata": {...}  # メタデータ
}
```

## パフォーマンスの考慮事項

- クロスエンコーダーによる再ランキングは、計算コストが高くなる可能性があります
- 大規模なデータセットでは、まずベクトル検索で上位のドキュメントを取得し、その後クロスエンコーダーで再ランキングすることで効率を向上させることができます
- GPUを使用することで、クロスエンコーダーの処理速度を大幅に向上させることができます

## カスタマイズ

再ランキング機能は以下の方法でカスタマイズできます：

1. **モデルの選択**: 異なるクロスエンコーダーモデルを選択
2. **カスタム戦略**: 独自のランキング関数を実装
3. **デバイスの指定**: CPUまたはGPUを指定して処理速度を最適化

## 今後の拡張

将来的に追加される可能性のある機能：

1. **ハイブリッドランキング**: クロスエンコーダーと他のランキング手法を組み合わせた戦略
2. **バッチ処理の最適化**: 大量のドキュメントを効率的に処理するためのバッチ処理の改善
3. **学習ベースのランキング**: ユーザーフィードバックに基づいて再ランキングモデルを学習
4. **言語固有の最適化**: 日本語テキスト向けの特別な処理の強化

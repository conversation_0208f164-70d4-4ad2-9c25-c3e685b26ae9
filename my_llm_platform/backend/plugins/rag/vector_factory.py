import logging
from typing import Dict, Type, List
from .vector_stores.base import BaseVectorStore, DEFAULT_COLLECTION_NAME
from .vector_stores.faiss_store import FAISSVectorStore
from .vector_stores.qdrant_store import QdrantVectorStore
from .vector_stores.weaviate_store import WeaviateVectorStore
from .vector_stores.supabase_store import SupabaseVectorStore
from .embeddings import EmbeddingModel
from ...config import settings

logger = logging.getLogger(__name__)

class VectorStoreFactory:
    """
    ベクトルストアのファクトリークラス
    設定に基づいて適切なベクトルストアを作成します

    工場パターンの正しい実装：
    - 具体的な実装の詳細を隠蔽
    - 抽象インターフェースに依存
    - 新しい実装の追加が容易
    """
    # 利用可能なベクトルストアタイプ
    VECTOR_STORE_TYPES: Dict[str, Type[BaseVectorStore]] = {
        "faiss": FAISSVectorStore,
        "qdrant": QdrantVectorStore,
        "weaviate": WeaviateVectorStore,
        "supabase": SupabaseVectorStore,
        # 将来的に他のベクトルストアタイプを追加可能
    }

    @classmethod
    def create_vector_store(cls,
                           vector_store_type: str = None,
                           collection_name: str = DEFAULT_COLLECTION_NAME,
                           model_name: str = None) -> BaseVectorStore:
        """
        指定されたタイプのベクトルストアを作成します
        純粋な工場メソッド - エラー処理は呼び出し側に委任

        Args:
            vector_store_type: ベクトルストアのタイプ（"faiss", "qdrant", "weaviate"など）
            collection_name: コレクション名
            model_name: 埋め込みモデル名

        Returns:
            BaseVectorStore: 作成されたベクトルストアのインスタンス

        Raises:
            ValueError: 無効なベクトルストアタイプが指定された場合
            Exception: ベクトルストアの作成中にエラーが発生した場合
        """
        # 設定からベクトルストアタイプを取得（指定がない場合）
        if vector_store_type is None:
            vector_store_type = settings.VECTOR_DATABASE_TYPE.lower()
            logger.info(f"設定からベクトルストアタイプを取得しました: {vector_store_type}")

        # 埋め込みモデルを初期化
        embedding_model = EmbeddingModel(model_name=model_name)

        # ベクトルストアタイプが有効かチェック
        if vector_store_type not in cls.VECTOR_STORE_TYPES:
            raise ValueError(f"無効なベクトルストアタイプ: {vector_store_type}")

        # ベクトルストアを作成
        vector_store_class = cls.VECTOR_STORE_TYPES[vector_store_type]
        vector_store = vector_store_class(collection_name, embedding_model)
        logger.info(f"{vector_store_type} ベクトルストアが正常に作成されました")
        return vector_store

    @classmethod
    def get_available_store_types(cls) -> List[str]:
        """
        利用可能なベクトルストアタイプのリストを返します

        Returns:
            List[str]: 利用可能なベクトルストアタイプのリスト
        """
        return list(cls.VECTOR_STORE_TYPES.keys())

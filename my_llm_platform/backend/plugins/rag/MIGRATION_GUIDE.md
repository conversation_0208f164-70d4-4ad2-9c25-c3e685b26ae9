# Reranker Migration Guide

このガイドは、旧バージョンのRerankerから新しいクロスエンコーダーベースのRerankerへの移行方法を説明します。

## 主な変更点

1. **ランキング戦略の変更**:
   - 旧戦略: `VECTOR_SCORE`, `KEYWORD_MATCH`, `TFIDF`, `HYBRID`, `CUSTOM`
   - 新戦略: `CROSS_ENCODER`, `VECTOR_SCORE`, `CUSTOM`

2. **デフォルト戦略の変更**:
   - 旧デフォルト: `RankingStrategy.HYBRID`
   - 新デフォルト: `RankingStrategy.CROSS_ENCODER`

3. **重み設定メソッドの削除**:
   - 旧バージョン: `set_weights(keyword_weight, tfidf_weight, vector_weight)`
   - 新バージョン: このメソッドは削除されました

4. **新しいメソッド**:
   - `set_model(model_name)`: クロスエンコーダーモデルを変更
   - `set_top_k(top_k)`: 返す結果の最大数を設定

## コード移行例

### 旧バージョン

```python
from backend.plugins.rag.rag_service import RAGService
from backend.plugins.rag.reranker import RankingStrategy

# RAGServiceの初期化
rag = RAGService(
    collection_name="my_documents",
    ranking_strategy=RankingStrategy.HYBRID,
    enable_reranking=True
)

# ランキング戦略を変更
rag.set_ranking_strategy(RankingStrategy.KEYWORD_MATCH)

# ハイブリッドランキングの重みを調整
rag.set_ranking_weights(keyword_weight=0.4, tfidf_weight=0.3, vector_weight=0.3)

# クエリを実行
results = rag.query("検索クエリ", top_k=5)
```

### 新バージョン

```python
from backend.plugins.rag.rag_service import RAGService
from backend.plugins.rag.reranker import RankingStrategy

# RAGServiceの初期化
rag = RAGService(
    collection_name="my_documents",
    ranking_strategy=RankingStrategy.CROSS_ENCODER,  # 新しいデフォルト戦略
    enable_reranking=True,
    reranker_model="hotchpotch/japanese-reranker-cross-encoder-base-v1"  # 新しいパラメータ
)

# ランキング戦略を変更
rag.set_ranking_strategy(RankingStrategy.CROSS_ENCODER)

# クロスエンコーダーモデルを変更
rag.set_reranker_model("hotchpotch/japanese-reranker-cross-encoder-large-v1")

# 返す結果の最大数を設定
rag.set_top_k(5)

# クエリを実行
results = rag.query("検索クエリ", top_k=5)
```

## 戦略の選択ガイド

- **CROSS_ENCODER**: 最も高精度な再ランキングが必要な場合（推奨）
- **VECTOR_SCORE**: 高速な処理が必要な場合や、計算リソースが限られている場合
- **CUSTOM**: 特殊な要件がある場合に独自のランキング関数を実装

## 注意事項

1. `RankingStrategy.HYBRID`, `RankingStrategy.KEYWORD_MATCH`, `RankingStrategy.TFIDF` は削除されました。これらを使用しているコードは `RankingStrategy.CROSS_ENCODER` に更新する必要があります。

2. `set_ranking_weights` メソッドは削除されました。重み付けは不要になりました。

3. 新しいクロスエンコーダーモデルは計算コストが高い場合があります。GPUを使用することで処理速度を向上させることができます。

## 必要な依存関係

新しいRerankerを使用するには、以下の依存関係が必要です:

```
langchain==0.3.23
langchain-community>=0.0.16
sentence-transformers>=2.2.2
torch>=2.0.0
transformers>=4.30.0
```

これらのパッケージをインストールするには:

```bash
pip install -r backend/plugins/rag/requirements.txt
```

import sys
import os

# プロジェクトのルートディレクトリをPythonパスに追加
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), "../../../"))
sys.path.insert(0, project_root)

import logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler(sys.stdout)]
)

try:
    from .rag_service import RAGService

    print("RAGService初期化開始")
    # recreate_collection=True を指定して、既存のコレクションを再作成
    rag = RAGService(
        # collection_name="my_collection_sample",
        # embedding_model_name="hotchpotch-japanese",
        recreate_collection=True
    )



    # 絶対パスを使用してデータディレクトリを指定
    data_dir = os.path.join(os.path.dirname(__file__), "data")
    rag.add_documents_from_directory(data_dir)
    print("ディレクトリ追加完了")


    result = rag.run_rag("SR会社の売上高はいくらですか")

    print("\n=== 回答 ===")
    print(result["result"])

    print("\n=== 思考プロセス ===")
    print(result["think"])

    print("\n=== 使用されたコンテキスト ===")
    for i, ctx in enumerate(result["context"], 1):
        print(f"{i}. {ctx}")
        
    result = rag.run_rag("SR会社の社長は誰ですか")

    print("\n=== 回答 ===")
    print(result["result"])

    print("\n=== 思考プロセス ===")
    print(result["think"])

    print("\n=== 使用されたコンテキスト ===")
    for i, ctx in enumerate(result["context"], 1):
        print(f"{i}. {ctx}")
except Exception as e:
    import traceback
    print(f"エラーが発生しました: {e}")
    print(traceback.format_exc())

# python -m backend.plugins.rag.rag_sample_1_search1
"""
Plugin manager for the LLM platform plugin system.
"""

import logging
import importlib
import pkgutil
import inspect
from typing import Dict, List, Any, Optional, Type, TypeVar, cast
from .base import BasePlugin, PreprocessorPlugin, PostprocessorPlugin, CommandPlugin, ToolPlugin, UIPlugin, AgentPlugin, RAGPlugin
from .types import Message, PluginResult, PluginType, PluginInfo

logger = logging.getLogger(__name__)

T = TypeVar('T', bound=BasePlugin)


class PluginManager:
    """Manager for loading and managing plugins."""

    def __init__(self):
        """Initialize the plugin manager."""
        self.plugins: Dict[str, BasePlugin] = {}
        self.plugin_classes: Dict[str, Type[BasePlugin]] = {}
        self.enabled = True

    def register_plugin(self, plugin_id: str, plugin_class: Type[BasePlugin], config: Optional[Dict[str, Any]] = None) -> BasePlugin:
        """Register a plugin with the manager."""
        if plugin_id in self.plugins:
            logger.warning(f"Plugin {plugin_id} is already registered. Overwriting.")

        # Store the plugin class
        self.plugin_classes[plugin_id] = plugin_class

        # Create an instance of the plugin
        plugin = plugin_class(config)
        plugin.id = plugin_id

        # Store the plugin instance
        self.plugins[plugin_id] = plugin

        logger.info(f"Registered plugin: {plugin_id} ({plugin.name})")
        return plugin

    def unregister_plugin(self, plugin_id: str) -> None:
        """Unregister a plugin from the manager."""
        if plugin_id in self.plugins:
            del self.plugins[plugin_id]
            del self.plugin_classes[plugin_id]
            logger.info(f"Unregistered plugin: {plugin_id}")
        else:
            logger.warning(f"Plugin {plugin_id} is not registered.")

    def get_plugin(self, plugin_id: str) -> Optional[BasePlugin]:
        """Get a plugin by ID."""
        return self.plugins.get(plugin_id)

    def get_plugin_info(self, plugin_id: str) -> Optional[PluginInfo]:
        """Get plugin information by ID."""
        plugin = self.get_plugin(plugin_id)
        if plugin:
            return plugin.get_info()
        return None

    def get_plugins_by_type(self, plugin_type: PluginType) -> List[BasePlugin]:
        """Get all plugins of a specific type."""
        return [p for p in self.plugins.values() if p.get_info().type == plugin_type and p.enabled]

    def get_all_plugins(self) -> List[BasePlugin]:
        """Get all registered plugins."""
        return list(self.plugins.values())

    def get_all_plugin_info(self) -> List[PluginInfo]:
        """Get information about all registered plugins."""
        return [p.get_info() for p in self.plugins.values()]

    def enable_plugin(self, plugin_id: str) -> None:
        """Enable a plugin."""
        plugin = self.get_plugin(plugin_id)
        if plugin:
            plugin.enable()
            logger.info(f"Enabled plugin: {plugin_id}")
        else:
            logger.warning(f"Plugin {plugin_id} is not registered.")

    def disable_plugin(self, plugin_id: str) -> None:
        """Disable a plugin."""
        plugin = self.get_plugin(plugin_id)
        if plugin:
            plugin.disable()
            logger.info(f"Disabled plugin: {plugin_id}")
        else:
            logger.warning(f"Plugin {plugin_id} is not registered.")

    def configure_plugin(self, plugin_id: str, config: Dict[str, Any]) -> None:
        """Configure a plugin."""
        plugin = self.get_plugin(plugin_id)
        if plugin:
            plugin.configure(config)
            logger.info(f"Configured plugin: {plugin_id}")
        else:
            logger.warning(f"Plugin {plugin_id} is not registered.")

    def enable_all_plugins(self) -> None:
        """Enable all plugins."""
        for plugin_id in self.plugins:
            self.enable_plugin(plugin_id)

    def disable_all_plugins(self) -> None:
        """Disable all plugins."""
        for plugin_id in self.plugins:
            self.disable_plugin(plugin_id)

    def enable_manager(self) -> None:
        """Enable the plugin manager."""
        self.enabled = True
        logger.info("Plugin manager enabled.")

    def disable_manager(self) -> None:
        """Disable the plugin manager."""
        self.enabled = False
        logger.info("Plugin manager disabled.")

    async def run_preprocessors(self, messages: List[Message], **kwargs) -> List[Message]:
        """Run all preprocessor plugins on messages."""
        if not self.enabled:
            return messages

        # Get all enabled preprocessor plugins, sorted by priority
        preprocessors = sorted(
            self.get_plugins_by_type(PluginType.PREPROCESSOR),
            key=lambda p: p.priority
        )

        # Process messages through each preprocessor
        current_messages = messages
        for preprocessor in preprocessors:
            try:
                preprocessor = cast(PreprocessorPlugin, preprocessor)
                result = await preprocessor.process(current_messages, **kwargs)
                current_messages = result.messages

                # If the plugin requests to stop processing, break the loop
                if result.stop_processing:
                    logger.info(f"Preprocessor {preprocessor.id} requested to stop processing.")
                    break

            except Exception as e:
                logger.error(f"Error in preprocessor {preprocessor.id}: {e}")

        return current_messages

    async def run_postprocessors(self, messages: List[Message], llm_response: Message, **kwargs) -> Message:
        """Run all postprocessor plugins on the LLM response."""
        if not self.enabled:
            return llm_response

        # Get all enabled postprocessor plugins, sorted by priority
        postprocessors = sorted(
            self.get_plugins_by_type(PluginType.POSTPROCESSOR),
            key=lambda p: p.priority
        )

        # Process response through each postprocessor
        current_response = llm_response
        for postprocessor in postprocessors:
            try:
                postprocessor = cast(PostprocessorPlugin, postprocessor)
                result = await postprocessor.process(messages, current_response, **kwargs)

                # Update the response if the plugin modified it
                if result.messages and len(result.messages) > 0:
                    current_response = result.messages[-1]

                # If the plugin requests to stop processing, break the loop
                if result.stop_processing:
                    logger.info(f"Postprocessor {postprocessor.id} requested to stop processing.")
                    break

            except Exception as e:
                logger.error(f"Error in postprocessor {postprocessor.id}: {e}")

        return current_response

    async def run_command_plugins(self, messages: List[Message], **kwargs) -> Optional[PluginResult]:
        """Run all command plugins on messages."""
        if not self.enabled:
            return None

        # Get all enabled command plugins
        command_plugins = self.get_plugins_by_type(PluginType.COMMAND)

        # Check if the last message contains a command
        if not messages or messages[-1]["role"] != "user":
            return None

        # Process the message through each command plugin
        for command_plugin in command_plugins:
            try:
                command_plugin = cast(CommandPlugin, command_plugin)
                result = await command_plugin.process(messages, **kwargs)

                # If the plugin handled the command, return its result
                if result.stop_processing:
                    logger.info(f"Command plugin {command_plugin.id} handled the command.")
                    return result

            except Exception as e:
                logger.error(f"Error in command plugin {command_plugin.id}: {e}")

        # No command was handled
        return None

    async def run_agent_plugins(self, messages: List[Message], **kwargs) -> Optional[PluginResult]:
        """Run all agent plugins on messages."""
        if not self.enabled:
            return None

        # Get all enabled agent plugins
        agent_plugins = self.get_plugins_by_type(PluginType.AGENT)

        # Process the messages through each agent plugin
        for agent_plugin in agent_plugins:
            try:
                agent_plugin = cast(AgentPlugin, agent_plugin)
                result = await agent_plugin.process(messages, **kwargs)

                # If the agent handled the request, return its result
                if result.stop_processing:
                    logger.info(f"Agent plugin {agent_plugin.id} handled the request.")
                    return result

            except Exception as e:
                logger.error(f"Error in agent plugin {agent_plugin.id}: {e}")

        # No agent handled the request
        return None

    async def run_rag_plugins(self, messages: List[Message], **kwargs) -> Optional[PluginResult]:
        """Run all RAG plugins on messages."""
        if not self.enabled:
            return None

        # Get all enabled RAG plugins
        rag_plugins = self.get_plugins_by_type(PluginType.RAG)

        # Process the messages through each RAG plugin
        for rag_plugin in rag_plugins:
            try:
                rag_plugin = cast(RAGPlugin, rag_plugin)
                result = await rag_plugin.process(messages, **kwargs)

                # If the RAG plugin modified the messages, return its result
                if result.messages != messages:
                    logger.info(f"RAG plugin {rag_plugin.id} modified the messages.")
                    return result

            except Exception as e:
                logger.error(f"Error in RAG plugin {rag_plugin.id}: {e}")

        # No RAG plugin modified the messages
        return None

    def get_tool_plugins(self) -> List[ToolPlugin]:
        """Get all enabled tool plugins."""
        return [cast(ToolPlugin, p) for p in self.get_plugins_by_type(PluginType.TOOL)]

    def get_tool_definitions(self) -> List[Dict[str, Any]]:
        """Get all tool definitions for LLM function/tool calling."""
        tool_plugins = self.get_tool_plugins()
        tool_definitions = []

        for plugin in tool_plugins:
            tool_definitions.extend([t.model_dump() for t in plugin.get_tool_definitions()])

        return tool_definitions

    async def execute_tool(self, tool_name: str, parameters: Dict[str, Any], **kwargs) -> Any:
        """Execute a tool by name."""
        if not self.enabled:
            return {"error": "Plugin manager is disabled."}

        # Find the plugin that implements this tool
        for plugin in self.get_tool_plugins():
            if tool_name in plugin.tools:
                try:
                    return await plugin.execute_tool(tool_name, parameters, **kwargs)
                except Exception as e:
                    logger.error(f"Error executing tool {tool_name} in plugin {plugin.id}: {e}")
                    return {"error": str(e)}

        logger.warning(f"Tool {tool_name} not found in any plugin.")
        return {"error": f"Tool {tool_name} not found."}

    def get_ui_components(self) -> List[Dict[str, Any]]:
        """Get all UI components for frontend rendering."""
        ui_plugins = [cast(UIPlugin, p) for p in self.get_plugins_by_type(PluginType.UI)]
        components = []

        for plugin in ui_plugins:
            try:
                components.extend([c.model_dump() for c in plugin.get_components()])
            except Exception as e:
                logger.error(f"Error getting UI components from plugin {plugin.id}: {e}")

        return components

    def discover_plugins(self, package_name: str = "backend.plugins") -> None:
        """Discover and register plugins from a package."""
        try:
            package = importlib.import_module(package_name)
            for _, name, is_pkg in pkgutil.iter_modules(package.__path__, package.__name__ + "."):
                if is_pkg:
                    # Recursively discover plugins in subpackages
                    self.discover_plugins(name)
                else:
                    try:
                        # Import the module
                        module = importlib.import_module(name)

                        # Find all plugin classes in the module
                        for _, obj in inspect.getmembers(module, inspect.isclass):
                            if (
                                issubclass(obj, BasePlugin) and
                                obj is not BasePlugin and
                                obj is not PreprocessorPlugin and
                                obj is not PostprocessorPlugin and
                                obj is not CommandPlugin and
                                obj is not ToolPlugin and
                                obj is not UIPlugin and
                                obj is not AgentPlugin and
                                obj is not RAGPlugin and
                                not inspect.isabstract(obj)  # Skip abstract classes
                            ):
                                # Register the plugin
                                plugin_id = obj.__name__.lower()
                                self.register_plugin(plugin_id, obj)
                    except Exception as e:
                        logger.error(f"Error discovering plugins in module {name}: {e}")
        except Exception as e:
            logger.error(f"Error discovering plugins in package {package_name}: {e}")


# Create a global plugin manager instance
plugin_manager = PluginManager()

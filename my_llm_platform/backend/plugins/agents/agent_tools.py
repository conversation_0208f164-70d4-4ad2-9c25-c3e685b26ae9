"""
エージェントが使用するツールの定義と実装。
"""
import re
import json
import requests
from typing import Dict, List, Any, Optional, Callable, Union
from langchain.tools import BaseTool, Tool
from pydantic import BaseModel, Field
import logging

logger = logging.getLogger(__name__)


class SearchInput(BaseModel):
    """検索ツールの入力モデル。"""
    query: str = Field(description="検索クエリ")


class CodeExecutionInput(BaseModel):
    """コード実行ツールの入力モデル。"""
    code: str = Field(description="実行するPythonコード")


# class DocumentLookupInput(BaseModel):
#     """ドキュメント検索ツールの入力モデル。"""
#     keyword: str = Field(description="検索キーワード")
#     max_results: int = Field(default=3, description="返す結果の最大数")


def search_web(query: str) -> str:
    """
    ウェブ検索を実行するツール関数。

    Args:
        query: 検索クエリ

    Returns:
        検索結果の要約
    """
    try:
        # 実際の実装では外部APIを呼び出す
        # ここではモック実装
        logger.info(f"ウェブ検索を実行: {query}")
        return f"「{query}」の検索結果: Pythonはプログラミング言語です。広く使われており、シンプルな構文が特徴です。"
    except Exception as e:
        logger.error(f"検索中にエラーが発生しました: {e}")
        return f"検索中にエラーが発生しました: {e}"


def execute_python_code(code: str) -> str:
    """
    Pythonコードを安全な環境で実行するツール関数。

    Args:
        code: 実行するPythonコード

    Returns:
        実行結果
    """
    try:
        # 実際の実装では安全なサンドボックスでコードを実行
        # ここではモック実装
        logger.info(f"コードを実行: {code[:50]}...")

        # 簡単なコードパターンの結果をシミュレート
        if "print" in code:
            match = re.search(r'print\([\'"](.+?)[\'"]\)', code)
            if match:
                return f"出力: {match.group(1)}"

        if "ZeroDivisionError" in code or "/ 0" in code:
            return "エラー: ZeroDivisionError: division by zero"

        return "コードが正常に実行されました。出力はありません。"
    except Exception as e:
        logger.error(f"コード実行中にエラーが発生しました: {e}")
        return f"コード実行中にエラーが発生しました: {e}"


def lookup_documentation(keyword: str) -> str:
    """
    ドキュメントを検索するツール関数。

    Args:
        keyword: 検索キーワード

    Returns:
        ドキュメント検索結果
    """
    try:
        # 実際の実装ではドキュメントデータベースを検索
        # ここではモック実装
        logger.info(f"ドキュメント検索: {keyword}")

        # 一般的なPythonエラーに関する情報をシミュレート
        docs = {
            "zerodivisionerror": "ZeroDivisionError: ゼロによる除算が行われたときに発生します。例: x = 1/0",
            "indexerror": "IndexError: リストやタプルなどのシーケンスの範囲外のインデックスにアクセスしたときに発生します。",
            "syntaxerror": "SyntaxError: Pythonの構文に違反するコードがあるときに発生します。",
            "typeerror": "TypeError: 不適切な型のオブジェクトに対して操作が実行されたときに発生します。",
            "valueerror": "ValueError: 関数に正しい型だが不適切な値の引数が渡されたときに発生します。"
        }

        # キーワードに一致するドキュメントを検索
        if keyword.lower() in docs:
            return docs[keyword.lower()]

        # 部分一致の検索
        matches = []
        for key, value in docs.items():
            if keyword.lower() in key:
                matches.append(f"{key}: {value}")

        if matches:
            return "\n\n".join(matches[:3])  # 最大3件まで返す
        else:
            return f"キーワード '{keyword}' に一致するドキュメントは見つかりませんでした。"
    except Exception as e:
        logger.error(f"ドキュメント検索中にエラーが発生しました: {e}")
        return f"ドキュメント検索中にエラーが発生しました: {e}"


def create_analysis_tools() -> List[BaseTool]:
    """
    分析エージェント用のツールを作成します。

    Returns:
        ツールのリスト
    """
    return [
        Tool(
            name="web_search",
            func=search_web,
            description="ウェブ検索を実行して情報を取得します。質問に関連する情報を探すのに役立ちます。",
            args_schema=SearchInput
        ),
        Tool(
            name="documentation_lookup",
            func=lookup_documentation,
            description="プログラミングドキュメントを検索して、特定のキーワードに関する情報を取得します。",
            # args_schema=DocumentLookupInput
        )
    ]


def create_diagnosis_tools() -> List[BaseTool]:
    """
    診断エージェント用のツールを作成します。

    Returns:
        ツールのリスト
    """
    return [
        Tool(
            name="execute_code",
            func=execute_python_code,
            description="Pythonコードを実行して結果を取得します。コードの問題を診断するのに役立ちます。",
            args_schema=CodeExecutionInput
        ),
        Tool(
            name="documentation_lookup",
            func=lookup_documentation,
            description="プログラミングドキュメントを検索して、特定のエラーや問題に関する情報を取得します。",
            # args_schema=DocumentLookupInput
        )
    ]


def create_answer_tools() -> List[BaseTool]:
    """
    回答生成エージェント用のツールを作成します。

    Returns:
        ツールのリスト
    """
    return [
        Tool(
            name="documentation_lookup",
            func=lookup_documentation,
            description="プログラミングドキュメントを検索して、回答に含める正確な情報を取得します。",
            # args_schema=DocumentLookupInput
        )
    ]


def create_agent_tools() -> Dict[str, List[BaseTool]]:
    """
    各エージェントに必要なツールを作成します。

    Returns:
        エージェント名をキー、ツールのリストを値とする辞書
    """
    return {
        "analyze_question": create_analysis_tools(),
        "diagnose_code": create_diagnosis_tools(),
        "compose_answer": create_answer_tools()
    }

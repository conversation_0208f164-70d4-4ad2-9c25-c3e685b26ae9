"""
マルチエージェントカスタマーアシスタントのワークフロー設定。
"""
from typing import Optional, Dict, List, Any
from langchain_core.language_models import BaseChatModel
from langchain_openai import ChatOpenAI
from langgraph.graph import StateGraph, START, END
from langchain.tools import BaseTool

from .models import SupportState
from .agent_functions import create_agent_functions
from .agent_tools import create_agent_tools
from ...inference import get_client
from ...config import settings

def create_workflow(
    llm: Optional[BaseChatModel] = None,
    model_name: str = "qwen3:latest",
    temperature: float = 0,
    custom_tools: Optional[Dict[str, List[BaseTool]]] = None
) -> StateGraph:
    """
    カスタマーアシスタンス用のマルチエージェントワークフローを作成します。

    Args:
        llm: 使用する言語モデル（Noneの場合、ChatOpenAIインスタンスを作成）
        model_name: 新しいLLMを作成する場合に使用するモデル名
        temperature: LLMの温度設定
        custom_tools: カスタムツール（エージェント名をキー、ツールのリストを値とする辞書）

    Returns:
        コンパイルされたStateGraphワークフロー
    """
    # LLMが提供されていない場合は初期化
    if llm is None:
        openai_client = get_client(settings.DEFAULT_LLM_BACKEND)
        llm = ChatOpenAI(
                    model_name=settings.DEFAULT_LLM_MODEL or "llama3.2:3b",
                    openai_api_key=openai_client.api_key,
                    openai_api_base=str(openai_client.base_url)
                )
        # llm = ChatOpenAI(model=model_name, temperature=temperature)

    # ツールを設定
    tools = custom_tools
    if tools is None:
        # デフォルトのツールを使用
        tools = create_agent_tools()

    # LLMとツールを使用してエージェント関数を取得
    agent_funcs = create_agent_functions(llm, tools)

    # ワークフローを作成
    workflow = StateGraph(SupportState)

    # ノードを追加
    workflow.add_node("analyze_question", agent_funcs["analyze_question"])
    workflow.add_node("diagnose_code", agent_funcs["diagnose_code"])
    workflow.add_node("compose_answer", agent_funcs["compose_answer"])

    # エッジを追加
    workflow.add_edge(START, "analyze_question")
    workflow.add_conditional_edges(
        "analyze_question",
        lambda state: state.get("next_step", "compose_answer")
    )
    workflow.add_edge("diagnose_code", "compose_answer")
    workflow.add_edge("compose_answer", END)

    return workflow.compile()

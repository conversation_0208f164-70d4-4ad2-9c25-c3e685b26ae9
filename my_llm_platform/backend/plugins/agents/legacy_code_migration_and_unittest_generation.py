from typing import TypedDict
from langchain_openai import ChatOpenAI
from langgraph.graph import StateGraph, START, END

# モジュールとして実行される場合と直接実行される場合の両方に対応
try:
    from ...inference import get_client
    from ...config import settings
except ImportError:
    import sys
    import os
    sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), "../../../..")))
    from backend.inference import get_client
    from backend.config import settings

#ソリューション2: 中程度の複雑さ - レガシーコードの移行とユニットテストの生成

# 機能概要:
# 古いPythonコードを受け取り、最新のベストプラクティスに従ってリファクタリング
# リファクタリングされたコードに対して自動的にpytestベースの単体テストを生成
# 生成されたテストを実行し、テスト結果を報告

# ワークフロー図:
# 1. 旧コードの受け取り
# 2. LLMによるコードのリファクタリング
# 3. LLMによるテストコードの生成
# 4. テストコードの実行
# 5. テスト結果の報告

# START
#   ↓
# [rewrite_code] → 古いコードをリファクタリング
#   ↓
# [generate_tests] → リファクタリングされたコードに対するテストを生成
#   ↓
# [run_tests] → 生成されたテストを実行し結果を報告
#   ↓
# END

# 1. Stateデータ構造を定義し、プロセス内で渡される内容を含む
class CodeMigrationState(TypedDict):
    original_code: str   # ユーザーが提供する旧コード
    new_code: str        # LLMがリファクタリングした新コード
    tests: str           # LLMが生成した新コード用のテストコード
    test_results: str    # テスト実行結果の出力

# LLMを初期化（OpenAI APIキーは環境変数で設定済みと仮定）
openai_client = get_client(settings.DEFAULT_LLM_BACKEND)
llm = ChatOpenAI(
                    model_name=settings.DEFAULT_LLM_MODEL or "llama3.2:3b",
                    openai_api_key=openai_client.api_key,
                    openai_api_base=str(openai_client.base_url)
                )

# 2. 各ノードの処理関数を定義
def rewrite_code(state: CodeMigrationState) -> dict:
    """LLMを使用して旧コードをリファクタリングし、新しいコードに変換"""
    old_code = state["original_code"]
    prompt = (
        "あなたは上級Python開発者です。以下の旧コードをリファクタリングし、最新のベストプラクティスとコードアーキテクチャに従ってください。\n"
        "機能はそのままに、コードをより明確で効率的にし、PEP8規約に準拠させてください：\n\n"
        f"{old_code}\n\nリファクタリング後のコード："
    )
    try:
        response = llm.invoke(prompt).content
        return {"new_code": response.strip()}
# response.strip() の部分では、LLMからの応答をそのまま使用しているため、以下のような問題が発生する可能性があります：

# モデルがコードの代わりに説明文を返す
# コードの前後に余計な説明を追加する
# コードと説明を混在させる
# 不完全なコードや構文エラーのあるコードを生成する
# これは特に、プロンプトエンジニアリングが不十分な場合や、モデルがタスクを誤解した場合に起こりやすい問題です。
# より堅牢なソリューションとしては、返された応答からコードブロックだけを抽出するパーサーを追加するか、
# 応答の形式を検証するバリデーションステップを追加することが考えられます。

    except Exception as e:
        return {"new_code": f"エラーが発生しました: {e}"}

def generate_tests(state: CodeMigrationState) -> dict:
    """LLMを使用して新コードの単体テストを生成（pytest形式）"""
    new_code = state["new_code"]
    prompt = (
        "以下のコードに対してPython単体テストを作成してください。pytest構文を使用し、主要な機能と境界条件をカバーしてください：\n\n"
        f"{new_code}\n\n# テストコードのみを記載し、説明は不要です。"
    )
    try:
        tests_code = llm.invoke(prompt).content
        return {"tests": tests_code.strip()}
    except Exception as e:
        return {"tests": f"エラーが発生しました: {e}"}

def run_tests(state: CodeMigrationState) -> dict:
    """生成されたテストコードを実行し、テストの合格/不合格の結果を返す"""
    import io, contextlib
    new_code = state["new_code"]
    tests_code = state["tests"]
    # コードを実行するための独立したグローバル名前空間を準備し、汚染を防ぐ
    namespace = {}
    output = io.StringIO()
    result_summary = ""
    try:
        # 新コードの定義を実行（主に関数やクラスの定義であり、即時実行されないと仮定）
        exec(new_code, namespace)
        # テストコードを実行
        with contextlib.redirect_stdout(output):
            exec(tests_code, namespace)
        # AssertionErrorがなければ、すべて合格とみなす
        result_summary = "✅ すべての単体テストに合格"
    except AssertionError as e:
        result_summary = f"❌ テストに不合格: {e}"
    except Exception as e:
        result_summary = f"❌ テスト実行中にエラー: {e}"
    # テストプロセスの標準出力をキャッチ
    logs = output.getvalue()
    full_report = result_summary
    if logs:
        full_report += "\nテスト出力:\n" + logs
    return {"test_results": full_report}

# 3-1. ワークフローグラフを構築
def build_workflow():
    """ワークフローグラフを構築して返す"""
    workflow = StateGraph(CodeMigrationState)
    workflow.add_node("rewrite_code", rewrite_code)
    workflow.add_node("generate_tests", generate_tests)
    workflow.add_node("run_tests", run_tests)
    
    # 順次エッジを追加: Start -> コードリファクタリング -> テスト生成 -> テスト実行 -> End
    workflow.add_edge(START, "rewrite_code")
    workflow.add_edge("rewrite_code", "generate_tests")
    workflow.add_edge("generate_tests", "run_tests")
    workflow.add_edge("run_tests", END)
    return workflow.compile()

# 人間の確認ノードの実装
def human_check_function(state: CodeMigrationState) -> dict:
    """
    リファクタリングされたコードを人間が確認し、承認、拒否、または修正するためのノード。
    LangGraphのinterruptを使用して一時停止を実現します。

    引数:
        state: 現在のグラフの状態

    戻り値:
        更新された状態の辞書

    処理の流れ:
    1. 承認の場合 (True): 変更なし（リファクタリングされたコードをそのまま使用）
    2. 拒否の場合 (False): 元のコードを使用
    3. 修正の場合 (文字列): 修正されたコードを使用
    """
    from langgraph.types import interrupt

    # 人間に表示する情報を準備
    display_info = {
        "original_code": state["original_code"],
        "new_code": state["new_code"],
        "message": "リファクタリングされたコードを確認してください。承認、拒否、または修正できます。"
    }

    # interruptを使用してグラフを一時停止し、人間の入力を待つ
    human_response = interrupt(display_info)

    # 人間の応答を処理
    if isinstance(human_response, dict):
        # 辞書形式の応答の場合
        if "action" in human_response and "code" in human_response:
            action = human_response["action"]
            code = human_response["code"]

            if action == "approve":
                # 承認（変更なし）
                return {}
            elif action == "modify":
                # コードを修正
                return {"new_code": code}
            elif action == "reject":
                # コードを拒否し、元のコードを使用
                return {"new_code": state["original_code"]}
    elif isinstance(human_response, str):
        # 文字列の場合、修正されたコードとして扱う
        return {"new_code": human_response}
    elif isinstance(human_response, bool):
        # ブール値の場合
        if human_response:
            # True=承認 - 変更なし
            return {}
        else:
            # False=拒否 - 元のコードを使用
            return {"new_code": state["original_code"]}

    # デフォルトでは変更なし
    return {}

# 3-2. ワークフローグラフを構築（オプション）人間の確認後にテストを実行するメカニズム：
def build_workflow_human_check():
    """ワークフローグラフを構築して返す"""
    workflow = StateGraph(CodeMigrationState)
    workflow.add_node("rewrite_code", rewrite_code)
    workflow.add_node("generate_tests", generate_tests)
    workflow.add_node("human_check", human_check_function)
    workflow.add_node("run_tests", run_tests)
    # 順次エッジを追加: Start -> コードリファクタリング -> テスト生成 -> テスト実行 -> End
    workflow.add_edge(START, "rewrite_code")
    workflow.add_edge("rewrite_code", "human_check")
    workflow.add_edge("human_check", "generate_tests")
    workflow.add_edge("generate_tests", "run_tests")
    workflow.add_edge("run_tests", END)
    # チェックポインターを設定（interruptを使用するために必要）
    from langgraph.checkpoint.memory import MemorySaver
    checkpointer = MemorySaver()
    return workflow.compile(checkpointer=checkpointer)

# 4. 使用例
def run_example(agent):
    """サンプルコードを実行する関数"""
    original_code = '''
    # 旧コードの例：階乗を計算するが、記述が不適切
    def fact(n):
        # 再帰を使用して階乗を計算
        if n == 0 or n == 1:
            return 1
        return n*fact(n-1)
    print(fact(5))
    '''
    state = agent.invoke({"original_code": original_code})
    print("=== リファクタリング後の新コード ===")
    print(state["new_code"])
    print("=== 生成されたテストコード ===")
    print(state["tests"])
    print("=== テスト結果 ===")
    print(state["test_results"])

# 5. 人間の確認を含む使用例
def run_example_with_human_check(agent_with_human_check):
    """人間の確認を含むサンプルコードを実行する関数"""
    import uuid
    from langgraph.types import Command

    original_code = '''
    # 旧コードの例：階乗を計算するが、記述が不適切
    def fact(n):
        # 再帰を使用して階乗を計算
        if n == 0 or n == 1:
            return 1
        return n*fact(n-1)
    print(fact(5))
    '''

    # スレッドIDを生成（必須）
    thread_id = str(uuid.uuid4())
    thread_config = {"configurable": {"thread_id": thread_id}}

    # グラフを実行し、人間の確認で一時停止
    print("=== グラフを実行中... ===")
    
    # 中断回数を追跡するカウンター
    interrupt_count = 0
    # 最初の実行（リファクタリングまで）
    for chunk in agent_with_human_check.stream({"original_code": original_code}, config=thread_config):
        if "__interrupt__" in chunk:
            # 中断カウントをインクリメント
            interrupt_count += 1
            
            # 人間の確認が必要な場合
            interrupt_info = chunk["__interrupt__"][0]
            display_info = interrupt_info.value
            # 1回目の中断処理
            if interrupt_count == 1:
                print("\n=== 人間の確認が必要です ===")
                print("元のコード:")
                print(display_info["original_code"])
                print("\nリファクタリング後のコード:")
                print(display_info["new_code"])
                print("\n" + display_info["message"])

                # 実際のアプリケーションでは、ユーザーからの入力を受け取る
                print("\n=== 操作を選択してください ===")
                print("1. 承認（リファクタリングされたコードをそのまま使用）")
                print("2. 拒否（元のコードを使用）")
                print("3. 修正（リファクタリングされたコードを修正）")

                choice = input("選択（1-3）: ")

                if choice == "1":
                    # ケース1: 承認 - リファクタリングされたコードをそのまま使用
                    print("リファクタリングされたコードを承認しました。")
                    human_decision = True
                elif choice == "2":
                    # ケース2: 拒否 - 元のコードを使用
                    print("リファクタリングを拒否しました。元のコードを使用します。")
                    human_decision = False

                    # print("リファクタリングを拒否しました。プログラムを終了します。")
                    # import sys
                    # sys.exit(0)  # プログラム全体を終了
                elif choice == "3":
                    # ケース3: コード修正 - ユーザーが修正したコードを使用
                    print("リファクタリングされたコードを修正します。")
                    print("修正後のコードを入力してください（入力終了後、[q!]を入力）：")

                    # 複数行の入力を受け取る
                    modified_code = ""
                    while True:
                        line = input()
                        if line.strip() == "q!":
                            break
                        modified_code += line + "\n"

                    print(f"修正されたコード（{len(modified_code)}バイト）を使用します。")
                    # 修正されたコードを使用
                    human_decision = modified_code
                else:
                    print("無効な選択です。デフォルトで承認します。")
                    human_decision = True

                # グラフを再開して完了まで実行
                print("\n=== グラフを再開します ===")

                # # 部分的な実行を継続（次の中断まで）
                # for next_chunk in agent_with_human_check.stream(Command(resume=human_decision), config=thread_config):
                #     if "__interrupt__" in next_chunk:
                #         # 2回目の中断に到達
                #         break
                            # 2回目の中断処理
                # elif interrupt_count == 2:


            # 完全な状態を取得するために invoke を使用
            try:
                final_state = agent_with_human_check.invoke(Command(resume=human_decision), config=thread_config)

                # 最終結果を表示
                print("\n=== 最終結果 ===")
                print("リファクタリング後の新コード:")
                print(final_state.get("new_code", "情報がありません"))
                print("\n生成されたテストコード:")
                print(final_state.get("tests", "情報がありません"))
                print("\nテスト結果:")
                print(final_state.get("test_results", "情報がありません"))
            except Exception as e:
                print(f"\n=== エラーが発生しました: {e} ===")
                print("グラフの実行中にエラーが発生しました。")

            # 処理完了
            return

    # interruptが発生しなかった場合
    print("\n=== 人間の確認が必要なポイントに到達しませんでした ===")
    return

if __name__ == "__main__":
    import sys

    # コマンドライン引数で実行モードを指定できるようにする
    # デフォルトは人間の確認なし
    if len(sys.argv) > 1 and sys.argv[1] == "--human-check":
        print("人間の確認を含むモードで実行します...")
        # 人間の確認を含むワークフローをコンパイル（オプション）
        agent_with_human_check = build_workflow_human_check()
        run_example_with_human_check(agent_with_human_check)
    else:
        print("通常モードで実行します（人間の確認なし）...")
        # ワークフローをコンパイル（デフォルトは人間の確認なし）
        agent = build_workflow()
        run_example(agent)
# python -m backend.plugins.agents.legacy_code_migration_and_unittest_generation --human-check

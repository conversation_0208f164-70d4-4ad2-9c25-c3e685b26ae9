"""
マルチエージェントカスタマーアシスタントの状態とモデル定義。
"""
from typing import TypedDict, Literal, Optional, Dict, Any, List


class SupportState(TypedDict):
    """カスタマーサポートワークフローでエージェント間で共有される状態。"""
    question: str        # ユーザーの質問
    code: str            # ユーザーが提供したコード（存在しない場合は空文字列）
    analysis: str        # 問題の分析/理解
    diagnosis: str       # コード問題の診断結果
    answer: str          # ユーザーへの最終回答
    next_step: Optional[str]  # 次のステップを示す
    tool_results: Dict[str, Any]  # ツールの実行結果を保存

"""
マルチエージェントカスタマーアシスタントの使用例。
"""
import sys
import os
import logging

# プロジェクトルートをPythonパスに追加
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

from .multi_agent_customer_assistant import MultiAgentCustomerAssistant

from .agent_tools import create_agent_tools

# 必要なツールを作成
custom_tools = create_agent_tools()
diagnosis_tools = {
    "diagnose_code": custom_tools["diagnose_code"]
}

# サンプルコードと質問
user_code = '''
def divide(a, b):
    return a/b

print(divide(10, 0))
'''
question2 = "上記のコードを実行するとエラーが発生しました。原因は何ですか？修正方法を教えてください。"

# ロギングの設定
logging.basicConfig(
    level=logging.WARNING, # DEBUG,  # DEBUGレベルに変更して詳細なログを表示
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def main():
    """
    マルチエージェントカスタマーアシスタントの使用例。
    """

    # 4. ストリーミング形式でのカスタムアシスタントの使用例

    logger.info("ストリーミング形式でのカスタムアシスタントの使用1...\n")

    # 特定のツールを使用するアシスタントを初期化
    # 例: 検索ツールのみを使用
    search_tools = {
        "analyze_question": [tool for tool in custom_tools["analyze_question"] if tool.name == "web_search"]
    }

    streaming_assistant = MultiAgentCustomerAssistant(custom_tools=search_tools)

    # ストリーミング形式で質問を処理
    question3 = "Pythonの非同期処理について教えてください"
    logger.info(f"ストリーミング質問: {question3}")

    # ストリーミングレスポンスを処理
    print("\nストリーミングレスポンス:\n ", end="", flush=True)
    for chunk in streaming_assistant.stream(question=question3, stream_mode="values"):
        if "answer" in chunk:
            # 回答部分のみを表示
            print(chunk["answer"], end="", flush=True)
    print("\n")

    # コード診断のストリーミング例
    logger.info("コード診断のストリーミング例2...\n")

    # 診断ツールのみを使用するストリーミングアシスタント
    streaming_diagnosis_assistant = MultiAgentCustomerAssistant(custom_tools=diagnosis_tools)

    # ストリーミング形式でコード診断を処理
    print("\nコード診断ストリーミングレスポンス: ", end="", flush=True)
    for chunk in streaming_diagnosis_assistant.stream(question=question2, code=user_code, stream_mode="values"):
        if "answer" in chunk:
            # 回答部分のみを表示
            print(chunk["answer"], end="", flush=True)
    print("\n")

    # 5. 高度なストリーミング処理の例
    logger.info("高度なストリーミング処理の例3...")

    # すべてのツールを使用するアシスタント
    advanced_streaming_assistant = MultiAgentCustomerAssistant(custom_tools=custom_tools)

    # 高度なストリーミング処理
    question4 = "このPythonコードを最適化してください: for i in range(10): print(i)"
    logger.info(f"高度なストリーミング質問: {question4}")

    print("\n高度なストリーミング処理:")

    # デバッグ情報を表示するフラグ
    debug_mode = True

    # ストリーミングモードを「values」に変更して処理
    for chunk in advanced_streaming_assistant.stream(question=question4, stream_mode="values"):
        # デバッグモードの場合、受信したチャンクの内容を表示
        if debug_mode:
            # チャンクの内容をログに出力
            logger.debug(f"受信チャンク: {chunk}")

            # チャンクに含まれるキーを表示
            if chunk:
                print(f"\nチャンクキー: {', '.join(chunk.keys())}")

        # 分析結果を処理
        if "analysis" in chunk:
            print(f"\n分析結果: {chunk['analysis'][:100]}...")  # 最初の100文字のみ表示

        # 診断結果を処理
        if "diagnosis" in chunk:
            print(f"\n診断結果: {chunk['diagnosis'][:100]}...")  # 最初の100文字のみ表示

        # ツール結果を処理
        if "tool_results" in chunk:
            print("\nツール実行結果:")
            for tool_name, result in chunk["tool_results"].items():
                print(f"- {tool_name}: {result[:50]}...")  # 最初の50文字のみ表示

        # 最終回答を処理
        if "answer" in chunk:
            print(f"\n回答: {chunk['answer']}", end="", flush=True)

    print("\n--- 処理完了 ---\n")

    # 5.1 別のストリーミングモードを試す
    logger.info("別のストリーミングモードでの処理...")

    # 同じ質問で別のストリーミングモードを試す
    print("\n別のストリーミングモード (deltas) での処理:")

    # ストリーミングモードを「deltas」に変更して処理
    for chunk in advanced_streaming_assistant.stream(question=question4, stream_mode="deltas"):
        # チャンクの内容をログに出力
        logger.debug(f"デルタチャンク: {chunk}")

        # チャンクに何かしらの内容があれば表示
        if chunk:
            # チャンクのキーを表示
            keys = list(chunk.keys())
            if keys:
                # 最初のキーの値を表示
                key = keys[0]
                if chunk[key]:
                    print(f"{key}: {chunk[key]}", end="", flush=True)

    print("\n--- 処理完了 ---\n")

    # 6. カスタムツールとストリーミングの組み合わせ例
    logger.info("カスタムツールとストリーミングの組み合わせ例...")

    # カスタムツールを定義（実際のプロジェクトでは別ファイルに定義することを推奨）
    from langchain.tools import Tool

    def custom_data_processor(query: str) -> str:
        """
        カスタムデータ処理ツール。

        Args:
            query: 処理するクエリ

        Returns:
            処理結果
        """
        logger.info(f"カスタムデータ処理: {query}")
        # 実際のプロジェクトでは、ここで実際のデータ処理を行う
        return f"「{query}」の処理結果: データ処理が完了しました。"

    # カスタムツールを作成
    custom_processor_tool = Tool(
        name="custom_data_processor",
        func=custom_data_processor,
        description="特定のデータ処理タスクを実行します。"
    )

    # カスタムツールを使用するアシスタントを初期化
    custom_tools_config = {
        "analyze_question": [custom_processor_tool],
        "compose_answer": [custom_processor_tool]
    }

    custom_streaming_assistant = MultiAgentCustomerAssistant(custom_tools=custom_tools_config)

    # カスタムツールを使用したストリーミング処理
    question5 = "大量のデータを処理する方法を教えてください"
    logger.info(f"カスタムツール質問: {question5}")

    print("\nカスタムツールストリーミングレスポンス:")

    # ストリーミングモードを指定して処理
    for chunk in custom_streaming_assistant.stream(question=question5, stream_mode="values"):
        if "answer" in chunk:
            print(chunk["answer"], end="", flush=True)

    print("\n\n--- すべての例が完了しました ---\n")


if __name__ == "__main__":
    main()

# python -m backend.plugins.agents.multi_agent_stream_example

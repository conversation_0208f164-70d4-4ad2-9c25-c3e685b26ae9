"""
質問分析とコード診断のためのマルチエージェントカスタマーアシスタント。
"""
from typing import Optional, Dict, List, Any
from langchain_core.language_models import BaseChatModel
from langchain.tools import BaseTool

# ローカルモジュールからインポート
from .models import SupportState
from .workflow import create_workflow


class MultiAgentCustomerAssistant:
    """
    質問を分析し、コードの問題を診断し、回答を作成するマルチエージェントシステム。
    """

    def __init__(
        self,
        llm: Optional[BaseChatModel] = None,
        model_name: str = "qwen3:latest",
        temperature: float = 0,
        custom_tools: Optional[Dict[str, List[BaseTool]]] = None
    ):
        """
        マルチエージェントカスタマーアシスタントを初期化します。

        Args:
            llm: 使用する言語モデル（Noneの場合、ChatOpenAIインスタンスを作成）
            model_name: 新しいLLMを作成する場合に使用するモデル名
            temperature: LLMの温度設定
            custom_tools: カスタムツール（エージェント名をキー、ツールのリストを値とする辞書）
        """
        self.agent = create_workflow(
            llm=llm,
            model_name=model_name,
            temperature=temperature,
            custom_tools=custom_tools
        )

    def invoke(self, question: str, code: str = "") -> str:
        """
        質問とオプションのコードスニペットを処理して回答を生成します。

        Args:
            question: ユーザーの質問
            code: ユーザーが提供したオプションのコードスニペット

        Returns:
            アシスタントの回答
        """
        state = self.agent.invoke({
            "question": question,
            "code": code,
            "tool_results": {}  # ツール結果の初期化
        })
        return state["answer"]

    def stream(self, question: str, code: str = "", stream_mode: str = "values"):
        """
        質問とオプションのコードスニペットに対する応答をストリーミングします。

        Args:
            question: ユーザーの質問
            code: ユーザーが提供したオプションのコードスニペット
            stream_mode: 使用するストリームモード（デフォルト: "values"）

        Yields:
            生成された応答のチャンク
        """
        for chunk in self.agent.stream(
            {"question": question, "code": code, "tool_results": {}},
            stream_mode=stream_mode
        ):
            yield chunk


# 使用例は examples/multi_agent_example.py を参照してください

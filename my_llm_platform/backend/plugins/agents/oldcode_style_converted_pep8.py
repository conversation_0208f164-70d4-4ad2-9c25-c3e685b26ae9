import autopep8  # autopep8をインストールしてください: pip install autopep8
from langchain_core.tools import tool
from langchain_openai import ChatOpenAI
from langgraph.prebuilt import ToolNode
from langgraph.graph import StateGraph, MessagesState, START, END
from langchain_core.prompts import ChatPromptTemplate, MessagesPlaceholder
from langchain_core.messages import HumanMessage, SystemMessage
from ...inference import get_client
from ...config import settings
from ...utils.response_parser import ResponseParser

# ソリューション21：低難度 — 古いコードスタイルをPEP8準拠に変換する
# 目的：
# 古いスタイルのPythonコードを、自動的に現在のPEP8標準に準拠したフォーマットに変換する。

# 設計方針：
# コードスタイル変換を実現するため、フォーマッタツールと連携したLLMチャットエージェントを使用。
# エージェントはユーザーが提供した旧コードを入力として受け取り、フォーマットツールを呼び出すかどうかを自律的に判断する。

# 全体フロー：
# LangGraphにより最もシンプルなワークフローを構築。
# •	1つのLLM決定ノードと
# •	1つのツール実行ノードで構成される。
# この2ノードがループしながらタスクを完了する。

# ________________________________________
# アーキテクチャの特徴：
# •	ノード設計：
# LLMノードとツールノードの2つで構成。
# o	LLMノードは、ユーザーからのコード入力を解析し、フォーマットの必要性を判断。
# o	ツールノードは、autopep8ライブラリを使って実際のコードフォーマット処理を行う。

# •	ツール統合：
# LangChainが提供するツールラッパー機能を活用し、autopep8の整形機能をAgent用ツールとして登録。
# これにより、LangGraph内のToolNodeからLLMがこの整形ツールを呼び出すことが可能になる。

# •	ワークフローフロー：
# Start → LLMがフォーマットの必要性を判断 → ToolNodeが整形を実行 → LLMに結果を返す → LLMが最終整形コードを出力
# 本ユースケースでは、すべてのコードが変換対象となるため、毎回ツールを呼び出して処理を終了する単純な直列フローで十分であり、分岐処理は不要。
# •	記憶メカニズム：
# LangGraph標準のMessagesStateを使用し、会話メッセージ（短期記憶）を保持。
# これにより、ユーザーコードとフォーマット結果の間のデータ受け渡しが、単一の対話内で実現可能。
# 今回の一回限りの処理タスクには、長期記憶は不要。

# •	拡張性：
# このAgent構成は非常にシンプルで、機能追加が容易。
# たとえば：
# o	コード解析ノードの追加
# o	文法チェックツールの統合
# o	LLMプロンプトの最適化
# などによって強化可能。
# 実プロダクトの観点から見ても、本モジュールは「コードスタイル変換サービス」として独立利用でき、柔軟に拡張できる。

    # +-------+     +-----+     +-------+
    # | START | --> | LLM | --> |  END  |
    # +-------+     +-----+     +-------+
    #                 ^  |
    #                 |  v
    #                 |  |
    #              +--------+
    #              | tools  |
    #              +--------+


# ツール定義
@tool
def format_code(code: str) -> str:
    """autopep8 を使用してコードをPEP8スタイルにフォーマットする"""
    try:
        return autopep8.fix_code(code)
    except Exception as e:
        raise ValueError(f"フォーマットに失敗しました: {e}")

# LLM初期化
def initialize_llm():
    """LLMとツールの初期化"""
    tools = [format_code]
    openai_client = get_client(settings.DEFAULT_LLM_BACKEND)
    llm = ChatOpenAI(
                    model_name=settings.DEFAULT_LLM_MODEL or "llama3.2:3b",
                    openai_api_key=openai_client.api_key,
                    openai_api_base=str(openai_client.base_url)
                )
    # llm = ChatOpenAI(model="gpt-3.5-turbo-0613", temperature=0)
    return llm.bind_tools(tools), ToolNode(tools)

# プロンプトテンプレートの作成
def create_prompt_template():
    """構造化された出力を促すプロンプトテンプレートを作成"""
    system_template = """あなたはPythonコードのフォーマット専門家です。
ユーザーから提供されたコードをPEP8スタイルに変換してください。

出力形式:
1. まず、<think>タグ内で思考プロセスを詳細に記述してください。
2. 次に、フォーマット後のコードを```python```コードブロック内に記述してください。
3. 最後に、「変更点：」という見出しの後に、番号付きリストで変更内容を説明してください。

例:
<think>
このコードはPEP8に準拠していません。関数名はスネークケースにし、演算子の前後にスペースを入れる必要があります。
</think>

PEP8スタイルにフォーマットしたコードです：

```python

```

変更点：

"""
    
    prompt = ChatPromptTemplate.from_messages([
        ("system", system_template),
        MessagesPlaceholder(variable_name="chat_history"),
        ("human", "{input}")
    ])
    
    return prompt

# エージェントの意思決定関数
def call_model(state: MessagesState, llm_with_tools, prompt_template):
    """LLM意思決定ノード：対話メッセージを読み取り、ツールがバインドされたLLMを呼び出す"""
    try:
        messages = state["messages"]
        
        # 最後のユーザーメッセージを取得
        last_user_message = None
        for msg in reversed(messages):
            if msg.type == "human":
                last_user_message = msg.content
                break
        
        if last_user_message:
            # プロンプトテンプレートを使用してメッセージを整形
            formatted_messages = prompt_template.format_messages(
                chat_history=messages[:-1] if len(messages) > 1 else [],
                input=last_user_message
            )
            response = llm_with_tools.invoke(formatted_messages)
            return {"messages": messages[:-1] + [response]}
        else:
            # ユーザーメッセージがない場合はそのまま処理
            response = llm_with_tools.invoke(messages)
            return {"messages": [response]}
    except Exception as e:
        raise RuntimeError(f"LLM呼び出し中にエラーが発生しました: {e}")

def call_tools(state: MessagesState):
    """前のステップでLLMがツール呼び出しを要求したかどうかを判断"""
    try:
        last_message = state["messages"][-1]
        if last_message.tool_calls:
            return "tools"
        return END
    except Exception as e:
        raise RuntimeError(f"ツール呼び出しの判断中にエラーが発生しました: {e}")

# ワークフロー構築
def build_workflow():
    """LangGraph ワークフローを構築"""
    llm_with_tools, tool_node = initialize_llm()
    prompt_template = create_prompt_template()
    
    workflow = StateGraph(MessagesState)
    workflow.add_node("LLM", lambda state: call_model(state, llm_with_tools, prompt_template))
    workflow.add_node("tools", tool_node)
    
    workflow.add_edge(START, "LLM")
    workflow.add_conditional_edges("LLM", call_tools)
    workflow.add_edge("tools", "LLM")
    return workflow.compile()

# 使用例
if __name__ == "__main__":
    old_code = """
    def addNumbers(x,y):
        total=x+y
        print(total1)
        return total1
    """
    try:
        agent = build_workflow()
        
        # LangChainのメッセージ形式を使用
        human_message = HumanMessage(content=f"以下のコードをPEP8にフォーマットしてください:\n{old_code}")
        result_state = agent.invoke({"messages": [human_message]})
        
        # LLMの生成結果を取得
        raw_response = result_state["messages"][-1].content
        print("\nLLMの生成結果:\n", raw_response)
        
        
        # 生成結果を構造化
        parsed_result = ResponseParser.parse_response(raw_response)
        
        # 構造化された結果を表示
        print("\n変換後のコード:\n", parsed_result["code"])
        print("\n説明:\n", parsed_result["explanation"])
        
        # if parsed_result["changes"]:
        #     print("\n変更点:")
        #     for i, change in enumerate(parsed_result["changes"], 1):
        #         print(f"{i}. {change}")
        
        print("\n思考:\n", parsed_result["thinking"])

    except Exception as e:
        print(f"エラーが発生しました: {e}")
        
# python -m backend.plugins.agents.oldcode_style_converted_pep8

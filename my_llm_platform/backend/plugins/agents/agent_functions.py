"""
マルチエージェントカスタマーアシスタントのエージェント関数。
"""
from typing import Dict, Any, List, Optional
from langchain_core.language_models import BaseChatModel
from langchain.tools import BaseTool

from .models import SupportState


def analyze_question(state: SupportState, llm: BaseChatModel, tools: Optional[List[BaseTool]] = None) -> dict:
    """
    サブAgent1: ユーザーの質問を分析し、コード診断が必要かどうかを判断

    Args:
        state: 現在の状態
        llm: 使用する言語モデル
        tools: 使用可能なツールのリスト

    Returns:
        更新された状態の一部
    """
    user_question = state["question"]
    code_snippet = state.get("code", "").strip()
    tool_results = {}

    # ツールが提供されている場合、必要に応じて使用
    if tools:
        for tool in tools:
            if tool.name == "web_search" and not code_snippet:
                # コードがない場合は検索を使用して情報を収集
                tool_result = tool.run({"query": user_question})
                tool_results["web_search"] = tool_result

    # ツール結果を含めたプロンプトを作成
    tool_info = ""
    if tool_results:
        tool_info = "\n\n検索結果:\n" + "\n".join([f"{k}: {v}" for k, v in tool_results.items()])

    prompt = (
        f"ユーザーの質問: {user_question}\n"
        + ("ユーザーが以下のコードスニペットを提供しました。このコードに潜在する問題を判断してください。\n"
           f"{code_snippet}\n" if code_snippet else
           "この質問にはコードが提供されていません。ユーザーの意図を分析し、一般的な質問かコードデバッグが必要かを判断してください。")
        + tool_info
    )

    analysis_result = llm.invoke(prompt).content

    return {
        "analysis": analysis_result.strip(),
        "next_step": "diagnose_code" if code_snippet else "compose_answer",
        "tool_results": tool_results
    }


def diagnose_code(state: SupportState, llm: BaseChatModel, tools: Optional[List[BaseTool]] = None) -> dict:
    """
    サブAgent2: ユーザーが提供したコードのエラーを診断

    Args:
        state: 現在の状態
        llm: 使用する言語モデル
        tools: 使用可能なツールのリスト

    Returns:
        更新された状態の一部
    """
    code_snippet = state["code"]
    tool_results = state.get("tool_results", {})

    # ツールが提供されている場合、必要に応じて使用
    if tools:
        for tool in tools:
            if tool.name == "execute_code":
                # コードを実行してエラーを確認
                execution_result = tool.run({"code": code_snippet})
                tool_results["code_execution"] = execution_result
            elif tool.name == "documentation_lookup" and "ZeroDivisionError" in code_snippet:
                # エラーに関するドキュメントを検索
                doc_result = tool.run("zerodivisionerror")
                tool_results["documentation"] = doc_result

    # ツール結果を含めたプロンプトを作成
    tool_info = ""
    if "code_execution" in tool_results or "documentation" in tool_results:
        tool_info = "\n\nツール実行結果:\n"
        if "code_execution" in tool_results:
            tool_info += f"コード実行: {tool_results['code_execution']}\n"
        if "documentation" in tool_results:
            tool_info += f"ドキュメント: {tool_results['documentation']}\n"

    prompt = (
        "あなたは熟練したPythonエンジニアです。\n"
        f"ユーザーが提供したコードは以下の通りです:\n{code_snippet}\n\n"
        "このコードに潜在するエラーや例外の原因を詳しく調べ、診断結果を提供してください。"
        + tool_info
    )

    diagnosis_result = llm.invoke(prompt).content

    # 既存のツール結果を保持しながら新しい結果を追加
    updated_tool_results = {**state.get("tool_results", {}), **tool_results}

    return {
        "diagnosis": diagnosis_result.strip(),
        "tool_results": updated_tool_results
    }


def compose_answer(state: SupportState, llm: BaseChatModel, tools: Optional[List[BaseTool]] = None) -> dict:
    """
    サブAgent3: 分析と診断結果に基づいて最終回答を生成

    Args:
        state: 現在の状態
        llm: 使用する言語モデル
        tools: 使用可能なツールのリスト

    Returns:
        更新された状態の一部
    """
    question = state["question"]
    analysis = state.get("analysis", "")
    diagnosis = state.get("diagnosis", "")
    tool_results = state.get("tool_results", {})

    # ツールが提供されている場合、必要に応じて使用
    if tools:
        for tool in tools:
            if tool.name == "documentation_lookup" and diagnosis and "ZeroDivisionError" in diagnosis:
                # 解決策のためのドキュメントを検索
                doc_result = tool.run("zerodivisionerror")
                tool_results["solution_docs"] = doc_result

    # ツール結果を含めたプロンプトを作成
    tool_info = ""
    if tool_results:
        tool_info = "\n\n参考情報:\n"
        for key, value in tool_results.items():
            if key not in ["web_search", "code_execution", "documentation"]:  # 既に使用済みの結果は除外
                tool_info += f"{key}: {value}\n"

    prompt = (
        f"ユーザーの質問: {question}\n"
        f"分析: {analysis}\n"
        + (f"診断: {diagnosis}\n\n上記の分析と診断結果に基づき、専門的かつ分かりやすい言葉でユーザーに回答し、具体的な解決策を提案してください。"
           if diagnosis else
           "\n上記の分析に基づき、簡潔で明確な言葉でユーザーの質問に回答してください。")
        + tool_info
    )

    final_answer = llm.invoke(prompt).content

    return {"answer": final_answer.strip()}


# Factory functions to create agent functions with bound LLM and tools
def create_agent_functions(llm: BaseChatModel, tools: Optional[Dict[str, List[BaseTool]]] = None) -> Dict[str, Any]:
    """
    LLMとツールを使用してエージェント関数を作成します。

    Args:
        llm: 使用する言語モデル
        tools: エージェント名をキー、ツールのリストを値とする辞書

    Returns:
        エージェント関数の辞書
    """
    if tools is None:
        tools = {}

    return {
        "analyze_question": lambda state: analyze_question(
            state, llm, tools=tools.get("analyze_question")
        ),
        "diagnose_code": lambda state: diagnose_code(
            state, llm, tools=tools.get("diagnose_code")
        ),
        "compose_answer": lambda state: compose_answer(
            state, llm, tools=tools.get("compose_answer")
        )
    }

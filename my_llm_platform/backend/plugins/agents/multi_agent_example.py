"""
マルチエージェントカスタマーアシスタントの使用例。
"""
import sys
import os
import logging

# プロジェクトルートをPythonパスに追加
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

from .multi_agent_customer_assistant import MultiAgentCustomerAssistant
from .agent_tools import create_agent_tools

# ロギングの設定
logging.basicConfig(
    level=logging.DEBUG,  # DEBUGレベルに変更して詳細なログを表示
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def main():
    """
    マルチエージェントカスタマーアシスタントの使用例。
    """
    logger.info("マルチエージェントカスタマーアシスタントの初期化...")

    # 1. デフォルトのアシスタント（ツールなし）
    assistant = MultiAgentCustomerAssistant()

    # 例1: コードなしの一般的な質問
    question1 = "PythonでCSVファイルを読み取る方法は？"
    logger.info(f"質問1: {question1}")

    answer1 = assistant.invoke(question=question1)
    logger.info(f"回答1: {answer1}")

    # 例2: コード診断
    user_code = '''
    def divide(a, b):
        return a/b

    print(divide(10, 0))
    '''
    question2 = "上記のコードを実行するとエラーが発生しました。原因は何ですか？修正方法を教えてください。"
    logger.info(f"質問2: {question2}")
    logger.info(f"コード: {user_code}")

    answer2 = assistant.invoke(question=question2, code=user_code)
    logger.info(f"回答2: {answer2}")

    # 2. カスタムツールを使用するアシスタント
    logger.info("ツール付きマルチエージェントカスタマーアシスタントの初期化...")

    # ツールを作成
    custom_tools = create_agent_tools()

    # ツール付きアシスタントを初期化
    assistant_with_tools = MultiAgentCustomerAssistant(custom_tools=custom_tools)

    # 同じ質問でツール付きアシスタントを使用
    logger.info(f"ツール付きアシスタントへの質問: {question2}")

    answer3 = assistant_with_tools.invoke(question=question2, code=user_code)
    logger.info(f"ツール付きアシスタントの回答: {answer3}")

    # 3. 特定のツールだけを使用するカスタムアシスタント
    logger.info("カスタムツール付きアシスタントの初期化...")

    # 診断ツールのみを使用
    diagnosis_tools = {
        "diagnose_code": custom_tools["diagnose_code"]
    }

    # カスタムツール付きアシスタントを初期化
    custom_assistant = MultiAgentCustomerAssistant(custom_tools=diagnosis_tools)

    # カスタムツール付きアシスタントを使用
    logger.info(f"カスタムツール付きアシスタントへの質問: {question2}")

    answer4 = custom_assistant.invoke(question=question2, code=user_code)
    logger.info(f"カスタムツール付きアシスタントの回答: {answer4}")   

    print("\n\n--- すべての例が完了しました ---\n")


if __name__ == "__main__":
    main()

# python -m backend.plugins.agents.multi_agent_example

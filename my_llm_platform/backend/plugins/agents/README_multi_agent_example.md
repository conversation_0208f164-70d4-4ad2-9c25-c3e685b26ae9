# マルチエージェントシステム multi_agent_example.py

このディレクトリには、質問分析、コード診断、回答生成を行うマルチエージェントシステムの実装が含まれています。

## システム概要

このマルチエージェントシステムは、以下の3つの主要なエージェントで構成されています：

1. **質問分析エージェント** - ユーザーの質問を分析し、コード診断が必要かどうかを判断
2. **コード診断エージェント** - ユーザーが提供したコードの問題を診断
3. **回答生成エージェント** - 分析と診断結果に基づいて最終回答を生成

各エージェントは特定のツールを使用して、より正確で有用な情報を提供します。

## 機能説明と適用ガイド

### 1. 質問分析エージェント (analyze_question)

#### 機能説明
質問分析エージェントは、ユーザーの質問を理解し、コード診断が必要かどうかを判断します。このエージェントは以下の処理を行います：
- ユーザーの質問文を解析して意図を把握
- コードスニペットが提供されているかを確認
- 必要に応じてウェブ検索ツールを使用して関連情報を収集
- 分析結果と次のステップ（診断または回答生成）を決定

#### 適用説明
```python
# 質問分析エージェントの使用例
from my_llm_platform.backend.plugins.agents.multi_agent_customer_assistant import MultiAgentCustomerAssistant

# 一般的な質問（コードなし）の場合
assistant = MultiAgentCustomerAssistant()
result = assistant.invoke(question="PythonでCSVファイルを読み取る方法は？")

# 検索ツールを使用したい場合
from my_llm_platform.backend.plugins.agents.agent_tools import create_analysis_tools
custom_tools = {"analyze_question": create_analysis_tools()}
assistant_with_tools = MultiAgentCustomerAssistant(custom_tools=custom_tools)
```

### 2. コード診断エージェント (diagnose_code)

#### 機能説明
コード診断エージェントは、ユーザーが提供したコードの問題点を特定し、診断結果を生成します：
- Pythonコードの構文エラーや論理エラーを検出
- コード実行ツールを使用して実際にコードを実行し結果を確認
- エラーに関するドキュメントを検索して詳細情報を取得
- 診断結果をわかりやすく整理して返却

#### 適用説明
```python
# コード診断エージェントの使用例
from my_llm_platform.backend.plugins.agents.multi_agent_customer_assistant import MultiAgentCustomerAssistant
from my_llm_platform.backend.plugins.agents.agent_tools import create_diagnosis_tools

# コード診断ツールのみを使用する場合
diagnosis_tools = {"diagnose_code": create_diagnosis_tools()}
code_assistant = MultiAgentCustomerAssistant(custom_tools=diagnosis_tools)

# エラーのあるコードを診断
user_code = '''
def divide(a, b):
    return a/b

print(divide(10, 0))
'''
result = code_assistant.invoke(
    question="このコードを実行するとエラーが発生します。原因は？", 
    code=user_code
)
```

### 3. 回答生成エージェント (compose_answer)

#### 機能説明
回答生成エージェントは、分析結果と診断結果に基づいて最終的な回答を作成します：
- 質問分析と（必要に応じて）コード診断の結果を統合
- 専門的かつわかりやすい言葉で回答を構成
- 必要に応じて追加のドキュメント検索を行い、正確な情報を提供
- コード問題の場合は具体的な解決策を提案

#### 適用説明
```python
# 完全なマルチエージェントシステムの使用例
from my_llm_platform.backend.plugins.agents.multi_agent_customer_assistant import MultiAgentCustomerAssistant
from my_llm_platform.backend.plugins.agents.agent_tools import create_agent_tools

# すべてのエージェントにツールを提供
all_tools = create_agent_tools()
full_assistant = MultiAgentCustomerAssistant(custom_tools=all_tools)

# 質問とコードを処理して回答を生成
response = full_assistant.invoke(
    question="このコードを実行するとエラーが発生します。修正方法を教えてください。",
    code="def divide(a, b): return a/b\nprint(divide(10, 0))"
)

# ストリーミングモードで回答を取得
for chunk in full_assistant.stream(
    question="Pythonの非同期処理について教えてください",
    stream_mode="values"
):
    print(chunk["answer"], end="", flush=True)
```

## ファイル構成

- `models.py` - 状態とモデル定義
- `agent_functions.py` - 各エージェントの関数実装
- `agent_tools.py` - エージェントが使用するツール
- `workflow.py` - ワークフロー設定
- `multi_agent_customer_assistant.py` - メインクラス

## 使用例

詳細な使用例については、`examples/multi_agent_example.py`を参照してください。

## カスタマイズ

各エージェントは独自のツールセットを持つことができます。新しいツールを追加するには、`agent_tools.py`に新しいツール関数を実装し、適切なエージェントのツール作成関数に追加してください。

## 注意事項

- このシステムはLLMを使用するため、適切なAPIキーと設定が必要です
- ツールの実行には外部リソースへのアクセスが必要な場合があります
- コード実行ツールはサンドボックス環境で実行することをお勧めします

"""
Base plugin interfaces for the LLM platform plugin system.
"""

import logging
from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional, Type, ClassVar
from .types import Message, PluginResult, PluginType, PluginInfo, PluginConfig, ToolDefinition, UIComponent, MCPAction, MCPResponse

logger = logging.getLogger(__name__)


class BasePlugin(ABC):
    """Base class for all plugins."""

    # Class variables
    plugin_type: ClassVar[PluginType]

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """Initialize the plugin with optional configuration."""
        self.config = config or {}
        self.id = self.__class__.__name__.lower()
        self.name = self.id
        self.description = "Base plugin"
        self.version = "0.1.0"
        self.author = "Unknown"
        self.enabled = True
        self.priority = 100
        self.dependencies = []
        self.tags = []

    def get_info(self) -> PluginInfo:
        """Get plugin information."""
        return PluginInfo(
            id=self.id,
            name=self.name,
            description=self.description,
            version=self.version,
            type=self.plugin_type,
            author=self.author,
            config=PluginConfig(
                enabled=self.enabled,
                priority=self.priority,
                config=self.config
            ),
            dependencies=self.dependencies,
            tags=self.tags
        )

    def configure(self, config: Dict[str, Any]) -> None:
        """Configure the plugin."""
        self.config.update(config)

    def enable(self) -> None:
        """Enable the plugin."""
        self.enabled = True

    def disable(self) -> None:
        """Disable the plugin."""
        self.enabled = False


class PreprocessorPlugin(BasePlugin):
    """Plugin that processes messages before they are sent to the LLM."""

    plugin_type = PluginType.PREPROCESSOR

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        super().__init__(config)
        self.description = "Preprocessor plugin"

    @abstractmethod
    async def process(self, messages: List[Message], **kwargs) -> PluginResult:
        """Process messages before they are sent to the LLM."""
        pass


class PostprocessorPlugin(BasePlugin):
    """Plugin that processes messages after they are received from the LLM."""

    plugin_type = PluginType.POSTPROCESSOR

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        super().__init__(config)
        self.description = "Postprocessor plugin"

    @abstractmethod
    async def process(self, messages: List[Message], llm_response: Message, **kwargs) -> PluginResult:
        """Process messages after they are received from the LLM."""
        pass


class CommandPlugin(BasePlugin):
    """Plugin that adds custom commands to the chat interface."""

    plugin_type = PluginType.COMMAND

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        super().__init__(config)
        self.description = "Command plugin"
        self.command_prefix = self.config.get("command_prefix", "/")
        self.commands = {}

    def register_command(self, name: str, handler, description: str = "") -> None:
        """Register a command."""
        self.commands[name] = {
            "handler": handler,
            "description": description
        }

    @abstractmethod
    async def process(self, messages: List[Message], **kwargs) -> PluginResult:
        """Process messages to check for commands."""
        pass


class ToolPlugin(BasePlugin):
    """Plugin that implements tools for LLM function/tool calling."""

    plugin_type = PluginType.TOOL

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        super().__init__(config)
        self.description = "Tool plugin"
        self.tools = {}

    def register_tool(self, name: str, handler, description: str, parameters: Dict[str, Any], required: List[str] = None) -> None:
        """Register a tool."""
        self.tools[name] = {
            "handler": handler,
            "description": description,
            "parameters": parameters,
            "required": required or []
        }

    def get_tool_definitions(self) -> List[ToolDefinition]:
        """Get tool definitions for LLM function/tool calling."""
        return [
            ToolDefinition(
                name=name,
                description=tool["description"],
                parameters=tool["parameters"],
                required=tool["required"]
            )
            for name, tool in self.tools.items()
        ]

    @abstractmethod
    async def execute_tool(self, tool_name: str, parameters: Dict[str, Any], **kwargs) -> Any:
        """Execute a tool."""
        pass


class UIPlugin(BasePlugin):
    """Plugin that adds custom UI components to the chat interface."""

    plugin_type = PluginType.UI

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        super().__init__(config)
        self.description = "UI plugin"

    @abstractmethod
    def get_components(self) -> List[UIComponent]:
        """Get UI components for frontend rendering."""
        pass


class AgentPlugin(BasePlugin):
    """Plugin that implements an agent for the chat interface."""

    plugin_type = PluginType.AGENT

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        super().__init__(config)
        self.description = "Agent plugin"

    @abstractmethod
    async def process(self, messages: List[Message], **kwargs) -> PluginResult:
        """Process messages with the agent."""
        pass


class RAGPlugin(BasePlugin):
    """Plugin that implements RAG (Retrieval Augmented Generation) for the chat interface."""

    plugin_type = PluginType.RAG

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        super().__init__(config)
        self.description = "RAG plugin"

    @abstractmethod
    async def retrieve(self, query: str, **kwargs) -> List[Dict[str, Any]]:
        """Retrieve relevant documents for a query."""
        pass

    @abstractmethod
    async def process(self, messages: List[Message], **kwargs) -> PluginResult:
        """Process messages with RAG."""
        pass


class MCPPlugin(BasePlugin):
    """Plugin that implements Model Control Protocol (MCP) for the chat interface.

    MCP is a standardized protocol that allows LLMs to interact with external tools and services.
    It provides a structured way for models to call external tools, perform complex reasoning,
    manage multi-turn conversations, and handle structured data.
    """

    plugin_type = PluginType.MCP

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        super().__init__(config)
        self.description = "MCP plugin"
        self.actions = {}

    def register_action(self, name: str, handler, description: str) -> None:
        """Register an MCP action."""
        self.actions[name] = {
            "handler": handler,
            "description": description
        }

    @abstractmethod
    async def execute_action(self, action: MCPAction, **kwargs) -> MCPResponse:
        """Execute an MCP action."""
        pass

    @abstractmethod
    async def process(self, messages: List[Message], **kwargs) -> PluginResult:
        """Process messages with MCP."""
        pass

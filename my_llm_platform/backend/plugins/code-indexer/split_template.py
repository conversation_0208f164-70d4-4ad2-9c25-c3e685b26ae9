# split_template.py  ─ 2025-06-02 fixed
"""
将 code_indexer_template.py 拆分为真实文件。
用法:  python split_template.py code_indexer_template.py
"""
import re, sys, pathlib, textwrap

FILE_PATTERN = re.compile(r"^# =+[\r\n]+# file: (.+?)\r?\n", re.M)

def split(template_path: str, root: pathlib.Path):
    text = pathlib.Path(template_path).read_text(encoding="utf-8")

    # 找到所有文件标记
    matches = list(FILE_PATTERN.finditer(text))
    if not matches:
        raise RuntimeError("No '# file:' markers found!")

    for idx, match in enumerate(matches):
        file_path = root / match.group(1).strip()
        file_path.parent.mkdir(parents=True, exist_ok=True)

        # 计算该文件的 “正文区间”
        start = match.end()
        end = matches[idx + 1].start() if idx + 1 < len(matches) else len(text)
        content = text[start:end]

        # 去掉模板头部多余缩进（如果需要可保留）
        if content.startswith("\n"):
            content = content[1:]
        file_path.write_text(content, encoding="utf-8")
        print(f"✔  wrote {file_path.relative_to(root)}")

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("用法: python split_template.py code_indexer_template.py")
        sys.exit(1)
    tpl = sys.argv[1]
    project_root = pathlib.Path(".").resolve()
    split(tpl, project_root)
    print("🎉 全部文件生成完毕 →", project_root)

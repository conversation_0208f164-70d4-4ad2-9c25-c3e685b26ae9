# ==============================================================
"""スキャン → 変化差分抽出 → Faiss 反映までを司る高水準クラス。"""

from __future__ import annotations
import json
from pathlib import Path
from typing import Dict, Set

import faiss  # type: ignore[import]
import numpy as np

from .file_scanner import DirectoryScanner
from .chunker import CodeChunker
from .embedder import Embedder
from .watcher import start_watcher
from .merkle import build_merkle

FAISS_DIM_PLACEHOLDER = 768  # モデル次第で自動取得可
STATE_FILE = ".index_state.json"


class CodeIndexer:
    """コードベース向けインデクサ。"""

    def __init__(self, repo_path: str | Path, index_path: str | Path):
        self.repo_path = Path(repo_path).resolve()
        self.index_path = Path(index_path).resolve()
        self.scanner = DirectoryScanner(self.repo_path)
        self.chunker = CodeChunker()
        self.embedder = Embedder()
        self.faiss_index = self._init_faiss()
        self._state = self._load_state()

    # ────────────────────────────────────────
    # パブリック API
    # ────────────────────────────────────────
    def full_build(self):
        """全ファイルを再インデックス。"""
        file_hashes = self.scanner.scan()
        self._state["merkle_root"] = build_merkle(list(file_hashes.values()))
        self._state["hashes"] = file_hashes
        self._reindex_files(file_hashes.keys())
        self._save_state()

    def watch(self):
        """ファイル変更を監視し、自動更新する。"""
        start_watcher(
            self.repo_path,
            callback=self._handle_change,
            ignore_patterns=["*.log", "*.tmp", ".git/*"],
        )

    # ────────────────────────────────────────
    # 内部処理
    # ────────────────────────────────────────
    def _init_faiss(self):
        dim = FAISS_DIM_PLACEHOLDER
        if self.index_path.exists():
            return faiss.read_index(str(self.index_path))
        index = faiss.IndexFlatL2(dim)
        return index

    def _save_index(self):
        faiss.write_index(self.faiss_index, str(self.index_path))

    def _load_state(self):
        p = self.index_path.with_suffix(".json")
        if p.exists():
            return json.loads(p.read_text("utf-8"))
        return {"hashes": {}, "merkle_root": ""}

    def _save_state(self):
        p = self.index_path.with_suffix(".json")
        p.write_text(json.dumps(self._state, ensure_ascii=False, indent=2))
        self._save_index()

    # ファイル変更コールバック
    def _handle_change(self, changed_path: str):
        rel = Path(changed_path).resolve()
        if rel.is_dir():
            return  # ディレクトリ自体は無視
        new_hash = self.scanner._file_hash(rel)
        old_hash = self._state["hashes"].get(str(rel))
        if new_hash == old_hash:
            return  # 実質変化なし
        self._state["hashes"][str(rel)] = new_hash
        self._reindex_files([rel])
        self._save_state()

    def _reindex_files(self, files: Iterable[str | Path]):
        vectors = []
        for fp in files:
            text = Path(fp).read_text(encoding="utf-8", errors="ignore")
            chunks = self.chunker.split(text, str(fp))
            vec = self.embedder.embed(chunks)
            vectors.append(vec)
        if vectors:
            mat = np.vstack(vectors)
            self.faiss_index.add(mat)



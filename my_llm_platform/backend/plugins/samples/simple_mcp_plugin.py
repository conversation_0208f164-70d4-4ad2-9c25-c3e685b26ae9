"""
Simple MCP plugin implementation.

This plugin demonstrates how to implement a Model Control Protocol (MCP) plugin
for the LLM platform.
"""

import logging
import json
from typing import Dict, List, Any, Optional
from ..base import MCPPlugin
from ..types import Message, PluginResult, MCPAction, MCPResponse

logger = logging.getLogger(__name__)


class SimpleMCPPlugin(MCPPlugin):
    """Simple MCP plugin implementation."""

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        super().__init__(config)
        self.id = "simple_mcp"
        self.name = "Simple MCP Plugin"
        self.description = "A simple MCP plugin that demonstrates the MCP protocol"
        self.version = "0.1.0"
        self.author = "LLM Platform Team"
        
        # Register MCP actions
        self.register_action("get_weather", self._get_weather, "Get weather information for a location")
        self.register_action("calculate", self._calculate, "Perform a calculation")
        self.register_action("search", self._search, "Search for information")

    async def execute_action(self, action: MCPAction, **kwargs) -> MCPResponse:
        """Execute an MCP action."""
        if action.name not in self.actions:
            return MCPResponse(
                type="error",
                content=f"Unknown action: {action.name}",
                status="error",
                error=f"Action {action.name} is not registered"
            )

        try:
            handler = self.actions[action.name]["handler"]
            result = await handler(action.input, **kwargs)
            return MCPResponse(
                type="result",
                content=result,
                status="success"
            )
        except Exception as e:
            logger.error(f"Error executing MCP action {action.name}: {e}")
            return MCPResponse(
                type="error",
                content=str(e),
                status="error",
                error=str(e)
            )

    async def process(self, messages: List[Message], **kwargs) -> PluginResult:
        """Process messages with MCP."""
        if not messages:
            return PluginResult(messages=messages)

        # Get the last user message
        last_message = messages[-1]
        if last_message.get("role") != "user":
            return PluginResult(messages=messages)

        content = last_message.get("content", "")
        
        # Check if the message contains MCP action markers
        if "[[MCP:" in content and "]]" in content:
            try:
                # Extract MCP action
                start_idx = content.find("[[MCP:")
                end_idx = content.find("]]", start_idx)
                mcp_str = content[start_idx + 6:end_idx].strip()
                
                # Parse MCP action
                mcp_data = json.loads(mcp_str)
                action = MCPAction(
                    type=mcp_data.get("type", "action"),
                    name=mcp_data.get("name", ""),
                    input=mcp_data.get("input", {}),
                    thought=mcp_data.get("thought", "")
                )
                
                # Execute MCP action
                response = await self.execute_action(action, **kwargs)
                
                # Create a new assistant message with the response
                new_messages = messages.copy()
                new_messages.append({
                    "role": "assistant",
                    "content": f"I executed the {action.name} action.\n\nResult: {json.dumps(response.content, indent=2)}"
                })
                
                return PluginResult(
                    messages=new_messages,
                    metadata={"mcp_action": action.dict(), "mcp_response": response.dict()},
                    stop_processing=True
                )
                
            except Exception as e:
                logger.error(f"Error processing MCP action: {e}")
                return PluginResult(
                    messages=messages,
                    error=f"Error processing MCP action: {e}"
                )
        
        return PluginResult(messages=messages)

    async def _get_weather(self, input_data: Dict[str, Any], **kwargs) -> Dict[str, Any]:
        """Get weather information for a location."""
        location = input_data.get("location", "Unknown")
        return {
            "location": location,
            "temperature": 22,
            "condition": "Sunny",
            "humidity": 65,
            "wind_speed": 10,
            "note": "This is simulated weather data for demonstration purposes."
        }

    async def _calculate(self, input_data: Dict[str, Any], **kwargs) -> Dict[str, Any]:
        """Perform a calculation."""
        expression = input_data.get("expression", "")
        try:
            # SECURITY NOTE: In a real implementation, you would need to sanitize the input
            # to prevent code injection. This is just a simple example.
            result = eval(expression)
            return {
                "expression": expression,
                "result": result
            }
        except Exception as e:
            return {
                "expression": expression,
                "error": str(e)
            }

    async def _search(self, input_data: Dict[str, Any], **kwargs) -> Dict[str, Any]:
        """Search for information."""
        query = input_data.get("query", "")
        return {
            "query": query,
            "results": [
                {
                    "title": f"Sample result 1 for {query}",
                    "snippet": "This is a sample search result for demonstration purposes."
                },
                {
                    "title": f"Sample result 2 for {query}",
                    "snippet": "This is another sample search result for demonstration purposes."
                }
            ],
            "note": "These are simulated search results for demonstration purposes."
        }

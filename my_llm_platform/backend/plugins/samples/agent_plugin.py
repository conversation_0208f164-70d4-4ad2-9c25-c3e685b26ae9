"""
Sample agent plugin for the LLM platform.
"""

import logging
import json
from typing import Dict, List, Any, Optional
from ..base import AgentPlugin, ToolPlugin
from ..types import Message, PluginResult, ToolDefinition

logger = logging.getLogger(__name__)


class SimpleAgentPlugin(AgentPlugin):
    """
    A simple agent plugin that implements a basic ReAct agent.

    This is a sample implementation that demonstrates how to create an agent plugin.
    In a real implementation, you would implement a more sophisticated agent.
    """

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """Initialize the plugin with optional configuration."""
        super().__init__(config)
        self.id = "simple_agent"
        self.name = "Simple Agent"
        self.description = "A simple agent that implements a basic ReAct pattern"
        self.version = "0.1.0"
        self.author = "LLM Platform"

        # Sample tools for demonstration
        self.tools = [
            {
                "name": "search",
                "description": "Search for information on the web",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "query": {
                            "type": "string",
                            "description": "The search query"
                        }
                    },
                    "required": ["query"]
                }
            },
            {
                "name": "calculator",
                "description": "Perform a calculation",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "expression": {
                            "type": "string",
                            "description": "The mathematical expression to evaluate"
                        }
                    },
                    "required": ["expression"]
                }
            }
        ]

    async def search(self, query: str) -> str:
        """Simulate a search tool."""
        logger.info(f"Searching for: {query}")
        # In a real implementation, this would call a search API
        return f"Search results for '{query}': This is a simulated search result."

    async def calculator(self, expression: str) -> str:
        """Simulate a calculator tool."""
        logger.info(f"Calculating: {expression}")
        try:
            # WARNING: eval is unsafe in production code
            # This is just for demonstration purposes
            result = eval(expression)
            return f"The result of '{expression}' is {result}"
        except Exception as e:
            return f"Error calculating '{expression}': {str(e)}"

    async def execute_tool(self, tool_name: str, parameters: Dict[str, Any]) -> str:
        """Execute a tool by name."""
        if tool_name == "search":
            return await self.search(parameters.get("query", ""))
        elif tool_name == "calculator":
            return await self.calculator(parameters.get("expression", ""))
        else:
            return f"Unknown tool: {tool_name}"

    async def process(self, messages: List[Message], **kwargs) -> PluginResult:
        """Process messages with the agent."""
        # Check if this is a tool response
        if messages and messages[-1].get("role") == "tool":
            # Add the tool response to the conversation
            return PluginResult(messages=messages)

        # Check if the last message is from the assistant and contains tool calls
        if messages and messages[-1].get("role") == "assistant" and messages[-1].get("tool_calls"):
            # Process tool calls
            tool_calls = messages[-1].get("tool_calls", [])

            for tool_call in tool_calls:
                tool_id = tool_call.get("id")
                tool_name = tool_call.get("function", {}).get("name")
                tool_args_str = tool_call.get("function", {}).get("arguments", "{}")

                try:
                    # Parse tool arguments
                    tool_args = json.loads(tool_args_str)

                    # Execute the tool
                    tool_result = await self.execute_tool(tool_name, tool_args)

                    # Add tool response to messages
                    messages.append({
                        "role": "tool",
                        "tool_call_id": tool_id,
                        "content": tool_result
                    })

                    logger.info(f"Executed tool {tool_name} with result: {tool_result}")
                except Exception as e:
                    logger.error(f"Error executing tool {tool_name}: {e}")
                    messages.append({
                        "role": "tool",
                        "tool_call_id": tool_id,
                        "content": f"Error: {str(e)}"
                    })

            return PluginResult(
                messages=messages,
                metadata={"plugin": self.id, "tool_calls": len(tool_calls)}
            )

        # If this is a new conversation, add the agent system message
        if not any(m.get("role") == "system" for m in messages):
            system_message = {
                "role": "system",
                "content": (
                    "You are an AI assistant with access to tools. "
                    "When you need information or need to perform a calculation, "
                    "use the appropriate tool. "
                    "Think step by step and use tools when necessary."
                )
            }
            messages.insert(0, system_message)

        # Add tools to the conversation context
        from ..manager import plugin_manager

        # Get the OpenAI client from kwargs
        client = kwargs.get("client")
        if client:
            # Add tools to the client parameters
            kwargs["tools"] = self.tools

        return PluginResult(
            messages=messages,
            metadata={"plugin": self.id, "added_tools": True}
        )

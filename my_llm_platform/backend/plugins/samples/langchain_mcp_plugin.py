"""
LangChain MCP plugin implementation.

This plugin integrates LangChain's MCP adapters to provide a more robust
and standardized implementation of the Model Control Protocol (MCP).
"""

import logging
import json
from typing import Dict, List, Any, Optional, Union, Callable
import asyncio
from pydantic import BaseModel

# Lang<PERSON>hain imports
try:
    from langchain_core.tools import BaseTool, Tool
    from langchain_core.language_models import BaseLanguageModel
    from langchain_core.messages import HumanMessage, AIMessage, SystemMessage
    from langchain_core.prompts import ChatPromptTemplate
    from langchain.agents import AgentExecutor, create_openai_tools_agent
    from langchain_core.runnables import Runnable
    LANGCHAIN_AVAILABLE = True
except ImportError:
    LANGCHAIN_AVAILABLE = False
    logging.warning("LangChain not available. Install with 'pip install langchain langchain-openai'")

from ..base import MCPPlugin
from ..types import Message, PluginResult, MCPAction, MCPResponse

logger = logging.getLogger(__name__)


class LangChainMCPPlugin(MCPPlugin):
    """LangChain MCP plugin implementation.

    This plugin uses LangChain's tools and agents to implement the MCP protocol.
    It provides a more robust and standardized implementation compared to the
    simple MCP plugin.
    """

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        super().__init__(config)
        self.id = "langchain_mcp"
        self.name = "LangChain MCP Plugin"
        self.description = "An MCP plugin that integrates with LangChain"
        self.version = "0.1.0"
        self.author = "LLM Platform Team"

        if not LANGCHAIN_AVAILABLE:
            logger.error("LangChain is not available. This plugin will not work.")
            return

        # Initialize LangChain tools
        self.tools = []
        self.agent_executor = None
        self.llm = None

        # Register MCP actions
        self.register_action("get_weather", self._get_weather, "Get weather information for a location")
        self.register_action("calculate", self._calculate, "Perform a calculation")
        self.register_action("search", self._search, "Search for information")

        # Initialize LangChain tools
        self._init_langchain_tools()

    def _init_langchain_tools(self):
        """Initialize LangChain tools."""
        if not LANGCHAIN_AVAILABLE:
            return

        # Create LangChain tools from registered actions
        for action_name, action_info in self.actions.items():
            handler = action_info["handler"]
            description = action_info["description"]

            # Create a LangChain tool for this action
            tool = Tool(
                name=action_name,
                func=lambda handler=handler, **kwargs: asyncio.run(handler(kwargs)),
                description=description
            )
            self.tools.append(tool)

    def set_llm(self, llm: BaseLanguageModel):
        """Set the LLM to use for the agent."""
        if not LANGCHAIN_AVAILABLE:
            logger.error("LangChain is not available. Cannot set LLM.")
            return

        self.llm = llm

        # Create an agent with the tools and LLM
        prompt = ChatPromptTemplate.from_messages([
            ("system", "You are a helpful AI assistant that can use tools to answer user questions."),
            ("human", "{input}"),
        ])

        agent = create_openai_tools_agent(self.llm, self.tools, prompt)
        self.agent_executor = AgentExecutor(agent=agent, tools=self.tools)

    async def execute_action(self, action: MCPAction, **kwargs) -> MCPResponse:
        """Execute an MCP action."""
        if action.name not in self.actions:
            return MCPResponse(
                type="error",
                content=f"Unknown action: {action.name}",
                status="error",
                error=f"Action {action.name} is not registered"
            )

        try:
            handler = self.actions[action.name]["handler"]
            result = await handler(action.input, **kwargs)
            return MCPResponse(
                type="result",
                content=result,
                status="success"
            )
        except Exception as e:
            logger.error(f"Error executing MCP action {action.name}: {e}")
            return MCPResponse(
                type="error",
                content=str(e),
                status="error",
                error=str(e)
            )

    async def process(self, messages: List[Message], **kwargs) -> PluginResult:
        """Process messages with MCP."""
        if not messages:
            return PluginResult(messages=messages)

        if not LANGCHAIN_AVAILABLE:
            return PluginResult(
                messages=messages,
                error="LangChain is not available. Install with 'pip install langchain langchain-openai'"
            )

        # Get the last user message
        last_message = messages[-1]
        if last_message.get("role") != "user":
            return PluginResult(messages=messages)

        content = last_message.get("content", "")

        # Check if the message contains MCP action markers
        if "[[MCP:" in content and "]]" in content:
            try:
                # Extract MCP action
                start_idx = content.find("[[MCP:")
                end_idx = content.find("]]", start_idx)
                mcp_str = content[start_idx + 6:end_idx].strip()

                # Parse MCP action
                mcp_data = json.loads(mcp_str)
                action = MCPAction(
                    type=mcp_data.get("type", "action"),
                    name=mcp_data.get("name", ""),
                    input=mcp_data.get("input", {}),
                    thought=mcp_data.get("thought", "")
                )

                # Execute MCP action
                response = await self.execute_action(action, **kwargs)

                # Create a new assistant message with the response
                new_messages = messages.copy()
                new_messages.append({
                    "role": "assistant",
                    "content": f"I executed the {action.name} action.\n\nResult: {json.dumps(response.content, indent=2)}"
                })

                return PluginResult(
                    messages=new_messages,
                    metadata={"mcp_action": action.dict(), "mcp_response": response.dict()},
                    stop_processing=True
                )

            except Exception as e:
                logger.error(f"Error processing MCP action: {e}")
                return PluginResult(
                    messages=messages,
                    error=f"Error processing MCP action: {e}"
                )

        # If no explicit MCP action, use LangChain agent if available
        if self.agent_executor and self.llm:
            try:
                # Convert messages to LangChain format
                lc_messages = []
                for msg in messages:
                    role = msg.get("role", "")
                    content = msg.get("content", "")

                    if role == "user":
                        lc_messages.append(HumanMessage(content=content))
                    elif role == "assistant":
                        lc_messages.append(AIMessage(content=content))
                    elif role == "system":
                        lc_messages.append(SystemMessage(content=content))

                # Run the agent on the last user message
                agent_result = self.agent_executor.invoke({"input": content})

                # Create a new assistant message with the agent's response
                new_messages = messages.copy()
                new_messages.append({
                    "role": "assistant",
                    "content": agent_result["output"]
                })

                return PluginResult(
                    messages=new_messages,
                    metadata={"agent_result": agent_result},
                    stop_processing=True
                )

            except Exception as e:
                logger.error(f"Error running LangChain agent: {e}")
                # Fall back to normal processing if agent fails

        return PluginResult(messages=messages)

    async def _get_weather(self, input_data: Dict[str, Any], **kwargs) -> Dict[str, Any]:
        """Get weather information for a location."""
        location = input_data.get("location", "Unknown")
        return {
            "location": location,
            "temperature": 22,
            "condition": "Sunny",
            "humidity": 65,
            "wind_speed": 10,
            "note": "This is simulated weather data for demonstration purposes."
        }

    async def _calculate(self, input_data: Dict[str, Any], **kwargs) -> Dict[str, Any]:
        """Perform a calculation."""
        expression = input_data.get("expression", "")
        try:
            # SECURITY NOTE: In a real implementation, you would need to sanitize the input
            # to prevent code injection. This is just a simple example.
            result = eval(expression)
            return {
                "expression": expression,
                "result": result
            }
        except Exception as e:
            return {
                "expression": expression,
                "error": str(e)
            }

    async def _search(self, input_data: Dict[str, Any], **kwargs) -> Dict[str, Any]:
        """Search for information."""
        query = input_data.get("query", "")
        return {
            "query": query,
            "results": [
                {
                    "title": f"Sample result 1 for {query}",
                    "snippet": "This is a sample search result for demonstration purposes."
                },
                {
                    "title": f"Sample result 2 for {query}",
                    "snippet": "This is another sample search result for demonstration purposes."
                }
            ],
            "note": "These are simulated search results for demonstration purposes."
        }

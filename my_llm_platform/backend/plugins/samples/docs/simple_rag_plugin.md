# Simple RAG Plugin

## Overview

The Simple RAG (Retrieval-Augmented Generation) plugin enhances LLM responses by retrieving relevant documents and adding them to the system message. This allows the LLM to access external knowledge when generating responses.

## Features

- Document retrieval from a vector database
- Automatic query enhancement
- Seamless integration with chat interface
- Configurable retrieval parameters

## Configuration

The plugin supports the following configuration options:

| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `retrieval_count` | Integer | 3 | Number of documents to retrieve |
| `similarity_threshold` | Float | 0.7 | Minimum similarity score for retrieved documents |
| `use_query_enhancement` | Boolean | true | Whether to enhance the query before retrieval |
| `max_tokens_per_doc` | Integer | 500 | Maximum number of tokens per retrieved document |

## Usage

The plugin is automatically applied to all chat messages when enabled. No special syntax is required.

## Example

**User Input:**
```
What is the capital of France?
```

**Plugin Action:**
1. Retrieves documents about France
2. Adds the retrieved information to the system message
3. LLM generates a response using both its training data and the retrieved information

**Enhanced Response:**
The LLM can now provide more detailed and accurate information about Paris as the capital of France, including specific facts from the retrieved documents.

## Technical Details

The plugin uses a vector database to store document embeddings and performs similarity search to retrieve relevant documents. The retrieval process happens before the LLM generates a response, and the retrieved documents are added to the system message.

## Limitations

- The plugin requires a properly configured vector database
- Retrieval quality depends on the quality of the document embeddings
- Large documents may be truncated to fit within token limits

# LangChain MCP Plugin

## Overview

The LangChain MCP Plugin integrates the Model Control Protocol with LangChain's powerful agent and tool framework. This plugin provides a more advanced implementation of MCP that leverages LangChain's ecosystem for robust tool usage and agent capabilities.

## Features

- Integration with LangChain's tool system
- Advanced agent-based processing
- Compatibility with LangChain's ecosystem
- Enhanced error handling and fallbacks

## Configuration

The plugin supports the following configuration options:

| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `agent_type` | String | "openai-tools" | Type of LangChain agent to use |
| `verbose` | Boolean | false | Whether to show detailed agent steps |
| `max_iterations` | Integer | 10 | Maximum number of reasoning steps |
| `return_intermediate_steps` | Boolean | false | Whether to include intermediate steps in the response |

## Available Tools

The plugin provides the same tools as SimpleMCPPlugin, but implemented using LangChain's tool system:

1. **get_weather** - Get weather information for a specified location
2. **calculate** - Perform mathematical calculations
3. **search** - Search for information

Additional tools can be easily added by extending the plugin.

## Usage

The LLM can use MCP actions in the same way as with SimpleMCPPlugin:

```
[[MCP:{"type":"action","name":"get_weather","input":{"location":"Tokyo"},"thought":"I need to check the weather in Tokyo."}]]
```

Alternatively, the plugin can operate in agent mode, where it automatically determines which tools to use based on the user's query.

## Example

**User Input:**
```
What's the weather like in Tokyo today and what's 15% of 230?
```

**LLM Response with MCP:**
```
Let me check the weather in Tokyo and calculate 15% of 230 for you.

[[MCP:{"type":"action","name":"get_weather","input":{"location":"Tokyo"},"thought":"I need to get the current weather information for Tokyo."}]]

Tokyo is currently experiencing a temperature of 22°C with sunny conditions.

[[MCP:{"type":"action","name":"calculate","input":{"expression":"0.15 * 230"},"thought":"I need to calculate 15% of 230."}]]

And 15% of 230 is 34.5.
```

**Final Response to User:**
The markup is replaced with the actual weather data and calculation results.

## Technical Details

The plugin uses LangChain's agent framework to:
1. Parse MCP actions from the LLM output
2. Convert them to LangChain tool calls
3. Execute the tools using LangChain's infrastructure
4. Return the results to the LLM

This provides better error handling, more consistent execution, and access to LangChain's extensive tool ecosystem.

## Integration with LangChain

The plugin can be extended to use any LangChain tool by adding it to the tool registry:

```python
from langchain.tools import BaseTool
from langchain.agents import AgentType

# Add a custom LangChain tool
plugin.add_langchain_tool(my_custom_tool)

# Change the agent type
plugin.set_agent_type(AgentType.STRUCTURED_CHAT_ZERO_SHOT_REACT_DESCRIPTION)
```

## Limitations

- Requires LangChain to be installed (`pip install langchain langchain-openai`)
- More complex than SimpleMCPPlugin
- May have higher latency due to the additional processing
- Depends on external libraries that may change

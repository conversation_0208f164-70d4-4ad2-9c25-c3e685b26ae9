"""
Sample RAG plugin for the LLM platform.
"""

import logging
from typing import Dict, List, Any, Optional
from ..base import RAGPlugin
from ..types import Message, PluginResult

logger = logging.getLogger(__name__)


class SimpleRAGPlugin(RAGPlugin):
    """
    A simple RAG plugin that adds retrieved documents to the system message.

    This is a sample implementation that demonstrates how to create a RAG plugin.
    In a real implementation, you would connect to a vector database like Qdrant
    to retrieve relevant documents.
    """

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """Initialize the plugin with optional configuration."""
        super().__init__(config)
        self.id = "simple_rag"
        self.name = "Simple RAG"
        self.description = "A simple RAG plugin that adds retrieved documents to the system message"
        self.version = "0.1.0"
        self.author = "LLM Platform"

        # Sample documents for demonstration
        self.documents = self.config.get("documents", [
            {
                "id": "doc1",
                "content": "LLM Platform is a modular platform for building LLM-powered applications.",
                "metadata": {"source": "documentation", "section": "overview"}
            },
            {
                "id": "doc2",
                "content": "The plugin system allows you to extend the platform with custom functionality.",
                "metadata": {"source": "documentation", "section": "plugins"}
            },
            {
                "id": "doc3",
                "content": "RAG (Retrieval Augmented Generation) enhances LLM responses with relevant information from a knowledge base.",
                "metadata": {"source": "documentation", "section": "rag"}
            }
        ])

    async def retrieve(self, query: str, **kwargs) -> List[Dict[str, Any]]:
        """
        Retrieve relevant documents for a query.

        In a real implementation, this would query a vector database.
        This sample implementation just returns all documents.
        """
        # In a real implementation, you would:
        # 1. Convert the query to a vector embedding
        # 2. Search for similar documents in a vector database
        # 3. Return the most relevant documents

        # For this sample, we'll just return all documents
        logger.info(f"Retrieving documents for query: {query}")
        return self.documents

    async def process(self, messages: List[Message], **kwargs) -> PluginResult:
        """Process messages with RAG."""
        # Only process if there's at least one user message
        if not messages or not any(m["role"] == "user" for m in messages):
            return PluginResult(messages=messages)

        # Get the last user message
        last_user_message = next((m["content"] for m in reversed(messages) if m["role"] == "user"), "")

        # Retrieve relevant documents
        retrieved_docs = await self.retrieve(last_user_message)

        # Find or create a system message
        system_message_index = next((i for i, m in enumerate(messages) if m["role"] == "system"), None)

        if system_message_index is not None:
            # Update existing system message
            system_content = messages[system_message_index]["content"]

            # Check if we've already added RAG content
            if "### Retrieved Documents ###" not in system_content:
                # Add retrieved documents to the system message
                rag_content = "\n\n### Retrieved Documents ###\n"
                for doc in retrieved_docs:
                    rag_content += f"- {doc['content']}\n"

                messages[system_message_index]["content"] = system_content + rag_content
        else:
            # Create a new system message with retrieved documents
            rag_content = "You are a helpful assistant. Use the following retrieved documents to help answer the user's question:\n\n"
            rag_content += "### Retrieved Documents ###\n"
            for doc in retrieved_docs:
                rag_content += f"- {doc['content']}\n"

            # Insert system message at the beginning
            messages.insert(0, {"role": "system", "content": rag_content})

        logger.info(f"Added {len(retrieved_docs)} retrieved documents to the conversation")

        return PluginResult(
            messages=messages,
            metadata={"plugin": self.id, "retrieved_docs": len(retrieved_docs)}
        )

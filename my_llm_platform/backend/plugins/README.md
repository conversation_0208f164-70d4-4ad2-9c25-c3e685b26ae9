# LLM Platform Plugin System

This module provides a plugin system for the LLM platform, allowing easy integration of additional functionality like agents, RAG, and other extensions into the chat interface.

## Overview

The plugin system follows a modular architecture that allows developers to create plugins that can:

1. Process messages before they are sent to the LLM
2. Process responses after they are received from the LLM
3. Add custom commands to the chat interface
4. Integrate with external systems
5. Add custom UI components to the chat interface

## Plugin Types

The system supports several types of plugins:

1. **Preprocessor Plugins**: Modify user messages before they are sent to the LLM
2. **Postprocessor Plugins**: Modify LLM responses before they are sent to the user
3. **Command Plugins**: Add custom commands that can be triggered by specific patterns in user messages
4. **Tool Plugins**: Implement tools that can be called by the LLM during function/tool calling
5. **UI Plugins**: Add custom UI components to the chat interface

## Creating a Plugin

To create a plugin, you need to:

1. Create a new Python module in the `plugins` directory
2. Implement the appropriate plugin interface
3. Register the plugin with the plugin manager

## Example

Here's a simple example of a preprocessor plugin that adds context to user messages:

```python
from backend.plugins.base import PreprocessorPlugin
from backend.plugins.types import Message, PluginResult

class ContextEnhancerPlugin(PreprocessorPlugin):
    def __init__(self, config=None):
        super().__init__(config)
        self.name = "context_enhancer"
        self.description = "Adds context to user messages"
        
    async def process(self, messages: list[Message], **kwargs) -> PluginResult:
        # Add context to the last user message
        if messages and messages[-1]["role"] == "user":
            messages[-1]["content"] = f"Consider the following context: {self.config.get('context', '')}. {messages[-1]['content']}"
        
        return PluginResult(
            messages=messages,
            metadata={"plugin": self.name, "action": "added_context"}
        )
```

## Plugin Configuration

Plugins can be configured through the `.env` file or through the plugin manager API. The configuration is passed to the plugin constructor.

## Plugin Registration

Plugins are registered with the plugin manager, which is responsible for loading and managing plugins. The plugin manager is initialized during application startup.

```python
from backend.plugins.manager import plugin_manager

# Register a plugin
plugin_manager.register_plugin("context_enhancer", ContextEnhancerPlugin, {"context": "You are talking to a helpful assistant."})

# Get a plugin
plugin = plugin_manager.get_plugin("context_enhancer")

# Execute a plugin
result = await plugin.process(messages)
```

## Plugin Execution Flow

The plugin system follows a pipeline architecture, where messages flow through a series of plugins before being sent to the LLM, and responses flow through another series of plugins before being sent to the user.

1. User sends a message
2. Message is processed by preprocessor plugins
3. Message is sent to the LLM
4. Response is received from the LLM
5. Response is processed by postprocessor plugins
6. Response is sent to the user

## Plugin Dependencies

Plugins can depend on other plugins. The plugin manager ensures that plugins are executed in the correct order based on their dependencies.

## Plugin API

The plugin system provides a REST API for managing plugins:

- `GET /plugins`: List all registered plugins
- `GET /plugins/{plugin_id}`: Get information about a specific plugin
- `POST /plugins/{plugin_id}/enable`: Enable a plugin
- `POST /plugins/{plugin_id}/disable`: Disable a plugin
- `POST /plugins/{plugin_id}/configure`: Configure a plugin

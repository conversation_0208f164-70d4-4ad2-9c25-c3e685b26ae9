"""
MCP tool registry.

This module provides a registry for MCP tools.
"""

import logging
from typing import Dict, Any, Optional, List, Callable, Awaitable, Union
from .tool import MCPTool

logger = logging.getLogger(__name__)


class MCPToolRegistry:
    """Registry for MCP tools."""
    
    _instance = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(MCPToolRegistry, cls).__new__(cls)
            cls._instance.tools = {}
        return cls._instance
    
    def register(self, tool: MCPTool) -> None:
        """
        Register a tool.
        
        Args:
            tool: The tool to register
        """
        if tool.name in self.tools:
            logger.warning(f"Tool {tool.name} already registered. Overwriting.")
        
        self.tools[tool.name] = tool
        logger.info(f"Registered tool: {tool.name}")
    
    def get(self, name: str) -> Optional[MCPTool]:
        """
        Get a tool by name.
        
        Args:
            name: The name of the tool
            
        Returns:
            The tool, or None if not found
        """
        return self.tools.get(name)
    
    def list(self) -> List[str]:
        """
        List all registered tools.
        
        Returns:
            A list of tool names
        """
        return list(self.tools.keys())
    
    def get_all(self) -> Dict[str, MCPTool]:
        """
        Get all registered tools.
        
        Returns:
            A dictionary of tool names to tools
        """
        return self.tools.copy()
    
    def to_langchain_tools(self) -> List[Any]:
        """
        Convert all tools to LangChain tools.
        
        Returns:
            A list of LangChain tools
        """
        try:
            return [tool.to_langchain_tool() for tool in self.tools.values()]
        except ImportError:
            logger.warning("LangChain is not installed. Cannot convert tools.")
            return []
    
    def to_openai_tools(self) -> List[Dict[str, Any]]:
        """
        Convert all tools to OpenAI tool specifications.
        
        Returns:
            A list of OpenAI tool specifications
        """
        return [tool.to_openai_tool() for tool in self.tools.values()]


# Global functions for convenience
def register_tool(
    name: str,
    description: str,
    func: Callable[[Dict[str, Any]], Union[Any, Awaitable[Any]]],
    parameters: Optional[Dict[str, Any]] = None
) -> MCPTool:
    """
    Register a new tool.
    
    Args:
        name: The name of the tool
        description: A description of what the tool does
        func: The function that implements the tool
        parameters: A dictionary describing the parameters the tool accepts
        
    Returns:
        The registered tool
    """
    tool = MCPTool(name=name, description=description, func=func, parameters=parameters)
    MCPToolRegistry().register(tool)
    return tool


def get_tool(name: str) -> Optional[MCPTool]:
    """
    Get a tool by name.
    
    Args:
        name: The name of the tool
        
    Returns:
        The tool, or None if not found
    """
    return MCPToolRegistry().get(name)


def list_tools() -> List[str]:
    """
    List all registered tools.
    
    Returns:
        A list of tool names
    """
    return MCPToolRegistry().list()

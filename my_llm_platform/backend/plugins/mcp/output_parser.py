import re

def extract_think(text: str) -> str:
    """<think> タグの中身だけを返す"""
    match = re.search(r"<think>(.*?)</think>", text, re.DOTALL)
    return match.group(1).strip() if match else ""

def strip_think(text: str) -> str:
    """<think> タグごと削除して、最終出力用に整形"""
    return re.sub(r"<think>.*?</think>", "", text, flags=re.DOTALL).strip()

def parse_agent_result(result) -> dict:
    """
    agent の出力を解析して、最終的な値・理由・thinking等を抽出。
    result が dict(messagesあり) でない場合、直接使う。
    """
    # 📌 LangGraphのmessages構造なら処理する
    if isinstance(result, dict) and "messages" in result:
        messages = result["messages"]
        user_question = None
        reasoning_steps = []
        final_answer_raw = None

        for msg in messages:
            cls_name = msg.__class__.__name__

            if cls_name == "HumanMessage":
                user_question = msg.content

            elif cls_name == "AIMessage":
                tool_calls = msg.additional_kwargs.get("tool_calls", None)
                if tool_calls:
                    for tool_call in tool_calls:
                        name = tool_call.get("function", {}).get("name")
                        args = tool_call.get("function", {}).get("arguments")
                        reasoning_steps.append(f"🤖 ツール `{name}` を呼び出し → 引数 {args}")
                elif msg.content:
                    final_answer_raw = msg.content

            elif cls_name == "ToolMessage":
                reasoning_steps.append(f"🛠 ツール [{msg.name}] → {msg.content}")

        think_log = extract_think(final_answer_raw or "")
        final_answer = strip_think(final_answer_raw or "")

        return {
            "question": user_question,
            "steps": reasoning_steps,
            "think_log": think_log,
            "final_answer": final_answer,
        }

    else:
        # 📌 非 message 形式：そのまま使えるようにする
        return {
            "question": None,
            "steps": [],
            "think_log": None,
            "final_answer": str(result),  # int, float, listでも str にする
        }


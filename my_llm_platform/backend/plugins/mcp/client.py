from mcp import ClientSession, StdioServerParameters
from mcp.client.stdio import stdio_client
from mcp.client.sse import sse_client

from langchain_mcp_adapters.tools import load_mcp_tools
from langgraph.prebuilt import create_react_agent
from langchain_openai import ChatOpenAI
import asyncio
from dotenv import load_dotenv
import os
import re

from .config import Config, SSEServerParameters
from .output_parser import parse_agent_result

load_dotenv()
# print(f"API KEY: {os.getenv('OPENAI_API_KEY')}")
print(f"OPENAI_BASE_URL: {Config.OPENAI_BASE_URL}")
basic_url = Config.OPENAI_BASE_URL
api_key = os.getenv("OPENAI_API_KEY")
mcp_srv_name = "server/mcp_server.py"

# 🧠 サーバーパラメータの型によりクライアントを自動選択
def get_client_by_param_type(server_params):

    if isinstance(server_params, StdioServerParameters):
        print("\n====== 👀 transport（デバッグ用） :stdio======\n")
        return stdio_client(server_params)
    elif isinstance(server_params, SSEServerParameters):
        print("\n====== 👀 transport（デバッグ用） :sse======\n")
        return sse_client(**server_params.to_kwargs())
    else:
        raise ValueError(f"未対応のserver_params型: {type(server_params)}")

# 💡 推理ログ出力用
def extract_think_content(text):
    match = re.search(r"<think>(.*?)</think>", text, re.DOTALL)
    return match.group(1).strip() if match else None

# 💡 最終回答から<think>を除去
def clean_final_answer(text):
    # <think>...</think> を全部消す
    return re.sub(r"<think>.*?</think>", "", text, flags=re.DOTALL).strip()

async def run_agent(server_params, model):

    try:
        async with get_client_by_param_type(server_params) as (read, write):
            async with ClientSession(read, write) as session:
                print("セッション初期化完了")  # デバッグログ
                await session.initialize()

                tools = await load_mcp_tools(session)
                print(f"ロードされたツール: {tools}")  # デバッグログ

                # エージェントを作成して実行
                agent = create_react_agent(model, tools)
                print("エージェント作成完了")  # デバッグログ

                agent_response = await agent.ainvoke({"messages": "(3 + 5) * 12 はいくつ？"})
                print(f"エージェントの応答: {agent_response}")  # デバッグログ
                return parse_agent_result(agent_response)

    except Exception as e:
        print(f"エラーが発生しました: {e}")  # エラー情報を出力

from pprint import pprint

if __name__ == "__main__":
    transport = "sse"#sse
    mcp_srv = "http://localhost:8005/sse" # 前提 ./server/start_server_01.sh を先に実行
    model_name = "qwen3:latest" #"command-r7b:latest" #"qwen2.5-coder:latest" #"gpt-4o" qwen3
    model = ChatOpenAI(model=model_name, temperature=0.0, max_tokens=100,api_key=api_key,base_url=basic_url)
    if transport == "stdio":
        server_params = StdioServerParameters(
        command="python",
        args=[mcp_srv_name, "stdio"],
        )
    else:
        server_params = SSEServerParameters(url=mcp_srv)
    # SSE接続用の設定（必要に応じて変更可能）
    # server_params = SSEServerParameters(
    # url="http://localhost:8005/sse",  # サーバーURL
    # headers={"X-API-KEY": "secret123"},  # 任意のヘッダー（例：認証トークン）
    # timeout=10,  # 接続タイムアウト（秒）
    # sse_read_timeout=None  # 指定しなければデフォルトを使用
    # )

    result = asyncio.run(run_agent(server_params, model))

    print("\n")
    print(f"🧑 ユーザーの質問: {result['question']}\n")
    print("\n====== 👀 Agent 処理ログ ======")
    pprint(result["steps"])

    if result["think_log"]:
        print("\n🧠 モデルの内部思考（<think>）:")
        print(result["think_log"])

    text = result["final_answer"]
    print("\n💡 最終回答:")
    print(text)

    print("\n💡 最終値:")
    # 去掉 Markdown 的 **
    text = re.sub(r"\*\*", "", text)
    matches = re.findall(r"\d+(?:\.\d+)?(?=\s*です)", text)
    print(matches[0] if matches else "None!")

    # print("\n====== 👀 エージェント全体出力（デバッグ用） ======\n")
    # pprint(result)

    # print("\n====== ✅ エージェント最終構造化出力 ======\n")

    # messages = result.get("messages", [])

    # user_question = None
    # reasoning_steps = []
    # final_answer = None

    # for msg in messages:
    #     cls_name = msg.__class__.__name__

    #     if cls_name == "HumanMessage":
    #         user_question = msg.content

    #     elif cls_name == "AIMessage":
    #         # tool_calls（推論過程）が含まれているかを確認
    #         tool_calls = msg.additional_kwargs.get("tool_calls", None)
    #         if tool_calls:
    #             for tool_call in tool_calls:
    #                 name = tool_call.get("function", {}).get("name")
    #                 args = tool_call.get("function", {}).get("arguments")
    #                 reasoning_steps.append(f"🤖 思考中 → ツール `{name}` を呼び出し、引数 {args}")
    #         elif msg.content:
    #             final_answer = msg.content

    #     elif cls_name == "ToolMessage":
    #         reasoning_steps.append(f"🛠 ツール [{msg.name}] → {msg.content}")

    # # 整理された内容を出力
    # print(f"🧑 ユーザーの質問: {user_question}\n")

    # print("🧠 モデルの思考過程:")
    # for step in reasoning_steps:
    #     print(f"{step}")
    # print("")

    # # ⏬ 追加：<think> の中身だけ抽出してログ出力
    # think_log = extract_think_content(final_answer)
    # if think_log:
    #     print("\n🧠 モデル内思考ログ（<think>抜粋）:\n")
    #     print(think_log)

    # # ⏬ <think> タグを取り除いた回答を最終出力に使用
    # final_answer = clean_final_answer(final_answer)

    # print(f"💡 最終回答: {final_answer}")


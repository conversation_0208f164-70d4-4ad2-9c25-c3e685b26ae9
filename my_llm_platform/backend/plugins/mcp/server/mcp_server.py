from mcp.server.fastmcp import FastMCP
import sys
import asyncio
import os

# MCPサーバーのインスタンスを作成
mcp = FastMCP("数学工具",
    host="0.0.0.0",
    port=8005,  
    sse_path="/sse",
    message_path="/message/"
          )

@mcp.tool()
def add(a: int, b: int) -> int:
    """
    2つの数値を加算する

    Args:
        a (int): 1番目の数値
        b (int): 2番目の数値

    Returns:
        int: 加算結果
    """
    return a + b

@mcp.tool()
def multiply(a: int, b: int) -> int:
    """
    2つの数値を乗算する

    Args:
        a (int): 1番目の数値
        b (int): 2番目の数値

    Returns:
        int: 乗算結果
    """
    return a * b

if __name__ == "__main__":
    try:
        if len(sys.argv) < 2:
            print("Usage: python mcp_server.py [stdio|sse]")
            sys.exit(1)
        elif "stdio" in sys.argv:
            mcp.run()
        elif "sse" in sys.argv:
            #asyncio.run(mcp.run_sse_async()) 8000

            # Run the server with SSE transport
            mcp.run(transport="sse")
        else:
            print("Usage: python mcp_server.py [stdio|sse]")
            sys.exit(1)
    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)
#!/bin/bash
# 実行中の MCP サーバーを確認

echo "MCP サーバーの状態を確認中..."
echo "-----------------------------------"

# 一般的なポートを確認
ports=("8005" "8006" "8007" "3001")
hosts=("127.0.0.1" "localhost" "0.0.0.0")

for port in "${ports[@]}"; do
    for host in "${hosts[@]}"; do
        echo -n "http://${host}:${port}/sse を確認中 ... "
        if curl -s --max-time 2 "http://${host}:${port}/sse" > /dev/null; then
            echo "✅ アクセス可能"
        else
            echo "❌ アクセス不可"
        fi
    done
done

echo "-----------------------------------"
echo "実行中の MCP 関連プロセスを確認中..."
ps aux | grep -E "mcp|fastmcp" | grep -v grep

echo "-----------------------------------"
echo "ネットワークポートを確認中..."
netstat -tuln | grep -E "8005|8006|8007|3001"

echo "-----------------------------------"
echo "ヒント: MCP Inspector では以下の形式の URL を使用してください:"
echo "http://localhost:ポート番号/sse"
echo "例: http://localhost:8005/sse"

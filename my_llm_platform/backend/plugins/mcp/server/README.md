# MCP サーバーと Inspector (テストMCP用)起動ガイド (WSL版)

このディレクトリには、MCP サーバーと MCP Inspector を起動するためのスクリプトが含まれています。

## 使用方法

### 方法1：サーバーと Inspector を個別に起動

1. まず、ターミナルウィンドウで MCP サーバーを起動します：

```bash
./start_server_01.sh
```

2. 次に、別のターミナルウィンドウで MCP Inspector を起動します：

```bash
./start_inspector_test.sh
```

3. ブラウザで MCP Inspector にアクセスします：
   - Windows の場合：http://localhost:5173
   - WSL の場合：http://127.0.0.1:5173

4. MCP Inspector で MCP サーバーに接続します(mcp_server.py)：
   - URL として使用：http://localhost:8005/sse


### よくある問題

1. **MCP サーバーに接続できない**：
   - MCP サーバーが実行中であることを確認してください
   - 正しい URL（http://localhost:8005/sse）を使用していることを確認してください
   - WSL で実行している場合は、localhost の代わりに WSL の IP アドレスを使用する必要があるかもしれません

2. **ポートが既に使用されている**：
   - ポート 8005 が既に使用されている場合は、`start_server_01.sh` の PORT 変数を変更できます
   - 同時に `start_inspector_test.sh` の MCP_PORT 変数も更新することを忘れないでください

3. **MCP Inspector の起動に失敗**：
   - MCP Inspector がインストールされていることを確認してください：`npm install -g @mcp-tools/inspector`
   - Node.js のバージョンが互換性があるか確認してください

#!/bin/bash
# start_inspector_test.sh
# 🚀 MCP Inspector の起動 (WSL版)

# MCP Server のアドレスとポート（情報表示用）
MCP_HOST="0.0.0.0"
MCP_PORT="8005"

# MCP Inspector を起動
echo "🚀 MCP Inspector を起動中..."
npx @modelcontextprotocol/inspector &
INSPECTOR_PID=$!

# Inspector が正常に起動したか確認
sleep 2
if ! ps -p $INSPECTOR_PID > /dev/null; then
    echo "❌ MCP Inspector の起動に失敗しました"
    exit 1
else
    echo "✅ MCP Inspector が正常に起動しました"
fi

# アクセス情報の表示
echo ""
echo "📋 MCP サービス情報："
echo "-----------------------------------"
echo "MCP Server: http://${MCP_HOST}:${MCP_PORT}/sse"
echo "MCP Inspector: http://127.0.0.1:5173"
echo ""
echo "🌐 Windows でブラウザからアクセスする場合："
echo "MCP Inspector: http://localhost:5173"
echo "-----------------------------------"
echo ""
echo "⚠️ 注意：WSL では自動的にブラウザを開くことができません。上記のアドレスを Windows ブラウザで手動で開いてください"
echo "⚠️ 重要：MCP Inspector で接続する際は、URL として http://localhost:8005/sse を使用してください"
echo ""
echo "Ctrl+C で Inspector を停止します"

# ユーザーが Ctrl+C を押すのを待つ
trap "echo 'Inspector を停止中...'; kill $INSPECTOR_PID; exit" INT
wait $INSPECTOR_PID

"""
MCP (Model Control Protocol) plugin system.

This module provides a simple framework for creating and using MCP tools
that can be easily integrated with various agent frameworks.
"""

from .registry import MCPToolRegistry, register_tool, get_tool, list_tools
from .tool import MCPTool, MCPToolResult

__all__ = [
    'MCPToolRegistry',
    'register_tool',
    'get_tool',
    'list_tools',
    'MCPTool',
    'MCPToolResult'
]

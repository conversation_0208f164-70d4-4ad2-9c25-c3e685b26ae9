import os
from typing import Optional, Dict

class SSEServerParameters:
    def __init__(
        self,
        url: str,
        headers: Optional[Dict[str, str]] = None,
        timeout: Optional[int] = None,
        sse_read_timeout: Optional[int] = None
    ):
        self.url = url  # 接続するSSEサーバーのURL
        self.headers = headers  # HTTPヘッダー（省略可能）
        self.timeout = timeout  # 接続タイムアウト（秒、任意）
        self.sse_read_timeout = sse_read_timeout  # SSE読み込みタイムアウト（省略可能）

    def to_kwargs(self):
        """
        None以外の値だけを辞書として返却する関数。
        sse_clientに渡す引数辞書として使う。
        """
        result = {"url": self.url}
        if self.headers is not None:
            result["headers"] = self.headers
        if self.timeout is not None:
            result["timeout"] = self.timeout
        if self.sse_read_timeout is not None:
            result["sse_read_timeout"] = self.sse_read_timeout
        return result

class Config:
    OPENAI_API_KEY = os.getenv("OPENAI_API_KEY", "your_openai_api_key")
    POSTGRES_DB_URL = os.getenv("POSTGRES_DB_URL", "postgresql://mcp_user:mcp_password@localhost/mcp_db")
    CHROMA_COLLECTION_NAME = os.getenv("CHROMA_COLLECTION_NAME", "code_vectors_db")
    OPENAI_BASE_URL = os.getenv("OPENAI_BASE_URL", "https://api.openai.com/v1")


"""
MCP tool definitions.

This module defines the base classes for MCP tools.
"""

from typing import Dict, Any, Optional, List, Callable, Awaitable, Union
from pydantic import BaseModel, Field


class MCPToolResult(BaseModel):
    """Result of an MCP tool execution."""
    
    success: bool = True
    result: Any = None
    error: Optional[str] = None


class MCPTool:
    """Base class for MCP tools."""
    
    def __init__(
        self,
        name: str,
        description: str,
        func: Callable[[Dict[str, Any]], Union[Any, Awaitable[Any]]],
        parameters: Optional[Dict[str, Any]] = None
    ):
        """
        Initialize an MCP tool.
        
        Args:
            name: The name of the tool
            description: A description of what the tool does
            func: The function that implements the tool
            parameters: A dictionary describing the parameters the tool accepts
        """
        self.name = name
        self.description = description
        self.func = func
        self.parameters = parameters or {}
    
    async def execute(self, **kwargs) -> MCPToolResult:
        """
        Execute the tool with the given parameters.
        
        Args:
            **kwargs: The parameters to pass to the tool
            
        Returns:
            The result of the tool execution
        """
        try:
            # Check if the function is async
            if hasattr(self.func, "__await__"):
                result = await self.func(**kwargs)
            else:
                result = self.func(**kwargs)
            
            return MCPToolResult(success=True, result=result)
        except Exception as e:
            return MCPToolResult(success=False, error=str(e))
    
    def to_dict(self) -> Dict[str, Any]:
        """
        Convert the tool to a dictionary representation.
        
        Returns:
            A dictionary representation of the tool
        """
        return {
            "name": self.name,
            "description": self.description,
            "parameters": self.parameters
        }
    
    def to_langchain_tool(self):
        """
        Convert the tool to a LangChain tool.
        
        Returns:
            A LangChain tool
        """
        try:
            from langchain.tools import Tool
            
            async def _wrapper(**kwargs):
                result = await self.execute(**kwargs)
                if result.success:
                    return result.result
                else:
                    raise Exception(result.error)
            
            return Tool(
                name=self.name,
                description=self.description,
                func=_wrapper
            )
        except ImportError:
            raise ImportError("LangChain is not installed. Install with 'pip install langchain'")
    
    def to_openai_tool(self) -> Dict[str, Any]:
        """
        Convert the tool to an OpenAI tool specification.
        
        Returns:
            An OpenAI tool specification
        """
        # Convert parameters to OpenAI format
        properties = {}
        required = []
        
        for param_name, param_info in self.parameters.items():
            properties[param_name] = {
                "type": param_info.get("type", "string"),
                "description": param_info.get("description", "")
            }
            
            if param_info.get("required", False):
                required.append(param_name)
        
        return {
            "type": "function",
            "function": {
                "name": self.name,
                "description": self.description,
                "parameters": {
                    "type": "object",
                    "properties": properties,
                    "required": required
                }
            }
        }

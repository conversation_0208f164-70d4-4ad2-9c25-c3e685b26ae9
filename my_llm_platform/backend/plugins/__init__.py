"""
Plugin system for the LLM platform.
"""

from .manager import plugin_manager
from .base import (
    BasePlugin,
    PreprocessorPlugin,
    PostprocessorPlugin,
    CommandPlugin,
    ToolPlugin,
    UIPlugin,
    AgentPlugin,
    RAGPlugin
)
from .types import (
    PluginType,
    Message,
    PluginResult,
    PluginInfo,
    PluginConfig,
    ToolDefinition,
    UIComponent
)
from .router import router as plugin_router

__all__ = [
    # Plugin manager
    'plugin_manager',

    # Base plugin classes
    'BasePlugin',
    'PreprocessorPlugin',
    'PostprocessorPlugin',
    'CommandPlugin',
    'ToolPlugin',
    'UIPlugin',
    'AgentPlugin',
    'RAGPlugin',

    # Type definitions
    'PluginType',
    'Message',
    'PluginResult',
    'PluginInfo',
    'PluginConfig',
    'ToolDefinition',
    'UIComponent',

    # Router
    'plugin_router'
]

"""
Integration of the plugin system with the chat API.
"""

import logging
from typing import List, Dict, Any, Optional
from ..plugins.manager import plugin_manager
from ..plugins.types import Message as PluginMessage

logger = logging.getLogger(__name__)


async def process_with_plugins(messages: List[Dict[str, Any]], **kwargs) -> List[Dict[str, Any]]:
    """
    Process messages with all registered plugins.

    This function is called before sending messages to the LLM.
    It runs the messages through all preprocessor plugins, command plugins, agent plugins, and RAG plugins.

    Args:
        messages: List of messages to process
        **kwargs: Additional arguments to pass to plugins

    Returns:
        Processed messages
    """
    if not plugin_manager.enabled:
        return messages

    try:
        # Convert messages to plugin message format
        plugin_messages = [dict(msg) for msg in messages]

        # Check for command plugins first
        command_result = await plugin_manager.run_command_plugins(plugin_messages, **kwargs)
        if command_result and command_result.stop_processing:
            logger.info("Command plugin handled the request")
            return command_result.messages

        # Check for agent plugins
        agent_result = await plugin_manager.run_agent_plugins(plugin_messages, **kwargs)
        if agent_result and agent_result.stop_processing:
            logger.info("Agent plugin handled the request")
            return agent_result.messages

        # Check for RAG plugins
        rag_result = await plugin_manager.run_rag_plugins(plugin_messages, **kwargs)
        if rag_result and rag_result.messages != plugin_messages:
            logger.info("RAG plugin modified the messages")
            plugin_messages = rag_result.messages

        # Run preprocessor plugins
        processed_messages = await plugin_manager.run_preprocessors(plugin_messages, **kwargs)

        return processed_messages
    except Exception as e:
        logger.error(f"Error processing messages with plugins: {e}")
        return messages


async def process_response_with_plugins(
    messages: List[Dict[str, Any]],
    llm_response: Dict[str, Any],
    **kwargs
) -> Dict[str, Any]:
    """
    Process LLM response with all registered plugins.

    This function is called after receiving a response from the LLM.
    It runs the response through all postprocessor plugins.

    Args:
        messages: List of messages that were sent to the LLM
        llm_response: Response from the LLM
        **kwargs: Additional arguments to pass to plugins

    Returns:
        Processed response
    """
    if not plugin_manager.enabled:
        return llm_response

    try:
        # Convert messages to plugin message format
        plugin_messages = [dict(msg) for msg in messages]

        # Run postprocessor plugins
        processed_response = await plugin_manager.run_postprocessors(plugin_messages, dict(llm_response), **kwargs)

        return processed_response
    except Exception as e:
        logger.error(f"Error processing response with plugins: {e}")
        return llm_response


def get_tool_definitions() -> List[Dict[str, Any]]:
    """
    Get all tool definitions for LLM function/tool calling.

    Returns:
        List of tool definitions
    """
    if not plugin_manager.enabled:
        return []

    try:
        return plugin_manager.get_tool_definitions()
    except Exception as e:
        logger.error(f"Error getting tool definitions: {e}")
        return []


async def execute_tool(tool_name: str, parameters: Dict[str, Any], **kwargs) -> Any:
    """
    Execute a tool by name.

    Args:
        tool_name: Name of the tool to execute
        parameters: Parameters to pass to the tool
        **kwargs: Additional arguments to pass to the tool

    Returns:
        Result of the tool execution
    """
    if not plugin_manager.enabled:
        return {"error": "Plugin manager is disabled."}

    try:
        return await plugin_manager.execute_tool(tool_name, parameters, **kwargs)
    except Exception as e:
        logger.error(f"Error executing tool {tool_name}: {e}")
        return {"error": str(e)}

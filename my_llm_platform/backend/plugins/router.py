"""
FastAPI router for the plugin system API.
"""

import os
import json
import markdown
from pathlib import Path
import logging
from fastapi import APIRouter, HTTPException, Depends
from fastapi.responses import HTMLResponse
from typing import List, Dict, Any, Optional
from pydantic import BaseModel

from ..auth import get_current_user
from .manager import plugin_manager
from .types import PluginInfo, PluginType
from ..config import settings

router = APIRouter(prefix="/plugins", tags=["Plugins APIs"])
# ログ記録用のロガーを設定
logger = logging.getLogger("plugins.router")


class PluginConfigRequest(BaseModel):
    """Request model for plugin configuration."""
    config: Dict[str, Any]


@router.get("/", response_model=List[PluginInfo])
async def list_plugins(current_user = Depends(get_current_user)):
    """List all registered plugins."""
    return plugin_manager.get_all_plugin_info()


@router.get("/status")
async def get_plugin_system_status(current_user = Depends(get_current_user)):
    """Get the status of the plugin system."""
    logger.info("Plugin system status requested")
    logger.info(f"Plugin system status: {plugin_manager.enabled}")
    return {
        "enabled": plugin_manager.enabled,
        "plugin_count": len(plugin_manager.get_all_plugins()),
        "enabled_plugins": len([p for p in plugin_manager.get_all_plugins() if p.enabled]),
        "system_setting": settings.ENABLE_PLUGINS
    }


@router.get("/{plugin_id}", response_model=PluginInfo)
async def get_plugin(plugin_id: str, current_user = Depends(get_current_user)):
    """Get information about a specific plugin."""
    plugin_info = plugin_manager.get_plugin_info(plugin_id)
    if not plugin_info:
        raise HTTPException(status_code=404, detail=f"Plugin {plugin_id} not found")
    return plugin_info


@router.post("/{plugin_id}/enable")
async def enable_plugin(plugin_id: str, current_user = Depends(get_current_user)):
    """Enable a plugin."""
    plugin = plugin_manager.get_plugin(plugin_id)
    if not plugin:
        raise HTTPException(status_code=404, detail=f"Plugin {plugin_id} not found")

    plugin_manager.enable_plugin(plugin_id)
    return {"status": "success", "message": f"Plugin {plugin_id} enabled"}


@router.post("/{plugin_id}/disable")
async def disable_plugin(plugin_id: str, current_user = Depends(get_current_user)):
    """Disable a plugin."""
    plugin = plugin_manager.get_plugin(plugin_id)
    if not plugin:
        raise HTTPException(status_code=404, detail=f"Plugin {plugin_id} not found")

    plugin_manager.disable_plugin(plugin_id)
    return {"status": "success", "message": f"Plugin {plugin_id} disabled"}


@router.post("/{plugin_id}/configure")
async def configure_plugin(
    plugin_id: str,
    request: PluginConfigRequest,
    current_user = Depends(get_current_user)
):
    """Configure a plugin."""
    plugin = plugin_manager.get_plugin(plugin_id)
    if not plugin:
        raise HTTPException(status_code=404, detail=f"Plugin {plugin_id} not found")

    plugin_manager.configure_plugin(plugin_id, request.config)
    return {"status": "success", "message": f"Plugin {plugin_id} configured"}


@router.get("/types/{plugin_type}", response_model=List[PluginInfo])
async def get_plugins_by_type(plugin_type: PluginType, current_user = Depends(get_current_user)):
    """Get all plugins of a specific type."""
    plugins = plugin_manager.get_plugins_by_type(plugin_type)
    return [p.get_info() for p in plugins]


@router.get("/ui/components")
async def get_ui_components(current_user = Depends(get_current_user)):
    """Get all UI components for frontend rendering."""
    return plugin_manager.get_ui_components()


@router.get("/tools/definitions")
async def get_tool_definitions(current_user = Depends(get_current_user)):
    """Get all tool definitions for LLM function/tool calling."""
    return plugin_manager.get_tool_definitions()


@router.get("/{plugin_id}/docs", response_class=HTMLResponse)
async def get_plugin_docs(plugin_id: str, current_user = Depends(get_current_user)):
    """Get documentation for a specific plugin."""
    # Check if plugin exists
    plugin = plugin_manager.get_plugin(plugin_id)
    if not plugin:
        raise HTTPException(status_code=404, detail=f"Plugin {plugin_id} not found")

    # Look for documentation file in multiple locations
    # First try in the samples/docs directory
    docs_dir = Path(__file__).parent / "samples" / "docs"
    doc_file = docs_dir / f"{plugin_id}_plugin.md"

    # If not found, try in the documentation directory
    if not doc_file.exists():
        docs_dir = Path(__file__).parent.parent.parent / "docs" / "backend" / "plugins" / "samples"
        doc_file = docs_dir / f"{plugin_id}_plugin.md"

    if not doc_file.exists():
        # Try alternative naming
        doc_file = docs_dir / f"{plugin_id}.md"
        if not doc_file.exists():
            return f"<h1>Documentation for {plugin.name}</h1><p>No documentation available for this plugin.</p>"

    try:
        # Read and convert markdown to HTML
        md_content = doc_file.read_text(encoding="utf-8")
        html_content = markdown.markdown(md_content, extensions=['tables', 'fenced_code'])

        # Wrap in basic HTML with some styling
        return f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>{plugin.name} Documentation</title>
            <style>
                body {{ font-family: Arial, sans-serif; line-height: 1.6; max-width: 800px; margin: 0 auto; padding: 20px; }}
                pre {{ background-color: #f5f5f5; padding: 10px; border-radius: 5px; overflow-x: auto; }}
                code {{ background-color: #f5f5f5; padding: 2px 4px; border-radius: 3px; }}
                table {{ border-collapse: collapse; width: 100%; }}
                th, td {{ border: 1px solid #ddd; padding: 8px; }}
                th {{ background-color: #f2f2f2; }}
            </style>
        </head>
        <body>
            {html_content}
        </body>
        </html>
        """
    except Exception as e:
        return f"<h1>Error loading documentation</h1><p>Error: {str(e)}</p>"

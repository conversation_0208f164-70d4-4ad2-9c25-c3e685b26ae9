"""
MongoDBコネクタ - MongoDBデータベースへの接続を管理します
"""
import pandas as pd
from pymongo import MongoClient
from typing import Dict, List, Any, Optional
import logging
import json

from .base import DataConnector
from ..config import settings

logger = logging.getLogger(__name__)

class MongoDBConnector(DataConnector):
    """
    MongoDBデータベースコネクタ
    """
    
    def __init__(self, connection_string: Optional[str] = None, db_name: Optional[str] = None):
        """
        MongoDBコネクタを初期化します
        
        Args:
            connection_string: MongoDB接続文字列（指定しない場合は設定から取得）
            db_name: データベース名（指定しない場合は設定から取得）
        """
        self.connection_string = connection_string or settings.MONGODB_URI
        self.db_name = db_name or settings.MONGODB_DB_NAME
        self.client = None
        self.db = None
        
    def connect(self) -> bool:
        """
        MongoDBデータベースへの接続を確立します
        
        Returns:
            bool: 接続が成功した場合はTrue、それ以外はFalse
        """
        try:
            if not self.connection_string:
                logger.error("MongoDB接続文字列が設定されていません")
                return False
                
            self.client = MongoClient(self.connection_string)
            self.db = self.client[self.db_name]
            # 接続テスト
            self.client.server_info()
            logger.info(f"MongoDBデータベース '{self.db_name}' に接続しました")
            return True
        except Exception as e:
            logger.error(f"MongoDBデータベース接続エラー: {str(e)}")
            return False
    
    def execute_query(self, query: str, params: Optional[Dict[str, Any]] = None) -> pd.DataFrame:
        """
        MongoDBクエリを実行し、結果をDataFrameとして返します
        
        Args:
            query: 実行するMongoDBクエリ（JSON形式の文字列）
            params: 追加パラメータ（コレクション名など）
            
        Returns:
            pd.DataFrame: クエリ結果を含むDataFrame
        """
        try:
            if not self.db:
                self.connect()
                
            # パラメータからコレクション名を取得
            collection_name = params.get("collection") if params else None
            if not collection_name:
                raise ValueError("コレクション名が指定されていません")
                
            collection = self.db[collection_name]
            
            # クエリ文字列をJSONに変換
            query_dict = json.loads(query)
            
            # クエリタイプを判断
            if "find" in query_dict:
                # find操作
                find_query = query_dict["find"]
                projection = query_dict.get("projection", None)
                limit = query_dict.get("limit", 0)
                skip = query_dict.get("skip", 0)
                sort = query_dict.get("sort", None)
                
                cursor = collection.find(
                    filter=find_query,
                    projection=projection
                )
                
                if sort:
                    cursor = cursor.sort(sort)
                if skip:
                    cursor = cursor.skip(skip)
                if limit:
                    cursor = cursor.limit(limit)
                    
                # カーソルをリストに変換してDataFrameを作成
                result = pd.DataFrame(list(cursor))
                
            elif "aggregate" in query_dict:
                # aggregate操作
                pipeline = query_dict["aggregate"]
                cursor = collection.aggregate(pipeline)
                result = pd.DataFrame(list(cursor))
                
            else:
                raise ValueError("サポートされていないクエリタイプです")
                
            # ObjectIdをstr型に変換
            if '_id' in result.columns:
                result['_id'] = result['_id'].astype(str)
                
            logger.info(f"MongoDBクエリを実行しました: {query[:100]}...")
            return result
        except Exception as e:
            logger.error(f"MongoDBクエリ実行エラー: {str(e)}")
            raise
    
    def get_table_schema(self, table_name: str) -> Dict[str, Any]:
        """
        コレクションのスキーマ情報を取得します（MongoDBはスキーマレスですが、サンプルドキュメントから推測）
        
        Args:
            table_name: スキーマを取得するコレクション名
            
        Returns:
            Dict[str, Any]: コレクションスキーマ情報
        """
        if not self.db:
            self.connect()
            
        collection = self.db[table_name]
        
        # サンプルドキュメントを取得
        sample_doc = collection.find_one()
        
        if not sample_doc:
            return {"table_name": table_name, "fields": []}
            
        # フィールド情報を抽出
        fields = []
        for field_name, value in sample_doc.items():
            field_type = type(value).__name__
            fields.append({
                "name": field_name,
                "type": field_type,
                "sample": str(value)[:50] if value is not None else None
            })
            
        return {
            "table_name": table_name,
            "fields": fields,
            "document_count": collection.count_documents({})
        }
    
    def get_tables(self) -> List[str]:
        """
        利用可能なコレクションのリストを取得します
        
        Returns:
            List[str]: コレクション名のリスト
        """
        if not self.db:
            self.connect()
            
        return self.db.list_collection_names()
    
    def close(self) -> None:
        """
        データベース接続を閉じます
        """
        if self.client:
            self.client.close()
            logger.info("MongoDBデータベース接続を閉じました")

"""
データコネクタファクトリ - 適切なデータコネクタを作成します
"""
from typing import Dict, Optional, Any
import logging

from .base import DataConnector
from .postgresql import PostgreSQLConnector
from .mongodb import MongoDBConnector
from ..models import DataSourceType
from ..config import settings

logger = logging.getLogger(__name__)

class DataConnectorFactory:
    """
    データコネクタファクトリクラス
    データソースタイプに基づいて適切なコネクタを作成します
    """
    
    @staticmethod
    def create_connector(data_source_type: str, connection_params: Optional[Dict[str, Any]] = None) -> DataConnector:
        """
        データソースタイプに基づいてコネクタを作成します
        
        Args:
            data_source_type: データソースタイプ
            connection_params: 接続パラメータ（オプション）
            
        Returns:
            DataConnector: 作成されたデータコネクタ
            
        Raises:
            ValueError: サポートされていないデータソースタイプの場合
        """
        connection_params = connection_params or {}
        
        if data_source_type == DataSourceType.POSTGRESQL:
            connection_string = connection_params.get("connection_string", settings.SUPABASE_DB_URI)
            return PostgreSQLConnector(connection_string=connection_string)
            
        elif data_source_type == DataSourceType.MONGODB:
            connection_string = connection_params.get("connection_string", settings.MONGODB_URI)
            db_name = connection_params.get("db_name", settings.MONGODB_DB_NAME)
            return MongoDBConnector(connection_string=connection_string, db_name=db_name)
            
        elif data_source_type == DataSourceType.MYSQL:
            # 将来的に実装予定
            raise NotImplementedError("MySQLコネクタはまだ実装されていません")
            
        elif data_source_type == DataSourceType.ORACLE:
            # 将来的に実装予定
            raise NotImplementedError("Oracleコネクタはまだ実装されていません")
            
        else:
            logger.error(f"サポートされていないデータソースタイプ: {data_source_type}")
            raise ValueError(f"サポートされていないデータソースタイプ: {data_source_type}")
            
    @staticmethod
    def get_default_connector() -> DataConnector:
        """
        デフォルトのデータコネクタを取得します
        
        Returns:
            DataConnector: デフォルトのデータコネクタ
        """
        # デフォルトはPostgreSQLコネクタ
        return PostgreSQLConnector()

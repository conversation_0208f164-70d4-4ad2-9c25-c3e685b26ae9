"""
データコネクタベースクラス - 様々なデータソースへの接続を管理します
"""
from abc import ABC, abstractmethod
import pandas as pd
from typing import Dict, List, Any, Optional, Union

class DataConnector(ABC):
    """
    データコネクタの抽象基底クラス
    すべてのデータソースコネクタはこのクラスを継承する必要があります
    """
    
    @abstractmethod
    def connect(self) -> bool:
        """
        データソースへの接続を確立します
        
        Returns:
            bool: 接続が成功した場合はTrue、それ以外はFalse
        """
        pass
    
    @abstractmethod
    def execute_query(self, query: str, params: Optional[Dict[str, Any]] = None) -> pd.DataFrame:
        """
        クエリを実行し、結果をDataFrameとして返します
        
        Args:
            query: 実行するクエリ文字列
            params: クエリパラメータ（オプション）
            
        Returns:
            pd.DataFrame: クエリ結果を含むDataFrame
        """
        pass
    
    @abstractmethod
    def get_table_schema(self, table_name: str) -> Dict[str, Any]:
        """
        テーブルのスキーマ情報を取得します
        
        Args:
            table_name: スキーマを取得するテーブル名
            
        Returns:
            Dict[str, Any]: テーブルスキーマ情報
        """
        pass
    
    @abstractmethod
    def get_tables(self) -> List[str]:
        """
        利用可能なテーブルのリストを取得します
        
        Returns:
            List[str]: テーブル名のリスト
        """
        pass
    
    @abstractmethod
    def close(self) -> None:
        """
        データソース接続を閉じます
        """
        pass
    
    def __enter__(self):
        """
        コンテキストマネージャのエントリーポイント
        
        Returns:
            DataConnector: 自身のインスタンス
        """
        self.connect()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """
        コンテキストマネージャの終了ポイント
        
        Args:
            exc_type: 例外タイプ
            exc_val: 例外値
            exc_tb: トレースバック
        """
        self.close()

"""
PostgreSQLコネクタ - PostgreSQLデータベースへの接続を管理します
"""
import pandas as pd
import sqlalchemy
from sqlalchemy import inspect, text
from typing import Dict, List, Any, Optional
import logging

from .base import DataConnector
from ..config import settings

logger = logging.getLogger(__name__)

class PostgreSQLConnector(DataConnector):
    """
    PostgreSQLデータベースコネクタ
    """
    
    def __init__(self, connection_string: Optional[str] = None):
        """
        PostgreSQLコネクタを初期化します
        
        Args:
            connection_string: データベース接続文字列（指定しない場合は設定から取得）
        """
        self.connection_string = connection_string or settings.SUPABASE_DB_URI
        self.engine = None
        self.connection = None
        self.inspector = None
        
    def connect(self) -> bool:
        """
        PostgreSQLデータベースへの接続を確立します
        
        Returns:
            bool: 接続が成功した場合はTrue、それ以外はFalse
        """
        try:
            self.engine = sqlalchemy.create_engine(self.connection_string)
            self.connection = self.engine.connect()
            self.inspector = inspect(self.engine)
            logger.info("PostgreSQLデータベースに接続しました")
            return True
        except Exception as e:
            logger.error(f"PostgreSQLデータベース接続エラー: {str(e)}")
            return False
    
    def execute_query(self, query: str, params: Optional[Dict[str, Any]] = None) -> pd.DataFrame:
        """
        SQLクエリを実行し、結果をDataFrameとして返します
        
        Args:
            query: 実行するSQL文
            params: クエリパラメータ（オプション）
            
        Returns:
            pd.DataFrame: クエリ結果を含むDataFrame
        """
        try:
            if not self.connection:
                self.connect()
                
            if params:
                result = pd.read_sql(text(query), self.connection, params=params)
            else:
                result = pd.read_sql(text(query), self.connection)
                
            logger.info(f"クエリを実行しました: {query[:100]}...")
            return result
        except Exception as e:
            logger.error(f"クエリ実行エラー: {str(e)}")
            raise
    
    def get_table_schema(self, table_name: str) -> Dict[str, Any]:
        """
        テーブルのスキーマ情報を取得します
        
        Args:
            table_name: スキーマを取得するテーブル名
            
        Returns:
            Dict[str, Any]: テーブルスキーマ情報
        """
        if not self.inspector:
            self.connect()
            
        columns = self.inspector.get_columns(table_name)
        primary_keys = self.inspector.get_primary_keys(table_name)
        foreign_keys = self.inspector.get_foreign_keys(table_name)
        
        return {
            "table_name": table_name,
            "columns": columns,
            "primary_keys": primary_keys,
            "foreign_keys": foreign_keys
        }
    
    def get_tables(self) -> List[str]:
        """
        利用可能なテーブルのリストを取得します
        
        Returns:
            List[str]: テーブル名のリスト
        """
        if not self.inspector:
            self.connect()
            
        return self.inspector.get_table_names()
    
    def close(self) -> None:
        """
        データベース接続を閉じます
        """
        if self.connection:
            self.connection.close()
            self.engine.dispose()
            logger.info("PostgreSQLデータベース接続を閉じました")

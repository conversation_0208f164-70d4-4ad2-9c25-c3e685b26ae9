"""
テストスクリプト - モジュールのインポートをテストします
"""
import sys
import os

# 現在のディレクトリをPythonパスに追加
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

print("Python path:")
for path in sys.path:
    print(f"  - {path}")

try:
    print("\nTesting imports...")
    
    # 設定のインポート
    from config import settings
    print("✅ config.settings imported successfully")
    
    # モデルのインポート
    from models import Language, ChartType
    print("✅ models imported successfully")
    
    # コネクタのインポート
    from connectors import DataConnectorFactory
    print("✅ connectors imported successfully")
    
    # ユーティリティのインポート
    from utils import recommend_chart_types, detect_language
    print("✅ utils imported successfully")
    
    # サービスのインポート
    from services import SQLGenerationService, ReportService
    print("✅ services imported successfully")
    
    # APIのインポート
    from api import router
    print("✅ api imported successfully")
    
    print("\nAll imports successful!")
    
except ImportError as e:
    print(f"\n❌ Import error: {e}")
    
except Exception as e:
    print(f"\n❌ Unexpected error: {e}")


"""
Smart Report Assistant - 自然言語からデータ分析レポートを生成するアプリケーション
"""
import os
import logging
import sys
from fastapi import FastAPI, Request, Depends, HTTPException, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from fastapi.security import OAuth2PasswordBearer
import time
from typing import Dict, List, Any, Optional, Callable
import traceback

# 内部モジュールのインポート
from .config import settings
from .api import router as api_router
from .utils import get_user_id_from_token, create_audit_log
from .models import ErrorResponse

# ロギングの設定
logging.basicConfig(
    level=getattr(logging, settings.LOG_LEVEL),
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler(sys.stdout)]
)

logger = logging.getLogger(__name__)

# FastAPIアプリケーションの作成
app = FastAPI(
    title="Smart Report Assistant",
    description="自然言語からデータ分析レポートを生成するAPIサービス",
    version="1.0.0"
)

# CORSミドルウェアの設定
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# リクエスト処理時間計測ミドルウェア
@app.middleware("http")
async def add_process_time_header(request: Request, call_next):
    """
    リクエスト処理時間を計測し、レスポンスヘッダーに追加します
    """
    start_time = time.time()
    try:
        response = await call_next(request)
        process_time = time.time() - start_time
        response.headers["X-Process-Time"] = str(process_time)
        return response
    except Exception as e:
        process_time = time.time() - start_time
        logger.error(f"リクエスト処理中にエラーが発生しました: {str(e)}")
        logger.error(traceback.format_exc())

        # エラーレスポンスの作成
        error_response = JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content={"error": "内部サーバーエラー", "detail": str(e)}
        )
        error_response.headers["X-Process-Time"] = str(process_time)
        return error_response

# 監査ログミドルウェア
@app.middleware("http")
async def audit_log_middleware(request: Request, call_next):
    """
    APIリクエストの監査ログを記録します
    """
    # リクエスト情報の取得
    path = request.url.path
    method = request.method
    client_host = request.client.host if request.client else "unknown"
    user_agent = request.headers.get("user-agent", "unknown")

    # 監査対象外のパスをスキップ
    skip_audit_paths = ["/docs", "/redoc", "/openapi.json", "/health"]
    if any(path.startswith(skip_path) for skip_path in skip_audit_paths):
        return await call_next(request)

    # リクエスト開始ログ
    logger.info(f"リクエスト開始: {method} {path} from {client_host}")

    # レスポンスの処理
    start_time = time.time()
    response = await call_next(request)
    process_time = time.time() - start_time

    # レスポンス完了ログ
    status_code = response.status_code
    logger.info(f"リクエスト完了: {method} {path} - {status_code} in {process_time:.4f}s")

    return response

# APIルーターの登録
app.include_router(api_router, prefix="/api/v1")

# ヘルスチェックエンドポイント
@app.get("/health")
async def health_check():
    """
    アプリケーションの健全性を確認します
    """
    return {
        "status": "ok",
        "version": "1.0.0",
        "environment": settings.ENVIRONMENT
    }

# エラーハンドラー
@app.exception_handler(HTTPException)
async def http_exception_handler(request: Request, exc: HTTPException):
    """
    HTTPExceptionのハンドラー
    """
    logger.warning(f"HTTPException: {exc.status_code} - {exc.detail}")

    # 監査ログの記録
    try:
        token = request.headers.get("Authorization", "").replace("Bearer ", "")
        if token:
            user_id = get_user_id_from_token(token)
            create_audit_log(
                user_id=user_id,
                action=request.method,
                resource=request.url.path,
                status="error",
                details={"status_code": exc.status_code, "detail": exc.detail},
                request=request
            )
    except Exception as e:
        logger.error(f"監査ログ記録エラー: {str(e)}")

    return JSONResponse(
        status_code=exc.status_code,
        content={"error": exc.detail}
    )

@app.exception_handler(Exception)
async def general_exception_handler(request: Request, exc: Exception):
    """
    一般的な例外のハンドラー
    """
    logger.error(f"未処理の例外: {str(exc)}")
    logger.error(traceback.format_exc())

    # 監査ログの記録
    try:
        token = request.headers.get("Authorization", "").replace("Bearer ", "")
        if token:
            user_id = get_user_id_from_token(token)
            create_audit_log(
                user_id=user_id,
                action=request.method,
                resource=request.url.path,
                status="error",
                details={"error": str(exc)},
                request=request
            )
    except Exception as e:
        logger.error(f"監査ログ記録エラー: {str(e)}")

    return JSONResponse(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        content={"error": "内部サーバーエラー", "detail": str(exc)}
    )

# アプリケーション起動時の処理
@app.on_event("startup")
async def startup_event():
    """
    アプリケーション起動時に実行される処理
    """
    logger.info("Smart Report Assistantを起動しています...")

    # 設定の検証
    required_settings = [
        "OPENAI_API_KEY"
    ]

    recommended_settings = [
        "SUPABASE_URL",
        "SUPABASE_SERVICE_ROLE_KEY",
        "SUPABASE_DB_URI",
        "SUPABASE_JWT_SECRET"
    ]

    missing_required = [setting for setting in required_settings if not getattr(settings, setting, None)]
    missing_recommended = [setting for setting in recommended_settings if not getattr(settings, setting, None) or getattr(settings, setting) == "dummy-key" or getattr(settings, setting) == "https://example.supabase.co"]

    if missing_required:
        logger.error(f"必須設定が不足しています: {', '.join(missing_required)}")
        logger.error("アプリケーションを終了します")
        sys.exit(1)

    if missing_recommended:
        logger.warning(f"推奨設定が不足しています: {', '.join(missing_recommended)}")
        logger.warning("一部の機能が制限される可能性があります")
        logger.warning("ローカルストレージにフォールバックします")

    # 一時ディレクトリの作成
    os.makedirs(settings.TEMP_DIR, exist_ok=True)

    # アプリケーション専用の一時ディレクトリを作成
    app_temp_dir = os.path.join(settings.TEMP_DIR, "smart_report_assistant")
    os.makedirs(app_temp_dir, exist_ok=True)

    # 設定を更新して、アプリケーション専用の一時ディレクトリを使用するようにする
    settings.TEMP_DIR = app_temp_dir

    logger.info("Smart Report Assistantが正常に起動しました")

# アプリケーション終了時の処理
@app.on_event("shutdown")
async def shutdown_event():
    """
    アプリケーション終了時に実行される処理
    """
    logger.info("Smart Report Assistantをシャットダウンしています...")

    # 一時ファイルのクリーンアップ
    try:
        import glob
        import os.path

        # アプリケーション専用の一時ディレクトリを使用する
        app_temp_dir = os.path.join(settings.TEMP_DIR, "smart_report_assistant")

        # ディレクトリが存在する場合のみクリーンアップを実行
        if os.path.exists(app_temp_dir) and os.path.isdir(app_temp_dir):
            temp_files = glob.glob(os.path.join(app_temp_dir, "*"))
            for temp_file in temp_files:
                try:
                    # ファイルの場合のみ削除を試みる
                    if os.path.isfile(temp_file):
                        os.remove(temp_file)
                        logger.debug(f"一時ファイルを削除しました: {temp_file}")
                    elif os.path.isdir(temp_file):
                        import shutil
                        shutil.rmtree(temp_file)
                        logger.debug(f"一時ディレクトリを削除しました: {temp_file}")
                except Exception as e:
                    logger.warning(f"一時ファイルの削除に失敗しました: {temp_file} - {str(e)}")
    except Exception as e:
        logger.error(f"一時ファイルのクリーンアップ中にエラーが発生しました: {str(e)}")

    logger.info("Smart Report Assistantが正常にシャットダウンしました")

    # python -m uvicorn backend.routers.smart-report-assistant.main:app --reload --host 0.0.0.0 --port 8000

    # 本番環境での起動例
# uvicorn main:app --host 0.0.0.0 --port 8000 --workers 4

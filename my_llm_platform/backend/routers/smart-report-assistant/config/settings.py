"""
設定モジュール - アプリケーション全体の設定を管理します
"""
import os
from pydantic import Field
from pydantic_settings import BaseSettings
from typing import Dict, Any, List, Optional

class Settings(BaseSettings):
    """
    アプリケーション設定クラス
    環境変数から設定を読み込み、デフォルト値を提供します
    """
    # データベース設定
    SUPABASE_URL: str = Field("https://example.supabase.co", env="SUPABASE_URL")
    SUPABASE_SERVICE_ROLE_KEY: str = Field("dummy-key", env="SUPABASE_SERVICE_ROLE_KEY")
    SUPABASE_DB_URI: str = Field("sqlite:///./test.db", env="SUPABASE_DB_URI")
    SUPABASE_JWT_SECRET: str = Field("dummy-secret", env="SUPABASE_JWT_SECRET")

    # MongoDB設定（新規追加）
    MONGODB_URI: Optional[str] = Field(None, env="MONGODB_URI")
    MONGODB_DB_NAME: str = Field("smart_report", env="MONGODB_DB_NAME")

    # MySQL設定（新規追加）
    MYSQL_URI: Optional[str] = Field(None, env="MYSQL_URI")

    # Oracle設定（新規追加）
    ORACLE_URI: Optional[str] = Field(None, env="ORACLE_URI")

    # AI設定
    OPENAI_API_KEY: str = Field("sk-dummy-key", env="OPENAI_API_KEY")
    DEFAULT_MODEL: str = Field("gpt-4", env="DEFAULT_MODEL")
    DEFAULT_TEMPERATURE: float = Field(0.2, env="DEFAULT_TEMPERATURE")

    # アプリケーション設定
    TEMP_DIR: str = Field("/tmp", env="TEMP_DIR")
    LOG_LEVEL: str = Field("INFO", env="LOG_LEVEL")
    ENABLE_AUDIT_LOG: bool = Field(True, env="ENABLE_AUDIT_LOG")

    # 多言語サポート
    DEFAULT_LANGUAGE: str = Field("ja", env="DEFAULT_LANGUAGE")
    SUPPORTED_LANGUAGES: List[str] = Field(["ja", "zh", "en"], env="SUPPORTED_LANGUAGES")

    # キャッシュ設定
    ENABLE_QUERY_CACHE: bool = Field(True, env="ENABLE_QUERY_CACHE")
    CACHE_TTL_SECONDS: int = Field(3600, env="CACHE_TTL_SECONDS")

    # セキュリティ設定
    TOKEN_EXPIRY_MINUTES: int = Field(60, env="TOKEN_EXPIRY_MINUTES")
    ACCESS_TOKEN_EXPIRE_MINUTES: int = Field(60, env="ACCESS_TOKEN_EXPIRE_MINUTES")
    CORS_ORIGINS: List[str] = Field(["*"], env="CORS_ORIGINS")
    JWT_SECRET_KEY: str = Field("dummy-jwt-secret-key-change-in-production", env="JWT_SECRET_KEY")

    # チャート設定
    SUPPORTED_CHART_TYPES: List[str] = Field(
        ["bar", "line", "pie", "scatter", "heatmap", "area", "histogram"],
        env="SUPPORTED_CHART_TYPES"
    )

    # 環境設定
    ENVIRONMENT: str = Field("development", env="ENVIRONMENT")

    model_config = {
        "env_file": ".env",
        "case_sensitive": True
    }

# シングルトンインスタンスを作成
settings = Settings()

"""
ユーザー設定サービス - ユーザーの設定と好みを管理します
"""
import logging
from typing import Dict, Any, Optional, List
from datetime import datetime

from ..models import UserPreference, ChartType, Language

logger = logging.getLogger(__name__)

class UserPreferenceService:
    """
    ユーザーの設定と好みを管理するサービス
    """
    
    def __init__(self):
        """
        UserPreferenceServiceを初期化します
        """
        self.user_preferences: Dict[str, UserPreference] = {}
    
    def get_user_preference(self, user_id: str) -> UserPreference:
        """
        ユーザーの設定を取得します。存在しない場合は新しく作成します。
        
        Args:
            user_id: ユーザーID
            
        Returns:
            UserPreference: ユーザー設定
        """
        if user_id not in self.user_preferences:
            self.user_preferences[user_id] = UserPreference(user_id=user_id)
        
        return self.user_preferences[user_id]
    
    def update_user_preference(self, user_id: str, preferences: Dict[str, Any]) -> UserPreference:
        """
        ユーザーの設定を更新します
        
        Args:
            user_id: ユーザーID
            preferences: 更新する設定
            
        Returns:
            UserPreference: 更新されたユーザー設定
        """
        user_pref = self.get_user_preference(user_id)
        
        # 設定の更新
        if "preferred_language" in preferences:
            user_pref.preferred_language = preferences["preferred_language"]
        
        if "theme" in preferences:
            user_pref.theme = preferences["theme"]
        
        if "preferred_chart_types" in preferences:
            user_pref.preferred_chart_types.update(preferences["preferred_chart_types"])
        
        if "favorite_reports" in preferences:
            user_pref.favorite_reports = preferences["favorite_reports"]
        
        logger.info(f"ユーザー {user_id} の設定を更新しました")
        return user_pref
    
    def set_preferred_language(self, user_id: str, language: Language) -> None:
        """
        ユーザーの優先言語を設定します
        
        Args:
            user_id: ユーザーID
            language: 言語
        """
        user_pref = self.get_user_preference(user_id)
        user_pref.preferred_language = language
        logger.info(f"ユーザー {user_id} の優先言語を {language} に設定しました")
    
    def get_preferred_language(self, user_id: str) -> Language:
        """
        ユーザーの優先言語を取得します
        
        Args:
            user_id: ユーザーID
            
        Returns:
            Language: 優先言語
        """
        user_pref = self.get_user_preference(user_id)
        return user_pref.preferred_language
    
    def set_preferred_chart_type(self, user_id: str, query_context: str, chart_type: ChartType) -> None:
        """
        特定のクエリコンテキストに対するユーザーの優先チャートタイプを設定します
        
        Args:
            user_id: ユーザーID
            query_context: クエリコンテキスト（タイトルなど）
            chart_type: チャートタイプ
        """
        user_pref = self.get_user_preference(user_id)
        user_pref.preferred_chart_types[query_context] = chart_type
        logger.info(f"ユーザー {user_id} のクエリコンテキスト '{query_context}' の優先チャートタイプを {chart_type} に設定しました")
    
    def get_preferred_chart_type(self, user_id: str, query_context: str) -> Optional[ChartType]:
        """
        特定のクエリコンテキストに対するユーザーの優先チャートタイプを取得します
        
        Args:
            user_id: ユーザーID
            query_context: クエリコンテキスト（タイトルなど）
            
        Returns:
            Optional[ChartType]: 優先チャートタイプ（設定されていない場合はNone）
        """
        user_pref = self.get_user_preference(user_id)
        return user_pref.preferred_chart_types.get(query_context)
    
    def add_favorite_report(self, user_id: str, report_id: str) -> None:
        """
        ユーザーのお気に入りレポートにレポートを追加します
        
        Args:
            user_id: ユーザーID
            report_id: レポートID
        """
        user_pref = self.get_user_preference(user_id)
        if report_id not in user_pref.favorite_reports:
            user_pref.favorite_reports.append(report_id)
            logger.info(f"ユーザー {user_id} のお気に入りレポートに {report_id} を追加しました")
    
    def remove_favorite_report(self, user_id: str, report_id: str) -> None:
        """
        ユーザーのお気に入りレポートからレポートを削除します
        
        Args:
            user_id: ユーザーID
            report_id: レポートID
        """
        user_pref = self.get_user_preference(user_id)
        if report_id in user_pref.favorite_reports:
            user_pref.favorite_reports.remove(report_id)
            logger.info(f"ユーザー {user_id} のお気に入りレポートから {report_id} を削除しました")
    
    def is_favorite_report(self, user_id: str, report_id: str) -> bool:
        """
        レポートがユーザーのお気に入りかどうかを確認します
        
        Args:
            user_id: ユーザーID
            report_id: レポートID
            
        Returns:
            bool: お気に入りの場合はTrue、それ以外はFalse
        """
        user_pref = self.get_user_preference(user_id)
        return report_id in user_pref.favorite_reports
    
    def update_recent_query(self, user_id: str, query: str, max_queries: int = 10) -> None:
        """
        ユーザーの最近のクエリを更新します
        
        Args:
            user_id: ユーザーID
            query: クエリ
            max_queries: 保存する最大クエリ数
        """
        user_pref = self.get_user_preference(user_id)
        
        # 既存のクエリを削除
        if query in user_pref.recent_queries:
            user_pref.recent_queries.remove(query)
        
        # 新しいクエリを先頭に追加
        user_pref.recent_queries.insert(0, query)
        
        # 最大数を超えた場合は古いクエリを削除
        if len(user_pref.recent_queries) > max_queries:
            user_pref.recent_queries = user_pref.recent_queries[:max_queries]
        
        logger.info(f"ユーザー {user_id} の最近のクエリを更新しました")
    
    def get_recent_queries(self, user_id: str) -> List[str]:
        """
        ユーザーの最近のクエリを取得します
        
        Args:
            user_id: ユーザーID
            
        Returns:
            List[str]: 最近のクエリリスト
        """
        user_pref = self.get_user_preference(user_id)
        return user_pref.recent_queries

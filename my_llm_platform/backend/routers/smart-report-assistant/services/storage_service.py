"""
ストレージサービス - ファイルの保存と取得を管理します
"""
import os
import logging
from typing import Dict, Any, Optional, BinaryIO, Union
from supabase import create_client
import boto3
from botocore.exceptions import ClientError

from ..config import settings

logger = logging.getLogger(__name__)

class StorageService:
    """
    ファイルストレージを管理するサービス
    """

    def __init__(self, storage_type: str = "supabase"):
        """
        StorageServiceを初期化します

        Args:
            storage_type: ストレージタイプ（"supabase", "s3", "local"）
        """
        self.storage_type = storage_type
        self.supabase = None
        self.s3 = None
        self.s3_bucket = None
        self.local_storage_path = None

        try:
            if storage_type == "supabase":
                self._init_supabase()
            elif storage_type == "s3":
                self._init_s3()
            elif storage_type == "local":
                self._init_local()
            else:
                logger.error(f"サポートされていないストレージタイプ: {storage_type}")
                logger.info("ローカルストレージにフォールバックします")
                self._init_local()
                self.storage_type = "local"
        except Exception as e:
            logger.error(f"ストレージの初期化エラー: {str(e)}")
            logger.info("ローカルストレージにフォールバックします")
            self._init_local()
            self.storage_type = "local"

    def _init_supabase(self):
        """
        Supabaseストレージを初期化します
        """
        try:
            # Supabase URLとキーが有効かチェック
            if not settings.SUPABASE_URL or settings.SUPABASE_URL == "https://example.supabase.co":
                logger.warning("有効なSUPABASE_URLが設定されていません")
                raise ValueError("有効なSUPABASE_URLが設定されていません")

            if not settings.SUPABASE_SERVICE_ROLE_KEY or settings.SUPABASE_SERVICE_ROLE_KEY == "dummy-key":
                logger.warning("有効なSUPABASE_SERVICE_ROLE_KEYが設定されていません")
                raise ValueError("有効なSUPABASE_SERVICE_ROLE_KEYが設定されていません")

            self.supabase = create_client(
                settings.SUPABASE_URL,
                settings.SUPABASE_SERVICE_ROLE_KEY
            )
            logger.info("Supabaseストレージを初期化しました")
        except Exception as e:
            logger.error(f"Supabaseストレージの初期化エラー: {str(e)}")
            raise

    def _init_s3(self):
        """
        Amazon S3ストレージを初期化します
        """
        try:
            self.s3 = boto3.client(
                's3',
                aws_access_key_id=os.getenv("AWS_ACCESS_KEY_ID"),
                aws_secret_access_key=os.getenv("AWS_SECRET_ACCESS_KEY"),
                region_name=os.getenv("AWS_REGION", "ap-northeast-1")
            )
            self.s3_bucket = os.getenv("AWS_S3_BUCKET")
            logger.info("Amazon S3ストレージを初期化しました")
        except Exception as e:
            logger.error(f"Amazon S3ストレージの初期化エラー: {str(e)}")
            raise

    def _init_local(self):
        """
        ローカルファイルストレージを初期化します
        """
        try:
            self.local_storage_path = os.getenv("LOCAL_STORAGE_PATH", "./storage")
            os.makedirs(self.local_storage_path, exist_ok=True)
            logger.info(f"ローカルストレージを初期化しました: {self.local_storage_path}")
        except Exception as e:
            logger.error(f"ローカルストレージの初期化エラー: {str(e)}")
            raise

    def upload_file(self, file_path: str, storage_path: str) -> str:
        """
        ファイルをストレージにアップロードします

        Args:
            file_path: アップロードするファイルのパス
            storage_path: ストレージ内の保存先パス

        Returns:
            str: アップロードされたファイルのURL
        """
        try:
            if self.storage_type == "supabase" and self.supabase:
                try:
                    return self._upload_to_supabase(file_path, storage_path)
                except Exception as e:
                    logger.error(f"Supabaseへのアップロードに失敗しました: {str(e)}")
                    logger.info("ローカルストレージにフォールバックします")
                    return self._upload_to_local(file_path, storage_path)
            elif self.storage_type == "s3" and self.s3:
                try:
                    return self._upload_to_s3(file_path, storage_path)
                except Exception as e:
                    logger.error(f"S3へのアップロードに失敗しました: {str(e)}")
                    logger.info("ローカルストレージにフォールバックします")
                    return self._upload_to_local(file_path, storage_path)
            elif self.storage_type == "local" or not self.storage_type:
                return self._upload_to_local(file_path, storage_path)
            else:
                logger.warning(f"サポートされていないストレージタイプ: {self.storage_type}")
                logger.info("ローカルストレージにフォールバックします")
                return self._upload_to_local(file_path, storage_path)
        except Exception as e:
            logger.error(f"ファイルアップロードエラー: {str(e)}")
            # 最終的なフォールバック - ローカルストレージを試みる
            try:
                return self._upload_to_local(file_path, storage_path)
            except Exception as local_error:
                logger.error(f"ローカルストレージへのフォールバックも失敗しました: {str(local_error)}")
                raise

    def _upload_to_supabase(self, file_path: str, storage_path: str) -> str:
        """
        ファイルをSupabaseストレージにアップロードします

        Args:
            file_path: アップロードするファイルのパス
            storage_path: ストレージ内の保存先パス

        Returns:
            str: アップロードされたファイルのURL
        """
        # バケット名とファイルパスを分離
        parts = storage_path.split('/', 1)
        bucket = parts[0]
        path = parts[1] if len(parts) > 1 else storage_path

        with open(file_path, "rb") as f:
            self.supabase.storage.from_(bucket).upload(
                file=f,
                path=path,
                file_options={"upsert": True}
            )

        # 公開URLを返す
        return f"{settings.SUPABASE_URL}/storage/v1/object/public/{bucket}/{path}"

    def _upload_to_s3(self, file_path: str, storage_path: str) -> str:
        """
        ファイルをAmazon S3ストレージにアップロードします

        Args:
            file_path: アップロードするファイルのパス
            storage_path: ストレージ内の保存先パス

        Returns:
            str: アップロードされたファイルのURL
        """
        with open(file_path, "rb") as f:
            self.s3.upload_fileobj(f, self.s3_bucket, storage_path)

        # 公開URLを返す
        return f"https://{self.s3_bucket}.s3.amazonaws.com/{storage_path}"

    def _upload_to_local(self, file_path: str, storage_path: str) -> str:
        """
        ファイルをローカルストレージにアップロードします

        Args:
            file_path: アップロードするファイルのパス
            storage_path: ストレージ内の保存先パス

        Returns:
            str: アップロードされたファイルのパス
        """
        # 保存先ディレクトリを作成
        dest_dir = os.path.dirname(os.path.join(self.local_storage_path, storage_path))
        os.makedirs(dest_dir, exist_ok=True)

        # ファイルをコピー
        import shutil
        dest_path = os.path.join(self.local_storage_path, storage_path)
        shutil.copy2(file_path, dest_path)

        return dest_path

    def download_file(self, storage_path: str, local_path: Optional[str] = None) -> Union[str, bytes]:
        """
        ファイルをストレージからダウンロードします

        Args:
            storage_path: ストレージ内のファイルパス
            local_path: ダウンロード先のローカルパス（指定しない場合はバイトデータを返す）

        Returns:
            Union[str, bytes]: ダウンロードされたファイルのパスまたはバイトデータ
        """
        try:
            if self.storage_type == "supabase" and self.supabase:
                try:
                    return self._download_from_supabase(storage_path, local_path)
                except Exception as e:
                    logger.error(f"Supabaseからのダウンロードに失敗しました: {str(e)}")
                    logger.info("ローカルストレージにフォールバックします")
                    return self._download_from_local(storage_path, local_path)
            elif self.storage_type == "s3" and self.s3:
                try:
                    return self._download_from_s3(storage_path, local_path)
                except Exception as e:
                    logger.error(f"S3からのダウンロードに失敗しました: {str(e)}")
                    logger.info("ローカルストレージにフォールバックします")
                    return self._download_from_local(storage_path, local_path)
            elif self.storage_type == "local" or not self.storage_type:
                return self._download_from_local(storage_path, local_path)
            else:
                logger.warning(f"サポートされていないストレージタイプ: {self.storage_type}")
                logger.info("ローカルストレージにフォールバックします")
                return self._download_from_local(storage_path, local_path)
        except Exception as e:
            logger.error(f"ファイルダウンロードエラー: {str(e)}")
            # 最終的なフォールバック - ローカルストレージを試みる
            try:
                return self._download_from_local(storage_path, local_path)
            except Exception as local_error:
                logger.error(f"ローカルストレージからのダウンロードも失敗しました: {str(local_error)}")
                # ファイルが見つからない場合は空のバイトデータを返す
                if local_path:
                    # 空のファイルを作成
                    with open(local_path, 'wb') as f:
                        pass
                    return local_path
                else:
                    return b""

    def _download_from_supabase(self, storage_path: str, local_path: Optional[str] = None) -> Union[str, bytes]:
        """
        ファイルをSupabaseストレージからダウンロードします

        Args:
            storage_path: ストレージ内のファイルパス
            local_path: ダウンロード先のローカルパス（指定しない場合はバイトデータを返す）

        Returns:
            Union[str, bytes]: ダウンロードされたファイルのパスまたはバイトデータ
        """
        # バケット名とファイルパスを分離
        parts = storage_path.split('/', 1)
        bucket = parts[0]
        path = parts[1] if len(parts) > 1 else storage_path

        # ファイルをダウンロード
        response = self.supabase.storage.from_(bucket).download(path)

        if local_path:
            # ローカルファイルに保存
            with open(local_path, "wb") as f:
                f.write(response)
            return local_path
        else:
            # バイトデータを返す
            return response

    def delete_file(self, storage_path: str) -> bool:
        """
        ストレージからファイルを削除します

        Args:
            storage_path: 削除するファイルのパス

        Returns:
            bool: 削除に成功した場合はTrue、それ以外はFalse
        """
        try:
            if self.storage_type == "supabase" and self.supabase:
                try:
                    return self._delete_from_supabase(storage_path)
                except Exception as e:
                    logger.error(f"Supabaseからの削除に失敗しました: {str(e)}")
                    # ローカルストレージも確認
                    try:
                        return self._delete_from_local(storage_path)
                    except:
                        pass
                    return False
            elif self.storage_type == "s3" and self.s3:
                try:
                    return self._delete_from_s3(storage_path)
                except Exception as e:
                    logger.error(f"S3からの削除に失敗しました: {str(e)}")
                    # ローカルストレージも確認
                    try:
                        return self._delete_from_local(storage_path)
                    except:
                        pass
                    return False
            elif self.storage_type == "local" or not self.storage_type:
                return self._delete_from_local(storage_path)
            else:
                logger.warning(f"サポートされていないストレージタイプ: {self.storage_type}")
                # ローカルストレージを試す
                try:
                    return self._delete_from_local(storage_path)
                except:
                    pass
                return False
        except Exception as e:
            logger.error(f"ファイル削除エラー: {str(e)}")
            return False

    def _delete_from_supabase(self, storage_path: str) -> bool:
        """
        Supabaseストレージからファイルを削除します

        Args:
            storage_path: 削除するファイルのパス

        Returns:
            bool: 削除に成功した場合はTrue、それ以外はFalse
        """
        # バケット名とファイルパスを分離
        parts = storage_path.split('/', 1)
        bucket = parts[0]
        path = parts[1] if len(parts) > 1 else storage_path

        self.supabase.storage.from_(bucket).remove([path])
        return True

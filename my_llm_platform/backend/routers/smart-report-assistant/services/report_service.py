"""
レポートサービス - レポートの生成、保存、取得を管理します
"""
import os
import pandas as pd
import logging
import time
from typing import Dict, Any, Optional, List, Tuple, Union
from datetime import datetime
import json
import tempfile
import plotly.graph_objects as go

from ..config import settings
from ..models import ReportMetadata, ChartType, Language
from ..utils import (
    recommend_chart_types, create_chart, detect_anomalies,
    generate_secure_filename, export_to_csv, export_chart_to_image
)
from ..connectors import DataConnector
from .storage_service import StorageService
from .user_preference_service import UserPreferenceService

logger = logging.getLogger(__name__)

class ReportService:
    """
    レポート生成と管理のためのサービス
    """
    
    def __init__(
        self, 
        connector: DataConnector,
        storage_service: StorageService,
        user_preference_service: Optional[UserPreferenceService] = None
    ):
        """
        ReportServiceを初期化します
        
        Args:
            connector: データコネクタ
            storage_service: ストレージサービス
            user_preference_service: ユーザー設定サービス（オプション）
        """
        self.connector = connector
        self.storage_service = storage_service
        self.user_preference_service = user_preference_service
        self.report_registry: List[ReportMetadata] = []
        
    def generate_report(
        self,
        sql: str,
        title: str,
        user_id: str,
        session_id: str = "default",
        preferred_chart_type: Optional[ChartType] = None,
        is_shared: bool = False,
        language: Language = Language.JAPANESE,
        data_source: str = "postgresql",
        tags: List[str] = []
    ) -> Dict[str, Any]:
        """
        SQLクエリからレポートを生成します
        
        Args:
            sql: 実行するSQLクエリ
            title: レポートのタイトル
            user_id: ユーザーID
            session_id: セッションID
            preferred_chart_type: 優先するチャートタイプ
            is_shared: レポートを共有するかどうか
            language: レポートの言語
            data_source: データソース名
            tags: レポートのタグ
            
        Returns:
            Dict[str, Any]: 生成されたレポート情報
        """
        try:
            # クエリの実行時間を計測
            start_time = time.time()
            
            # SQLクエリの実行
            df = self.connector.execute_query(sql)
            
            # 実行時間の計算
            execution_time = time.time() - start_time
            
            # データフレームが空の場合
            if df.empty:
                logger.warning(f"クエリ結果が空です: {sql[:100]}...")
                raise ValueError("クエリ結果が空です")
            
            # レポートIDの生成
            report_id = generate_secure_filename(user_id, title)
            
            # チャートタイプの決定
            if preferred_chart_type:
                chart_type = preferred_chart_type
            else:
                # ユーザー設定からチャートタイプを取得
                if self.user_preference_service:
                    chart_type = self.user_preference_service.get_preferred_chart_type(user_id, title)
                
                # 設定がない場合は自動推奨
                if not chart_type:
                    recommended_types = recommend_chart_types(df)
                    chart_type = recommended_types[0] if recommended_types else ChartType.BAR
            
            # チャートの作成
            fig = create_chart(df, chart_type, title)
            
            # 一時ファイルの作成
            temp_dir = settings.TEMP_DIR
            csv_path = os.path.join(temp_dir, f"{report_id}.csv")
            img_path = os.path.join(temp_dir, f"{report_id}.png")
            
            # CSVとイメージの保存
            df.to_csv(csv_path, index=False)
            export_chart_to_image(fig, img_path)
            
            # ストレージへのアップロード
            csv_url = self.storage_service.upload_file(csv_path, f"report-data/{user_id}/{report_id}.csv")
            img_url = self.storage_service.upload_file(img_path, f"report-img/{user_id}/{report_id}.png")
            
            # 一時ファイルの削除
            os.remove(csv_path)
            os.remove(img_path)
            
            # レポートメタデータの作成
            report_metadata = ReportMetadata(
                report_id=report_id,
                user_id=user_id,
                session_id=session_id,
                title=title,
                chart_type=chart_type,
                csv_url=csv_url,
                img_url=img_url,
                timestamp=datetime.now(),
                is_shared=is_shared,
                tags=tags,
                data_source=data_source,
                query=sql,
                parameters={
                    "row_count": len(df),
                    "column_count": len(df.columns),
                    "execution_time_ms": execution_time * 1000
                }
            )
            
            # レポートレジストリに追加
            self.report_registry.append(report_metadata)
            
            # ユーザー設定の更新
            if self.user_preference_service:
                self.user_preference_service.update_recent_query(user_id, sql)
                if preferred_chart_type:
                    self.user_preference_service.set_preferred_chart_type(user_id, title, preferred_chart_type)
            
            # レポート情報の返却
            return {
                "report_id": report_id,
                "title": title,
                "chart_type": chart_type,
                "csv_url": csv_url,
                "img_url": img_url,
                "execution_time_ms": execution_time * 1000,
                "row_count": len(df),
                "column_count": len(df.columns),
                "suggested_chart_types": recommend_chart_types(df)
            }
            
        except Exception as e:
            logger.error(f"レポート生成エラー: {str(e)}")
            raise
    
    def get_report(self, report_id: str, user_id: str, is_admin: bool = False) -> Optional[ReportMetadata]:
        """
        レポートIDに基づいてレポートを取得します
        
        Args:
            report_id: 取得するレポートのID
            user_id: リクエストしているユーザーのID
            is_admin: ユーザーが管理者かどうか
            
        Returns:
            Optional[ReportMetadata]: レポートメタデータ（存在しない場合はNone）
        """
        for report in self.report_registry:
            if report.report_id == report_id:
                # アクセス権のチェック
                if report.user_id == user_id or report.is_shared or is_admin:
                    return report
                else:
                    logger.warning(f"ユーザー {user_id} はレポート {report_id} へのアクセス権がありません")
                    return None
        
        logger.warning(f"レポート {report_id} が見つかりません")
        return None
    
    def list_reports(
        self, 
        user_id: str, 
        page: int = 1, 
        limit: int = 20, 
        include_shared: bool = True,
        is_admin: bool = False,
        tags: Optional[List[str]] = None,
        search_term: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        ユーザーのレポートリストを取得します
        
        Args:
            user_id: ユーザーID
            page: ページ番号
            limit: 1ページあたりの件数
            include_shared: 共有レポートを含めるかどうか
            is_admin: ユーザーが管理者かどうか
            tags: フィルタリングするタグ
            search_term: 検索キーワード
            
        Returns:
            Dict[str, Any]: ページネーション情報とレポートリスト
        """
        # フィルタリング
        filtered_reports = []
        
        for report in self.report_registry:
            # アクセス権のチェック
            has_access = (
                report.user_id == user_id or 
                (include_shared and report.is_shared) or 
                is_admin
            )
            
            if not has_access:
                continue
            
            # タグでフィルタリング
            if tags and not any(tag in report.tags for tag in tags):
                continue
            
            # 検索キーワードでフィルタリング
            if search_term:
                search_term_lower = search_term.lower()
                if (
                    search_term_lower not in report.title.lower() and
                    search_term_lower not in report.query.lower() and
                    not any(search_term_lower in tag.lower() for tag in report.tags)
                ):
                    continue
            
            filtered_reports.append(report)
        
        # ソート（新しい順）
        sorted_reports = sorted(filtered_reports, key=lambda x: x.timestamp, reverse=True)
        
        # ページネーション
        total = len(sorted_reports)
        offset = (page - 1) * limit
        paginated_reports = sorted_reports[offset:offset+limit]
        
        return {
            "total": total,
            "page": page,
            "limit": limit,
            "results": paginated_reports
        }
    
    def delete_report(self, report_id: str, user_id: str, is_admin: bool = False) -> bool:
        """
        レポートを削除します
        
        Args:
            report_id: 削除するレポートのID
            user_id: リクエストしているユーザーのID
            is_admin: ユーザーが管理者かどうか
            
        Returns:
            bool: 削除に成功した場合はTrue、それ以外はFalse
        """
        for i, report in enumerate(self.report_registry):
            if report.report_id == report_id:
                # アクセス権のチェック
                if report.user_id == user_id or is_admin:
                    # ストレージからファイルを削除
                    try:
                        self.storage_service.delete_file(f"report-data/{report.user_id}/{report_id}.csv")
                        self.storage_service.delete_file(f"report-img/{report.user_id}/{report_id}.png")
                    except Exception as e:
                        logger.warning(f"ストレージからのファイル削除エラー: {str(e)}")
                    
                    # レジストリから削除
                    self.report_registry.pop(i)
                    logger.info(f"レポート {report_id} を削除しました")
                    return True
                else:
                    logger.warning(f"ユーザー {user_id} はレポート {report_id} を削除する権限がありません")
                    return False
        
        logger.warning(f"レポート {report_id} が見つかりません")
        return False

"""
SQL生成サービス - 自然言語からSQLクエリを生成します
"""
import logging
from typing import Dict, Any, Optional, List, Tuple
from langchain_community.agent_toolkits.sql.toolkit import SQLDatabaseToolkit
from langchain_community.agent_toolkits.sql.base import create_sql_agent
from langchain_community.chat_models import Chat<PERSON>penAI
from langchain.prompts import PromptTemplate
from langchain_community.utilities import SQLDatabase
import time

from ..config import settings
from ..utils import get_prompt_template, detect_language, sanitize_sql_query
from ..models import Language
from ..connectors import DataConnector

logger = logging.getLogger(__name__)

class SQLGenerationService:
    """
    自然言語からSQLクエリを生成するサービス
    """

    def __init__(self, connector: DataConnector):
        """
        SQLGenerationServiceを初期化します

        Args:
            connector: データコネクタ
        """
        self.connector = connector
        self.llm = ChatOpenAI(
            temperature=settings.DEFAULT_TEMPERATURE,
            model=settings.DEFAULT_MODEL,
            openai_api_key=settings.OPENAI_API_KEY
        )

        # SQLデータベースの初期化
        self.db = SQLDatabase.from_uri(settings.SUPABASE_DB_URI)

        # SQLエージェントの初期化
        self.agent_executor = None
        self._initialize_agent()

    def _initialize_agent(self):
        """
        SQLエージェントを初期化します
        """
        try:
            # SQLエージェントの作成
            self.agent_executor = create_sql_agent(
                llm=self.llm,
                toolkit=SQLDatabaseToolkit(db=self.db, llm=self.llm),
                verbose=True,
                agent_type="zero-shot-react-description",
                agent_kwargs={"prompt": PromptTemplate(
                    input_variables=["input", "table_info"],
                    template=get_prompt_template("sql_generation", Language.JAPANESE)
                )}
            )
            logger.info("SQLエージェントを初期化しました")
        except Exception as e:
            logger.error(f"SQLエージェントの初期化エラー: {str(e)}")
            raise

    def generate_sql(self, question: str, language: Optional[Language] = None, context: Optional[str] = None) -> Tuple[str, float]:
        """
        自然言語の質問からSQLクエリを生成します

        Args:
            question: 自然言語の質問
            language: 質問の言語（指定しない場合は自動検出）
            context: 追加のコンテキスト情報（前回の質問など）

        Returns:
            Tuple[str, float]: 生成されたSQLクエリと実行時間（秒）
        """
        try:
            # 言語の検出
            detected_language = language or detect_language(question)
            logger.info(f"検出された言語: {detected_language}")

            # プロンプトテンプレートの更新
            if self.agent_executor:
                self.agent_executor.agent.llm_chain.prompt = PromptTemplate(
                    input_variables=["input", "table_info"],
                    template=get_prompt_template("sql_generation", detected_language)
                )

            # 入力の準備
            full_input = question
            if context:
                full_input = f"前回の質問: {context}\n今回の質問: {question}"

            # SQL生成の実行時間を計測
            start_time = time.time()

            # SQLの生成
            sql = self.agent_executor.run(full_input)

            # 実行時間の計算
            execution_time = time.time() - start_time

            # SQLのサニタイズ
            sanitized_sql = sanitize_sql_query(sql)

            logger.info(f"SQLクエリを生成しました: {sanitized_sql[:100]}...")
            return sanitized_sql, execution_time

        except Exception as e:
            logger.error(f"SQL生成エラー: {str(e)}")
            raise

    def validate_sql(self, sql: str) -> bool:
        """
        SQLクエリの構文を検証します

        Args:
            sql: 検証するSQLクエリ

        Returns:
            bool: クエリが有効な場合はTrue、それ以外はFalse
        """
        try:
            # SQLのサニタイズ
            sanitized_sql = sanitize_sql_query(sql)

            # 実際にクエリを実行せずに構文を検証
            # SQLiteの場合はSQLite3モジュールのparse機能を使用
            # PostgreSQLの場合はpsycopg2のparse機能を使用

            # ここでは簡易的な検証として、コネクタを使用して実行してみる
            # 実際のアプリケーションでは、より安全な検証方法を使用すべき
            self.connector.execute_query(f"EXPLAIN {sanitized_sql}")

            logger.info(f"SQLクエリの検証に成功しました")
            return True

        except Exception as e:
            logger.warning(f"SQLクエリの検証に失敗しました: {str(e)}")
            return False

"""
クエリAPI - 自然言語クエリとSQLクエリの実行を処理します
"""
import logging
from fastapi import APIRouter, Depends, HTTPException, Request, status
from typing import Dict, Any, Optional, List

from ..models import QueryRequest, QueryResponse, ErrorResponse, Language
from ..services import SQLGenerationService, ReportService, StorageService, UserPreferenceService
from ..utils import get_user_id_from_token, detect_language, create_audit_log
from ..connectors import DataConnectorFactory

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/query", tags=["Query"])

# 依存関係の注入
def get_sql_service():
    connector = DataConnectorFactory.get_default_connector()
    return SQLGenerationService(connector)

def get_report_service():
    connector = DataConnectorFactory.get_default_connector()
    storage_service = StorageService()
    user_preference_service = UserPreferenceService()
    return ReportService(connector, storage_service, user_preference_service)

@router.post("/", response_model=QueryResponse)
async def query_data(
    request: QueryRequest,
    req: Request,
    sql_service: SQLGenerationService = Depends(get_sql_service),
    report_service: ReportService = Depends(get_report_service),
    user_id: str = Depends(get_user_id_from_token)
):
    """
    自然言語クエリを実行し、結果を返します

    Args:
        request: クエリリクエスト
        req: FastAPIリクエスト
        sql_service: SQL生成サービス
        report_service: レポートサービス
        user_id: ユーザーID

    Returns:
        QueryResponse: クエリ結果
    """
    try:
        # 言語の検出
        language = request.language or detect_language(request.question)
        logger.info(f"ユーザー {user_id} からのクエリリクエスト: '{request.question}' (言語: {language})")

        # 監査ログの作成
        create_audit_log(
            user_id=user_id,
            action="query",
            resource="data",
            status="started",
            details={"question": request.question, "session_id": request.session_id},
            request=req
        )

        # SQLの生成
        sql, execution_time = sql_service.generate_sql(request.question, language)
        logger.info(f"生成されたSQL: {sql[:100]}...")

        # レポートの生成
        title = request.question[:50] + "..." if len(request.question) > 50 else request.question
        report_result = report_service.generate_report(
            sql=sql,
            title=title,
            user_id=user_id,
            session_id=request.session_id,
            preferred_chart_type=request.preferred_chart_type,
            is_shared=request.is_shared,
            language=language
        )

        # 監査ログの更新
        create_audit_log(
            user_id=user_id,
            action="query",
            resource="data",
            status="success",
            details={
                "question": request.question,
                "session_id": request.session_id,
                "report_id": report_result["report_id"],
                "execution_time_ms": report_result["execution_time_ms"]
            },
            request=req
        )

        # レスポンスの作成
        response = QueryResponse(
            sql=sql,
            chart_type=report_result["chart_type"],
            csv_url=report_result["csv_url"],
            img_url=report_result["img_url"],
            report_id=report_result["report_id"],
            title=report_result["title"],
            execution_time_ms=report_result["execution_time_ms"],
            row_count=report_result["row_count"],
            column_count=report_result["column_count"],
            suggested_chart_types=report_result["suggested_chart_types"]
        )

        return response

    except Exception as e:
        logger.error(f"クエリ実行エラー: {str(e)}")

        # 監査ログの更新
        create_audit_log(
            user_id=user_id,
            action="query",
            resource="data",
            status="error",
            details={"question": request.question, "session_id": request.session_id, "error": str(e)},
            request=req
        )

        # エラーレスポンス
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )

@router.post("/sql", response_model=QueryResponse)
async def execute_sql(
    sql: str,
    title: str,
    session_id: Optional[str] = "default",
    is_shared: Optional[bool] = False,
    preferred_chart_type: Optional[str] = None,
    req: Request = None,
    report_service: ReportService = Depends(get_report_service),
    user_id: str = Depends(get_user_id_from_token)
):
    """
    SQLクエリを直接実行し、結果を返します

    Args:
        sql: 実行するSQLクエリ
        title: レポートのタイトル
        session_id: セッションID
        is_shared: レポートを共有するかどうか
        preferred_chart_type: 優先するチャートタイプ
        req: FastAPIリクエスト
        report_service: レポートサービス
        user_id: ユーザーID

    Returns:
        QueryResponse: クエリ結果
    """
    try:
        logger.info(f"ユーザー {user_id} からのSQL実行リクエスト: '{sql[:100]}...'")

        # 監査ログの作成
        create_audit_log(
            user_id=user_id,
            action="execute_sql",
            resource="data",
            status="started",
            details={"sql": sql, "session_id": session_id},
            request=req
        )

        # レポートの生成
        report_result = report_service.generate_report(
            sql=sql,
            title=title,
            user_id=user_id,
            session_id=session_id,
            preferred_chart_type=preferred_chart_type,
            is_shared=is_shared
        )

        # 監査ログの更新
        create_audit_log(
            user_id=user_id,
            action="execute_sql",
            resource="data",
            status="success",
            details={
                "sql": sql,
                "session_id": session_id,
                "report_id": report_result["report_id"],
                "execution_time_ms": report_result["execution_time_ms"]
            },
            request=req
        )

        # レスポンスの作成
        response = QueryResponse(
            sql=sql,
            chart_type=report_result["chart_type"],
            csv_url=report_result["csv_url"],
            img_url=report_result["img_url"],
            report_id=report_result["report_id"],
            title=report_result["title"],
            execution_time_ms=report_result["execution_time_ms"],
            row_count=report_result["row_count"],
            column_count=report_result["column_count"],
            suggested_chart_types=report_result["suggested_chart_types"]
        )

        return response

    except Exception as e:
        logger.error(f"SQL実行エラー: {str(e)}")

        # 監査ログの更新
        create_audit_log(
            user_id=user_id,
            action="execute_sql",
            resource="data",
            status="error",
            details={"sql": sql, "session_id": session_id, "error": str(e)},
            request=req
        )

        # エラーレスポンス
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )

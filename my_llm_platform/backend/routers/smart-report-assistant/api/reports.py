"""
レポートAPI - レポートの管理と操作を処理します
"""
import logging
from fastapi import APIRouter, Depends, HTTPException, Request, status, Query
from typing import Dict, Any, Optional, List
from fastapi.responses import FileResponse, StreamingResponse
from io import BytesIO

from ..models import ReportMetadata, PaginatedResponse, ErrorResponse
from ..services import ReportService, StorageService
from ..utils import get_user_id_from_token, create_audit_log, check_permissions, export_to_csv, export_to_pdf, export_chart_to_image
from ..connectors import DataConnectorFactory

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/reports", tags=["Reports"])

# 依存関係の注入
def get_report_service():
    connector = DataConnectorFactory.get_default_connector()
    storage_service = StorageService()
    return ReportService(connector, storage_service)

def get_storage_service():
    return StorageService()

@router.get("/", response_model=PaginatedResponse)
async def list_reports(
    page: int = Query(1, ge=1),
    limit: int = Query(20, ge=1, le=100),
    include_shared: bool = Query(True),
    tags: Optional[List[str]] = Query(None),
    search: Optional[str] = Query(None),
    req: Request=None,
    report_service: ReportService = Depends(get_report_service),
    user_id: str = Depends(get_user_id_from_token)
):
    """
    ユーザーのレポートリストを取得します
    
    Args:
        page: ページ番号
        limit: 1ページあたりの件数
        include_shared: 共有レポートを含めるかどうか
        tags: フィルタリングするタグ
        search: 検索キーワード
        req: FastAPIリクエスト
        report_service: レポートサービス
        user_id: ユーザーID
        
    Returns:
        PaginatedResponse: ページネーション情報とレポートリスト
    """
    try:
        logger.info(f"ユーザー {user_id} からのレポートリスト取得リクエスト")
        
        # 監査ログの作成
        create_audit_log(
            user_id=user_id,
            action="list_reports",
            resource="reports",
            status="started",
            details={"page": page, "limit": limit, "include_shared": include_shared},
            request=req
        )
        
        # レポートリストの取得
        result = report_service.list_reports(
            user_id=user_id,
            page=page,
            limit=limit,
            include_shared=include_shared,
            tags=tags,
            search_term=search
        )
        
        # 監査ログの更新
        create_audit_log(
            user_id=user_id,
            action="list_reports",
            resource="reports",
            status="success",
            details={"page": page, "limit": limit, "total": result["total"]},
            request=req
        )
        
        return result
        
    except Exception as e:
        logger.error(f"レポートリスト取得エラー: {str(e)}")
        
        # 監査ログの更新
        create_audit_log(
            user_id=user_id,
            action="list_reports",
            resource="reports",
            status="error",
            details={"page": page, "limit": limit, "error": str(e)},
            request=req
        )
        
        # エラーレスポンス
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )

@router.get("/{report_id}", response_model=ReportMetadata)
async def get_report(
    report_id: str,
    req: Request,
    report_service: ReportService = Depends(get_report_service),
    user_id: str = Depends(get_user_id_from_token)
):
    """
    レポートIDに基づいてレポートを取得します
    
    Args:
        report_id: レポートID
        req: FastAPIリクエスト
        report_service: レポートサービス
        user_id: ユーザーID
        
    Returns:
        ReportMetadata: レポートメタデータ
    """
    try:
        logger.info(f"ユーザー {user_id} からのレポート取得リクエスト: {report_id}")
        
        # 監査ログの作成
        create_audit_log(
            user_id=user_id,
            action="get_report",
            resource=f"reports/{report_id}",
            status="started",
            request=req
        )
        
        # レポートの取得
        report = report_service.get_report(report_id, user_id)
        
        if not report:
            # 監査ログの更新
            create_audit_log(
                user_id=user_id,
                action="get_report",
                resource=f"reports/{report_id}",
                status="not_found",
                request=req
            )
            
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"レポート {report_id} が見つかりません"
            )
        
        # アクセス権のチェック
        if not check_permissions(user_id, report.user_id, is_admin=False) and not report.is_shared:
            # 監査ログの更新
            create_audit_log(
                user_id=user_id,
                action="get_report",
                resource=f"reports/{report_id}",
                status="forbidden",
                request=req
            )
            
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="このレポートにアクセスする権限がありません"
            )
        
        # 監査ログの更新
        create_audit_log(
            user_id=user_id,
            action="get_report",
            resource=f"reports/{report_id}",
            status="success",
            request=req
        )
        
        return report
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"レポート取得エラー: {str(e)}")
        
        # 監査ログの更新
        create_audit_log(
            user_id=user_id,
            action="get_report",
            resource=f"reports/{report_id}",
            status="error",
            details={"error": str(e)},
            request=req
        )
        
        # エラーレスポンス
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )

@router.delete("/{report_id}")
async def delete_report(
    report_id: str,
    req: Request,
    report_service: ReportService = Depends(get_report_service),
    user_id: str = Depends(get_user_id_from_token)
):
    """
    レポートを削除します
    
    Args:
        report_id: 削除するレポートのID
        req: FastAPIリクエスト
        report_service: レポートサービス
        user_id: ユーザーID
        
    Returns:
        Dict[str, Any]: 削除結果
    """
    try:
        logger.info(f"ユーザー {user_id} からのレポート削除リクエスト: {report_id}")
        
        # 監査ログの作成
        create_audit_log(
            user_id=user_id,
            action="delete_report",
            resource=f"reports/{report_id}",
            status="started",
            request=req
        )
        
        # レポートの削除
        result = report_service.delete_report(report_id, user_id)
        
        if not result:
            # 監査ログの更新
            create_audit_log(
                user_id=user_id,
                action="delete_report",
                resource=f"reports/{report_id}",
                status="failed",
                request=req
            )
            
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"レポート {report_id} が見つからないか、削除する権限がありません"
            )
        
        # 監査ログの更新
        create_audit_log(
            user_id=user_id,
            action="delete_report",
            resource=f"reports/{report_id}",
            status="success",
            request=req
        )
        
        return {"status": "success", "message": f"レポート {report_id} を削除しました"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"レポート削除エラー: {str(e)}")
        
        # 監査ログの更新
        create_audit_log(
            user_id=user_id,
            action="delete_report",
            resource=f"reports/{report_id}",
            status="error",
            details={"error": str(e)},
            request=req
        )
        
        # エラーレスポンス
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )

@router.get("/{report_id}/export/{format}")
async def export_report(
    report_id: str,
    format: str,
    req: Request,
    report_service: ReportService = Depends(get_report_service),
    storage_service: StorageService = Depends(get_storage_service),
    user_id: str = Depends(get_user_id_from_token)
):
    """
    レポートを指定された形式でエクスポートします
    
    Args:
        report_id: エクスポートするレポートのID
        format: エクスポート形式（csv, excel, pdf, png, jpg, svg）
        req: FastAPIリクエスト
        report_service: レポートサービス
        storage_service: ストレージサービス
        user_id: ユーザーID
        
    Returns:
        FileResponse or StreamingResponse: エクスポートされたファイル
    """
    try:
        logger.info(f"ユーザー {user_id} からのレポートエクスポートリクエスト: {report_id} ({format})")
        
        # 監査ログの作成
        create_audit_log(
            user_id=user_id,
            action="export_report",
            resource=f"reports/{report_id}",
            status="started",
            details={"format": format},
            request=req
        )
        
        # レポートの取得
        report = report_service.get_report(report_id, user_id)
        
        if not report:
            # 監査ログの更新
            create_audit_log(
                user_id=user_id,
                action="export_report",
                resource=f"reports/{report_id}",
                status="not_found",
                details={"format": format},
                request=req
            )
            
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"レポート {report_id} が見つかりません"
            )
        
        # アクセス権のチェック
        if not check_permissions(user_id, report.user_id, is_admin=False) and not report.is_shared:
            # 監査ログの更新
            create_audit_log(
                user_id=user_id,
                action="export_report",
                resource=f"reports/{report_id}",
                status="forbidden",
                details={"format": format},
                request=req
            )
            
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="このレポートにアクセスする権限がありません"
            )
        
        # CSVファイルのダウンロード
        if format.lower() == 'csv':
            # CSVファイルのダウンロード
            csv_data = storage_service.download_file(report.csv_url.split('/')[-1])
            
            # 監査ログの更新
            create_audit_log(
                user_id=user_id,
                action="export_report",
                resource=f"reports/{report_id}",
                status="success",
                details={"format": format},
                request=req
            )
            
            return StreamingResponse(
                BytesIO(csv_data),
                media_type="text/csv",
                headers={"Content-Disposition": f"attachment; filename={report.title}.csv"}
            )
        
        # その他の形式は将来的に実装
        else:
            # 監査ログの更新
            create_audit_log(
                user_id=user_id,
                action="export_report",
                resource=f"reports/{report_id}",
                status="not_implemented",
                details={"format": format},
                request=req
            )
            
            raise HTTPException(
                status_code=status.HTTP_501_NOT_IMPLEMENTED,
                detail=f"形式 {format} のエクスポートはまだ実装されていません"
            )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"レポートエクスポートエラー: {str(e)}")
        
        # 監査ログの更新
        create_audit_log(
            user_id=user_id,
            action="export_report",
            resource=f"reports/{report_id}",
            status="error",
            details={"format": format, "error": str(e)},
            request=req
        )
        
        # エラーレスポンス
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )

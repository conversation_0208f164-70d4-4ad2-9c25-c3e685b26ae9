"""
APIパッケージ初期化
"""
from fastapi import APIRouter

from .query import router as query_router
from .reports import router as reports_router
from .preferences import router as preferences_router
from .auth import router as auth_router

# メインルーター
router = APIRouter()

# サブルーターの登録
router.include_router(auth_router)
router.include_router(query_router)
router.include_router(reports_router)
router.include_router(preferences_router)

__all__ = ["router"]

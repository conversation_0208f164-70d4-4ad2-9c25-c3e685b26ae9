"""
認証API - ユーザー認証と登録を処理します
"""
import logging
import jwt
import uuid
from datetime import datetime, timedelta
from fastapi import APIRouter, Depends, HTTPException, Request, status
from fastapi.security import O<PERSON><PERSON>2<PERSON><PERSON><PERSON><PERSON>earer, OAuth2PasswordRequestForm
from typing import Dict, Any, Optional, List
from pydantic import BaseModel, EmailStr, Field

from ..config import settings
from ..utils import create_audit_log, get_password_hash, verify_password
from ..models import UserCreate, UserLogin, UserResponse, TokenResponse

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/auth", tags=["Authentication"])

# OAuth2スキーム
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="/api/v1/auth/login")

# ユーザーデータベース（実際の実装ではデータベースを使用）
# テスト用の仮実装
USERS_DB = {
    "admin": {
        "id": "1",
        "username": "admin",
        "email": "<EMAIL>",
        "hashed_password": get_password_hash("password"),  # 実際の実装では安全なパスワード管理を行う
        "roles": ["admin", "user"],
        "is_active": True,
        "created_at": datetime.now().isoformat()
    },
    "user": {
        "id": "2",
        "username": "user",
        "email": "<EMAIL>",
        "hashed_password": get_password_hash("password"),  # 実際の実装では安全なパスワード管理を行う
        "roles": ["user"],
        "is_active": True,
        "created_at": datetime.now().isoformat()
    }
}

def create_access_token(data: dict, expires_delta: Optional[timedelta] = None):
    """
    アクセストークンを生成します

    Args:
        data: トークンに含めるデータ
        expires_delta: トークンの有効期限

    Returns:
        str: 生成されたJWTトークン
    """
    to_encode = data.copy()

    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=30)

    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, settings.JWT_SECRET_KEY, algorithm="HS256")
    return encoded_jwt

@router.post("/register", response_model=UserResponse)
async def register_user(
    user_data: UserCreate,
    req: Request
):
    """
    新規ユーザーを登録します

    Args:
        user_data: ユーザー登録データ
        req: FastAPIリクエスト

    Returns:
        UserResponse: 登録されたユーザー情報
    """
    try:
        # ユーザー名が既に存在するか確認
        if user_data.username in USERS_DB:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="このユーザー名は既に使用されています"
            )

        # メールアドレスが既に存在するか確認
        for user in USERS_DB.values():
            if user["email"] == user_data.email:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="このメールアドレスは既に使用されています"
                )

        # 新規ユーザーを作成
        user_id = str(uuid.uuid4())
        hashed_password = get_password_hash(user_data.password)

        new_user = {
            "id": user_id,
            "username": user_data.username,
            "email": user_data.email,
            "hashed_password": hashed_password,
            "roles": ["user"],  # デフォルトは一般ユーザー
            "is_active": True,
            "created_at": datetime.now().isoformat()
        }

        # ユーザーをデータベースに保存
        USERS_DB[user_data.username] = new_user

        # 監査ログの作成
        create_audit_log(
            user_id=user_id,
            action="register",
            resource="users",
            status="success",
            details={"username": user_data.username, "email": user_data.email},
            request=req
        )

        # レスポンスを返す
        return {
            "id": user_id,
            "username": user_data.username,
            "email": user_data.email,
            "roles": ["user"],
            "created_at": new_user["created_at"]
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"ユーザー登録エラー: {str(e)}")

        # 監査ログの作成
        create_audit_log(
            user_id="anonymous",
            action="register",
            resource="users",
            status="error",
            details={"username": user_data.username, "error": str(e)},
            request=req
        )

        # エラーレスポンス
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )

@router.post("/login", response_model=TokenResponse)
async def login(
    form_data: OAuth2PasswordRequestForm = Depends(),
    req: Request = None
):
    """
    ユーザーログイン処理を行います

    Args:
        form_data: ログインフォームデータ
        req: FastAPIリクエスト

    Returns:
        TokenResponse: アクセストークンとユーザー情報
    """
    try:
        # ユーザー名が存在するか確認
        if form_data.username not in USERS_DB:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="ユーザー名またはパスワードが無効です",
                headers={"WWW-Authenticate": "Bearer"},
            )

        user = USERS_DB[form_data.username]

        # パスワードが正しいか確認
        if not verify_password(form_data.password, user["hashed_password"]):
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="ユーザー名またはパスワードが無効です",
                headers={"WWW-Authenticate": "Bearer"},
            )

        # アクセストークンを生成
        access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
        access_token = create_access_token(
            data={"sub": user["id"], "username": user["username"]},
            expires_delta=access_token_expires
        )

        # 監査ログの作成
        create_audit_log(
            user_id=user["id"],
            action="login",
            resource="auth",
            status="success",
            details={"username": user["username"]},
            request=req
        )

        # レスポンスを返す
        return {
            "access_token": access_token,
            "token_type": "bearer",
            "user": {
                "id": user["id"],
                "username": user["username"],
                "email": user["email"],
                "roles": user["roles"]
            }
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"ログインエラー: {str(e)}")

        # 監査ログの作成
        create_audit_log(
            user_id="anonymous",
            action="login",
            resource="auth",
            status="error",
            details={"username": form_data.username, "error": str(e)},
            request=req
        )

        # エラーレスポンス
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )

@router.get("/me", response_model=UserResponse)
async def get_current_user(
    token: str = Depends(oauth2_scheme),
    req: Request = None
):
    """
    現在のユーザー情報を取得します

    Args:
        token: アクセストークン
        req: FastAPIリクエスト

    Returns:
        UserResponse: ユーザー情報
    """
    try:
        # トークンを検証
        payload = jwt.decode(token, settings.JWT_SECRET_KEY, algorithms=["HS256"])
        user_id = payload.get("sub")

        if user_id is None:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="無効な認証情報",
                headers={"WWW-Authenticate": "Bearer"},
            )

        # ユーザーIDからユーザー情報を取得
        user = None
        for u in USERS_DB.values():
            if u["id"] == user_id:
                user = u
                break

        if user is None:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="ユーザーが見つかりません",
                headers={"WWW-Authenticate": "Bearer"},
            )

        # レスポンスを返す
        return {
            "id": user["id"],
            "username": user["username"],
            "email": user["email"],
            "roles": user["roles"],
            "created_at": user["created_at"]
        }

    except jwt.PyJWTError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="無効な認証情報",
            headers={"WWW-Authenticate": "Bearer"},
        )
    except Exception as e:
        logger.error(f"ユーザー情報取得エラー: {str(e)}")

        # エラーレスポンス
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )

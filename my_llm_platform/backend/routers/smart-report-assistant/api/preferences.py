"""
ユーザー設定API - ユーザーの設定と好みを管理します
"""
import logging
from fastapi import APIRouter, Depends, HTTPException, Request, status
from typing import Dict, Any, Optional, List

from ..models import UserPreference, Language, ChartType
from ..services import UserPreferenceService
from ..utils import get_user_id_from_token, create_audit_log

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/preferences", tags=["User Preferences"])

# 依存関係の注入
def get_preference_service():
    return UserPreferenceService()

@router.get("/", response_model=UserPreference)
async def get_preferences(
    req: Request,
    preference_service: UserPreferenceService = Depends(get_preference_service),
    user_id: str = Depends(get_user_id_from_token)
):
    """
    ユーザーの設定を取得します
    
    Args:
        req: FastAPIリクエスト
        preference_service: ユーザー設定サービス
        user_id: ユーザーID
        
    Returns:
        UserPreference: ユーザー設定
    """
    try:
        logger.info(f"ユーザー {user_id} からの設定取得リクエスト")
        
        # 監査ログの作成
        create_audit_log(
            user_id=user_id,
            action="get_preferences",
            resource="preferences",
            status="started",
            request=req
        )
        
        # ユーザー設定の取得
        preferences = preference_service.get_user_preference(user_id)
        
        # 監査ログの更新
        create_audit_log(
            user_id=user_id,
            action="get_preferences",
            resource="preferences",
            status="success",
            request=req
        )
        
        return preferences
        
    except Exception as e:
        logger.error(f"ユーザー設定取得エラー: {str(e)}")
        
        # 監査ログの更新
        create_audit_log(
            user_id=user_id,
            action="get_preferences",
            resource="preferences",
            status="error",
            details={"error": str(e)},
            request=req
        )
        
        # エラーレスポンス
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )

@router.put("/", response_model=UserPreference)
async def update_preferences(
    preferences: Dict[str, Any],
    req: Request,
    preference_service: UserPreferenceService = Depends(get_preference_service),
    user_id: str = Depends(get_user_id_from_token)
):
    """
    ユーザーの設定を更新します
    
    Args:
        preferences: 更新する設定
        req: FastAPIリクエスト
        preference_service: ユーザー設定サービス
        user_id: ユーザーID
        
    Returns:
        UserPreference: 更新されたユーザー設定
    """
    try:
        logger.info(f"ユーザー {user_id} からの設定更新リクエスト")
        
        # 監査ログの作成
        create_audit_log(
            user_id=user_id,
            action="update_preferences",
            resource="preferences",
            status="started",
            details={"preferences": preferences},
            request=req
        )
        
        # ユーザー設定の更新
        updated_preferences = preference_service.update_user_preference(user_id, preferences)
        
        # 監査ログの更新
        create_audit_log(
            user_id=user_id,
            action="update_preferences",
            resource="preferences",
            status="success",
            request=req
        )
        
        return updated_preferences
        
    except Exception as e:
        logger.error(f"ユーザー設定更新エラー: {str(e)}")
        
        # 監査ログの更新
        create_audit_log(
            user_id=user_id,
            action="update_preferences",
            resource="preferences",
            status="error",
            details={"preferences": preferences, "error": str(e)},
            request=req
        )
        
        # エラーレスポンス
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )

@router.put("/language/{language}")
async def set_language(
    language: Language,
    req: Request,
    preference_service: UserPreferenceService = Depends(get_preference_service),
    user_id: str = Depends(get_user_id_from_token)
):
    """
    ユーザーの優先言語を設定します
    
    Args:
        language: 言語
        req: FastAPIリクエスト
        preference_service: ユーザー設定サービス
        user_id: ユーザーID
        
    Returns:
        Dict[str, Any]: 設定結果
    """
    try:
        logger.info(f"ユーザー {user_id} からの言語設定リクエスト: {language}")
        
        # 監査ログの作成
        create_audit_log(
            user_id=user_id,
            action="set_language",
            resource="preferences/language",
            status="started",
            details={"language": language},
            request=req
        )
        
        # 言語の設定
        preference_service.set_preferred_language(user_id, language)
        
        # 監査ログの更新
        create_audit_log(
            user_id=user_id,
            action="set_language",
            resource="preferences/language",
            status="success",
            details={"language": language},
            request=req
        )
        
        return {"status": "success", "message": f"言語を {language} に設定しました"}
        
    except Exception as e:
        logger.error(f"言語設定エラー: {str(e)}")
        
        # 監査ログの更新
        create_audit_log(
            user_id=user_id,
            action="set_language",
            resource="preferences/language",
            status="error",
            details={"language": language, "error": str(e)},
            request=req
        )
        
        # エラーレスポンス
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )

@router.put("/chart-type/{query_context}/{chart_type}")
async def set_chart_type(
    query_context: str,
    chart_type: ChartType,
    req: Request,
    preference_service: UserPreferenceService = Depends(get_preference_service),
    user_id: str = Depends(get_user_id_from_token)
):
    """
    特定のクエリコンテキストに対するユーザーの優先チャートタイプを設定します
    
    Args:
        query_context: クエリコンテキスト（タイトルなど）
        chart_type: チャートタイプ
        req: FastAPIリクエスト
        preference_service: ユーザー設定サービス
        user_id: ユーザーID
        
    Returns:
        Dict[str, Any]: 設定結果
    """
    try:
        logger.info(f"ユーザー {user_id} からのチャートタイプ設定リクエスト: {query_context} -> {chart_type}")
        
        # 監査ログの作成
        create_audit_log(
            user_id=user_id,
            action="set_chart_type",
            resource="preferences/chart-type",
            status="started",
            details={"query_context": query_context, "chart_type": chart_type},
            request=req
        )
        
        # チャートタイプの設定
        preference_service.set_preferred_chart_type(user_id, query_context, chart_type)
        
        # 監査ログの更新
        create_audit_log(
            user_id=user_id,
            action="set_chart_type",
            resource="preferences/chart-type",
            status="success",
            details={"query_context": query_context, "chart_type": chart_type},
            request=req
        )
        
        return {"status": "success", "message": f"クエリコンテキスト '{query_context}' のチャートタイプを {chart_type} に設定しました"}
        
    except Exception as e:
        logger.error(f"チャートタイプ設定エラー: {str(e)}")
        
        # 監査ログの更新
        create_audit_log(
            user_id=user_id,
            action="set_chart_type",
            resource="preferences/chart-type",
            status="error",
            details={"query_context": query_context, "chart_type": chart_type, "error": str(e)},
            request=req
        )
        
        # エラーレスポンス
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )

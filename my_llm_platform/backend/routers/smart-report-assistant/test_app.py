"""
テストアプリケーション - 実装の検証
"""
import os
import sys
import logging
from fastapi import FastAPI, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse

# ロギングの設定
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

# FastAPIアプリケーションの作成
app = FastAPI(
    title="Smart Report Assistant Test",
    description="テスト用アプリケーション",
    version="1.0.0"
)

# CORSミドルウェアの設定
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 設定のインポートテスト
try:
    from config import settings
    logger.info("設定のインポートに成功しました")
    logger.info(f"SUPABASE_URL: {settings.SUPABASE_URL}")
    logger.info(f"DEFAULT_MODEL: {settings.DEFAULT_MODEL}")
except ImportError as e:
    logger.error(f"設定のインポートに失敗しました: {e}")

# モデルのインポートテスト
try:
    from models import Language, ChartType
    logger.info("モデルのインポートに成功しました")
    logger.info(f"サポートされている言語: {[lang.value for lang in Language]}")
    logger.info(f"サポートされているチャートタイプ: {[chart.value for chart in ChartType]}")
except ImportError as e:
    logger.error(f"モデルのインポートに失敗しました: {e}")

# ヘルスチェックエンドポイント
@app.get("/health")
async def health_check():
    """
    アプリケーションの健全性を確認します
    """
    return {
        "status": "ok",
        "version": "1.0.0",
        "imports": {
            "config": "settings" in sys.modules.get("config", {}).__dict__,
            "models": "Language" in sys.modules.get("models", {}).__dict__
        }
    }

# アプリケーション起動時の処理
@app.on_event("startup")
async def startup_event():
    """
    アプリケーション起動時に実行される処理
    """
    logger.info("テストアプリケーションを起動しています...")
    logger.info("テストアプリケーションが正常に起動しました")

# アプリケーション終了時の処理
@app.on_event("shutdown")
async def shutdown_event():
    """
    アプリケーション終了時に実行される処理
    """
    logger.info("テストアプリケーションをシャットダウンしています...")
    logger.info("テストアプリケーションが正常にシャットダウンしました")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)

"""
データモデルスキーマ - アプリケーションで使用されるデータモデルを定義します
"""
from pydantic import BaseModel, Field, EmailStr
from typing import Dict, List, Optional, Any, Union
from datetime import datetime
from enum import Enum

class Language(str, Enum):
    """
    サポートされている言語の列挙型
    """
    JAPANESE = "ja"
    CHINESE = "zh"
    ENGLISH = "en"

class ChartType(str, Enum):
    """
    サポートされているチャートタイプの列挙型
    """
    BAR = "bar"
    LINE = "line"
    PIE = "pie"
    SCATTER = "scatter"
    HEATMAP = "heatmap"
    AREA = "area"
    HISTOGRAM = "histogram"
    TABLE = "table"

class DataSourceType(str, Enum):
    """
    サポートされているデータソースタイプの列挙型
    """
    POSTGRESQL = "postgresql"
    MYSQL = "mysql"
    MONGODB = "mongodb"
    ORACLE = "oracle"
    CSV = "csv"
    EXCEL = "excel"

class QueryRequest(BaseModel):
    """
    クエリリクエストモデル
    """
    question: str
    session_id: str = "default"
    is_shared: bool = False
    preferred_chart_type: Optional[ChartType] = None
    language: Language = Language.JAPANESE
    data_source: Optional[str] = None
    filters: Optional[Dict[str, Any]] = None

    class Config:
        use_enum_values = True

class UserPreference(BaseModel):
    """
    ユーザー設定モデル
    """
    user_id: str
    preferred_language: Language = Language.JAPANESE
    preferred_chart_types: Dict[str, ChartType] = {}
    favorite_reports: List[str] = []
    recent_queries: List[str] = []
    theme: str = "light"

    class Config:
        use_enum_values = True

class ReportMetadata(BaseModel):
    """
    レポートメタデータモデル
    """
    report_id: str
    user_id: str
    session_id: str
    title: str
    description: Optional[str] = None
    chart_type: ChartType
    csv_url: str
    img_url: str
    timestamp: datetime
    is_shared: bool = False
    tags: List[str] = []
    data_source: str
    query: str
    parameters: Optional[Dict[str, Any]] = None

    class Config:
        use_enum_values = True

class QueryResponse(BaseModel):
    """
    クエリレスポンスモデル
    """
    sql: str
    chart_type: ChartType
    csv_url: str
    img_url: str
    report_id: str
    title: str
    execution_time_ms: Optional[float] = None
    row_count: Optional[int] = None
    column_count: Optional[int] = None
    suggested_chart_types: Optional[List[ChartType]] = None

    class Config:
        use_enum_values = True

class PaginatedResponse(BaseModel):
    """
    ページネーションレスポンスモデル
    """
    total: int
    page: int
    limit: int
    results: List[Any]

class ErrorResponse(BaseModel):
    """
    エラーレスポンスモデル
    """
    error: str
    detail: Optional[str] = None
    code: Optional[int] = None

class AuditLog(BaseModel):
    """
    監査ログモデル
    """
    timestamp: datetime = Field(default_factory=datetime.now)
    user_id: str
    action: str
    resource: str
    status: str
    details: Optional[Dict[str, Any]] = None
    ip_address: Optional[str] = None
    user_agent: Optional[str] = None

class UserCreate(BaseModel):
    """
    ユーザー登録モデル
    """
    username: str = Field(..., min_length=3, max_length=50)
    email: EmailStr
    password: str = Field(..., min_length=8)

    class Config:
        schema_extra = {
            "example": {
                "username": "testuser",
                "email": "<EMAIL>",
                "password": "password123"
            }
        }

class UserLogin(BaseModel):
    """
    ユーザーログインモデル
    """
    username: str
    password: str

    class Config:
        schema_extra = {
            "example": {
                "username": "testuser",
                "password": "password123"
            }
        }

class UserResponse(BaseModel):
    """
    ユーザーレスポンスモデル
    """
    id: str
    username: str
    email: str
    roles: List[str]
    created_at: str

    class Config:
        schema_extra = {
            "example": {
                "id": "1",
                "username": "testuser",
                "email": "<EMAIL>",
                "roles": ["user"],
                "created_at": "2023-01-01T00:00:00"
            }
        }

class TokenResponse(BaseModel):
    """
    トークンレスポンスモデル
    """
    access_token: str
    token_type: str
    user: UserResponse

    class Config:
        schema_extra = {
            "example": {
                "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
                "token_type": "bearer",
                "user": {
                    "id": "1",
                    "username": "testuser",
                    "email": "<EMAIL>",
                    "roles": ["user"],
                    "created_at": "2023-01-01T00:00:00"
                }
            }
        }

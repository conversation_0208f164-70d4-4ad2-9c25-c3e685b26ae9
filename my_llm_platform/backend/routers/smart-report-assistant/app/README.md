# Smart Report Assistant フロントエンド

自然言語からデータ分析レポートを生成するStreamlitフロントエンド

## 機能

- 🌐 **自然言語提問**: 自然言語でデータに関する質問を入力
- 📊 **自動図表生成**: データの特性に基づいて最適なチャートを表示
- 🧮 **SQL表示**: 生成されたSQLクエリを表示
- 📈 **ユーザー偏好学習**: チャートタイプの変更と保存
- 📁 **レポート下載/導出機能**: CSV, PDF, PNG形式でのエクスポート
- 🤖 **多言語サポート**: 日本語、中国語、英語に対応
- 💬 **チャット履歴**: 会話履歴の保存と表示

## 使用方法

### 直接実行

```bash
# 必要なパッケージのインストール
pip install -r requirements.txt

# アプリケーションの起動
streamlit run app.py
```

または、提供されているスクリプトを使用:

```bash
chmod +x run.sh
./run.sh
```

### Docker

```bash
# イメージのビルド
docker build -t smart-report-assistant-frontend .

# コンテナの起動
docker run -p 8501:8501 -e API_URL=http://localhost:8000/api/v1 smart-report-assistant-frontend
```

## 環境変数

`.env`ファイルに以下の環境変数を設定できます：

- `API_URL`: バックエンドAPIのURL（デフォルト: `http://localhost:8000/api/v1`）
- `DEBUG`: デバッグモード（`true`または`false`）

例：
```
API_URL=http://localhost:8000/api/v1
DEBUG=true
```

## 使用例

1. アプリケーションにアクセス: `http://localhost:8501`
2. 自然言語で質問を入力（例: "過去3ヶ月の売上トップ5の製品を教えて"）
3. 「分析する」ボタンをクリック
4. 生成されたSQLとデータ可視化を確認
5. 必要に応じてチャートタイプを変更
6. CSVやPDFとしてレポートをエクスポート

## 注意事項

- バックエンドAPIが実行されていることを確認してください
- 認証が必要な場合は、適切なトークンを設定してください

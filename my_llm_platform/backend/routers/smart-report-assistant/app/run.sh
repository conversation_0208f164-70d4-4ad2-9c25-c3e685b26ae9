#!/bin/bash

# 環境変数の設定
# .envファイルが存在する場合は読み込む
if [ -f .env ]; then
    echo "Loading environment variables from .env file"
    export $(grep -v '^#' .env | xargs)
else
    echo "No .env file found, using default settings"
    # デフォルト設定
    export API_URL="http://localhost:8000/api/v1"
fi

# 認証は不要

# 必要なパッケージのインストール
echo "Installing required packages..."
pip install -r requirements.txt

# Streamlitアプリケーションの起動
echo "Starting Smart Report Assistant frontend..."
streamlit run app.py

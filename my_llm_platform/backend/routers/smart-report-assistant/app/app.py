"""
Smart Report Assistant - Streamlitフロントエンド
"""
import streamlit as st
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
import requests
import json
import base64
from datetime import datetime
import os
import sys
from dotenv import load_dotenv

# 環境変数の読み込み
load_dotenv()

# APIのURL設定
API_URL = os.getenv("API_URL", "http://localhost:8000/api/v1")

# ページ設定
st.set_page_config(
    page_title="Smart Report Assistant",
    page_icon="📊",
    layout="wide"
)

# セッション状態の初期化
if "chat_history" not in st.session_state:
    st.session_state.chat_history = []
if "reports" not in st.session_state:
    st.session_state.reports = []
if "language" not in st.session_state:
    st.session_state.language = "ja"
if "current_result" not in st.session_state:
    st.session_state.current_result = None
if "authenticated" not in st.session_state:
    st.session_state.authenticated = False
if "token" not in st.session_state:
    st.session_state.token = None
if "page" not in st.session_state:
    st.session_state.page = "login" if not st.session_state.authenticated else "main"
if "login_error" not in st.session_state:
    st.session_state.login_error = None

# ログイン関数
def login(username, password):
    try:
        # ログインAPIを呼び出す
        response = requests.post(
            f"{API_URL}/auth/login",
            headers={"Content-Type": "application/json"},
            json={"username": username, "password": password}
        )

        if response.status_code == 200:
            data = response.json()
            st.session_state.token = data.get("access_token")
            st.session_state.authenticated = True
            st.session_state.page = "main"
            return True
        else:
            st.session_state.login_error = "ログインに失敗しました。ユーザー名とパスワードを確認してください。"
            return False
    except Exception as e:
        st.session_state.login_error = f"ログインエラー: {str(e)}"
        return False

# 言語設定
def get_text(key):
    texts = {
        "ja": {
            "title": "スマートレポートアシスタント",
            "subtitle": "自然言語でデータを分析",
            "query_placeholder": "データについて質問してください...",
            "submit_button": "分析する",
            "reports_title": "保存されたレポート",
            "no_reports": "保存されたレポートはありません",
            "sql_title": "生成されたSQL",
            "chart_title": "データ可視化",
            "change_chart": "チャートタイプを変更",
            "export_title": "エクスポート",
            "csv_download": "CSVダウンロード",
            "pdf_download": "PDFダウンロード",
            "image_download": "画像ダウンロード",
            "error": "エラーが発生しました",
            "processing": "処理中...",
            "chat_history": "会話履歴",
            "user": "あなた",
            "ai": "AI",
            "show_details": "詳細を表示",
            "chart_updated": "チャートタイプを更新しました",
            "data_analysis": "データ分析結果",
            "execution_time": "実行時間",
            "row_count": "行数",
            "column_count": "列数",
            "ms": "ミリ秒",
        },
        "zh": {
            "title": "智能报告助手",
            "subtitle": "使用自然语言分析数据",
            "query_placeholder": "请输入关于数据的问题...",
            "submit_button": "分析",
            "reports_title": "已保存的报告",
            "no_reports": "没有已保存的报告",
            "sql_title": "生成的SQL",
            "chart_title": "数据可视化",
            "change_chart": "更改图表类型",
            "export_title": "导出",
            "csv_download": "下载CSV",
            "pdf_download": "下载PDF",
            "image_download": "下载图片",
            "error": "发生错误",
            "processing": "处理中...",
            "chat_history": "对话历史",
            "user": "您",
            "ai": "AI",
            "show_details": "显示详情",
            "chart_updated": "图表类型已更新",
            "data_analysis": "数据分析结果",
            "execution_time": "执行时间",
            "row_count": "行数",
            "column_count": "列数",
            "ms": "毫秒",
        },
        "en": {
            "title": "Smart Report Assistant",
            "subtitle": "Analyze data with natural language",
            "query_placeholder": "Ask a question about your data...",
            "submit_button": "Analyze",
            "reports_title": "Saved Reports",
            "no_reports": "No saved reports",
            "sql_title": "Generated SQL",
            "chart_title": "Data Visualization",
            "change_chart": "Change Chart Type",
            "export_title": "Export",
            "csv_download": "Download CSV",
            "pdf_download": "Download PDF",
            "image_download": "Download Image",
            "error": "An error occurred",
            "processing": "Processing...",
            "chat_history": "Conversation History",
            "user": "You",
            "ai": "AI",
            "show_details": "Show Details",
            "chart_updated": "Chart type updated",
            "data_analysis": "Data Analysis Results",
            "execution_time": "Execution Time",
            "row_count": "Row Count",
            "column_count": "Column Count",
            "ms": "ms",
        }
    }
    return texts.get(st.session_state.language, {}).get(key, texts["ja"][key])

# APIリクエスト用のヘッダー
def get_headers():
    headers = {
        "Content-Type": "application/json"
    }

    # 認証トークンがある場合は追加
    if st.session_state.token:
        headers["Authorization"] = f"Bearer {st.session_state.token}"

    return headers

# レポート一覧を取得
def fetch_reports():
    try:
        response = requests.get(
            f"{API_URL}/reports/?page=1&limit=10",
            headers=get_headers()
        )
        if response.status_code == 200:
            return response.json().get("results", [])
        elif response.status_code == 401 or response.status_code == 403:
            st.sidebar.warning("APIサーバーへのアクセスが拒否されました")
            # 認証状態をリセット
            st.session_state.authenticated = False
            st.session_state.token = None
            # ログインページに切り替え
            st.session_state.page = "login"
            # ページをリロード
            st.experimental_rerun()
            return []
        else:
            try:
                error_data = response.json()
                error_message = error_data.get("detail", response.text)
                st.sidebar.error(f"{get_text('error')}: {error_message}")
            except:
                st.sidebar.error(f"{get_text('error')}: {response.text}")
            return []
    except requests.exceptions.ConnectionError:
        st.sidebar.warning("バックエンドAPIに接続できません")
        return []
    except Exception as e:
        st.sidebar.error(f"{get_text('error')}: {str(e)}")
        return []

# レポートを取得
def fetch_report(report_id):
    try:
        response = requests.get(
            f"{API_URL}/reports/{report_id}",
            headers=get_headers()
        )
        if response.status_code == 200:
            return response.json()
        else:
            st.error(f"{get_text('error')}: {response.text}")
            return None
    except Exception as e:
        st.error(f"{get_text('error')}: {str(e)}")
        return None

# クエリを実行
def execute_query(query, language):
    try:
        # リクエストを送信
        response = requests.post(
            f"{API_URL}/query/",
            headers=get_headers(),
            json={
                "question": query,
                "session_id": "default",
                "is_shared": True,
                "language": language
            }
        )

        # レスポンスを処理
        if response.status_code == 200:
            return response.json()
        elif response.status_code == 401 or response.status_code == 403:
            st.error("APIサーバーへのアクセスが拒否されました。ログイン画面に移動します。")
            # 認証状態をリセット
            st.session_state.authenticated = False
            st.session_state.token = None
            # ログインページに切り替え
            st.session_state.page = "login"
            # ページをリロード
            st.experimental_rerun()
            return None
        else:
            try:
                error_data = response.json()
                error_message = error_data.get("detail", response.text)
                st.error(f"{get_text('error')}: {error_message}")
            except:
                st.error(f"{get_text('error')}: {response.text}")
            return None
    except requests.exceptions.ConnectionError:
        st.error("接続エラー: バックエンドAPIに接続できません。APIサーバーが実行されていることを確認してください。")
        return None
    except Exception as e:
        st.error(f"{get_text('error')}: {str(e)}")
        return None

# チャートタイプを変更
def update_chart_type(report_id, chart_type):
    try:
        response = requests.put(
            f"{API_URL}/preferences/chart-type/{report_id}/{chart_type}",
            headers=get_headers()
        )
        if response.status_code == 200:
            st.success(get_text("chart_updated"))
            return True
        else:
            st.error(f"{get_text('error')}: {response.text}")
            return False
    except Exception as e:
        st.error(f"{get_text('error')}: {str(e)}")
        return False

# サイドバー
with st.sidebar:
    st.title("⚙️ " + get_text("title"))

    # 言語選択
    language_names = {"ja": "日本語", "zh": "中文", "en": "English"}
    selected_language = st.selectbox(
        "言語 / 语言 / Language",
        ["ja", "zh", "en"],
        format_func=lambda x: language_names[x],
        index=list(language_names.keys()).index(st.session_state.language)
    )

    if selected_language != st.session_state.language:
        st.session_state.language = selected_language
        st.experimental_rerun()

    # ログアウトボタン
    if st.button("ログアウト"):
        st.session_state.authenticated = False
        st.session_state.token = None
        st.session_state.page = "login"
        st.experimental_rerun()

    # レポート一覧
    st.subheader(get_text("reports_title"))

    reports = fetch_reports()
    st.session_state.reports = reports

    if not reports:
        st.info(get_text("no_reports"))
    else:
        for report in reports:
            if st.button(f"📊 {report['title'][:30]}...", key=f"report_{report['report_id']}"):
                report_data = fetch_report(report['report_id'])
                if report_data:
                    st.session_state.current_result = {
                        "title": report_data["title"],
                        "sql": report_data["query"],
                        "img_url": report_data["img_url"],
                        "csv_url": report_data["csv_url"],
                        "report_id": report_data["report_id"],
                        "chart_type": report_data["chart_type"],
                        "execution_time_ms": report_data.get("parameters", {}).get("execution_time_ms", 0),
                        "row_count": report_data.get("parameters", {}).get("row_count", 0),
                        "column_count": report_data.get("parameters", {}).get("column_count", 0)
                    }
                    st.experimental_rerun()

# メインコンテンツ
if st.session_state.page == "login":
    # ログインページを表示
    st.title("🔐 " + get_text("title"))
    st.subheader("ログイン")

    if st.session_state.login_error:
        st.error(st.session_state.login_error)
        st.session_state.login_error = None

    with st.form(key="login_form"):
        username = st.text_input("ユーザー名")
        password = st.text_input("パスワード", type="password")
        login_button = st.form_submit_button(label="ログイン")

        if login_button:
            login(username, password)

    # デモ用のクイックログインボタン
    st.markdown("---")
    st.subheader("デモ用クイックログイン")

    col1, col2 = st.columns(2)
    with col1:
        if st.button("一般ユーザーとしてログイン"):
            login("user", "password")
    with col2:
        if st.button("管理者としてログイン"):
            login("admin", "password")
else:
    # メインアプリケーションページを表示
    st.title("📊 " + get_text("title"))
    st.subheader(get_text("subtitle"))

    # 質問入力
    with st.form(key="query_form"):
        query = st.text_area(get_text("query_placeholder"), height=100)
        submit_button = st.form_submit_button(label=get_text("submit_button"))

# 質問が送信された場合
if st.session_state.authenticated and submit_button and query:
    with st.spinner(get_text("processing")):
        result = execute_query(query, st.session_state.language)

        if result:
            # チャット履歴に追加
            st.session_state.chat_history.append(("user", query))
            st.session_state.chat_history.append(("ai", result))

            # 結果を表示
            st.session_state.current_result = result

# 結果の表示
if st.session_state.authenticated and st.session_state.current_result:
    result = st.session_state.current_result

    # タイトル
    st.header(result["title"])

    # 分析情報
    info_col1, info_col2, info_col3 = st.columns(3)
    with info_col1:
        st.metric(get_text("execution_time"), f"{result.get('execution_time_ms', 0):.2f} {get_text('ms')}")
    with info_col2:
        st.metric(get_text("row_count"), result.get("row_count", 0))
    with info_col3:
        st.metric(get_text("column_count"), result.get("column_count", 0))

    # 2カラムレイアウト
    col1, col2 = st.columns([1, 1])

    with col1:
        # SQLの表示
        st.subheader(get_text("sql_title"))
        st.code(result["sql"], language="sql")

    with col2:
        # チャートの表示
        st.subheader(get_text("chart_title"))
        st.image(result["img_url"])

        # チャートタイプの変更
        chart_types = ["bar", "line", "pie", "scatter", "heatmap", "area", "histogram"]
        new_chart_type = st.selectbox(
            get_text("change_chart"),
            chart_types,
            index=chart_types.index(result["chart_type"]) if result["chart_type"] in chart_types else 0
        )

        if new_chart_type != result["chart_type"] and st.button("更新"):
            if update_chart_type(result['report_id'], new_chart_type):
                # レポートを再取得
                updated_report = fetch_report(result['report_id'])
                if updated_report:
                    st.session_state.current_result["chart_type"] = new_chart_type
                    st.session_state.current_result["img_url"] = updated_report["img_url"]
                    st.experimental_rerun()

    # エクスポートオプション
    st.subheader(get_text("export_title"))
    export_col1, export_col2, export_col3 = st.columns(3)

    with export_col1:
        st.markdown(f"[{get_text('csv_download')}]({result['csv_url']})")

    with export_col2:
        if st.button(get_text("pdf_download")):
            try:
                # PDFエクスポートAPIを呼び出す
                response = requests.get(
                    f"{API_URL}/reports/{result['report_id']}/export/pdf",
                    headers=get_headers()
                )
                if response.status_code == 200:
                    # PDFをダウンロード
                    b64_pdf = base64.b64encode(response.content).decode()
                    href = f'<a href="data:application/pdf;base64,{b64_pdf}" download="{result["title"]}.pdf">PDFをダウンロード</a>'
                    st.markdown(href, unsafe_allow_html=True)
                else:
                    st.error(f"{get_text('error')}: {response.text}")
            except Exception as e:
                st.error(f"{get_text('error')}: {str(e)}")

    with export_col3:
        st.markdown(f"[{get_text('image_download')}]({result['img_url']})")

# チャット履歴の表示
if st.session_state.authenticated and st.session_state.chat_history:
    st.subheader(get_text("chat_history"))

    for i, (role, message) in enumerate(st.session_state.chat_history):
        if role == "user":
            st.markdown(f"**{get_text('user')}**: {message}")
        else:
            if isinstance(message, dict):
                st.markdown(f"**{get_text('ai')}**: {message.get('title', '')}")
                with st.expander(get_text("show_details")):
                    st.code(message.get("sql", ""), language="sql")
                    st.image(message.get("img_url", ""))
            else:
                st.markdown(f"**{get_text('ai')}**: {message}")

# メイン関数
def main():
    pass

if __name__ == "__main__":
    main()

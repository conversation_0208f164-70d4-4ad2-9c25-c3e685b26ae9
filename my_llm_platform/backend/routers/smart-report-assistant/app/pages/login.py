"""
Smart Report Assistant - ログインページ
"""
import streamlit as st
import requests
import os
from dotenv import load_dotenv

# 環境変数の読み込み
load_dotenv()

# APIのURL設定
API_URL = os.getenv("API_URL", "http://localhost:8000/api/v1")

# ページ設定
st.set_page_config(
    page_title="Smart Report Assistant - ログイン",
    page_icon="🔐",
    layout="centered"
)

# セッション状態の初期化
if "authenticated" not in st.session_state:
    st.session_state.authenticated = False
if "token" not in st.session_state:
    st.session_state.token = None
if "login_error" not in st.session_state:
    st.session_state.login_error = None
if "redirect_after_login" not in st.session_state:
    st.session_state.redirect_after_login = None

# 既に認証されている場合はメインページにリダイレクト
if st.session_state.authenticated:
    st.switch_page("app")

# ログイン関数
def login(username, password):
    try:
        # ログインAPIを呼び出す
        response = requests.post(
            f"{API_URL}/auth/login",
            headers={"Content-Type": "application/json"},
            json={"username": username, "password": password}
        )

        if response.status_code == 200:
            data = response.json()
            st.session_state.token = data.get("access_token")
            st.session_state.authenticated = True

            # リダイレクト先が設定されていればそこに遷移、なければメインページへ
            if st.session_state.redirect_after_login:
                redirect_page = st.session_state.redirect_after_login
                st.session_state.redirect_after_login = None
                st.switch_page(redirect_page)
            else:
                st.switch_page("app")

            return True
        else:
            st.session_state.login_error = "ログインに失敗しました。ユーザー名とパスワードを確認してください。"
            return False
    except Exception as e:
        st.session_state.login_error = f"ログインエラー: {str(e)}"
        return False

# ログインページのUI
st.title("🔐 Smart Report Assistant")
st.subheader("ログイン")

if st.session_state.login_error:
    st.error(st.session_state.login_error)
    st.session_state.login_error = None

with st.form(key="login_form"):
    username = st.text_input("ユーザー名")
    password = st.text_input("パスワード", type="password")
    login_button = st.form_submit_button(label="ログイン")

    if login_button:
        login(username, password)

# デモ用のクイックログインボタン
st.markdown("---")
st.subheader("デモ用クイックログイン")

col1, col2 = st.columns(2)
with col1:
    if st.button("一般ユーザーとしてログイン"):
        login("user", "password")
with col2:
    if st.button("管理者としてログイン"):
        login("admin", "password")

# フッター
st.markdown("---")
st.markdown("© 2023 Smart Report Assistant")

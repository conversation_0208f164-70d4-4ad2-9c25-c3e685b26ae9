"""
Smart Report Assistant - ホームページ
"""
import streamlit as st
import os
from dotenv import load_dotenv

# 環境変数の読み込み
load_dotenv()

# ページ設定
st.set_page_config(
    page_title="Smart Report Assistant - ホーム",
    page_icon="🏠",
    layout="centered"
)

# セッション状態の初期化
if "authenticated" not in st.session_state:
    st.session_state.authenticated = False
if "token" not in st.session_state:
    st.session_state.token = None

# ホームページのUI
st.title("🏠 Smart Report Assistant")
st.subheader("自然言語でデータを分析")

st.markdown("""
このアプリケーションでは、自然言語でデータに関する質問を入力するだけで、
データ分析レポートを自動生成することができます。

### 主な機能

- 🌐 **自然言語提問**: 自然言語でデータに関する質問を入力
- 📊 **自動図表生成**: データの特性に基づいて最適なチャートを表示
- 🧮 **SQL表示**: 生成されたSQLクエリを表示
- 📈 **ユーザー偏好学習**: チャートタイプの変更と保存
- 📁 **レポート下載/導出機能**: CSV, PDF, PNG形式でのエクスポート
- 🤖 **多言語サポート**: 日本語、中国語、英語に対応
- 💬 **チャット履歴**: 会話履歴の保存と表示
""")

# ナビゲーションボタン
st.markdown("---")
st.subheader("アプリケーションを開始")

col1, col2 = st.columns(2)

with col1:
    if st.session_state.authenticated:
        if st.button("アプリケーションを開く", use_container_width=True):
            st.switch_page("app")
    else:
        if st.button("ログイン", use_container_width=True):
            st.switch_page("Login")

with col2:
    if st.session_state.authenticated:
        if st.button("ログアウト", use_container_width=True):
            st.session_state.authenticated = False
            st.session_state.token = None
            st.experimental_rerun()
    else:
        st.markdown("アプリケーションを使用するには、ログインが必要です。")

# フッター
st.markdown("---")
st.markdown("© 2023 Smart Report Assistant")

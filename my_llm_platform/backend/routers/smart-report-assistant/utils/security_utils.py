"""
セキュリティユーティリティ - 認証、認可、およびセキュリティ関連の機能
"""
import re
import logging
import hashlib
import secrets
import bcrypt
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List, Union
from fastapi import Depends, HTTPException, status, Request
from fastapi.security import OAuth2<PERSON>asswordBearer
from jose import jwt, JWTError
from pydantic import ValidationError

from ..config import settings

logger = logging.getLogger(__name__)

oauth2_scheme = OAuth2PasswordBearer(tokenUrl="/api/v1/auth/login")

def get_user_id_from_token(token: str = Depends(oauth2_scheme)) -> str:
    """
    JWTトークンからユーザーIDを取得します

    Args:
        token: JWTトークン

    Returns:
        str: ユーザーID

    Raises:
        HTTPException: トークンが無効な場合
    """
    try:
        payload = jwt.decode(
            token,
            settings.JWT_SECRET_KEY,
            algorithms=["HS256"]
        )
        user_id = payload.get("sub") or payload.get("user_id") or payload.get("email")

        if not user_id:
            logger.warning("トークンにユーザーIDが含まれていません")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="トークンにユーザーIDが含まれていません",
                headers={"WWW-Authenticate": "Bearer"},
            )

        return user_id

    except JWTError as e:
        logger.error(f"JWTトークン検証エラー: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="無効なトークンです",
            headers={"WWW-Authenticate": "Bearer"},
        )
    except ValidationError as e:
        logger.error(f"トークンペイロード検証エラー: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="無効なトークンペイロードです",
            headers={"WWW-Authenticate": "Bearer"},
        )

def sanitize_sql_query(query: str) -> str:
    """
    SQLインジェクション攻撃を防ぐためにSQLクエリを検証・サニタイズします

    Args:
        query: 検証するSQLクエリ

    Returns:
        str: サニタイズされたSQLクエリ

    Raises:
        ValueError: 危険なSQLコマンドが含まれている場合
    """
    # 危険なSQLコマンドのパターン
    dangerous_patterns = [
        r';\s*DROP\s+',
        r';\s*DELETE\s+',
        r';\s*UPDATE\s+',
        r';\s*INSERT\s+',
        r';\s*ALTER\s+',
        r';\s*CREATE\s+',
        r';\s*TRUNCATE\s+',
        r'EXEC\s+',
        r'EXECUTE\s+',
        r'xp_cmdshell',
        r'sp_execute',
        r'--',  # SQLコメント
    ]

    # クエリを小文字に変換して検査（大文字小文字を区別しない）
    query_lower = query.lower()

    # 危険なパターンをチェック
    for pattern in dangerous_patterns:
        if re.search(pattern, query_lower, re.IGNORECASE):
            logger.warning(f"危険なSQLパターンが検出されました: {pattern}")
            raise ValueError(f"セキュリティ上の理由により、このSQLクエリは許可されていません")

    return query

def generate_secure_filename(user_id: str, title: str) -> str:
    """
    ユーザーIDとタイトルに基づいて安全なファイル名を生成します

    Args:
        user_id: ユーザーID
        title: ファイルのタイトル

    Returns:
        str: 生成されたファイル名
    """
    timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
    random_suffix = secrets.token_hex(4)

    # ユーザーIDとタイトルのハッシュを作成
    hash_base = f"{user_id}:{title}:{timestamp}"
    hash_value = hashlib.md5(hash_base.encode()).hexdigest()[:8]

    # 安全なファイル名を生成
    safe_title = re.sub(r'[^\w\s-]', '', title).strip().lower()
    safe_title = re.sub(r'[-\s]+', '-', safe_title)

    return f"report_{safe_title}_{hash_value}_{random_suffix}"

def check_permissions(user_id: str, resource_owner_id: str, is_admin: bool = False) -> bool:
    """
    ユーザーがリソースにアクセスする権限を持っているかチェックします

    Args:
        user_id: アクセスを試みるユーザーのID
        resource_owner_id: リソース所有者のID
        is_admin: ユーザーが管理者かどうか

    Returns:
        bool: アクセス権がある場合はTrue、それ以外はFalse
    """
    # 管理者は常にアクセス可能
    if is_admin:
        return True

    # 自分自身のリソースにはアクセス可能
    return user_id == resource_owner_id

def get_password_hash(password: str) -> str:
    """
    パスワードをハッシュ化します

    Args:
        password: ハッシュ化するパスワード

    Returns:
        str: ハッシュ化されたパスワード
    """
    # パスワードをバイト列に変換
    password_bytes = password.encode('utf-8')

    # ソルトを生成
    salt = bcrypt.gensalt()

    # パスワードをハッシュ化
    hashed_password = bcrypt.hashpw(password_bytes, salt)

    # バイト列を文字列に変換して返す
    return hashed_password.decode('utf-8')

def verify_password(plain_password: str, hashed_password: str) -> bool:
    """
    平文パスワードとハッシュ化されたパスワードを比較します

    Args:
        plain_password: 平文パスワード
        hashed_password: ハッシュ化されたパスワード

    Returns:
        bool: パスワードが一致する場合はTrue、それ以外はFalse
    """
    # パスワードをバイト列に変換
    password_bytes = plain_password.encode('utf-8')
    hashed_bytes = hashed_password.encode('utf-8')

    # パスワードを検証
    return bcrypt.checkpw(password_bytes, hashed_bytes)

def create_audit_log(
    user_id: str,
    action: str,
    resource: str,
    status: str,
    details: Optional[Dict[str, Any]] = None,
    request: Optional[Request] = None
) -> Dict[str, Any]:
    """
    監査ログエントリを作成します

    Args:
        user_id: アクションを実行したユーザーのID
        action: 実行されたアクション
        resource: アクセスされたリソース
        status: アクションのステータス（成功/失敗）
        details: 追加の詳細情報（オプション）
        request: FastAPIリクエストオブジェクト（オプション）

    Returns:
        Dict[str, Any]: 作成された監査ログエントリ
    """
    log_entry = {
        "timestamp": datetime.now().isoformat(),
        "user_id": user_id,
        "action": action,
        "resource": resource,
        "status": status,
        "details": details or {}
    }

    # リクエスト情報が提供されている場合は追加
    if request:
        log_entry["ip_address"] = request.client.host if request.client else None
        log_entry["user_agent"] = request.headers.get("user-agent")

    # ログに記録
    if status == "success":
        logger.info(f"監査: {user_id} が {resource} に対して {action} を実行しました")
    else:
        logger.warning(f"監査: {user_id} が {resource} に対して {action} を実行しましたが、{status} でした")

    return log_entry

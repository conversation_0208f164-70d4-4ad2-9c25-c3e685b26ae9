"""
エクスポートユーティリティ - レポートの様々な形式へのエクスポート機能
"""
import os
import pandas as pd
import plotly.graph_objects as go
from typing import Dict, Any, Optional, BinaryIO, Union
import logging
import json
import csv
import tempfile
from datetime import datetime
import base64
from io import BytesIO
import plotly.io as pio

from ..config import settings

logger = logging.getLogger(__name__)

def export_to_csv(df: pd.DataFrame, file_path: Optional[str] = None) -> Union[str, BytesIO]:
    """
    データフレームをCSVファイルにエクスポートします
    
    Args:
        df: エクスポートするデータフレーム
        file_path: 保存先ファイルパス（指定しない場合はメモリ上に作成）
        
    Returns:
        Union[str, BytesIO]: ファイルパスまたはバイトストリーム
    """
    try:
        if file_path:
            df.to_csv(file_path, index=False, encoding='utf-8-sig')
            logger.info(f"CSVファイルをエクスポートしました: {file_path}")
            return file_path
        else:
            # メモリ上にCSVを作成
            csv_buffer = BytesIO()
            df.to_csv(csv_buffer, index=False, encoding='utf-8-sig')
            csv_buffer.seek(0)
            logger.info("CSVデータをメモリにエクスポートしました")
            return csv_buffer
    except Exception as e:
        logger.error(f"CSVエクスポートエラー: {str(e)}")
        raise

def export_to_excel(df: pd.DataFrame, file_path: Optional[str] = None) -> Union[str, BytesIO]:
    """
    データフレームをExcelファイルにエクスポートします
    
    Args:
        df: エクスポートするデータフレーム
        file_path: 保存先ファイルパス（指定しない場合はメモリ上に作成）
        
    Returns:
        Union[str, BytesIO]: ファイルパスまたはバイトストリーム
    """
    try:
        if file_path:
            df.to_excel(file_path, index=False, engine='openpyxl')
            logger.info(f"Excelファイルをエクスポートしました: {file_path}")
            return file_path
        else:
            # メモリ上にExcelを作成
            excel_buffer = BytesIO()
            df.to_excel(excel_buffer, index=False, engine='openpyxl')
            excel_buffer.seek(0)
            logger.info("Excelデータをメモリにエクスポートしました")
            return excel_buffer
    except Exception as e:
        logger.error(f"Excelエクスポートエラー: {str(e)}")
        raise

def export_chart_to_image(
    fig: go.Figure, 
    file_path: Optional[str] = None, 
    format: str = 'png', 
    width: int = 1200, 
    height: int = 800
) -> Union[str, bytes]:
    """
    Plotly図をイメージファイルにエクスポートします
    
    Args:
        fig: エクスポートするPlotly図
        file_path: 保存先ファイルパス（指定しない場合はバイトデータを返す）
        format: イメージ形式（'png', 'jpg', 'svg', 'pdf'）
        width: イメージの幅（ピクセル）
        height: イメージの高さ（ピクセル）
        
    Returns:
        Union[str, bytes]: ファイルパスまたはバイトデータ
    """
    try:
        # 有効な形式かチェック
        valid_formats = ['png', 'jpg', 'jpeg', 'svg', 'pdf']
        if format.lower() not in valid_formats:
            logger.warning(f"無効なイメージ形式: {format}、pngを使用します")
            format = 'png'
        
        # ファイルパスが指定されている場合
        if file_path:
            pio.write_image(fig, file_path, format=format, width=width, height=height)
            logger.info(f"チャートイメージをエクスポートしました: {file_path}")
            return file_path
        else:
            # メモリ上にイメージを作成
            img_bytes = pio.to_image(fig, format=format, width=width, height=height)
            logger.info(f"チャートイメージをメモリにエクスポートしました ({format})")
            return img_bytes
    except Exception as e:
        logger.error(f"チャートイメージエクスポートエラー: {str(e)}")
        raise

def export_to_pdf(
    df: pd.DataFrame, 
    fig: go.Figure, 
    title: str, 
    file_path: Optional[str] = None,
    metadata: Optional[Dict[str, Any]] = None
) -> Union[str, BytesIO]:
    """
    データフレームとチャートをPDFレポートにエクスポートします
    
    Args:
        df: エクスポートするデータフレーム
        fig: エクスポートするPlotly図
        title: レポートのタイトル
        file_path: 保存先ファイルパス（指定しない場合はメモリ上に作成）
        metadata: PDFに含めるメタデータ
        
    Returns:
        Union[str, BytesIO]: ファイルパスまたはバイトストリーム
    """
    try:
        # ReportLabを使用してPDFを生成
        from reportlab.lib.pagesizes import A4
        from reportlab.lib import colors
        from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer, Image
        from reportlab.lib.styles import getSampleStyleSheet
        
        # メタデータの設定
        metadata = metadata or {}
        metadata.update({
            'title': title,
            'creator': 'Smart Report Assistant',
            'subject': 'Data Analysis Report',
            'created': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        })
        
        # PDFの作成先を決定
        if file_path:
            pdf_buffer = file_path
        else:
            pdf_buffer = BytesIO()
        
        # PDFドキュメントの作成
        doc = SimpleDocTemplate(
            pdf_buffer,
            pagesize=A4,
            title=metadata['title'],
            author=metadata.get('author', 'Smart Report Assistant'),
            subject=metadata['subject'],
            creator=metadata['creator']
        )
        
        # スタイルの設定
        styles = getSampleStyleSheet()
        title_style = styles['Title']
        normal_style = styles['Normal']
        
        # ドキュメント要素のリスト
        elements = []
        
        # タイトルの追加
        elements.append(Paragraph(title, title_style))
        elements.append(Spacer(1, 20))
        
        # 作成日時の追加
        elements.append(Paragraph(f"作成日時: {metadata['created']}", normal_style))
        elements.append(Spacer(1, 20))
        
        # チャートイメージの追加
        chart_img_path = tempfile.mktemp(suffix='.png')
        pio.write_image(fig, chart_img_path, format='png', width=500, height=400)
        elements.append(Image(chart_img_path, width=450, height=350))
        elements.append(Spacer(1, 20))
        
        # データテーブルの追加
        data = [df.columns.tolist()] + df.values.tolist()
        table = Table(data)
        table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))
        elements.append(table)
        
        # PDFの生成
        doc.build(elements)
        
        # 一時ファイルの削除
        if os.path.exists(chart_img_path):
            os.remove(chart_img_path)
        
        # 結果の返却
        if not file_path:
            pdf_buffer.seek(0)
        
        logger.info(f"PDFレポートをエクスポートしました: {file_path if file_path else 'メモリ'}")
        return pdf_buffer
        
    except Exception as e:
        logger.error(f"PDFエクスポートエラー: {str(e)}")
        raise

def get_data_uri(data: bytes, mime_type: str) -> str:
    """
    バイトデータをData URI形式に変換します
    
    Args:
        data: 変換するバイトデータ
        mime_type: MIMEタイプ
        
    Returns:
        str: Data URI文字列
    """
    encoded = base64.b64encode(data).decode('utf-8')
    return f"data:{mime_type};base64,{encoded}"

"""
言語ユーティリティ - 多言語サポートと言語検出のためのユーティリティ関数
"""
import re
from typing import Dict, Any, Optional
import logging
from langdetect import detect, LangDetectException
from ..models import Language

logger = logging.getLogger(__name__)

# 言語ごとのプロンプトテンプレート
PROMPT_TEMPLATES = {
    Language.JAPANESE: {
        "sql_generation": """あなたはデータ分析エキスパートです。以下のテーブル情報に基づいて、質問に対応する SQL を生成してください。

### テーブル構造:
{table_info}

### 質問:
{input}

SQL のみを出力してください。""",
        
        "chart_recommendation": """以下のデータに最適なグラフの種類を選んでください。

### データの特徴:
{data_description}

### 分析の目的:
{analysis_purpose}

最適なグラフタイプとその理由を簡潔に説明してください。""",
        
        "report_title": """以下の質問からレポートに相応しい短いタイトルを10〜20文字で作成してください。

### 質問:
{question}

タイトルのみを出力してください。""",
        
        "error_messages": {
            "no_data": "データが見つかりません。",
            "query_error": "クエリの実行中にエラーが発生しました。",
            "auth_error": "認証エラーが発生しました。",
            "permission_error": "この操作を実行する権限がありません。",
            "invalid_input": "入力が無効です。"
        }
    },
    
    Language.CHINESE: {
        "sql_generation": """您是数据分析专家。请根据以下表格信息，生成对应问题的SQL查询。

### 表格结构:
{table_info}

### 问题:
{input}

请只输出SQL语句。""",
        
        "chart_recommendation": """请为以下数据选择最合适的图表类型。

### 数据特征:
{data_description}

### 分析目的:
{analysis_purpose}

请简洁说明最佳图表类型及其原因。""",
        
        "report_title": """请根据以下问题创建一个适合报告的简短标题（10-20个字符）。

### 问题:
{question}

请只输出标题。""",
        
        "error_messages": {
            "no_data": "未找到数据。",
            "query_error": "执行查询时发生错误。",
            "auth_error": "认证错误。",
            "permission_error": "您没有执行此操作的权限。",
            "invalid_input": "输入无效。"
        }
    },
    
    Language.ENGLISH: {
        "sql_generation": """You are a data analysis expert. Please generate SQL based on the following table information to answer the question.

### Table Structure:
{table_info}

### Question:
{input}

Please output only the SQL query.""",
        
        "chart_recommendation": """Please select the most appropriate chart type for the following data.

### Data Characteristics:
{data_description}

### Analysis Purpose:
{analysis_purpose}

Please briefly explain the best chart type and why.""",
        
        "report_title": """Please create a short title (10-20 characters) suitable for a report based on the following question.

### Question:
{question}

Please output only the title.""",
        
        "error_messages": {
            "no_data": "No data found.",
            "query_error": "An error occurred while executing the query.",
            "auth_error": "Authentication error.",
            "permission_error": "You don't have permission to perform this operation.",
            "invalid_input": "Invalid input."
        }
    }
}

def detect_language(text: str) -> Language:
    """
    テキストの言語を検出します
    
    Args:
        text: 言語を検出するテキスト
        
    Returns:
        Language: 検出された言語（デフォルトは日本語）
    """
    try:
        # 空のテキストの場合はデフォルト言語を返す
        if not text or not text.strip():
            return Language.JAPANESE
            
        # langdetectを使用して言語を検出
        detected = detect(text)
        
        # 検出結果をLanguage列挙型にマッピング
        if detected == 'ja':
            return Language.JAPANESE
        elif detected == 'zh-cn' or detected == 'zh-tw' or detected == 'zh':
            return Language.CHINESE
        elif detected == 'en':
            return Language.ENGLISH
        else:
            # サポートされていない言語の場合はデフォルト言語を返す
            logger.info(f"サポートされていない言語が検出されました: {detected}、デフォルト言語を使用します")
            return Language.JAPANESE
            
    except LangDetectException as e:
        logger.warning(f"言語検出エラー: {str(e)}、デフォルト言語を使用します")
        return Language.JAPANESE

def get_prompt_template(template_key: str, language: Optional[Language] = None) -> str:
    """
    指定された言語のプロンプトテンプレートを取得します
    
    Args:
        template_key: テンプレートキー
        language: 言語（指定しない場合は日本語）
        
    Returns:
        str: プロンプトテンプレート
    """
    lang = language or Language.JAPANESE
    
    # 指定された言語のテンプレートが存在しない場合は日本語を使用
    if lang not in PROMPT_TEMPLATES or template_key not in PROMPT_TEMPLATES[lang]:
        logger.warning(f"テンプレート '{template_key}' が言語 '{lang}' に存在しません、日本語を使用します")
        lang = Language.JAPANESE
        
    return PROMPT_TEMPLATES[lang].get(template_key, "")

def get_error_message(error_key: str, language: Optional[Language] = None) -> str:
    """
    指定された言語のエラーメッセージを取得します
    
    Args:
        error_key: エラーキー
        language: 言語（指定しない場合は日本語）
        
    Returns:
        str: エラーメッセージ
    """
    lang = language or Language.JAPANESE
    
    # 指定された言語のエラーメッセージが存在しない場合は日本語を使用
    if lang not in PROMPT_TEMPLATES or "error_messages" not in PROMPT_TEMPLATES[lang]:
        logger.warning(f"エラーメッセージが言語 '{lang}' に存在しません、日本語を使用します")
        lang = Language.JAPANESE
        
    error_messages = PROMPT_TEMPLATES[lang].get("error_messages", {})
    return error_messages.get(error_key, "エラーが発生しました。")

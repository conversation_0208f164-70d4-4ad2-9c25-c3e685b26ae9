"""
チャートユーティリティ - データ可視化と図表生成のためのユーティリティ関数
"""
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from typing import Dict, List, Any, Optional, Tuple
import logging
from ..models import ChartType

logger = logging.getLogger(__name__)

def recommend_chart_types(df: pd.DataFrame) -> List[ChartType]:
    """
    データフレームに基づいて適切なチャートタイプを推奨します
    
    Args:
        df: 分析対象のデータフレーム
        
    Returns:
        List[ChartType]: 推奨されるチャートタイプのリスト（優先順位順）
    """
    recommended_types = []
    
    # データフレームが空の場合
    if df.empty:
        logger.warning("空のデータフレームに対してチャートタイプを推奨できません")
        return [ChartType.TABLE]
    
    # 列数に基づく推奨
    num_columns = len(df.columns)
    numeric_columns = df.select_dtypes(include=['number']).columns.tolist()
    categorical_columns = df.select_dtypes(include=['object', 'category']).columns.tolist()
    date_columns = df.select_dtypes(include=['datetime', 'datetime64']).columns.tolist()
    
    # 2列の場合（X軸とY軸）
    if num_columns == 2:
        # 1つの数値列と1つのカテゴリ列
        if len(numeric_columns) == 1 and len(categorical_columns) == 1:
            recommended_types.extend([ChartType.BAR, ChartType.PIE])
        
        # 2つの数値列
        elif len(numeric_columns) == 2:
            recommended_types.extend([ChartType.SCATTER, ChartType.LINE])
        
        # 1つの日付列と1つの数値列
        elif len(date_columns) == 1 and len(numeric_columns) == 1:
            recommended_types.extend([ChartType.LINE, ChartType.BAR])
    
    # 3列以上の場合
    elif num_columns >= 3:
        # 複数の数値列がある場合
        if len(numeric_columns) >= 2:
            # 日付列がある場合は時系列データの可能性
            if date_columns:
                recommended_types.extend([ChartType.LINE, ChartType.BAR, ChartType.AREA])
            else:
                recommended_types.extend([ChartType.BAR, ChartType.SCATTER, ChartType.HEATMAP])
        
        # 1つの数値列と複数のカテゴリ列
        elif len(numeric_columns) == 1 and len(categorical_columns) >= 1:
            recommended_types.extend([ChartType.BAR, ChartType.HISTOGRAM])
    
    # デフォルトの推奨
    if not recommended_types:
        recommended_types = [ChartType.BAR, ChartType.TABLE]
    
    logger.info(f"推奨チャートタイプ: {recommended_types}")
    return recommended_types

def create_chart(
    df: pd.DataFrame, 
    chart_type: ChartType, 
    title: str = "", 
    x_column: Optional[str] = None, 
    y_column: Optional[str] = None,
    color_column: Optional[str] = None,
    **kwargs
) -> go.Figure:
    """
    データフレームと指定されたチャートタイプに基づいてPlotly図を作成します
    
    Args:
        df: チャートのデータを含むデータフレーム
        chart_type: 作成するチャートのタイプ
        title: チャートのタイトル
        x_column: X軸に使用する列名（指定しない場合は最初の列）
        y_column: Y軸に使用する列名（指定しない場合は2番目の列）
        color_column: 色分けに使用する列名（オプション）
        **kwargs: その他のチャート固有のパラメータ
        
    Returns:
        go.Figure: 作成されたPlotly図オブジェクト
    """
    # 列名が指定されていない場合はデフォルト値を使用
    if not x_column and not df.empty:
        x_column = df.columns[0]
    
    if not y_column and len(df.columns) > 1:
        y_column = df.columns[1]
    
    try:
        if chart_type == ChartType.BAR:
            fig = px.bar(df, x=x_column, y=y_column, color=color_column, title=title, **kwargs)
            
        elif chart_type == ChartType.LINE:
            fig = px.line(df, x=x_column, y=y_column, color=color_column, title=title, **kwargs)
            
        elif chart_type == ChartType.PIE:
            fig = px.pie(df, names=x_column, values=y_column, title=title, **kwargs)
            
        elif chart_type == ChartType.SCATTER:
            fig = px.scatter(df, x=x_column, y=y_column, color=color_column, title=title, **kwargs)
            
        elif chart_type == ChartType.HEATMAP:
            # ヒートマップの場合、データを適切な形式に変換
            if len(df.columns) >= 3 and not kwargs.get("z"):
                pivot_df = df.pivot(index=x_column, columns=y_column, values=df.columns[2])
                fig = px.imshow(pivot_df, title=title, **kwargs)
            else:
                fig = px.imshow(df, title=title, **kwargs)
            
        elif chart_type == ChartType.AREA:
            fig = px.area(df, x=x_column, y=y_column, color=color_column, title=title, **kwargs)
            
        elif chart_type == ChartType.HISTOGRAM:
            fig = px.histogram(df, x=x_column, y=y_column, color=color_column, title=title, **kwargs)
            
        else:  # デフォルトはテーブル表示
            fig = go.Figure(data=[go.Table(
                header=dict(values=list(df.columns)),
                cells=dict(values=[df[col] for col in df.columns])
            )])
            fig.update_layout(title=title)
        
        # 共通のレイアウト設定
        fig.update_layout(
            template="plotly_white",
            margin=dict(l=50, r=50, t=80, b=50),
            title_font=dict(size=16),
            legend_title_font=dict(size=12),
            legend_font=dict(size=10),
        )
        
        return fig
        
    except Exception as e:
        logger.error(f"チャート作成エラー: {str(e)}")
        # エラーが発生した場合はシンプルなテーブルを返す
        fig = go.Figure(data=[go.Table(
            header=dict(values=list(df.columns)),
            cells=dict(values=[df[col] for col in df.columns])
        )])
        fig.update_layout(title=f"{title} (エラーのためテーブル表示)")
        return fig

def detect_anomalies(df: pd.DataFrame, column: str) -> Tuple[pd.DataFrame, List[int]]:
    """
    指定された列の異常値を検出します
    
    Args:
        df: 分析対象のデータフレーム
        column: 異常値を検出する列名
        
    Returns:
        Tuple[pd.DataFrame, List[int]]: 異常値フラグ付きのデータフレームと異常値のインデックスリスト
    """
    try:
        # 数値列のみ処理
        if column not in df.columns or not pd.api.types.is_numeric_dtype(df[column]):
            logger.warning(f"列 '{column}' は数値型ではないため、異常値検出をスキップします")
            return df, []
        
        # 四分位範囲（IQR）を使用した異常値検出
        q1 = df[column].quantile(0.25)
        q3 = df[column].quantile(0.75)
        iqr = q3 - q1
        
        lower_bound = q1 - 1.5 * iqr
        upper_bound = q3 + 1.5 * iqr
        
        # 異常値フラグを追加
        df['is_anomaly'] = ((df[column] < lower_bound) | (df[column] > upper_bound)).astype(int)
        
        # 異常値のインデックスを取得
        anomaly_indices = df[df['is_anomaly'] == 1].index.tolist()
        
        logger.info(f"列 '{column}' で {len(anomaly_indices)} 個の異常値を検出しました")
        return df, anomaly_indices
        
    except Exception as e:
        logger.error(f"異常値検出エラー: {str(e)}")
        return df, []

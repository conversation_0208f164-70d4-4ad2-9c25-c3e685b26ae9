# Smart Report Assistant

自然言語からデータ分析レポートを生成するAPIサービス

## 機能概要

Smart Report Assistantは、自然言語の質問からSQLクエリを生成し、データベースに対して実行、その結果を分析してレポートを作成するサービスです。

### 主な機能

- 🌐 **自然言語提問**: LangChain + SQL Agentを使用して自然言語からSQLクエリを生成
- 📊 **自動図表生成**: データの特性に基づいて最適なチャートタイプを推奨
- 🧮 **SQL自動生成と実行**: OpenAI function callingを活用したText-to-SQL変換
- 📈 **ユーザー偏好学習**: ユーザーの選択や操作を記録し、次回の推奨に活用
- 📁 **レポート下載/導出機能**: CSV, PDF, PNG形式でのエクスポート
- 🤖 **多言語サポート**: 日本語、中国語、英語に対応

### 詳細機能一覧

Smart Report Assistant 実装機能一覧
1. 自然言語クエリ処理
自然言語からSQLへの変換: LangChainとSQL Agentを使用して自然言語をSQLクエリに変換
コンテキスト対応質問: 前回の質問を考慮した連続的な質問応答
多言語サポート: 日本語、中国語、英語の3言語に対応
言語自動検出: 入力テキストから言語を自動検出
2. データ可視化と図表生成
インテリジェントなチャート推奨: データの特性に基づいて最適なチャートタイプを自動推奨
複数チャートタイプ対応:
棒グラフ (bar)
折れ線グラフ (line)
円グラフ (pie)
散布図 (scatter)
ヒートマップ (heatmap)
エリアチャート (area)
ヒストグラム (histogram)
テーブル表示 (table)
チャートカスタマイズ: タイトル、軸ラベル、色分けなどの設定
異常値検出: 統計的手法を用いたデータ内の異常値検出機能
3. レポート管理
レポート生成: クエリ結果からレポートを自動生成
レポートメタデータ管理: タイトル、作成日時、タグなどの管理
レポート共有機能: 他のユーザーとのレポート共有設定
レポート検索・フィルタリング: タグや検索キーワードによるレポート検索
ページネーション: 大量のレポートを効率的に表示
4. エクスポート機能
CSVエクスポート: データをCSV形式でエクスポート
Excelエクスポート: データをExcel形式でエクスポート
画像エクスポート: チャートを画像形式(PNG, JPG, SVG)でエクスポート
PDFエクスポート: レポート全体をPDF形式でエクスポート
データURI生成: ブラウザでの直接表示用のデータURI生成
5. ユーザー設定と学習
ユーザー設定管理: 言語、テーマなどの設定保存
チャート設定記憶: ユーザーごとのチャートタイプ設定を記憶
お気に入りレポート: レポートのお気に入り登録と管理
最近のクエリ履歴: ユーザーの最近のクエリを記録
6. 多言語サポート
3言語対応: 日本語、中国語、英語をサポート
言語検出: 入力テキストから言語を自動検出
言語別プロンプト: 各言語に最適化されたプロンプトテンプレート
言語別エラーメッセージ: 各言語でのエラーメッセージ表示
7. データソース接続
複数データソース対応:
PostgreSQL: 主要データベースとしてサポート
MongoDB: NoSQLデータベースとしてサポート
MySQL: 将来的にサポート予定
Oracle: 将来的にサポート予定
コネクタファクトリー: データソース接続の抽象化と管理
スキーマ情報取得: テーブル構造やスキーマ情報の取得
8. セキュリティ機能
認証・認可: JWTトークンによるユーザー認証
SQLインジェクション対策: SQLクエリのサニタイズ
監査ログ: ユーザーアクションの詳細な記録
アクセス制御: レポートやデータへのアクセス権限管理
安全なファイル名生成: セキュアなファイル名生成アルゴリズム
9. ストレージ管理
複数ストレージバックエンド:
Supabase Storage: クラウドストレージ
Amazon S3: クラウドストレージ
ローカルファイルシステム: 開発・テスト用
ファイル管理: アップロード、ダウンロード、削除機能
一時ファイル管理: 一時ファイルの作成と自動クリーンアップ
10. エラー処理と監視
包括的エラーハンドリング: 様々なエラーシナリオに対応
詳細なログ記録: 操作とエラーの詳細なログ
処理時間計測: リクエスト処理時間の計測と記録
ヘルスチェック: アプリケーションの健全性確認エンドポイント
11. デプロイメント機能
Dockerコンテナ化: コンテナ化によるデプロイの簡素化
環境設定管理: 環境変数による設定管理
スケーラビリティ: 複数ワーカーによるスケーリング対応
ヘルスチェック: コンテナヘルスチェック機能
12. API設計
RESTful API: 標準的なRESTful APIデザイン
OpenAPI仕様: API仕様の自動ドキュメント生成
エンドポイント構成:
/api/v1/query/: 自然言語クエリ実行
/api/v1/query/sql: SQL直接実行
/api/v1/reports/: レポート管理
/api/v1/preferences/: ユーザー設定管理
/health: ヘルスチェック
13. パフォーマンス最適化
クエリキャッシュ: 頻繁なクエリの結果をキャッシュ
非同期処理: 非同期APIエンドポイント
処理時間計測: パフォーマンスモニタリング
効率的なデータ処理: 大規模データセットの効率的な処理
これらの機能により、Smart Report Assistantは自然言語からデータ分析レポートを生成する強力なツールとなっており、企業環境での導入に適した多くの機能を備えています。

## アーキテクチャ

```
smart-report-assistant/
├── api/                  # APIエンドポイント
│   ├── query.py          # クエリ実行API
│   ├── reports.py        # レポート管理API
│   └── preferences.py    # ユーザー設定API
├── config/               # 設定
│   └── settings.py       # アプリケーション設定
├── connectors/           # データコネクタ
│   ├── base.py           # 基底クラス
│   ├── postgresql.py     # PostgreSQLコネクタ
│   ├── mongodb.py        # MongoDBコネクタ
│   └── factory.py        # コネクタファクトリ
├── models/               # データモデル
│   └── schemas.py        # Pydanticモデル
├── services/             # ビジネスロジック
│   ├── sql_generation.py # SQL生成サービス
│   ├── report_service.py # レポートサービス
│   ├── storage_service.py # ストレージサービス
│   └── user_preference_service.py # ユーザー設定サービス
├── utils/                # ユーティリティ
│   ├── chart_utils.py    # チャート生成ユーティリティ
│   ├── language_utils.py # 言語処理ユーティリティ
│   ├── security_utils.py # セキュリティユーティリティ
│   └── export_utils.py   # エクスポートユーティリティ
├── main.py               # アプリケーションエントリーポイント
├── Dockerfile            # Dockerコンテナ定義
└── requirements.txt      # 依存パッケージ
```

## 企業導入のための拡張機能

### 1. データコネクタ (Data Connectors)

複数のデータソースに接続できるコネクタを実装:

- PostgreSQL (Supabase)
- MongoDB
- MySQL
- Oracle (将来実装予定)

### 2. 部署と拡張 (Deployment & Scaling)

- Dockerコンテナ化によるデプロイの簡素化
- ヘルスチェック機能
- 複数ワーカーによるスケーリング
- 監査ログ機能

### 3. ユーザー体験 (User Experience)

- 多言語サポート (日本語、中国語、英語)
- ユーザー設定の保存と適用
- エラーハンドリングの強化
- レスポンス時間の計測と最適化

### 4. コンプライアンス (Compliance)

- 監査ログによるアクセス追跡
- セキュリティ強化 (SQLインジェクション対策など)
- ユーザー権限管理
- データアクセス制御

## 使用方法

### 環境変数の設定

`.env`ファイルに以下の環境変数を設定します:

```
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_SERVICE_ROLE_KEY=your-supabase-service-role-key
SUPABASE_DB_URI=postgresql+psycopg2://user:password@dbhost:5432/database
SUPABASE_JWT_SECRET=your-jwt-secret-from-supabase
OPENAI_API_KEY=sk-your-openai-api-key
```

### 起動方法

```bash
# 直接起動
uvicorn main:app --reload

# Dockerで起動
docker build -t smart-report-assistant .
docker run -p 8000:8000 --env-file .env smart-report-assistant
```

### APIエンドポイント

- `POST /api/v1/query/`: 自然言語クエリの実行
- `POST /api/v1/query/sql`: SQLクエリの直接実行
- `GET /api/v1/reports/`: レポート一覧の取得
- `GET /api/v1/reports/{report_id}`: 特定のレポートの取得
- `DELETE /api/v1/reports/{report_id}`: レポートの削除
- `GET /api/v1/reports/{report_id}/export/{format}`: レポートのエクスポート
- `GET /api/v1/preferences/`: ユーザー設定の取得
- `PUT /api/v1/preferences/`: ユーザー設定の更新

## 開発者向け情報

### 依存パッケージ

主な依存パッケージ:

- FastAPI: Webフレームワーク
- LangChain: LLM統合フレームワーク
- Plotly: データ可視化
- SQLAlchemy: データベース操作
- Pydantic: データバリデーション

### テスト

```bash
# テストの実行
pytest tests/
```

### 貢献方法

1. このリポジトリをフォーク
2. 機能ブランチを作成 (`git checkout -b feature/amazing-feature`)
3. 変更をコミット (`git commit -m 'Add some amazing feature'`)
4. ブランチにプッシュ (`git push origin feature/amazing-feature`)
5. プルリクエストを作成

from fastapi import APIRouter, HTTPException
from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
import json
import logging
import os
from ..config import settings
from ..inference import get_client

# モデルパラメータ調整用のAPIルーターを作成
router = APIRouter(tags=["Tuning APIs"])

# モデル定義
class TuningParameter(BaseModel):
    name: str = Field(..., description="パラメータ名")
    value: Any = Field(..., description="パラメータ値")
    description: Optional[str] = Field(None, description="パラメータの説明")

class TuningProfile(BaseModel):
    name: str = Field(..., description="プロファイル名")
    description: Optional[str] = Field(None, description="プロファイルの説明")
    model: str = Field(..., description="ターゲットモデル名")
    backend: Optional[str] = Field(None, description="使用するバックエンド")
    parameters: List[TuningParameter] = Field(..., description="調整パラメータ")

class TuningRequest(BaseModel):
    profile_name: str = Field(..., description="使用するプロファイル名")
    prompt: str = Field(..., description="テストに使用するプロンプト")
    max_tokens: int = Field(default=128, description="生成する最大トークン数")

# 調整プロファイルを保存およびロードする関数
def get_profiles_path():
    """プロファイルの保存パスを取得"""
    base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    profiles_dir = os.path.join(base_dir, "data")
    os.makedirs(profiles_dir, exist_ok=True)
    return os.path.join(profiles_dir, "tuning_profiles.json")

def load_profiles():
    """すべての調整プロファイルをロード"""
    profiles_path = get_profiles_path()
    if not os.path.exists(profiles_path):
        return {}

    try:
        with open(profiles_path, "r", encoding="utf-8") as f:
            return json.load(f)
    except Exception as e:
        logging.error(f"調整プロファイルのロード中にエラーが発生しました: {e}")
        return {}

def save_profiles(profiles):
    """すべての調整プロファイルを保存"""
    profiles_path = get_profiles_path()
    try:
        with open(profiles_path, "w", encoding="utf-8") as f:
            json.dump(profiles, f, indent=2, ensure_ascii=False)
        return True
    except Exception as e:
        logging.error(f"調整プロファイルの保存中にエラーが発生しました: {e}")
        return False

# APIルート
@router.get("/tuning/profiles")
async def list_profiles():
    """
    利用可能なすべての調整プロファイルをリストします。
    """
    profiles = load_profiles()
    return {"profiles": list(profiles.values())}

@router.get("/tuning/profiles/{name}")
async def get_profile(name: str):
    """
    名前で特定の調整プロファイルを取得します。
    """
    profiles = load_profiles()
    if name not in profiles:
        raise HTTPException(status_code=404, detail=f"プロファイル '{name}' が見つかりません")

    return profiles[name]

@router.post("/tuning/profiles")
async def create_profile(profile: TuningProfile):
    """
    新しい調整プロファイルを作成します。
    """
    profiles = load_profiles()
    if profile.name in profiles:
        raise HTTPException(status_code=400, detail=f"プロファイル '{profile.name}' は既に存在します")

    profiles[profile.name] = profile.model_dump()
    if save_profiles(profiles):
        return {"message": f"プロファイル '{profile.name}' が正常に作成されました", "profile": profile}
    else:
        raise HTTPException(status_code=500, detail="プロファイルの保存に失敗しました")

@router.put("/tuning/profiles/{name}")
async def update_profile(name: str, profile: TuningProfile):
    """
    既存の調整プロファイルを更新します。
    """
    profiles = load_profiles()
    if name not in profiles:
        raise HTTPException(status_code=404, detail=f"プロファイル '{name}' が見つかりません")

    profiles[name] = profile.model_dump()
    if save_profiles(profiles):
        return {"message": f"プロファイル '{name}' が正常に更新されました", "profile": profile}
    else:
        raise HTTPException(status_code=500, detail="プロファイルの保存に失敗しました")

@router.delete("/tuning/profiles/{name}")
async def delete_profile(name: str):
    """
    調整プロファイルを削除します。
    """
    profiles = load_profiles()
    if name not in profiles:
        raise HTTPException(status_code=404, detail=f"プロファイル '{name}' が見つかりません")

    del profiles[name]
    if save_profiles(profiles):
        return {"message": f"プロファイル '{name}' が正常に削除されました"}
    else:
        raise HTTPException(status_code=500, detail="プロファイルの削除に失敗しました")

@router.post("/tuning/test")
async def test_profile(request: TuningRequest):
    """
    プロンプトを使用して調整プロファイルをテストします。
    """
    profiles = load_profiles()
    if request.profile_name not in profiles:
        raise HTTPException(status_code=404, detail=f"プロファイル '{request.profile_name}' が見つかりません")

    profile = profiles[request.profile_name]

    # パラメータを抽出
    model = profile["model"]
    backend = profile.get("backend")

    # パラメータを辞書に変換
    params = {param["name"]: param["value"] for param in profile["parameters"]}

    try:
        # バックエンドを取得
        backend_instance = get_client(backend, model, **params)

        # テキストを生成
        output = backend_instance.generate(request.prompt, request.max_tokens, **params)

        return {
            "profile": profile,
            "prompt": request.prompt,
            "output": output,
            "parameters_used": params
        }
    except Exception as e:
        logging.error(f"プロファイルのテスト中にエラーが発生しました: {e}")
        raise HTTPException(status_code=500, detail=f"プロファイルのテスト中にエラーが発生しました: {str(e)}")

# デフォルトの調整プロファイルを追加
@router.post("/tuning/init-default-profiles", status_code=201)
async def init_default_profiles():
    """
    デフォルトの調整プロファイルを初期化します。
    """
    profiles = load_profiles()

    # プロファイルが存在しない場合のみデフォルトプロファイルを追加
    if not profiles:
        default_profiles = {
            "creative": {
                "name": "creative",
                "description": "物語生成のための高い創造性設定",
                "model": "llama3",
                "backend": "ollama",
                "parameters": [
                    {"name": "temperature", "value": 0.9, "description": "高い創造性のための温度設定"},
                    {"name": "top_p", "value": 0.95, "description": "多様性を高めるためのtop_p設定"},
                    {"name": "top_k", "value": 60, "description": "選択肢を増やすためのtop_k設定"}
                ]
            },
            "precise": {
                "name": "precise",
                "description": "事実に基づいた応答のための低温度設定",
                "model": "llama3",
                "backend": "ollama",
                "parameters": [
                    {"name": "temperature", "value": 0.3, "description": "より決定論的な出力のための低温度設定"},
                    {"name": "top_p", "value": 0.85, "description": "適度な多様性のためのtop_p設定"},
                    {"name": "top_k", "value": 40, "description": "適度な選択肢のためのtop_k設定"}
                ]
            },
            "balanced": {
                "name": "balanced",
                "description": "一般的な使用のためのバランスの取れた設定",
                "model": "llama3",
                "backend": "ollama",
                "parameters": [
                    {"name": "temperature", "value": 0.7, "description": "適度な温度設定"},
                    {"name": "top_p", "value": 0.9, "description": "標準的なtop_p設定"},
                    {"name": "top_k", "value": 50, "description": "標準的なtop_k設定"}
                ]
            }
        }

        # マージして保存
        profiles.update(default_profiles)
        if save_profiles(profiles):
            return {"message": "デフォルトプロファイルが初期化されました", "profiles": list(default_profiles.keys())}
        else:
            raise HTTPException(status_code=500, detail="デフォルトプロファイルの保存に失敗しました")
    else:
        return {"message": "プロファイルは既に存在します。アクションは実行されません", "profiles": list(profiles.keys())}

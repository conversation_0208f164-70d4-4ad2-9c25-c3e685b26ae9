"""
Chat API with plugin system integration.
"""

from fastapi import APIRouter, Depends, HTTPException, Query
from fastapi.responses import StreamingResponse
from typing import List, Optional, Dict, Any, Union, Literal, Annotated
from pydantic import BaseModel, Field
from datetime import datetime, timezone
import uuid
import json
import time
from sqlalchemy.orm import Session
from ..db import get_db, ChatMessage, ChatSession
from ..auth import get_current_user
from ..config import settings
from ..inference import get_client
from ..plugins.chat_integration import (
    process_with_plugins,
    process_response_with_plugins,
    get_tool_definitions,
    execute_tool
)
import logging

router = APIRouter(prefix="/chat", tags=["Chat APIs"])

# OPTIONSリクエストを処理するルートを追加
# OPTIONSリクエストはプリフライトリクエストであり、認証は不要
@router.options("/history")
async def options_chat_history():
    return {"detail": "OK"}

@router.options("/sessions")
async def options_chat_sessions():
    return {}

@router.options("/sessions/{session_id}")
async def options_chat_session(_: str):
    # アンダースコアの変数名はこのパラメータを使用しないことを示す
    return {}

@router.options("/sessions/{session_id}/messages")
async def options_chat_messages(_: str):
    # アンダースコアの変数名はこのパラメータを使用しないことを示す
    return {}

class ChatSessionResponse(BaseModel):
    id: str
    title: str
    created_at: datetime
    last_message: str

class ChatMessageResponse(BaseModel):
    id: str
    session_id: str
    role: str
    content: str
    created_at: datetime

# モデル定義
class Message(BaseModel):
    role: str
    content: str
    name: Optional[str] = None
    function_call: Optional[Dict[str, Any]] = None
    tool_calls: Optional[List[Dict[str, Any]]] = None
    tool_call_id: Optional[str] = None

class ChatCompletionRequest(BaseModel):
    model: str = Field(default=settings.DEFAULT_LLM_MODEL, description="使用するモデル名")
    messages: List[Message] = Field(..., description="会話内のメッセージリスト")
    temperature: float = Field(default=0.7, ge=0.0, le=2.0, description="サンプリング温度")
    top_p: float = Field(default=0.9, ge=0.0, le=1.0, description="核サンプリングパラメータ")
    top_k: int = Field(default=50, ge=0, le=1000, description="Top-kサンプリングパラメータ")
    max_tokens: int = Field(default=500, ge=1, le=4096, description="生成する最大トークン数")
    stream: bool = Field(default=False, description="レスポンスをストリーミングするかどうか")
    backend: Optional[str] = Field(default=None, description="使用するバックエンド（ollama, vllm, sglang, x-inference, openai）")
    backend_options: Optional[Dict[str, Any]] = Field(default=None, description="追加のバックエンド固有のオプション")
    plugins: Optional[List[str]] = Field(default=None, description="使用するプラグインのリスト")
    use_tools: bool = Field(default=True, description="ツールを使用するかどうか")

class ChatRequest(BaseModel):
    messages: List[Message]
    model: Optional[str] = "gpt-3.5-turbo"

class ChatMessageRequest(BaseModel):
    role: str
    content: str

# チャット完了API
@router.post("/completions")
async def create_chat_completion(request: ChatCompletionRequest):
    """
    チャット完了を作成（OpenAI互換API）
    動的に選択されたバックエンドを使用
    stream=true パラメータを指定するとストリーミングレスポンスが返されます
    """
    # バックエンドを選択
    backend_type = request.backend

    try:
        # OpenAIクライアントを取得
        try:
            client = get_client(backend_type=backend_type, model=request.model)
            logging.info(f"バックエンド {backend_type or '自動選択'} でクライアントを初期化しました。モデル: {request.model}")
        except Exception as e:
            logging.error(f"クライアントの初期化エラー: {e}")
            return {"error": f"バックエンドクライアントの初期化エラー: {str(e)}"}

        # リクエストの詳細を出力
        logging.info(f"チャット完了リクエスト: モデル={request.model}, バックエンド={backend_type or '自動選択'}, ストリーミング={request.stream}")
        logging.info(f"メッセージ数: {len(request.messages)}")

        # メッセージを準備
        messages = [msg.model_dump(exclude_none=True) for msg in request.messages]

        # プラグインでメッセージを処理
        processed_messages = await process_with_plugins(
            messages,
            client=client,
            model=request.model,
            plugins=request.plugins
        )

        # ツール定義を取得
        tools = None
        if request.use_tools:
            tools = get_tool_definitions()
            if tools:
                logging.info(f"ツール定義を追加: {len(tools)} ツール")

        # ストリーミングリクエストの場合
        if request.stream:
            async def generate_stream():
                try:
                    # OpenAIクライアントを使用してストリーミングリクエストを送信
                    stream_params = {
                        "model": request.model,
                        "messages": processed_messages,
                        "temperature": request.temperature,
                        "top_p": request.top_p,
                        "max_tokens": request.max_tokens,
                        "stream": True
                    }

                    # ツールがある場合は追加
                    if tools:
                        stream_params["tools"] = tools

                    stream = client.chat.completions.create(**stream_params)

                    # ストリーミングレスポンスを処理
                    logging.info(f"ストリーミングレスポンスの処理を開始")

                    for chunk in stream:
                        if hasattr(chunk, 'choices') and chunk.choices:
                            choice = chunk.choices[0]

                            # チャンクをJSON形式に変換
                            chunk_dict = {
                                "id": chunk.id,
                                "object": "chat.completion.chunk",
                                "created": chunk.created,
                                "model": chunk.model,
                                "choices": [
                                    {
                                        "index": choice.index,
                                        "delta": {},
                                        "finish_reason": choice.finish_reason
                                    }
                                ]
                            }

                            # deltaの内容を追加
                            if hasattr(choice, 'delta'):
                                delta = choice.delta
                                if hasattr(delta, 'content') and delta.content is not None:
                                    chunk_dict["choices"][0]["delta"]["content"] = delta.content
                                if hasattr(delta, 'role') and delta.role is not None:
                                    chunk_dict["choices"][0]["delta"]["role"] = delta.role
                                if hasattr(delta, 'tool_calls') and delta.tool_calls:
                                    chunk_dict["choices"][0]["delta"]["tool_calls"] = [
                                        {
                                            "id": tool_call.id,
                                            "type": tool_call.type,
                                            "function": {
                                                "name": tool_call.function.name,
                                                "arguments": tool_call.function.arguments
                                            }
                                        } for tool_call in delta.tool_calls
                                    ]

                            # OpenAI形式のストリーミングレスポンスを返す
                            response_data = f"data: {json.dumps(chunk_dict)}\n\n"
                            yield response_data

                    # 完了を示す[DONE]メッセージを送信
                    yield "data: [DONE]\n\n"

                except Exception as e:
                    logging.error(f"ストリーミングレスポンスのエラー: {e}")
                    yield f"data: {json.dumps({'error': str(e)})}\n\n"

            return StreamingResponse(generate_stream(), media_type="text/event-stream")

        # 非ストリーミングリクエストの場合
        else:
            # OpenAIクライアントを使用してリクエストを送信
            completion_params = {
                "model": request.model,
                "messages": processed_messages,
                "temperature": request.temperature,
                "top_p": request.top_p,
                "max_tokens": request.max_tokens,
                "stream": False
            }

            # ツールがある場合は追加
            if tools:
                completion_params["tools"] = tools

            completion = client.chat.completions.create(**completion_params)

            # レスポンスを処理
            logging.info(f"レスポンスを受信: {completion.model}")

            # レスポンスをプラグインで処理
            response_message = completion.choices[0].message
            response_dict = {
                "role": response_message.role,
                "content": response_message.content
            }

            # ツール呼び出しがある場合は追加
            if hasattr(response_message, 'tool_calls') and response_message.tool_calls:
                response_dict["tool_calls"] = [
                    {
                        "id": tool_call.id,
                        "type": tool_call.type,
                        "function": {
                            "name": tool_call.function.name,
                            "arguments": tool_call.function.arguments
                        }
                    } for tool_call in response_message.tool_calls
                ]

            # プラグインでレスポンスを処理
            processed_response = await process_response_with_plugins(
                processed_messages,
                response_dict,
                client=client,
                model=request.model,
                plugins=request.plugins
            )

            # ツール呼び出しがある場合、ツールを実行
            if processed_response.get("tool_calls") and request.use_tools:
                for tool_call in processed_response["tool_calls"]:
                    try:
                        function_name = tool_call["function"]["name"]
                        function_args = json.loads(tool_call["function"]["arguments"])

                        # ツールを実行
                        tool_result = await execute_tool(
                            function_name,
                            function_args,
                            client=client,
                            model=request.model
                        )

                        # ツール結果をメッセージに追加
                        processed_messages.append(processed_response)
                        processed_messages.append({
                            "role": "tool",
                            "tool_call_id": tool_call["id"],
                            "content": json.dumps(tool_result) if isinstance(tool_result, dict) else str(tool_result)
                        })

                        # 再度LLMを呼び出して最終応答を取得
                        final_completion = client.chat.completions.create(
                            model=request.model,
                            messages=processed_messages,
                            temperature=request.temperature,
                            top_p=request.top_p,
                            max_tokens=request.max_tokens,
                            stream=False
                        )

                        # 最終応答を処理
                        final_response_message = final_completion.choices[0].message
                        processed_response = {
                            "role": final_response_message.role,
                            "content": final_response_message.content
                        }
                    except Exception as e:
                        logging.error(f"ツール実行エラー: {e}")

            # OpenAI形式のレスポンスを作成
            response_data = {
                "id": completion.id,
                "object": "chat.completion",
                "created": completion.created,
                "model": completion.model,
                "choices": [
                    {
                        "index": 0,
                        "message": processed_response,
                        "finish_reason": completion.choices[0].finish_reason
                    }
                ]
            }

            return response_data

    except Exception as e:
        import traceback
        error_trace = traceback.format_exc()
        logging.error(f"チャット完了エラー: {e}")
        logging.error(f"エラートレース: {error_trace}")

        # OpenAI形式のエラーレスポンスを返す
        return {
            "id": f"chatcmpl-error-{int(time.time())}",
            "object": "chat.completion",
            "created": int(time.time()),
            "model": request.model,
            "choices": [
                {
                    "index": 0,
                    "message": {
                        "role": "assistant",
                        "content": f"エラーが発生しました: {str(e)}"
                    },
                    "finish_reason": "stop"
                }
            ],
            "error": {
                "message": str(e),
                "type": type(e).__name__,
                "traceback": error_trace
            }
        }

@router.get("/history", response_model=List[ChatSessionResponse])
async def get_chat_history(
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """
    ユーザーのチャット履歴を取得
    """
    try:
        # ユーザーのチャットセッションを取得
        sessions = db.query(ChatSession).filter(
            ChatSession.user_id == current_user.id
        ).order_by(
            ChatSession.created_at.desc()
        ).offset(skip).limit(limit).all()

        # レスポンスを構築
        result = []
        for session in sessions:
            # 最後のメッセージを取得
            last_message = db.query(ChatMessage).filter(
                ChatMessage.session_id == session.id
            ).order_by(
                ChatMessage.created_at.desc()
            ).first()

            result.append(ChatSessionResponse(
                id=session.id,
                title=session.title,
                created_at=session.created_at,
                last_message=last_message.content if last_message else ""
            ))

        return result
    except Exception as e:
        # データベース操作が失敗した場合、エラーを返す
        print(f"チャット履歴の取得中にエラーが発生しました: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to fetch chat history: {str(e)}")

@router.get("/sessions/{session_id}", response_model=List[ChatMessageResponse])
async def get_chat_messages(
    session_id: str,
    skip: int = Query(0, ge=0),
    limit: int = Query(50, ge=1, le=100),
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """
    特定のチャットセッションのメッセージを取得
    """
    # セッションが存在し、現在のユーザーに属していることを確認
    session = db.query(ChatSession).filter(
        ChatSession.id == session_id,
        ChatSession.user_id == current_user.id
    ).first()

    if not session:
        raise HTTPException(status_code=404, detail="Chat session not found")

    # セッションメッセージを取得
    messages = db.query(ChatMessage).filter(
        ChatMessage.session_id == session_id
    ).order_by(
        ChatMessage.created_at.asc()
    ).offset(skip).limit(limit).all()

    return [
        ChatMessageResponse(
            id=msg.id,
            session_id=msg.session_id,
            role=msg.role,
            content=msg.content,
            created_at=msg.created_at
        ) for msg in messages
    ]

class ChatSessionRequest(BaseModel):
    title: str = "New Chat"

@router.post("/sessions", response_model=ChatSessionResponse)
async def create_chat_session(
    request: ChatSessionRequest = None,
    title: str = None,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    # リクエストボディのタイトルを優先し、なければクエリパラメータのタイトルを使用
    session_title = (request and request.title) or title or "New Chat"
    print(f"チャットセッションを作成中、タイトル: {session_title}")
    """
    新しいチャットセッションを作成
    """
    session_id = str(uuid.uuid4())
    created_at = datetime.now(timezone.utc)

    try:
        # セッションレコードを作成（データベーステーブルが存在する場合）
        new_session = ChatSession(
            id=session_id,
            user_id=current_user.id,
            title=session_title,
            created_at=created_at
        )

        db.add(new_session)
        db.commit()

        return ChatSessionResponse(
            id=session_id,
            title=session_title,
            created_at=new_session.created_at,
            last_message=""
        )
    except Exception as e:
        # データベース操作が失敗した場合、モックレスポンスを返す
        db.rollback()
        print(f"データベースエラー: {e}")

        return ChatSessionResponse(
            id=session_id,
            title=session_title,
            created_at=created_at,
            last_message=""
        )

@router.post("/sessions/{session_id}/messages", response_model=ChatMessageResponse)
async def add_chat_message(
    session_id: str,
    message: ChatMessageRequest,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """
    チャットセッションに新しいメッセージを追加
    """
    # セッションが存在し、現在のユーザーに属していることを確認
    session = db.query(ChatSession).filter(
        ChatSession.id == session_id,
        ChatSession.user_id == current_user.id
    ).first()

    if not session:
        raise HTTPException(status_code=404, detail="Chat session not found")

    # 新しいメッセージを作成
    message_id = str(uuid.uuid4())
    new_message = ChatMessage(
        id=message_id,
        session_id=session_id,
        role=message.role,
        content=message.content,
        created_at=datetime.now(timezone.utc)
    )

    # データベースに保存
    db.add(new_message)
    db.commit()
    db.refresh(new_message)

    return ChatMessageResponse(
        id=new_message.id,
        session_id=new_message.session_id,
        role=new_message.role,
        content=new_message.content,
        created_at=new_message.created_at
    )

# セッション履歴を更新する関数
@router.post("/sessions/{session_id}/update_history", response_model=ChatSessionResponse)
async def update_chat_history(
    session_id: str,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """
    セッション履歴を更新
    後端からAI応答後に呼び出され、セッションの最後のメッセージと他のメタデータを更新
    """
    try:
        # セッションが存在し、現在のユーザーに属していることを確認
        session = db.query(ChatSession).filter(
            ChatSession.id == session_id,
            ChatSession.user_id == current_user.id
        ).first()

        if not session:
            raise HTTPException(status_code=404, detail="Chat session not found")

        # 最後のメッセージを取得
        last_message = db.query(ChatMessage).filter(
            ChatMessage.session_id == session_id
        ).order_by(ChatMessage.created_at.desc()).first()

        # セッションタイトルが "New Chat" の場合、最初のユーザーメッセージに基づいてタイトルを生成
        if session.title == "New Chat" and last_message:
            # 最初のユーザーメッセージを取得
            first_user_message = db.query(ChatMessage).filter(
                ChatMessage.session_id == session_id,
                ChatMessage.role == "user"
            ).order_by(ChatMessage.created_at.asc()).first()

            if first_user_message:
                # メッセージの最初の50文字をタイトルとして使用
                new_title = first_user_message.content[:50]
                if len(first_user_message.content) > 50:
                    new_title += "..."

                # タイトルを更新
                session.title = new_title
                db.commit()

        return ChatSessionResponse(
            id=session.id,
            title=session.title,
            created_at=session.created_at,
            last_message=last_message.content if last_message else ""
        )
    except Exception as e:
        db.rollback()
        print(f"セッション履歴の更新中にエラーが発生しました: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to update chat history: {str(e)}")

# セッションタイトルを更新するリクエストモデル
class UpdateSessionTitleRequest(BaseModel):
    title: str

@router.put("/sessions/{session_id}/title", response_model=ChatSessionResponse)
async def update_session_title(
    session_id: str,
    request: UpdateSessionTitleRequest,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """
    チャットセッションのタイトルを更新
    """
    # セッションが存在し、現在のユーザーに属していることを確認
    session = db.query(ChatSession).filter(
        ChatSession.id == session_id,
        ChatSession.user_id == current_user.id
    ).first()

    if not session:
        raise HTTPException(status_code=404, detail="Chat session not found")

    # タイトルを更新
    session.title = request.title
    db.commit()

    # 最後のメッセージを取得
    last_message = db.query(ChatMessage).filter(
        ChatMessage.session_id == session_id
    ).order_by(ChatMessage.created_at.desc()).first()

    return ChatSessionResponse(
        id=session.id,
        title=session.title,
        created_at=session.created_at,
        last_message=last_message.content if last_message else ""
    )

@router.delete("/sessions/{session_id}", status_code=204)
async def delete_chat_session(
    session_id: str,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """
    チャットセッションを削除
    """
    # セッションが存在し、現在のユーザーに属しているか確認
    session = db.query(ChatSession).filter(
        ChatSession.id == session_id,
        ChatSession.user_id == current_user.id
    ).first()

    if not session:
        raise HTTPException(status_code=404, detail="Chat session not found")

    # セッションに関連するすべてのメッセージを削除
    db.query(ChatMessage).filter(
        ChatMessage.session_id == session_id
    ).delete()

    # セッションを削除
    db.delete(session)
    db.commit()

    return None

from fastapi import APIRouter, Depends, HTTPException, Query
from fastapi.responses import StreamingResponse
from typing import List, Optional, Dict, Any, Union, Literal, Annotated
from pydantic import BaseModel, Field
from datetime import datetime, timezone
import uuid
import json
import time
from sqlalchemy.orm import Session
from ..db import SessionLocal
from ..db import get_db, ChatMessage, ChatSession
from ..auth import get_current_user
from ..config import settings
from ..inference import get_client
import logging

router = APIRouter(prefix="/chat", tags=["Chat APIs"])

# OPTIONSリクエストを処理するルートを追加
# OPTIONSリクエストはプリフライトリクエストであり、認証は不要
@router.options("/history")
async def options_chat_history():
    return {"detail": "OK"}

@router.options("/sessions")
async def options_chat_sessions():
    return {}

@router.options("/sessions/{session_id}")
async def options_chat_session(_: str):
    # アンダースコアの変数名はこのパラメータを使用しないことを示す
    return {}

@router.options("/sessions/{session_id}/messages")
async def options_chat_messages(_: str):
    # アンダースコアの変数名はこのパラメータを使用しないことを示す
    return {}

class ChatSessionResponse(BaseModel):
    id: str
    title: str
    created_at: datetime
    last_message: str

class ChatMessageResponse(BaseModel):
    id: str
    session_id: str
    role: str
    content: str
    created_at: datetime

# モデル定義
class Message(BaseModel):
    role: str
    content: str

class ChatCompletionRequest(BaseModel):
    model: str = Field(default=settings.DEFAULT_LLM_MODEL, description="使用するモデル名")
    messages: List[Message] = Field(..., description="会話内のメッセージリスト")
    temperature: float = Field(default=0.7, ge=0.0, le=2.0, description="サンプリング温度")
    top_p: float = Field(default=0.9, ge=0.0, le=1.0, description="核サンプリングパラメータ")
    top_k: int = Field(default=50, ge=0, le=1000, description="Top-kサンプリングパラメータ")
    max_tokens: int = Field(default=500, ge=1, le=4096, description="生成する最大トークン数")
    stream: bool = Field(default=False, description="レスポンスをストリーミングするかどうか")
    backend: Optional[str] = Field(default=None, description="使用するバックエンド（ollama, vllm, sglang, x-inference, openai）")
    backend_options: Optional[Dict[str, Any]] = Field(default=None, description="追加のバックエンド固有のオプション")

class ChatRequest(BaseModel):
    messages: List[Message]
    model: Optional[str] = "gpt-3.5-turbo"
# チャット完了API
@router.post("/completions")
async def create_chat_completion(request: ChatCompletionRequest):
    """
    チャット完了を作成（OpenAI互換API）
    動的に選択されたバックエンドを使用
    stream=true パラメータを指定するとストリーミングレスポンスが返されます
    """
    # バックエンドを選択
    backend_type = request.backend

    try:
        # OpenAIクライアントを取得
        try:
            client = get_client(backend_type=backend_type, model=request.model)
            logging.info(f"バックエンド {backend_type or '自動選択'} でクライアントを初期化しました。モデル: {request.model}")
        except Exception as e:
            logging.error(f"クライアントの初期化エラー: {e}")
            return {"error": f"バックエンドクライアントの初期化エラー: {str(e)}"}

        # リクエストの詳細を出力
        logging.info(f"チャット完了リクエスト: モデル={request.model}, バックエンド={backend_type or '自動選択'}, ストリーミング={request.stream}")
        logging.info(f"メッセージ数: {len(request.messages)}")

        # メッセージを準備
        messages = [{"role": msg.role, "content": msg.content} for msg in request.messages]

        # ストリーミングリクエストの場合
        if request.stream:
            async def generate_stream():
                try:
                    # OpenAIクライアントを使用してストリーミングリクエストを送信
                    stream = client.chat.completions.create(
                        model=request.model,
                        messages=messages,
                        temperature=request.temperature,
                        top_p=request.top_p,
                        max_tokens=request.max_tokens,
                        stream=True
                    )

                    # ストリーミングレスポンスを処理
                    logging.info(f"ストリーミングレスポンスの処理を開始")

                    for chunk in stream:
                        if hasattr(chunk, 'choices') and chunk.choices:
                            choice = chunk.choices[0]

                            # チャンクをJSON形式に変換
                            chunk_dict = {
                                "id": chunk.id,
                                "object": "chat.completion.chunk",
                                "created": chunk.created,
                                "model": chunk.model,
                                "choices": [
                                    {
                                        "index": choice.index,
                                        "delta": {},
                                        "finish_reason": choice.finish_reason
                                    }
                                ]
                            }

                            # deltaの内容を追加
                            if hasattr(choice, 'delta'):
                                delta = choice.delta
                                if hasattr(delta, 'content') and delta.content is not None:
                                    chunk_dict["choices"][0]["delta"]["content"] = delta.content
                                if hasattr(delta, 'role') and delta.role is not None:
                                    chunk_dict["choices"][0]["delta"]["role"] = delta.role

                            # OpenAI形式のストリーミングレスポンスを返す
                            response_data = f"data: {json.dumps(chunk_dict)}\n\n"
                            yield response_data

                            # 完了したかどうかを確認
                            if choice.finish_reason is not None:
                                # チャット履歴を更新（必要な場合）
                                try:
                                    # リクエストにセッションIDがあるか確認
                                    session_id = None
                                    if hasattr(request, 'backend_options') and request.backend_options and 'session_id' in request.backend_options:
                                        session_id = request.backend_options['session_id']
                                        logging.info(f"リクエストでセッションIDを発見: {session_id}")

                                        # セッションIDがある場合、チャット履歴を更新
                                        if session_id:
                                            try:
                                                # データベースセッションを作成
                                                db = SessionLocal()
                                                # 現在のユーザーを取得
                                                current_user = get_current_user(db=db)
                                                # チャット履歴更新関数を呼び出す
                                                from ..routers.chat_api import update_chat_history
                                                await update_chat_history(session_id=session_id, db=db, current_user=current_user)
                                                logging.info(f"セッション {session_id} のチャット履歴を正常に更新")
                                            except Exception as e:
                                                logging.error(f"update_chat_historyの呼び出しエラー: {e}")
                                            finally:
                                                # データベースセッションを閉じる
                                                db.close()
                                except Exception as update_error:
                                    logging.error(f"チャット履歴の更新エラー: {update_error}")

                    # 完了を示す[DONE]メッセージを送信
                    yield "data: [DONE]\n\n"

                except Exception as e:
                    logging.error(f"ストリーミングレスポンスのエラー: {e}")
                    yield f"data: {json.dumps({'error': str(e)})}\n\n"

            return StreamingResponse(generate_stream(), media_type="text/event-stream")

        # 非ストリーミングリクエストの場合
        else:
            # OpenAIクライアントを使用してリクエストを送信
            completion = client.chat.completions.create(
                model=request.model,
                messages=messages,
                temperature=request.temperature,
                top_p=request.top_p,
                max_tokens=request.max_tokens,
                stream=False
            )

            # レスポンスを処理
            logging.info(f"レスポンスを受信: {completion.model}")

            # OpenAI形式のレスポンスを作成
            response_data = {
                "id": completion.id,
                "object": "chat.completion",
                "created": completion.created,
                "model": completion.model,
                "choices": [
                    {
                        "index": choice.index,
                        "message": {
                            "role": choice.message.role,
                            "content": choice.message.content
                        },
                        "finish_reason": choice.finish_reason
                    } for choice in completion.choices
                ]
            }

        # チャット履歴を更新（必要な場合）
        try:
            # リクエストにセッションIDがあるか確認
            session_id = None
            if hasattr(request, 'backend_options') and request.backend_options and 'session_id' in request.backend_options:
                session_id = request.backend_options['session_id']
                logging.info(f"リクエストでセッションIDを発見: {session_id}")

                # セッションIDがある場合、チャット履歴を更新
                if session_id:
                    try:
                        # データベースセッションを作成
                        db = SessionLocal()
                        # 現在のユーザーを取得
                        current_user = get_current_user(db=db)
                        # チャット履歴更新関数を呼び出す
                        from ..routers.chat_api import update_chat_history
                        await update_chat_history(session_id=session_id, db=db, current_user=current_user)
                        logging.info(f"セッション {session_id} のチャット履歴を正常に更新")
                    except Exception as e:
                        logging.error(f"update_chat_historyの呼び出しエラー: {e}")
                    finally:
                        # データベースセッションを閉じる
                        db.close()
        except Exception as update_error:
            logging.error(f"チャット履歴の更新エラー: {update_error}")

        return response_data

    except Exception as e:
        import traceback
        error_trace = traceback.format_exc()
        logging.error(f"チャット完了エラー: {e}")
        logging.error(f"エラートレース: {error_trace}")

        # OpenAI形式のエラーレスポンスを返す
        return {
            "id": f"chatcmpl-error-{int(time.time())}",
            "object": "chat.completion",
            "created": int(time.time()),
            "model": request.model,
            "choices": [
                {
                    "index": 0,
                    "message": {
                        "role": "assistant",
                        "content": f"エラーが発生しました: {str(e)}"
                    },
                    "finish_reason": "stop"
                }
            ],
            "error": {
                "message": str(e),
                "type": type(e).__name__,
                "traceback": error_trace
            }
        }

@router.get("/history", response_model=List[ChatSessionResponse])
async def get_chat_history(
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """
    ユーザーのチャット履歴を取得
    """
    try:
        # ユーザーのチャットセッションを取得
        sessions = db.query(ChatSession).filter(
            ChatSession.user_id == current_user.id
        ).order_by(
            ChatSession.created_at.desc()
        ).offset(skip).limit(limit).all()

        # レスポンスを構築
        result = []
        for session in sessions:
            # 最後のメッセージを取得
            last_message = db.query(ChatMessage).filter(
                ChatMessage.session_id == session.id
            ).order_by(
                ChatMessage.created_at.desc()
            ).first()

            result.append(ChatSessionResponse(
                id=session.id,
                title=session.title,
                created_at=session.created_at,
                last_message=last_message.content if last_message else ""
            ))

        return result
    except Exception as e:
        # データベース操作が失敗した場合、エラーを返す
        print(f"チャット履歴の取得中にエラーが発生しました: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to fetch chat history: {str(e)}")

@router.get("/sessions/{session_id}", response_model=List[ChatMessageResponse])
async def get_chat_messages(
    session_id: str,
    skip: int = Query(0, ge=0),
    limit: int = Query(50, ge=1, le=100),
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """
    特定のチャットセッションのメッセージを取得
    """
    # セッションが存在し、現在のユーザーに属していることを確認
    session = db.query(ChatSession).filter(
        ChatSession.id == session_id,
        ChatSession.user_id == current_user.id
    ).first()

    if not session:
        raise HTTPException(status_code=404, detail="Chat session not found")

    # セッションメッセージを取得
    messages = db.query(ChatMessage).filter(
        ChatMessage.session_id == session_id
    ).order_by(
        ChatMessage.created_at.asc()
    ).offset(skip).limit(limit).all()

    return [
        ChatMessageResponse(
            id=msg.id,
            session_id=msg.session_id,
            role=msg.role,
            content=msg.content,
            created_at=msg.created_at
        ) for msg in messages
    ]

class ChatSessionRequest(BaseModel):
    title: str = "New Chat"

@router.post("/sessions", response_model=ChatSessionResponse)
async def create_chat_session(
    request: ChatSessionRequest = None,
    title: str = None,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    # リクエストボディのタイトルを優先し、なければクエリパラメータのタイトルを使用
    session_title = (request and request.title) or title or "New Chat"
    print(f"チャットセッションを作成中、タイトル: {session_title}")
    """
    新しいチャットセッションを作成
    """
    session_id = str(uuid.uuid4())
    created_at = datetime.now(timezone.utc)

    try:
        # セッションレコードを作成（データベーステーブルが存在する場合）
        new_session = ChatSession(
            id=session_id,
            user_id=current_user.id,
            title=session_title,
            created_at=created_at
        )

        db.add(new_session)
        db.commit()

        return ChatSessionResponse(
            id=session_id,
            title=session_title,
            created_at=new_session.created_at,
            last_message=""
        )
    except Exception as e:
        # データベース操作が失敗した場合、モックレスポンスを返す
        db.rollback()
        print(f"データベースエラー: {e}")

        return ChatSessionResponse(
            id=session_id,
            title=session_title,
            created_at=created_at,
            last_message=""
        )

# セッションタイトルを更新するリクエストモデル
class UpdateSessionTitleRequest(BaseModel):
    title: str

# セッションタイトルを更新
@router.put("/sessions/{session_id}", response_model=ChatSessionResponse)
async def update_chat_session(
    session_id: str,
    request: UpdateSessionTitleRequest,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    try:
        # セッションを検索
        session = db.query(ChatSession).filter(ChatSession.id == session_id).first()

        if not session:
            raise HTTPException(status_code=404, detail="Chat session not found")

        # タイトルを更新
        session.title = request.title
        db.commit()

        # 最後のメッセージを取得
        last_message = ""
        last_msg = db.query(ChatMessage).filter(ChatMessage.session_id == session_id).order_by(ChatMessage.created_at.desc()).first()
        if last_msg:
            last_message = last_msg.content[:50] + "..." if len(last_msg.content) > 50 else last_msg.content

        return ChatSessionResponse(
            id=session.id,
            title=session.title,
            created_at=session.created_at,
            last_message=last_message
        )
    except HTTPException as he:
        raise he
    except Exception as e:
        db.rollback()
        print(f"セッションタイトルの更新中にエラーが発生しました: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to update chat session: {str(e)}")

class ChatMessageRequest(BaseModel):
    role: str
    content: str

@router.post("/sessions/{session_id}/messages", response_model=ChatMessageResponse)
async def add_chat_message(
    session_id: str,
    message: ChatMessageRequest,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """
    チャットセッションに新しいメッセージを追加
    """
    # セッションが存在し、現在のユーザーに属していることを確認
    session = db.query(ChatSession).filter(
        ChatSession.id == session_id,
        ChatSession.user_id == current_user.id
    ).first()

    if not session:
        raise HTTPException(status_code=404, detail="Chat session not found")

    # 新しいメッセージを作成
    message_id = str(uuid.uuid4())
    new_message = ChatMessage(
        id=message_id,
        session_id=session_id,
        role=message.role,
        content=message.content,
        created_at=datetime.now(timezone.utc)
    )

    db.add(new_message)
    db.commit()

    return ChatMessageResponse(
        id=message_id,
        session_id=session_id,
        role=message.role,
        content=message.content,
        created_at=new_message.created_at
    )

# セッション履歴を更新する関数
@router.post("/sessions/{session_id}/update_history", response_model=ChatSessionResponse)
async def update_chat_history(
    session_id: str,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """
    セッション履歴を更新
    後端からAI応答後に呼び出され、セッションの最後のメッセージと他のメタデータを更新
    """
    try:
        # セッションが存在し、現在のユーザーに属していることを確認
        session = db.query(ChatSession).filter(
            ChatSession.id == session_id,
            ChatSession.user_id == current_user.id
        ).first()

        if not session:
            raise HTTPException(status_code=404, detail="Chat session not found")

        # 最後のメッセージを取得
        last_message = db.query(ChatMessage).filter(
            ChatMessage.session_id == session_id
        ).order_by(ChatMessage.created_at.desc()).first()

        # 最後のメッセージがある場合、セッションのlast_messageフィールドを更新
        if last_message:
            # ここにさらなる更新ロジックを追加できます。例えば、セッションタイトルを更新する場合
            # セッションタイトルがデフォルトの"New Chat"の場合、ユーザーの最初のメッセージをタイトルとして使用できます

            if session.title == "New Chat":
                # ユーザーの最初のメッセージを取得
                first_user_message = db.query(ChatMessage).filter(
                    ChatMessage.session_id == session_id,
                    ChatMessage.role == "user"
                ).order_by(ChatMessage.created_at.asc()).first()

                if first_user_message:

                    title = first_user_message.content[:15] + "..." if len(first_user_message.content) > 15 else first_user_message.content
                    session.title = title

        # 提交更改
        db.commit()

        # 返回更新后的会话信息
        last_message_content = ""
        if last_message:
            last_message_content = last_message.content[:50] + "..." if len(last_message.content) > 50 else last_message.content

        return ChatSessionResponse(
            id=session.id,
            title=session.title,
            created_at=session.created_at,
            last_message=last_message_content
        )
    except HTTPException as he:
        raise he
    except Exception as e:
        db.rollback()
        print(f"セッション履歴の更新中にエラーが発生しました: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to update chat history: {str(e)}")

@router.delete("/sessions/{session_id}", status_code=204)
async def delete_chat_session(
    session_id: str,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """
    チャットセッションを削除
    """
    # セッションが存在し、現在のユーザーに属しているか確認
    session = db.query(ChatSession).filter(
        ChatSession.id == session_id,
        ChatSession.user_id == current_user.id
    ).first()

    if not session:
        raise HTTPException(status_code=404, detail="Chat session not found")

    # セッションに関連するすべてのメッセージを削除
    db.query(ChatMessage).filter(
        ChatMessage.session_id == session_id
    ).delete()

    # セッションを削除
    db.delete(session)
    db.commit()

    return None

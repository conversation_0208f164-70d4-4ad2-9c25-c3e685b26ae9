"""
RAG API ルーター
"""
import os
import logging
import tempfile
import shutil
from typing import List, Dict, Any, Optional
from fastapi import APIRouter, Depends, HTTPException, UploadFile, File, Form, BackgroundTasks, Query
from fastapi.responses import J<PERSON>NResponse
from pydantic import BaseModel, Field
from sqlalchemy.orm import Session

from ..database import get_db
from ..auth import get_current_user
from ..rag.rag_service import RAGService
from ..config import settings

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/rag", tags=["RAG APIs"])

# モデル定義
class TextInput(BaseModel):
    text: str
    metadata: Optional[Dict[str, Any]] = None

class QueryInput(BaseModel):
    query: str
    top_k: int = 5
    filter_params: Optional[Dict[str, Any]] = None

class DeleteInput(BaseModel):
    filter_params: Dict[str, Any]

# RAGサービスのインスタンスを取得
def get_rag_service():
    return RAGService()

# アップロードディレクトリを作成
UPLOAD_DIR = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "uploads")
os.makedirs(UPLOAD_DIR, exist_ok=True)

@router.post("/upload-file")
async def upload_file(
    file: UploadFile = File(...),
    metadata: Optional[str] = Form(None),
    current_user = Depends(get_current_user),
    rag_service = Depends(get_rag_service)
):
    """
    ファイルをアップロードしてRAGシステムに追加します
    """
    try:
        # メタデータをパース
        meta_dict = {}
        if metadata:
            import json
            try:
                meta_dict = json.loads(metadata)
            except json.JSONDecodeError:
                raise HTTPException(status_code=400, detail="メタデータが有効なJSON形式ではありません")
        
        # ユーザー情報をメタデータに追加
        meta_dict["user_id"] = current_user.id
        meta_dict["username"] = current_user.username
        
        # ファイルを一時ディレクトリに保存
        file_path = os.path.join(UPLOAD_DIR, file.filename)
        with open(file_path, "wb") as buffer:
            shutil.copyfileobj(file.file, buffer)
        
        try:
            # ファイルをRAGシステムに追加
            chunks_count = rag_service.add_document(file_path, meta_dict)
            
            return {
                "status": "success",
                "message": f"ファイル {file.filename} が正常にアップロードされ、{chunks_count} チャンクが追加されました",
                "file_name": file.filename,
                "chunks_count": chunks_count
            }
        finally:
            # 一時ファイルを削除
            os.remove(file_path)
    
    except Exception as e:
        logger.error(f"ファイルアップロード中にエラーが発生しました: {e}")
        raise HTTPException(status_code=500, detail=f"ファイルアップロード中にエラーが発生しました: {str(e)}")

@router.post("/upload-directory")
async def upload_directory(
    background_tasks: BackgroundTasks,
    directory_path: str = Form(...),
    recursive: bool = Form(True),
    metadata: Optional[str] = Form(None),
    current_user = Depends(get_current_user),
    rag_service = Depends(get_rag_service)
):
    """
    ディレクトリ内のファイルをRAGシステムに追加します
    """
    try:
        # メタデータをパース
        meta_dict = {}
        if metadata:
            import json
            try:
                meta_dict = json.loads(metadata)
            except json.JSONDecodeError:
                raise HTTPException(status_code=400, detail="メタデータが有効なJSON形式ではありません")
        
        # ユーザー情報をメタデータに追加
        meta_dict["user_id"] = current_user.id
        meta_dict["username"] = current_user.username
        
        # ディレクトリが存在するか確認
        if not os.path.exists(directory_path):
            raise HTTPException(status_code=404, detail=f"ディレクトリが見つかりません: {directory_path}")
        
        if not os.path.isdir(directory_path):
            raise HTTPException(status_code=400, detail=f"{directory_path} はディレクトリではありません")
        
        # バックグラウンドタスクとしてディレクトリを処理
        def process_directory():
            try:
                chunks_count = rag_service.add_documents_from_directory(directory_path, recursive, meta_dict)
                logger.info(f"ディレクトリ {directory_path} から {chunks_count} チャンクが追加されました")
            except Exception as e:
                logger.error(f"ディレクトリ処理中にエラーが発生しました: {e}")
        
        background_tasks.add_task(process_directory)
        
        return {
            "status": "processing",
            "message": f"ディレクトリ {directory_path} の処理がバックグラウンドで開始されました",
            "directory_path": directory_path
        }
    
    except Exception as e:
        logger.error(f"ディレクトリ処理中にエラーが発生しました: {e}")
        raise HTTPException(status_code=500, detail=f"ディレクトリ処理中にエラーが発生しました: {str(e)}")

@router.post("/add-text")
async def add_text(
    text_input: TextInput,
    current_user = Depends(get_current_user),
    rag_service = Depends(get_rag_service)
):
    """
    テキストをRAGシステムに追加します
    """
    try:
        # メタデータを準備
        metadata = text_input.metadata or {}
        
        # ユーザー情報をメタデータに追加
        metadata["user_id"] = current_user.id
        metadata["username"] = current_user.username
        
        # テキストをRAGシステムに追加
        chunks_count = rag_service.add_text(text_input.text, metadata)
        
        return {
            "status": "success",
            "message": f"テキストが正常に追加され、{chunks_count} チャンクが作成されました",
            "chunks_count": chunks_count
        }
    
    except Exception as e:
        logger.error(f"テキスト追加中にエラーが発生しました: {e}")
        raise HTTPException(status_code=500, detail=f"テキスト追加中にエラーが発生しました: {str(e)}")

@router.post("/query")
async def query(
    query_input: QueryInput,
    current_user = Depends(get_current_user),
    rag_service = Depends(get_rag_service)
):
    """
    RAGシステムに対してクエリを実行します
    """
    try:
        # クエリを実行
        results = rag_service.query(query_input.query, query_input.top_k, query_input.filter_params)
        
        return {
            "status": "success",
            "query": query_input.query,
            "results": results,
            "count": len(results)
        }
    
    except Exception as e:
        logger.error(f"クエリ実行中にエラーが発生しました: {e}")
        raise HTTPException(status_code=500, detail=f"クエリ実行中にエラーが発生しました: {str(e)}")

@router.post("/run-rag")
async def run_rag(
    query_input: QueryInput,
    current_user = Depends(get_current_user),
    rag_service = Depends(get_rag_service)
):
    """
    RAGを実行して回答を生成します
    """
    try:
        # RAGを実行
        response = rag_service.run_rag(query_input.query, query_input.top_k, query_input.filter_params)
        
        return {
            "status": "success",
            "query": query_input.query,
            "response": response
        }
    
    except Exception as e:
        logger.error(f"RAG実行中にエラーが発生しました: {e}")
        raise HTTPException(status_code=500, detail=f"RAG実行中にエラーが発生しました: {str(e)}")

@router.post("/delete")
async def delete_documents(
    delete_input: DeleteInput,
    current_user = Depends(get_current_user),
    rag_service = Depends(get_rag_service)
):
    """
    フィルタに一致するドキュメントを削除します
    """
    try:
        # 管理者権限を確認
        if not current_user.is_admin:
            # 自分のドキュメントのみ削除可能
            delete_input.filter_params["user_id"] = current_user.id
        
        # ドキュメントを削除
        rag_service.delete_documents(delete_input.filter_params)
        
        return {
            "status": "success",
            "message": "指定されたフィルタに一致するドキュメントが削除されました",
            "filter": delete_input.filter_params
        }
    
    except Exception as e:
        logger.error(f"ドキュメント削除中にエラーが発生しました: {e}")
        raise HTTPException(status_code=500, detail=f"ドキュメント削除中にエラーが発生しました: {str(e)}")

@router.get("/collection-info")
async def get_collection_info(
    current_user = Depends(get_current_user),
    rag_service = Depends(get_rag_service)
):
    """
    コレクションの情報を取得します
    """
    try:
        # コレクション情報を取得
        info = rag_service.get_collection_info()
        
        return {
            "status": "success",
            "info": info
        }
    
    except Exception as e:
        logger.error(f"コレクション情報取得中にエラーが発生しました: {e}")
        raise HTTPException(status_code=500, detail=f"コレクション情報取得中にエラーが発生しました: {str(e)}")

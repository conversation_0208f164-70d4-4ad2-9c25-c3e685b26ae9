from fastapi import APIRouter, Request, Response
import requests
import logging
from typing import List, Dict, Any
try:
    from config import settings, get_versioned_url
except ImportError:
    from ..config import settings, get_versioned_url
import subprocess

# ログを設定
logger = logging.getLogger(__name__)

router = APIRouter(tags=["Models APIs"])

@router.get("/v1/models", response_model=Dict[str, List[Dict[str, Any]]], operation_id="list_models_v1_unique")
async def list_models(request: Request, response: Response):
    # CORSヘッダーを追加
    response.headers["Access-Control-Allow-Origin"] = "*"
    response.headers["Access-Control-Allow-Methods"] = "GET, OPTIONS"
    response.headers["Access-Control-Allow-Headers"] = "Content-Type, Authorization"
    # リクエスト情報を記録
    logger.info(f"{request.client.host} からのモデルリクエストを受信しました。ヘッダー: {request.headers.get('origin', 'Unknown')}, {request.headers.get('user-agent', 'Unknown')}")
    """
    利用可能なすべてのモデルリストを取得します。
    Ollamaや他の設定されたバックエンドから自動的にモデルを取得します。

    レスポンス形式:
    - data: 利用可能なモデルの配列
      - id: モデルID
      - name: モデル名
      - backend: 使用するバックエンド
      - default: デフォルトモデルかどうか
      - description: モデルの説明
      - supports_streaming: ストリーミングをサポートするかどうか
    """
    models = []
    available_backends = []

    # まず、どのバックエンドが利用可能かを確認
    try:
        # Ollamaが利用可能か確認
        try:
            response = requests.get(f"{settings.OLLAMA_ENDPOINT}/api/version", timeout=2)
            if response.status_code == 200:
                available_backends.append("ollama")
                logger.info("Ollama バックエンドは利用可能です")
        except Exception as e:
            logger.warning(f"Ollama バックエンドは利用できません: {e}")

        # vLLMが利用可能か確認
        try:
            # 設定に基づいてAPIバージョンを含むURLを構築
            vllm_url = get_versioned_url(settings.VLLM_ENDPOINT)
            vllm_models_url = f"{vllm_url}/models"

            logger.info(f"vLLM モデル一覧を取得しています: {vllm_models_url}")

            try:
                # OpenAI互換APIを使用してモデル一覧を取得
                headers = {"Authorization": "Bearer EMPTY"}
                response = requests.get(vllm_models_url, headers=headers, timeout=2)

                if response.status_code == 200:
                    available_backends.append("vllm")
                    logger.info(f"vLLM バックエンドは利用可能です (URL: {vllm_models_url})")
                else:
                    logger.warning(f"vLLM バックエンドは利用できません: ステータスコード {response.status_code}")
                    # 別の方法を試す
                    try:
                        # 直接エンドポイントを使用
                        direct_url = f"{settings.VLLM_ENDPOINT}/v1/models"
                        logger.info(f"直接URLでvLLM モデル一覧を取得しています: {direct_url}")
                        direct_response = requests.get(direct_url, headers=headers, timeout=2)

                        if direct_response.status_code == 200:
                            available_backends.append("vllm")
                            logger.info(f"vLLM バックエンドは利用可能です (直接URL: {direct_url})")
                        else:
                            logger.warning(f"vLLM バックエンドは直接URLでも利用できません: ステータスコード {direct_response.status_code}")
                    except Exception as direct_err:
                        logger.warning(f"vLLM 直接URL {direct_url} へのリクエスト中にエラーが発生しました: {direct_err}")
            except Exception as url_err:
                logger.warning(f"vLLM URL {vllm_models_url} へのリクエスト中にエラーが発生しました: {url_err}")
                logger.warning("vLLM バックエンドは利用できません")

                # 別の方法を試す
                try:
                    # 直接エンドポイントを使用
                    direct_url = f"{settings.VLLM_ENDPOINT}/v1/models"
                    logger.info(f"直接URLでvLLM モデル一覧を取得しています: {direct_url}")
                    headers = {"Authorization": "Bearer EMPTY"}
                    direct_response = requests.get(direct_url, headers=headers, timeout=2)

                    if direct_response.status_code == 200:
                        available_backends.append("vllm")
                        logger.info(f"vLLM バックエンドは利用可能です (直接URL: {direct_url})")
                    else:
                        logger.warning(f"vLLM バックエンドは直接URLでも利用できません: ステータスコード {direct_response.status_code}")
                except Exception as direct_err:
                    logger.warning(f"vLLM 直接URL へのリクエスト中にエラーが発生しました: {direct_err}")
        except Exception as e:
            logger.warning(f"vLLM バックエンドは利用できません: {e}")

        # SGLangが利用可能か確認
        try:
            sglang_url = get_versioned_url(settings.SGLANG_ENDPOINT, include_version=False)
            response = requests.get(f"{sglang_url}/health", timeout=2)
            if response.status_code == 200:
                available_backends.append("sglang")
                logger.info("SGLang バックエンドは利用可能です")
        except Exception as e:
            logger.warning(f"SGLang バックエンドは利用できません: {e}")

        # X-Inferenceが利用可能か確認
        try:
            x_inference_url = get_versioned_url(settings.X_INFERENCE_ENDPOINT)
            response = requests.get(f"{x_inference_url}/models", timeout=2)
            if response.status_code == 200:
                available_backends.append("x-inference")
                logger.info("X-Inference バックエンドは利用可能です")
        except Exception as e:
            logger.warning(f"X-Inference バックエンドは利用できません: {e}")

        # OpenAIが利用可能か確認
        if settings.OPENAI_API_KEY:
            try:
                headers = {"Authorization": f"Bearer {settings.OPENAI_API_KEY}"}
                openai_url = get_versioned_url(settings.OPENAI_ENDPOINT)
                response = requests.get(f"{openai_url}/models", headers=headers, timeout=3)
                if response.status_code == 200:
                    available_backends.append("openai")
                    logger.info(f"OpenAI バックエンドは利用可能です (URL: {openai_url})")
                else:
                    logger.warning(f"OpenAI バックエンドは利用できません: ステータスコード {response.status_code}")
            except Exception as e:
                logger.warning(f"OpenAI バックエンドは利用できません: {e}")

        # 利用可能なバックエンドがない場合、Ollamaをデフォルトバックエンドとして追加
        if not available_backends:
            logger.warning("利用可能なバックエンドがありません。Ollamaをデフォルトとして使用します")
            available_backends.append("ollama")
    except Exception as e:
        logger.error(f"利用可能なバックエンドの確認中にエラーが発生しました: {e}")
        # エラーが発生した場合、Ollamaをデフォルトバックエンドとして追加
        available_backends.append("ollama")

    # Ollamaからモデルリストを取得
    if "ollama" in available_backends:
        try:
            # まずOllama APIを使用
            ollama_url = f"{settings.OLLAMA_ENDPOINT}/api/tags"
            logger.info(f"Ollamaモデル一覧を取得しています: {ollama_url}")

            try:
                response = requests.get(ollama_url, timeout=3)
                logger.info(f"Ollamaレスポンスステータス: {response.status_code}")

                if response.status_code == 200:
                    response_json = response.json()
                    logger.info(f"Ollamaレスポンス: {response_json}")

                    ollama_models = response_json.get("models", [])
                    logger.info(f"取得したOllamaモデル数: {len(ollama_models)}")

                    for model in ollama_models:
                        logger.info(f"Ollamaモデル追加: {model['name']}")
                        models.append({
                            "id": model["name"],
                            "name": model["name"],
                            "backend": "ollama",
                            "default": model["name"] == settings.DEFAULT_LLM_MODEL,
                            "description": f"Ollama モデル: {model['name']}",
                            "size": model.get("size", "Unknown"),
                            "modified_at": model.get("modified_at", ""),
                            "supports_streaming": True
                        })
                else:
                    logger.error(f"Ollamaモデル一覧の取得に失敗しました。ステータスコード: {response.status_code}")
                    logger.error(f"レスポンス内容: {response.text}")
            except requests.exceptions.RequestException as req_err:
                logger.error(f"Ollamaリクエスト例外: {req_err}")
            # API呼び出しが失敗した場合、コマンドラインを使用
            try:
                logger.info("コマンドラインでOllamaモデル一覧を取得しています")
                result = subprocess.run(["ollama", "list"], capture_output=True, text=True, check=True, timeout=5)

                logger.info(f"コマンドライン出力: {result.stdout}")

                lines = result.stdout.strip().split("\n")[1:]  # ヘッダー行をスキップ
                logger.info(f"取得したモデル行数: {len(lines)}")

                for line in lines:
                    if line.strip():
                        parts = line.split()
                        logger.info(f"モデル行解析: {parts}")

                        if len(parts) >= 1:
                            model_name = parts[0]
                            logger.info(f"コマンドラインからモデル追加: {model_name}")
                            models.append({
                                "id": model_name,
                                "name": model_name,
                                "backend": "ollama",
                                "default": model_name == settings.DEFAULT_LLM_MODEL,
                                "description": f"Ollama モデル: {model_name}",
                                "supports_streaming": True
                            })
            except Exception as e:
                logger.warning(f"コマンドラインでOllamaモデルの取得に失敗しました: {e}")
                # モデルが取得できない場合は何も追加しない
                logger.info("Ollamaモデルを取得できませんでした。空のリストを使用します。")
        except Exception as e:
            logging.warning(f"Ollamaモデルの取得に失敗しました: {e}")
            # モデルが取得できない場合は何も追加しない
            logger.info("Ollamaモデルを取得できませんでした。空のリストを使用します。")

    # vLLMモデルを取得
    try:
        # まずAPIから取得を試みる
        vllm_models_from_api = []
        try:
            # 設定に基づいてAPIバージョンを含むURLを構築
            vllm_url = get_versioned_url(settings.VLLM_ENDPOINT)
            vllm_models_url = f"{vllm_url}/models"

            logger.info(f"vLLM モデル一覧をAPIから取得しています: {vllm_models_url}")

            # OpenAI互換APIを使用してモデル一覧を取得
            headers = {"Authorization": "Bearer EMPTY"}
            response = requests.get(vllm_models_url, headers=headers, timeout=3)

            if response.status_code == 200:
                response_data = response.json()
                if "data" in response_data and isinstance(response_data["data"], list):
                    for model in response_data["data"]:
                        if isinstance(model, dict) and "id" in model:
                            vllm_models_from_api.append(model["id"])

                    logger.info(f"APIから取得したvLLMモデル数: {len(vllm_models_from_api)}")
                else:
                    logger.warning(f"vLLM APIレスポンスに 'data' フィールドがありません: {response_data}")
            else:
                logger.warning(f"vLLM モデル一覧の取得に失敗しました: ステータスコード {response.status_code}")

                # 別の方法を試す
                try:
                    # 直接エンドポイントを使用
                    direct_url = f"{settings.VLLM_ENDPOINT}/v1/models"
                    logger.info(f"直接URLでvLLM モデル一覧を取得しています: {direct_url}")
                    direct_response = requests.get(direct_url, headers=headers, timeout=3)

                    if direct_response.status_code == 200:
                        direct_data = direct_response.json()
                        if "data" in direct_data and isinstance(direct_data["data"], list):
                            for model in direct_data["data"]:
                                if isinstance(model, dict) and "id" in model:
                                    vllm_models_from_api.append(model["id"])

                            logger.info(f"直接URLから取得したvLLMモデル数: {len(vllm_models_from_api)}")
                        else:
                            logger.warning(f"vLLM 直接APIレスポンスに 'data' フィールドがありません: {direct_data}")
                    else:
                        logger.warning(f"vLLM モデル一覧の直接URLからの取得に失敗しました: ステータスコード {direct_response.status_code}")
                except Exception as direct_err:
                    logger.warning(f"vLLM 直接URL {direct_url} へのリクエスト中にエラーが発生しました: {direct_err}")
        except Exception as api_err:
            logger.warning(f"vLLM モデル一覧のAPI取得中にエラーが発生しました: {api_err}")

        # APIから取得できた場合はそれを使用し、できなかった場合は設定から取得
        if vllm_models_from_api:
            logger.info(f"APIから取得したvLLMモデルを使用します: {vllm_models_from_api}")
            for model_name in vllm_models_from_api:
                if not any(m["id"] == model_name for m in models):  # 重複を避ける
                    models.append({
                        "id": model_name,
                        "name": model_name,
                        "backend": "vllm",
                        "default": model_name == settings.DEFAULT_LLM_MODEL,
                        "description": f"vLLM モデル: {model_name}",
                        "supports_streaming": True
                    })
            logger.info(f"vLLMモデル数: {len(vllm_models_from_api)}")
        else:
            # APIから取得できなかった場合は設定から取得
            logger.info("APIからvLLMモデルを取得できなかったため、設定から取得します")
            vllm_models = getattr(settings, "VLLM_MODELS", [])
            if vllm_models:
                for model_name in vllm_models:
                    if not any(m["id"] == model_name for m in models):  # 重複を避ける
                        models.append({
                            "id": model_name,
                            "name": model_name,
                            "backend": "vllm",
                            "default": model_name == settings.DEFAULT_LLM_MODEL,
                            "description": f"設定からのモデル: {model_name}",
                            "supports_streaming": True
                        })
                logger.info(f"設定から取得したvLLMモデル数: {len(vllm_models)}")
            else:
                # 設定にもモデルがない場合はデフォルトモデルを追加
                default_vllm_models = ["facebook/opt-125m", "deepseek-r1:7b"]
                for model_name in default_vllm_models:
                    if not any(m["id"] == model_name for m in models):  # 重複を避ける
                        models.append({
                            "id": model_name,
                            "name": model_name,
                            "backend": "vllm",
                            "default": model_name == settings.DEFAULT_LLM_MODEL,
                            "description": f"デフォルトvLLMモデル: {model_name}",
                            "supports_streaming": True
                        })
                logger.info(f"デフォルトvLLMモデル数: {len(default_vllm_models)}")
    except Exception as e:
        logger.warning(f"vLLMモデルの取得に失敗しました: {e}")
        # エラーが発生した場合はデフォルトモデルを追加
        default_vllm_models = ["facebook/opt-125m", "deepseek-r1:7b"]
        for model_name in default_vllm_models:
            if not any(m["id"] == model_name for m in models):  # 重複を避ける
                models.append({
                    "id": model_name,
                    "name": model_name,
                    "backend": "vllm",
                    "default": model_name == settings.DEFAULT_LLM_MODEL,
                    "description": f"デフォルトvLLMモデル: {model_name}",
                    "supports_streaming": True
                })
        logger.info(f"エラー後のデフォルトvLLMモデル数: {len(default_vllm_models)}")

    # 設定ファイルで定義された他のモデルを追加
    for model_name, backend in settings.MODEL_BACKEND_MAPPING.items():
        # このモデルがすでに追加されているか確認（重複を避ける）
        if not any(m["id"] == model_name for m in models):
            # 利用可能なバックエンドのモデルのみ追加
            if backend in available_backends:
                models.append({
                    "id": model_name,
                    "name": model_name,
                    "backend": backend,
                    "default": model_name == settings.DEFAULT_LLM_MODEL,
                    "description": f"設定からのモデル: {model_name}",
                    "supports_streaming": True
                })

    # モデルが見つからない場合は空のリストを返す
    if not models:
        logger.warning("モデルが見つからないため、空のリストを返します")

    # 少なくとも1つのモデルがデフォルトとしてマークされていることを確認
    has_default = any(model["default"] for model in models)
    if not has_default and models:
        models[0]["default"] = True

    # モデルリストを返す
    logger.info(f"{len(models)} モデルを {len(available_backends)} の利用可能なバックエンドから返します")
    logger.debug(f"モデル: {models}")
    logger.debug(f"利用可能なバックエンド: {available_backends}")

    return {"data": models}

@router.get("/v1/backends", response_model=Dict[str, Any], operation_id="list_backends_v1_unique")
async def list_backends(request: Request):
    # リクエスト情報を記録
    logger.info(f"{request.client.host} からのバックエンドリクエストを受信しました。ヘッダー: {request.headers.get('origin', 'Unknown')}, {request.headers.get('user-agent', 'Unknown')}")
    """
    利用可能なすべてのバックエンドリストを取得します。

    非推奨: このエンドポイントは将来のバージョンで削除される予定です。

    レスポンス形式:
    - backends: 利用可能なバックエンドの配列
      - id: バックエンドID
      - name: バックエンド名
      - description: バックエンドの説明
      - endpoint: バックエンドのAPIエンドポイント
      - supports_streaming: ストリーミングをサポートするかどうか
      - available: 現在利用可能かどうか
    - default_backend: デフォルトで使用されるバックエンドID
    """
    backends = []
    available_backends = []

    # Ollamaが利用可能か確認
    try:
        response = requests.get(f"{settings.OLLAMA_ENDPOINT}/api/version", timeout=2)
        if response.status_code == 200:
            available_backends.append("ollama")
            backends.append({
                "id": "ollama",
                "name": "Ollama",
                "description": "Ollama を使用したローカル推論",
                "endpoint": settings.OLLAMA_ENDPOINT,
                "supports_streaming": True,
                "available": True
            })
            logger.info("Ollama バックエンドは利用可能です")
        else:
            logger.warning(f"Ollama バックエンドは利用できません: ステータスコード {response.status_code}")
            backends.append({
            "id": "ollama",
            "name": "Ollama",
            "description": "Ollama を使用したローカル推論（現在利用不可）",
            "endpoint": settings.OLLAMA_ENDPOINT,
            "supports_streaming": True,
            "available": False
        })
    except Exception as e:
        logger.warning(f"Ollama バックエンドは利用できません: {e}")
        backends.append({
            "id": "ollama",
            "name": "Ollama",
            "description": "Ollama を使用したローカル推論（現在利用不可）",
            "endpoint": settings.OLLAMA_ENDPOINT,
            "supports_streaming": True,
            "available": False
        })

    # vLLMが利用可能か確認
    try:
        # 設定に基づいてAPIバージョンを含むURLを構築
        vllm_url = get_versioned_url(settings.VLLM_ENDPOINT)
        vllm_models_url = f"{vllm_url}/models"

        logger.info(f"バックエンド一覧用の vLLM モデル一覧を取得しています: {vllm_models_url}")

        vllm_available = False
        try:
            response = requests.get(vllm_models_url, timeout=2)

            if response.status_code == 200:
                vllm_available = True
                logger.info(f"vLLM バックエンドは利用可能です (URL: {vllm_models_url})")
            else:
                logger.warning(f"vLLM バックエンドは利用できません: ステータスコード {response.status_code}")
        except Exception as url_err:
            logger.warning(f"vLLM URL {vllm_models_url} へのリクエスト中にエラーが発生しました: {url_err}")

        if vllm_available:
            available_backends.append("vllm")
            backends.append({
                "id": "vllm",
                "name": "vLLM",
                "description": "vLLM を使用した高性能推論",
                "endpoint": settings.VLLM_ENDPOINT,
                "supports_streaming": True,
                "available": True
            })
            logger.info("vLLM バックエンドは利用可能です")
        else:
            logger.warning(f"vLLM バックエンドは利用できません")
            backends.append({
                "id": "vllm",
                "name": "vLLM",
                "description": "vLLM を使用した高性能推論（現在利用不可）",
                "endpoint": settings.VLLM_ENDPOINT,
                "supports_streaming": True,
                "available": False
            })
    except Exception as e:
        logger.warning(f"vLLM バックエンドは利用できません: {e}")
        backends.append({
            "id": "vllm",
            "name": "vLLM",
            "description": "vLLM を使用した高性能推論（現在利用不可）",
            "endpoint": settings.VLLM_ENDPOINT,
            "supports_streaming": True,
            "available": False
        })

    # SGLangが利用可能か確認
    try:
        sglang_url = get_versioned_url(settings.SGLANG_ENDPOINT, include_version=False)
        response = requests.get(f"{sglang_url}/health", timeout=2)
        if response.status_code == 200:
            available_backends.append("sglang")
            backends.append({
                "id": "sglang",
                "name": "SGLang",
                "description": "SGLang を使用した構造化生成",
                "endpoint": settings.SGLANG_ENDPOINT,
                "supports_streaming": True,
                "available": True
            })
            logger.info("SGLang バックエンドは利用可能です")
        else:
            logger.warning(f"SGLang バックエンドは利用できません: ステータスコード {response.status_code}")
            backends.append({
            "id": "sglang",
            "name": "SGLang",
            "description": "SGLang を使用した構造化生成（現在利用不可）",
            "endpoint": settings.SGLANG_ENDPOINT,
            "supports_streaming": True,
            "available": False
            })
    except Exception as e:
        logger.warning(f"SGLang バックエンドは利用できません: {e}")
        backends.append({
            "id": "sglang",
            "name": "SGLang",
            "description": "SGLang を使用した構造化生成（現在利用不可）",
            "endpoint": settings.SGLANG_ENDPOINT,
            "supports_streaming": True,
            "available": False
        })

    # X-Inferenceが利用可能か確認
    try:
        x_inference_url = get_versioned_url(settings.X_INFERENCE_ENDPOINT)
        response = requests.get(f"{x_inference_url}/models", timeout=2)
        if response.status_code == 200:
            available_backends.append("x-inference")
            backends.append({
                "id": "x-inference",
                "name": "X-Inference",
                "description": "X-Inference バックエンド",
                "endpoint": settings.X_INFERENCE_ENDPOINT,
                "supports_streaming": True,
                "available": True
            })
            logger.info("X-Inference バックエンドは利用可能です")
        else:
            logger.warning(f"X-Inference バックエンドは利用できません: ステータスコード {response.status_code}")
            backends.append({
            "id": "x-inference",
            "name": "X-Inference",
            "description": "X-Inference バックエンド（現在利用不可）",
            "endpoint": settings.X_INFERENCE_ENDPOINT,
            "supports_streaming": True,
            "available": False
        })
    except Exception as e:
        logger.warning(f"X-Inference バックエンドは利用できません: {e}")
        backends.append({
            "id": "x-inference",
            "name": "X-Inference",
            "description": "X-Inference バックエンド（現在利用不可）",
            "endpoint": settings.X_INFERENCE_ENDPOINT,
            "supports_streaming": True,
            "available": False
        })

    # OpenAI APIキーが設定されている場合、OpenAIバックエンドを追加
    if settings.OPENAI_API_KEY:
        try:
            headers = {"Authorization": f"Bearer {settings.OPENAI_API_KEY}"}
            openai_url = get_versioned_url(settings.OPENAI_ENDPOINT)
            response = requests.get(f"{openai_url}/models", headers=headers, timeout=3)
            if response.status_code == 200:
                available_backends.append("openai")
                backends.append({
                    "id": "openai",
                    "name": "OpenAI",
                    "description": "OpenAI API（APIキーが必要）",
                    "endpoint": settings.OPENAI_ENDPOINT,
                    "supports_streaming": True,
                    "available": True
                })
                logger.info(f"OpenAI バックエンドは利用可能です (URL: {openai_url})")
            else:
                logger.warning(f"OpenAI バックエンドは利用できません: ステータスコード {response.status_code}")
                backends.append({
                    "id": "openai",
                    "name": "OpenAI",
                    "description": "OpenAI API（APIキーが必要、現在利用不可）",
                    "endpoint": settings.OPENAI_ENDPOINT,
                    "supports_streaming": True,
                    "available": False
                })
        except Exception as e:
            logger.warning(f"OpenAI バックエンドは利用できません: {e}")
            backends.append({
                "id": "openai",
                "name": "OpenAI",
                "description": "OpenAI API（APIキーが必要、現在利用不可）",
                "endpoint": settings.OPENAI_ENDPOINT,
                "supports_streaming": True,
                "available": False
            })

    # デフォルトバックエンドを決定
    default_backend = settings.DEFAULT_LLM_BACKEND

    # デフォルトバックエンドが利用できない場合、最初の利用可能なバックエンドを選択
    if default_backend not in available_backends and available_backends:
        default_backend = available_backends[0]
        logger.warning(f"デフォルトバックエンド {settings.DEFAULT_LLM_BACKEND} は利用できません。代わりに {default_backend} を使用します")

    # 利用可能なバックエンドがない場合、Ollamaをデフォルトバックエンドとして使用
    default_backend = settings.DEFAULT_LLM_BACKEND

    # バックエンドごとのエンドポイント情報を追加
    # 前端の設定に合わせて、適切なURLフォーマットを提供
    backend_endpoints = {}

    # 各バックエンドのエンドポイントを設定
    # 注意: 前端の設定に合わせて、APIバージョンを含めるかどうかを決定
    backend_endpoints["ollama"] = settings.OLLAMA_ENDPOINT
    backend_endpoints["vllm"] = settings.VLLM_ENDPOINT
    backend_endpoints["sglang"] = settings.SGLANG_ENDPOINT
    backend_endpoints["x-inference"] = settings.X_INFERENCE_ENDPOINT
    backend_endpoints["openai"] = settings.OPENAI_ENDPOINT

    # 各バックエンドのAPIバージョン情報を追加
    backend_api_versions = {
        "ollama": settings.API_VERSION,
        "vllm": settings.API_VERSION,
        "sglang": settings.API_VERSION,
        "x-inference": settings.API_VERSION,
        "openai": settings.API_VERSION
    }

    logger.info(f"バックエンドエンドポイント情報: {backend_endpoints}")
    if not available_backends:
        logger.warning("利用可能なバックエンドがありません。Ollamaをデフォルトとして使用します")
        # 少なくとも1つのバックエンドが利用可能であることを確認
        if not any(b["id"] == "ollama" for b in backends):
            backends.append({
                "id": "ollama",
                "name": "Ollama",
                "description": "Ollama を使用したローカル推論",
                "endpoint": settings.OLLAMA_ENDPOINT,
                "supports_streaming": True,
                "available": False
            })

        # 追加のデフォルトバックエンドを追加
        logger.info("追加のデフォルトバックエンドを追加します")
        if not any(b["id"] == "vllm" for b in backends):
            backends.append({
                "id": "vllm",
                "name": "vLLM",
                "description": "vLLM を使用した高性能推論",
                "endpoint": settings.VLLM_ENDPOINT,
                "supports_streaming": True,
                "available": False
            })
        if not any(b["id"] == "sglang" for b in backends):
            backends.append({
                "id": "sglang",
                "name": "SGLang",
                "description": "SGLang を使用した構造化生成",
                "endpoint": settings.SGLANG_ENDPOINT,
                "supports_streaming": True,
                "available": False
            })
        if not any(b["id"] == "x-inference" for b in backends):
            backends.append({
                "id": "x-inference",
                "name": "X-Inference",
                "description": "X-Inference バックエンド",
                "endpoint": settings.X_INFERENCE_ENDPOINT,
                "supports_streaming": True,
                "available": False
            })
    else:
        # 最初の利用可能なバックエンドをデフォルトバックエンドとして使用
        default_backend = available_backends[0]

    # バックエンドリストを返す
    logger.info(f"{len(backends)} バックエンドを返します。デフォルト: {default_backend}")
    logger.debug(f"バックエンド: {backends}")
    logger.debug(f"利用可能なバックエンド: {available_backends}")
    logger.debug(f"バックエンドエンドポイント: {backend_endpoints}")

    return {
        "backends": backends,
        "default_backend": default_backend,
        "backend_endpoints": backend_endpoints,
        "api_version": settings.API_VERSION,
        "backend_api_versions": backend_api_versions
    }

@router.get("/v1/backend-config", response_model=Dict[str, Any], operation_id="get_backend_config")
async def get_backend_config(request: Request):
    """
    バックエンドの設定情報を取得します。

    レスポンス形式:
    - backend_endpoints: 各バックエンドのエンドポイントURL
    - default_backend: デフォルトで使用されるバックエンドID
    """
    # リクエスト情報を記録
    logger.info(f"{request.client.host} からのバックエンド設定リクエストを受信しました。ヘッダー: {request.headers.get('origin', 'Unknown')}, {request.headers.get('user-agent', 'Unknown')}")

    # バックエンドごとのエンドポイント情報
    # 前端の設定に合わせて、適切なURLフォーマットを提供
    backend_endpoints = {}

    # 各バックエンドのエンドポイントを設定
    backend_endpoints["ollama"] = settings.OLLAMA_ENDPOINT
    backend_endpoints["vllm"] = settings.VLLM_ENDPOINT
    backend_endpoints["sglang"] = settings.SGLANG_ENDPOINT
    backend_endpoints["x-inference"] = settings.X_INFERENCE_ENDPOINT
    backend_endpoints["openai"] = settings.OPENAI_ENDPOINT

    # 各バックエンドのAPIバージョン情報を追加
    backend_api_versions = {
        "ollama": settings.API_VERSION,
        "vllm": settings.API_VERSION,
        "sglang": settings.API_VERSION,
        "x-inference": settings.API_VERSION,
        "openai": settings.API_VERSION
    }

    logger.info(f"バックエンドエンドポイント情報: {backend_endpoints}")

    # デフォルトバックエンド
    default_backend = settings.DEFAULT_LLM_BACKEND

    logger.info(f"バックエンド設定情報を返します。デフォルト: {default_backend}")
    logger.debug(f"バックエンドエンドポイント: {backend_endpoints}")

    return {
        "backend_endpoints": backend_endpoints,
        "default_backend": default_backend,
        "api_version": settings.API_VERSION,
        "backend_api_versions": backend_api_versions
    }

from fastapi import APIRouter, Depends, HTTPException, status
from ..auth import get_current_admin, UserInfo
import psutil
import platform
import datetime

router = APIRouter(prefix="/admin", tags=["Admin APIs"])

@router.get("/users")
def list_users(current_user = Depends(get_current_admin)):
    """
    ユーザー一覧を取得します。管理者権限が必要です。
    """
    # 実際の実装では、データベースからユーザー一覧を取得します
    return {
        "users": [
            {"id": "1", "username": "admin", "role": "admin"},
            {"id": "2", "username": "user1", "role": "user"},
            {"id": "3", "username": "user2", "role": "user"}
        ]
    }

@router.get("/system-status")
async def get_system_status(current_user = Depends(get_current_admin)):
    """
    システムのリアルタイムステータスを取得します。管理者権限が必要です。

    CPU使用率、メモリ使用率、ディスク使用率、およびシステム情報を含むシステムステータスを返します。
    """
    return {
        "timestamp": datetime.datetime.now().isoformat(),
        "system": {
            "platform": platform.system(),
            "platform_version": platform.version(),
            "python_version": platform.python_version(),
            "hostname": platform.node()
        },
        "resources": {
            "cpu_usage": psutil.cpu_percent(),
            "memory": {
                "total": psutil.virtual_memory().total,
                "available": psutil.virtual_memory().available,
                "used": psutil.virtual_memory().used,
                "percent": psutil.virtual_memory().percent
            },
            "disk": {
                "total": psutil.disk_usage('/').total,
                "used": psutil.disk_usage('/').used,
                "free": psutil.disk_usage('/').free,
                "percent": psutil.disk_usage('/').percent
            },
            "network": {
                "connections": len(psutil.net_connections())
            }
        },
        "performance": {
            "api_latency": 0  # 実際のレイテンシー測定ロジックを実装
        }
    }
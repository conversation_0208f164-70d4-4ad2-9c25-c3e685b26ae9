"""
データベース管理モジュール
初期化、マイグレーション、その他のデータベース操作を提供します
"""
import argparse
import logging
import os
import json
import sys
from sqlalchemy import create_engine, MetaData, Table, Column, String, Integer, DateTime, ForeignKey, Text
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, relationship
from datetime import datetime
import uuid

# 相対インポートを可能にするためにパスを追加
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from ..config import settings
from .database import Base, engine, User, SessionLocal, get_database_url, get_database_engine

# ログ設定
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s")
logger = logging.getLogger(__name__)

def is_database_initialized():
    """
    データベースが既に初期化されているかどうかを確認します
    ユーザーテーブルの存在を確認します
    """
    try:
        # データベースに接続してユーザーテーブルの存在を確認
        inspector = MetaData()
        inspector.reflect(bind=engine, only=['users'])
        return 'users' in inspector.tables
    except Exception as e:
        logger.error(f"データベース確認エラー: {e}")
        return False

def init_database():
    """
    データベーステーブルを初期化し、必要に応じてデモユーザーを作成します
    """
    # 強制初期化フラグとテストモードを確認
    force_init = settings.FORCE_DB_INIT
    is_test_mode = settings.AUTH_MODE == "self_hosted_test"

    # データベースが既に初期化されているか確認
    if is_database_initialized() and not force_init:
        # テストモードの場合は、強制初期化が有効でなくてもデータベースを再作成するか確認
        if is_test_mode:
            logger.info("テストモードですが、強制初期化が無効なため、既存のデータベースを使用します")
        else:
            logger.info("データベースは既に初期化されています")
    else:
        # 強制初期化が有効な場合はログを出力
        if force_init:
            logger.info("強制初期化フラグが有効なため、データベースを再作成します")
        elif is_test_mode:
            logger.info("テストモードのため、データベースを初期化します")
        else:
            logger.info("データベーステーブルを作成しています...")

        # データベーステーブルを作成
        Base.metadata.create_all(bind=engine)
        logger.info("データベーステーブルが正常に作成されました")

    # デモユーザーを作成
    db = SessionLocal()
    try:
        # デモユーザーが既に存在するか確認
        demo_user = db.query(User).filter(User.username == "demo").first()
        if not demo_user:
            logger.info("デモユーザーを作成しています...")
            demo_user = User(username="demo", hashed_password="hashed_password", role="admin")
            db.add(demo_user)
            db.commit()
            logger.info("デモユーザーが正常に作成されました")
        else:
            logger.info("デモユーザーは既に存在します")
    except Exception as e:
        logger.error(f"デモユーザーの作成エラー: {e}")
    finally:
        db.close()

def export_data(source_url, output_file):
    """
    ソースデータベースからデータをJSONファイルにエクスポート
    """
    # ソースデータベースエンジンとセッションを作成
    source_engine = get_database_engine(source_url)
    SourceSession = sessionmaker(bind=source_engine)
    source_session = SourceSession()

    # メタデータを定義
    metadata = MetaData()
    metadata.reflect(bind=source_engine)

    # データをエクスポート
    data = {}
    for table_name in metadata.tables:
        table = metadata.tables[table_name]
        query = source_session.query(table)
        rows = []
        for row in query:
            row_dict = {}
            for column in table.columns:
                value = getattr(row, column.name)
                # JSONシリアライズできない型を処理
                if isinstance(value, datetime):
                    value = value.isoformat()
                row_dict[column.name] = value
            rows.append(row_dict)
        data[table_name] = rows

    # JSONファイルに書き込み
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(data, f, ensure_ascii=False, indent=2)

    logger.info(f"データが {output_file} にエクスポートされました")
    source_session.close()

def import_data(target_url, input_file):
    """
    JSONファイルからターゲットデータベースにデータをインポート
    """
    # ターゲットデータベースエンジンとセッションを作成
    target_engine = get_database_engine(target_url)
    TargetSession = sessionmaker(bind=target_engine)
    target_session = TargetSession()

    # メタデータを定義
    metadata = MetaData()
    metadata.reflect(bind=target_engine)

    # JSONファイルからデータを読み込み
    with open(input_file, 'r', encoding='utf-8') as f:
        data = json.load(f)

    # テーブル依存順序に従ってデータをインポート
    table_order = ["users", "chat_sessions", "chat_messages"]

    # データをインポート
    for table_name in table_order:
        if table_name in data and table_name in metadata.tables:
            table = metadata.tables[table_name]
            rows = data[table_name]

            # テーブルをクリア
            target_session.execute(table.delete())

            # データを挿入
            for row_dict in rows:
                # 日付文字列を処理
                for column in table.columns:
                    if column.name in row_dict and isinstance(row_dict[column.name], str) and 'T' in row_dict[column.name]:
                        try:
                            row_dict[column.name] = datetime.fromisoformat(row_dict[column.name])
                        except ValueError:
                            pass

                target_session.execute(table.insert().values(**row_dict))

            logger.info(f"{len(rows)} 行が {table_name} にインポートされました")

    target_session.commit()
    target_session.close()
    logger.info(f"データが {input_file} からインポートされました")

def parse_args():
    """
    コマンドライン引数を解析
    """
    parser = argparse.ArgumentParser(description="データベース管理ツール")
    parser.add_argument("action", choices=["init", "export", "import"], help="操作タイプ: init (初期化), export (エクスポート), import (インポート)")

    # ソースデータベースパラメータ
    parser.add_argument("--source-type", choices=["sqlite", "postgresql", "mysql"], default=settings.DATABASE_TYPE, help="ソースデータベースの種類")
    parser.add_argument("--source-host", default=settings.DATABASE_HOST, help="ソースデータベースのホスト")
    parser.add_argument("--source-port", type=int, default=settings.DATABASE_PORT, help="ソースデータベースのポート")
    parser.add_argument("--source-user", default=settings.DATABASE_USER, help="ソースデータベースのユーザー名")
    parser.add_argument("--source-password", default=settings.DATABASE_PASSWORD, help="ソースデータベースのパスワード")
    parser.add_argument("--source-name", default=settings.DATABASE_NAME, help="ソースデータベースの名前")
    parser.add_argument("--source-url", help="ソースデータベースのURL (指定された場合、他のソースデータベースパラメータを上書き)")

    # ターゲットデータベースパラメータ
    parser.add_argument("--target-type", choices=["sqlite", "postgresql", "mysql"], help="ターゲットデータベースの種類")
    parser.add_argument("--target-host", help="ターゲットデータベースのホスト")
    parser.add_argument("--target-port", type=int, help="ターゲットデータベースのポート")
    parser.add_argument("--target-user", help="ターゲットデータベースのユーザー名")
    parser.add_argument("--target-password", help="ターゲットデータベースのパスワード")
    parser.add_argument("--target-name", help="ターゲットデータベースの名前")
    parser.add_argument("--target-url", help="ターゲットデータベースのURL (指定された場合、他のターゲットデータベースパラメータを上書き)")

    # ファイルパラメータ
    parser.add_argument("--file", default="database_export.json", help="インポート/エクスポートファイルのパス")

    return parser.parse_args()

def main():
    """
    メイン関数
    """
    args = parse_args()

    if args.action == "init":
        # データベースを初期化
        init_database()
        print("データベースの初期化が完了しました。")

    elif args.action == "export":
        # ソースデータベースURLを決定
        if args.source_url:
            source_url = args.source_url
        else:
            source_url = get_database_url()

        logger.info(f"{source_url} から {args.file} にデータをエクスポートしています")
        export_data(source_url, args.file)

    elif args.action == "import":
        # ターゲットデータベースURLを決定
        if args.target_url:
            target_url = args.target_url
        elif args.target_type:
            target_url = get_database_url()
        else:
            logger.error("インポートにはターゲットデータベースパラメータが必要です")
            return

        logger.info(f"{args.file} から {target_url} にデータをインポートしています")
        import_data(target_url, args.file)

if __name__ == "__main__":
    main()

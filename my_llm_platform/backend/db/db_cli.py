#!/usr/bin/env python
"""
データベース管理コマンドラインインターフェース
データベースの初期化、エクスポート、インポート機能を提供します
"""
import os
import sys
import argparse
import logging

# 相対インポートを可能にするためにパスを追加
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# ロガー設定
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s")
logger = logging.getLogger(__name__)

from .db_manager import init_database, export_data, import_data

def main():
    """
    メイン関数
    """
    parser = argparse.ArgumentParser(description="データベース管理ツール")
    parser.add_argument("action", choices=["init", "export", "import"], help="操作タイプ: init (初期化), export (エクスポート), import (インポート)")
    parser.add_argument("--file", default="database_export.json", help="インポート/エクスポートファイルのパス")
    args = parser.parse_args()

    if args.action == "init":
        # データベースを初期化
        init_database()
        logger.info("データベースの初期化が完了しました。")

    elif args.action == "export":
        # データをエクスポート
        export_data(None, args.file)
        logger.info(f"データが {args.file} にエクスポートされました。")

    elif args.action == "import":
        # データをインポート
        import_data(None, args.file)
        logger.info(f"データが {args.file} からインポートされました。")

if __name__ == "__main__":
    main()

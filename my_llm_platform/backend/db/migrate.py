"""
データベース移行ツール
"""
import argparse
import logging
import os
import json
from sqlalchemy import MetaData, create_engine
from sqlalchemy.orm import sessionmaker
from datetime import datetime
from ..config import settings
from .database import get_database_engine

# ログ設定
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s")
logger = logging.getLogger(__name__)

def get_database_url(db_type, db_host, db_port, db_user, db_password, db_name):
    """
    パラメータに基づいてデータベースURLを生成
    """
    if db_type == "sqlite":
        return f"sqlite:///./{db_name}.db"
    elif db_type == "postgresql":
        return f"postgresql://{db_user}:{db_password}@{db_host}:{db_port}/{db_name}"
    elif db_type == "mysql":
        return f"mysql+pymysql://{db_user}:{db_password}@{db_host}:{db_port}/{db_name}"
    else:
        raise ValueError(f"Unsupported database type: {db_type}")

def get_engine(url):
    """
    データベースエンジンを作成
    """
    # データベースエンジンを作成するためにインポートした create_engine を使用
    if url.startswith("sqlite"):
        return create_engine(url, connect_args={"check_same_thread": False})
    else:
        return create_engine(url)

def export_data(source_url, output_file):
    """
    ソースデータベースからデータをJSONファイルにエクスポート
    """
    # ソースデータベースエンジンとセッションを作成
    source_engine = get_engine(source_url)
    SourceSession = sessionmaker(bind=source_engine)
    source_session = SourceSession()

    # メタデータを定義
    metadata = MetaData()
    metadata.reflect(bind=source_engine)

    # データをエクスポート
    data = {}
    for table_name in metadata.tables:
        table = metadata.tables[table_name]
        query = source_session.query(table)
        rows = []
        for row in query:
            row_dict = {}
            for column in table.columns:
                value = getattr(row, column.name)
                # JSONシリアライズできない型を処理
                if isinstance(value, datetime):
                    value = value.isoformat()
                row_dict[column.name] = value
            rows.append(row_dict)
        data[table_name] = rows

    # JSONファイルに書き込み
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(data, f, ensure_ascii=False, indent=2)

    logger.info(f"データが {output_file} にエクスポートされました")
    source_session.close()

def import_data(target_url, input_file):
    """
    JSONファイルからターゲットデータベースにデータをインポート
    """
    # ターゲットデータベースエンジンとセッションを作成
    target_engine = get_engine(target_url)
    TargetSession = sessionmaker(bind=target_engine)
    target_session = TargetSession()

    # メタデータを定義
    metadata = MetaData()
    metadata.reflect(bind=target_engine)

    # JSONファイルからデータを読み込み
    with open(input_file, 'r', encoding='utf-8') as f:
        data = json.load(f)

    # テーブル依存順序に従ってデータをインポート
    table_order = ["users", "chat_sessions", "chat_messages"]

    # データをインポート
    for table_name in table_order:
        if table_name in data and table_name in metadata.tables:
            table = metadata.tables[table_name]
            rows = data[table_name]

            # テーブルをクリア
            target_session.execute(table.delete())

            # データを挿入
            for row_dict in rows:
                # 日付文字列を処理
                for column in table.columns:
                    if column.name in row_dict and isinstance(row_dict[column.name], str) and 'T' in row_dict[column.name]:
                        try:
                            row_dict[column.name] = datetime.fromisoformat(row_dict[column.name])
                        except ValueError:
                            pass

                target_session.execute(table.insert().values(**row_dict))

            logger.info(f"{len(rows)} 行が {table_name} にインポートされました")

    target_session.commit()
    target_session.close()
    logger.info(f"データが {input_file} からインポートされました")

def parse_args():
    """
    コマンドライン引数を解析
    """
    parser = argparse.ArgumentParser(description="データベース移行ツール")
    parser.add_argument("action", choices=["export", "import"], help="操作タイプ: export (エクスポート) または import (インポート)")

    # ソースデータベースパラメータ
    parser.add_argument("--source-type", choices=["sqlite", "postgresql", "mysql"], default=settings.DATABASE_TYPE, help="ソースデータベースの種類")
    parser.add_argument("--source-host", default=settings.DATABASE_HOST, help="ソースデータベースのホスト")
    parser.add_argument("--source-port", type=int, default=settings.DATABASE_PORT, help="ソースデータベースのポート")
    parser.add_argument("--source-user", default=settings.DATABASE_USER, help="ソースデータベースのユーザー名")
    parser.add_argument("--source-password", default=settings.DATABASE_PASSWORD, help="ソースデータベースのパスワード")
    parser.add_argument("--source-name", default=settings.DATABASE_NAME, help="ソースデータベースの名前")
    parser.add_argument("--source-url", help="ソースデータベースのURL (指定された場合、他のソースデータベースパラメータを上書き)")

    # ターゲットデータベースパラメータ
    parser.add_argument("--target-type", choices=["sqlite", "postgresql", "mysql"], help="ターゲットデータベースの種類")
    parser.add_argument("--target-host", help="ターゲットデータベースのホスト")
    parser.add_argument("--target-port", type=int, help="ターゲットデータベースのポート")
    parser.add_argument("--target-user", help="ターゲットデータベースのユーザー名")
    parser.add_argument("--target-password", help="ターゲットデータベースのパスワード")
    parser.add_argument("--target-name", help="ターゲットデータベースの名前")
    parser.add_argument("--target-url", help="ターゲットデータベースのURL (指定された場合、他のターゲットデータベースパラメータを上書き)")

    # ファイルパラメータ
    parser.add_argument("--file", default="database_export.json", help="インポート/エクスポートファイルのパス")

    return parser.parse_args()

def main():
    """
    メイン関数
    """
    args = parse_args()

    if args.action == "export":
        # ソースデータベースURLを決定
        if args.source_url:
            source_url = args.source_url
        else:
            source_url = get_database_url(
                args.source_type, args.source_host, args.source_port,
                args.source_user, args.source_password, args.source_name
            )

        logger.info(f"{source_url} から {args.file} にデータをエクスポートしています")
        export_data(source_url, args.file)

    elif args.action == "import":
        # ターゲットデータベースURLを決定
        if args.target_url:
            target_url = args.target_url
        elif args.target_type:
            target_url = get_database_url(
                args.target_type, args.target_host, args.target_port,
                args.target_user, args.target_password, args.target_name
            )
        else:
            logger.error("インポートにはターゲットデータベースパラメータが必要です")
            return

        logger.info(f"{args.file} から {target_url} にデータをインポートしています")
        import_data(target_url, args.file)

if __name__ == "__main__":
    main()

"""
データベースモデルと接続管理モジュール
SQLAlchemyを使用してデータベースモデルとセッション管理を提供します
"""
from sqlalchemy import create_engine, Column, Integer, String, DateTime, ForeignKey, Text
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, relationship
from datetime import datetime
import uuid
import logging
from ..config import settings

# ロガー設定
logger = logging.getLogger(__name__)

def get_database_url():
    """
    設定に基づいてデータベースURLを生成
    """
    # 直接DATABASE_URLが提供されている場合、優先的に使用
    if settings.DATABASE_URL:
        return settings.DATABASE_URL

    # データベースタイプに基づいてURLを生成
    db_type = settings.DATABASE_TYPE.lower()

    if db_type == "sqlite":
        return f"sqlite:///./chat_history.db"

    elif db_type == "postgresql":
        return f"postgresql://{settings.DATABASE_USER}:{settings.DATABASE_PASSWORD}@{settings.DATABASE_HOST}:{settings.DATABASE_PORT}/{settings.DATABASE_NAME}"

    elif db_type == "mysql":
        return f"mysql+pymysql://{settings.DATABASE_USER}:{settings.DATABASE_PASSWORD}@{settings.DATABASE_HOST}:{settings.DATABASE_PORT}/{settings.DATABASE_NAME}"

    else:
        logger.warning(f"不明なデータベースタイプ: {db_type}、SQLiteにフォールバックします")
        return "sqlite:///./chat_history.db"

def get_database_engine(url):
    """
    データベースURLに基づいてエンジンを作成
    """
    if url.startswith("sqlite"):
        return create_engine(url, connect_args={"check_same_thread": False})
    else:
        return create_engine(url)

# データベースURLとエンジンを取得
SQLALCHEMY_DATABASE_URL = get_database_url()
engine = get_database_engine(SQLALCHEMY_DATABASE_URL)

# セッションファクトリを作成
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
Base = declarative_base()

# 使用するデータベースタイプを記録
logger.info(f"使用するデータベース: {SQLALCHEMY_DATABASE_URL}")

# データベースモデル定義
class User(Base):
    """ユーザーモデル"""
    __tablename__ = "users"
    id = Column(Integer, primary_key=True)
    username = Column(String, unique=True)
    hashed_password = Column(String)
    role = Column(String)
    chat_sessions = relationship("ChatSession", back_populates="user")

class ChatSession(Base):
    """チャットセッションモデル"""
    __tablename__ = "chat_sessions"
    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    user_id = Column(Integer, ForeignKey("users.id"))
    title = Column(String)
    created_at = Column(DateTime, default=lambda: datetime.now())
    user = relationship("User", back_populates="chat_sessions")
    messages = relationship("ChatMessage", back_populates="session")

class ChatMessage(Base):
    """チャットメッセージモデル"""
    __tablename__ = "chat_messages"
    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    session_id = Column(String(36), ForeignKey("chat_sessions.id"), nullable=False)
    role = Column(String(50), nullable=False)
    content = Column(Text, nullable=False)
    created_at = Column(DateTime, default=lambda: datetime.now())
    session = relationship("ChatSession", back_populates="messages")

    def __repr__(self):
        return f"<ChatMessage(id={self.id}, role={self.role}, session_id={self.session_id})>"

def get_db():
    """
    データベースセッションを取得するためのジェネレータ関数
    FastAPIのDependsで使用されます
    """
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

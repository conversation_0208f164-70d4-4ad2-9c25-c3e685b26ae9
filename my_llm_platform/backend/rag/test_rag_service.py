"""
RAG Service Test Script
"""
import sys
import os
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

# Import the RAG service
from .rag_service import RAGService

def test_rag_service():
    """Test the RAG service functionality"""
    print("=== Testing RAG Service ===")

    # Initialize the RAG service
    rag = RAGService(collection_name="test_collection")
    print(f"Initialized RAG service with collection: {rag.collection_name}")

    # Test query functionality
    query_result = rag.query("テストクエリ")
    print("\nQuery results:")
    for i, result in enumerate(query_result, 1):
        print(f"{i}. Text: {result['text'][:50]}... Score: {result['score']}")

    # Test run_rag functionality
    rag_result = rag.run_rag("テスト質問")
    print("\nRAG results:")
    print(f"Result: {rag_result['result']}")
    print(f"Think: {rag_result['think']}")
    print("Context:")
    for i, ctx in enumerate(rag_result['context'], 1):
        print(f"{i}. {ctx[:50]}...")

    # Test collection info
    info = rag.get_collection_info()
    print("\nCollection info:")
    for key, value in info.items():
        print(f"{key}: {value}")

    print("\n=== Test completed successfully ===")

if __name__ == "__main__":
    test_rag_service()

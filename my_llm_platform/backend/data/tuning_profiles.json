{"creative": {"name": "creative", "description": "High creativity settings for story generation", "model": "llama3", "backend": "ollama", "parameters": [{"name": "temperature", "value": 0.9, "description": "Higher temperature for more creativity"}, {"name": "top_p", "value": 0.95, "description": "Higher top_p for more diversity"}, {"name": "top_k", "value": 60, "description": "Higher top_k for more options"}]}, "precise": {"name": "precise", "description": "Low temperature settings for factual responses", "model": "llama3", "backend": "ollama", "parameters": [{"name": "temperature", "value": 0.3, "description": "Lower temperature for more deterministic outputs"}, {"name": "top_p", "value": 0.85, "description": "Moderate top_p for some diversity"}, {"name": "top_k", "value": 40, "description": "Moderate top_k"}]}, "balanced": {"name": "balanced", "description": "Balanced settings for general use", "model": "llama3", "backend": "ollama", "parameters": [{"name": "temperature", "value": 0.7, "description": "Moderate temperature"}, {"name": "top_p", "value": 0.9, "description": "Standard top_p"}, {"name": "top_k", "value": 50, "description": "Standard top_k"}]}}
from pydantic_settings import BaseSettings

class Settings(BaseSettings):
    PROJECT_NAME: str = "My LLM Platform"
    DEBUG: bool = True

    # LLM設定
    DEFAULT_LLM_BACKEND: str = "ollama"  # デフォルトの推論バックエンド (vllm, sglang, ollama, x-inference)
    DEFAULT_LLM_MODEL: str = "llama3.2:3b"    # デフォルトのモデル

    # テスト用API設定
    TEST_API_KEY: str = "test-key-123"  # テスト用APIキー

    # API設定
    API_VERSION: str = "v1"  # API バージョン (v1, v2, etc.)

    # バックエンドのエンドポイント設定
    # 注意: エンドポイントURLには /v1 を含めないでください
    VLLM_ENDPOINT: str = "http://localhost:8001"
    SGLANG_ENDPOINT: str = "http://localhost:8000"
    OLLAMA_ENDPOINT: str = "http://localhost:11434"
    X_INFERENCE_ENDPOINT: str = "http://localhost:9999"

    # OpenAI設定
    OPENAI_API_KEY: str = ""
    OPENAI_ORGANIZATION: str = ""
    OPENAI_DEFAULT_MODEL: str = "gpt-3.5-turbo"
    OPENAI_ENDPOINT: str = "https://api.openai.com"  # /v1 は動的に追加されます

    # モデルマッピング設定
    # 特定のモデル名に対して、どのバックエンドを使用するかを指定する
    # 例: {"gpt-4": "openai", "llama3-70b": "vllm"}
    MODEL_BACKEND_MAPPING: dict = {
        "facebook/opt-125m": "vllm",  # facebook/opt-125mモデルはvLLMバックエンドを使用
        "deepseek-r1:7b": "ollama",     # deepseek-r1:7bモデルはollamaバックエンドを使用
        "gpt-3.5-turbo": "openai",    # gpt-3.5-turboモデルはOpenAIバックエンドを使用
    }

    # vLLMで利用可能なモデルのリスト
    VLLM_MODELS: list = [
        "facebook/opt-125m",
        "deepseek-r1:7b"
    ]

    # 認証設定
    # 認証モード: "self_hosted" (自前アカウント), "self_hosted_test" (テストモード) または "enterprise" (企業SSO)
    AUTH_MODE: str = "self_hosted_test"  # デフォルトはテストモード

    # Keycloak設定
    KEYCLOAK_SERVER_URL: str = "http://localhost:8080"

    # テストモードのユーザー設定
    TEST_USER_ID: str = "1"
    TEST_USER_NAME: str = "demo"
    TEST_USER_EMAIL: str = "<EMAIL>"
    TEST_USER_ROLES: list = ["admin", "sales", "user"]

    # テストユーザー情報を返すプロパティ
    @property
    def TEST_USER(self) -> dict:
        return {
            "id": self.TEST_USER_ID,
            "username": self.TEST_USER_NAME,
            "email": self.TEST_USER_EMAIL,
            "roles": self.TEST_USER_ROLES
        }

    # Keycloak設定
    KEYCLOAK_SERVER_URL: str = "http://localhost:8080"
    KEYCLOAK_REALM: str = "myrealm"
    KEYCLOAK_CLIENT_ID: str = "myclient"
    KEYCLOAK_CLIENT_SECRET: str = "mysecret"
    KEYCLOAK_CALLBACK_URI: str = "http://localhost:8000/auth/callback"

    # 企業SSO設定
    ENABLE_ENTERPRISE_SSO: bool = False
    ENTERPRISE_SSO_TYPE: str = "saml"  # "saml" または "ldap"
    # 企業ロールとプラットフォームロールのマッピング
    # 環境変数からの解析エラーを回避するために、デフォルト値を空の辞書に設定
    ENTERPRISE_ROLE_MAPPING: dict = {}

    #ベクトルデータベース設定
    VECTOR_ENABLED_FLAG: bool = True  # ベクトルデータベース機能の有効/無効
    VECTOR_DATABASE_TYPE : str= "qdrant"  # 選択肢: qdrant, weaviate, faiss, supabase
    # データベース設定
    DATABASE_TYPE: str = "sqlite"  # 選択肢: sqlite, postgresql, mysql
    DATABASE_HOST: str = "localhost"
    DATABASE_PORT: int = 5432  # PostgreSQLのデフォルトポート
    DATABASE_USER: str = "postgres"
    DATABASE_PASSWORD: str = "password"
    DATABASE_NAME: str = "llm_platform"
    DATABASE_URL: str = ""  # 設定されている場合、上記の設定を上書きします
    FORCE_DB_INIT: bool = False  # データベースを強制的に初期化するかどうか

    # 各サービスの有効/無効設定
    ENABLE_REDIS: bool = False
    ENABLE_RABBITMQ: bool = False
    ENABLE_KEYCLOAK: bool = False
    ENABLE_PLUGINS: bool = False  # プラグインシステムの有効/無効

    # Redis設定
    REDIS_URL: str = "redis://localhost:6379"

    # RabbitMQ設定
    RABBITMQ_URL: str = "amqp://user:password@localhost:5672//"

    # Qdrant設定
    QDRANT_HOST: str = "localhost"
    QDRANT_PORT: int = 6333

    # Weaviate設定
    WEAVIATE_HOST: str = "localhost"
    WEAVIATE_PORT: int = 8087
    WEAVIATE_URL: str = "http://localhost:8087"  # 設定されている場合、上記の設定を上書きします
    WEAVIATE_API_KEY: str = ""  # Weaviate Cloud Serviceを使用する場合に必要

    # Supabase設定
    SUPABASE_CONNECTION_STRING: str = "postgresql://postgres:password@localhost:54322/postgres"  # Supabase PostgreSQL接続文字列

    # CORS設定
    FRONTEND_URL: str = "http://localhost:3000"

    # プロパティとメソッドの定義
    @property
    def is_self_hosted_test_mode(self) -> bool:
        """
        自前アカウントのテストモードが有効かどうかを返します
        """
        return self.AUTH_MODE == "self_hosted_test"

    @property
    def is_enterprise_mode(self) -> bool:
        """
        企業認証モードが有効かどうかを返します
        """
        return self.AUTH_MODE == "enterprise" and self.ENABLE_ENTERPRISE_SSO

    @property
    def is_self_hosted_mode(self) -> bool:
        """
        自前アカウントモードが有効かどうかを返します
        """
        return self.AUTH_MODE == "self_hosted" or self.AUTH_MODE == "self_hosted_test"

    # モデルごとの最大コンテキスト長
    MODEL_MAX_LENGTHS: dict = {
        "facebook/opt-125m": 512,
        "llama3": 4096,
        "llama3.2:3b": 4096,
        "deepseek-r1:7b": 8192,
        "mistral": 8192,
        "qwen": 8192,
        "default": 2048  # デフォルト値
    }

    def get_model_max_length(self, model_name: str) -> int:
        """
        モデル名に基づいて最大コンテキスト長を取得します。
        
        Args:
            model_name: モデル名
            
        Returns:
            int: モデルの最大コンテキスト長
        """
        # 完全一致を試す
        if model_name in self.MODEL_MAX_LENGTHS:
            return self.MODEL_MAX_LENGTHS[model_name]
        
        # 部分一致を試す
        for key, value in self.MODEL_MAX_LENGTHS.items():
            if key in model_name:
                return value
        
        # デフォルト値を返す
        return self.MODEL_MAX_LENGTHS["default"]

    class Config:
        env_file = "backend/.env"
        env_file_encoding = "utf-8"

# 設定をロード
settings = Settings()

# APIバージョンを含むURLを取得するヘルパー関数
def get_versioned_url(base_url: str, include_version: bool = True) -> str:
    """
    APIバージョンを含むURLを取得します。

    Args:
        base_url: ベースURL（例: http://localhost:8000）
        include_version: APIバージョンを含めるかどうか

    Returns:
        APIバージョンを含むURL（例: http://localhost:8000/v1）
    """
    if not include_version:
        return base_url

    # URLの末尾にスラッシュがある場合は削除
    if base_url.endswith('/'):
        base_url = base_url[:-1]

    # すでにバージョンが含まれている場合はそのまま返す
    if f"/{settings.API_VERSION}" in base_url:
        return base_url

    # バージョンを追加
    return f"{base_url}/{settings.API_VERSION}"

# 設定の整合性を検証
def validate_settings():
    """
    設定の整合性を検証し、警告を表示します。
    """
    import logging
    logger = logging.getLogger(__name__)

    # 企業SSOモードの整合性を検証
    if settings.AUTH_MODE == "enterprise" and not settings.ENABLE_ENTERPRISE_SSO:
        logger.warning(
            "不整合な認証設定: AUTH_MODEが'enterprise'に設定されていますが、ENABLE_ENTERPRISE_SSOがFalseです。"
            "企業SSOモードを有効にするには、両方を有効にする必要があります。"
        )

    # 自前アカウントモードで企業SSOが有効になっている場合の警告
    if settings.AUTH_MODE == "self_hosted" and settings.ENABLE_ENTERPRISE_SSO:
        logger.warning(
            "不整合な認証設定: AUTH_MODEが'self_hosted'に設定されていますが、ENABLE_ENTERPRISE_SSOがTrueです。"
            "企業SSOを有効にするには、AUTH_MODEを'enterprise'に設定する必要があります。"
        )

    # 認証モードの値を検証
    if settings.AUTH_MODE not in ["self_hosted_test", "self_hosted", "enterprise"]:
        logger.warning(
            f"無効な認証モード: {settings.AUTH_MODE}. "
            "有効な値は 'self_hosted_test', 'self_hosted' または 'enterprise' です。"
            "デフォルトでは 'self_hosted_test' モードが使用されます。"
        )

# アプリケーション起動時に設定を検証
validate_settings()

# モデルごとの最大コンテキスト長
FACEBOOK_OPT_125M_MAX_LENGTH = 512  # facebook/opt-125mの最大コンテキスト長
LLAMA3_MAX_LENGTH = 4096  # Llama 3の最大コンテキスト長
MISTRAL_MAX_LENGTH = 8192  # Mistralの最大コンテキスト長
QWEN_MAX_LENGTH = 8192  # Qwenの最大コンテキスト長

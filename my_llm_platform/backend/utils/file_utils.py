"""
ファイル操作のためのユーティリティ関数。
"""
import os
import json
import csv
import shutil
import tempfile
from pathlib import Path
from typing import Dict, List, Any, Optional, Union, TextIO, Callable, Iterator
import logging

logger = logging.getLogger(__name__)


def ensure_directory_exists(directory_path: str) -> None:
    """
    ディレクトリが存在することを確認し、必要に応じて作成します。

    Args:
        directory_path: 確認するディレクトリのパス
    """
    if not os.path.exists(directory_path):
        os.makedirs(directory_path, exist_ok=True)
        logger.info(f"Created directory: {directory_path}")


def read_text_file(file_path: str, encoding: str = 'utf-8') -> str:
    """
    ファイルからテキストを読み込みます。

    Args:
        file_path: 読み込むファイルのパス
        encoding: ファイル読み込み時に使用するエンコーディング

    Returns:
        ファイルの内容を文字列として返します
    """
    try:
        with open(file_path, 'r', encoding=encoding) as file:
            return file.read()
    except Exception as e:
        logger.error(f"Error reading file {file_path}: {e}")
        raise


def write_text_file(file_path: str, content: str, encoding: str = 'utf-8') -> None:
    """
    テキストをファイルに書き込みます。

    Args:
        file_path: 書き込むファイルのパス
        content: ファイルに書き込む内容
        encoding: ファイル書き込み時に使用するエンコーディング
    """
    try:
        # ディレクトリが存在することを確認
        directory = os.path.dirname(file_path)
        if directory:
            ensure_directory_exists(directory)

        with open(file_path, 'w', encoding=encoding) as file:
            file.write(content)
    except Exception as e:
        logger.error(f"Error writing to file {file_path}: {e}")
        raise


def read_json_file(file_path: str, encoding: str = 'utf-8') -> Dict[str, Any]:
    """
    ファイルからJSONを読み込みます。

    Args:
        file_path: 読み込むファイルのパス
        encoding: ファイル読み込み時に使用するエンコーディング

    Returns:
        ファイルの内容を辞書として返します
    """
    try:
        with open(file_path, 'r', encoding=encoding) as file:
            return json.load(file)
    except Exception as e:
        logger.error(f"Error reading JSON file {file_path}: {e}")
        raise


def write_json_file(file_path: str, data: Dict[str, Any], encoding: str = 'utf-8', indent: int = 2) -> None:
    """
    JSONをファイルに書き込みます。

    Args:
        file_path: 書き込むファイルのパス
        data: ファイルに書き込むデータ
        encoding: ファイル書き込み時に使用するエンコーディング
        indent: インデントに使用するスペースの数
    """
    try:
        # ディレクトリが存在することを確認
        directory = os.path.dirname(file_path)
        if directory:
            ensure_directory_exists(directory)

        with open(file_path, 'w', encoding=encoding) as file:
            json.dump(data, file, ensure_ascii=False, indent=indent)
    except Exception as e:
        logger.error(f"Error writing JSON to file {file_path}: {e}")
        raise


def read_csv_file(file_path: str, encoding: str = 'utf-8', delimiter: str = ',') -> List[Dict[str, str]]:
    """
    ファイルからCSVを読み込みます。

    Args:
        file_path: 読み込むファイルのパス
        encoding: ファイル読み込み時に使用するエンコーディング
        delimiter: ファイル読み込み時に使用する区切り文字

    Returns:
        ファイルの内容を辞書のリストとして返します
    """
    try:
        with open(file_path, 'r', encoding=encoding, newline='') as file:
            reader = csv.DictReader(file, delimiter=delimiter)
            return list(reader)
    except Exception as e:
        logger.error(f"Error reading CSV file {file_path}: {e}")
        raise


def write_csv_file(
    file_path: str,
    data: List[Dict[str, Any]],
    fieldnames: Optional[List[str]] = None,
    encoding: str = 'utf-8',
    delimiter: str = ','
) -> None:
    """
    CSVをファイルに書き込みます。

    Args:
        file_path: 書き込むファイルのパス
        data: ファイルに書き込むデータ
        fieldnames: 使用するフィールド名のリスト（Noneの場合、最初の行のキーを使用）
        encoding: ファイル書き込み時に使用するエンコーディング
        delimiter: ファイル書き込み時に使用する区切り文字
    """
    try:
        # ディレクトリが存在することを確認
        directory = os.path.dirname(file_path)
        if directory:
            ensure_directory_exists(directory)

        if not data:
            logger.warning(f"No data to write to CSV file {file_path}")
            return

        if fieldnames is None:
            fieldnames = list(data[0].keys())

        with open(file_path, 'w', encoding=encoding, newline='') as file:
            writer = csv.DictWriter(file, fieldnames=fieldnames, delimiter=delimiter)
            writer.writeheader()
            writer.writerows(data)
    except Exception as e:
        logger.error(f"Error writing CSV to file {file_path}: {e}")
        raise


def file_exists(file_path: str) -> bool:
    """
    ファイルが存在するかどうかを確認します。

    Args:
        file_path: 確認するファイルのパス

    Returns:
        ファイルが存在する場合はTrue、存在しない場合はFalse
    """
    return os.path.isfile(file_path)


def get_file_size(file_path: str) -> int:
    """
    ファイルのサイズをバイト単位で取得します。

    Args:
        file_path: 確認するファイルのパス

    Returns:
        ファイルのサイズ（バイト単位）
    """
    if not file_exists(file_path):
        raise FileNotFoundError(f"File not found: {file_path}")

    return os.path.getsize(file_path)


def get_file_extension(file_path: str) -> str:
    """
    ファイルの拡張子を取得します。

    Args:
        file_path: 確認するファイルのパス

    Returns:
        ファイルの拡張子（ドットなし）
    """
    return os.path.splitext(file_path)[1][1:]


def copy_file(source_path: str, destination_path: str) -> None:
    """
    ファイルをソースから宛先にコピーします。

    Args:
        source_path: ソースファイルのパス
        destination_path: 宛先ファイルのパス
    """
    try:
        # 宛先ディレクトリが存在することを確認
        directory = os.path.dirname(destination_path)
        if directory:
            ensure_directory_exists(directory)

        shutil.copy2(source_path, destination_path)
    except Exception as e:
        logger.error(f"Error copying file from {source_path} to {destination_path}: {e}")
        raise


def move_file(source_path: str, destination_path: str) -> None:
    """
    ファイルをソースから宛先に移動します。

    Args:
        source_path: ソースファイルのパス
        destination_path: 宛先ファイルのパス
    """
    try:
        # 宛先ディレクトリが存在することを確認
        directory = os.path.dirname(destination_path)
        if directory:
            ensure_directory_exists(directory)

        shutil.move(source_path, destination_path)
    except Exception as e:
        logger.error(f"Error moving file from {source_path} to {destination_path}: {e}")
        raise


def delete_file(file_path: str) -> None:
    """
    ファイルを削除します。

    Args:
        file_path: 削除するファイルのパス
    """
    try:
        if file_exists(file_path):
            os.remove(file_path)
        else:
            logger.warning(f"File not found for deletion: {file_path}")
    except Exception as e:
        logger.error(f"Error deleting file {file_path}: {e}")
        raise


def create_temp_file(suffix: str = '', prefix: str = 'tmp', content: Optional[str] = None,
                    encoding: str = 'utf-8') -> str:
    """
    一時ファイルを作成し、オプションでコンテンツを書き込みます。

    Args:
        suffix: 一時ファイルのサフィックス
        prefix: 一時ファイルのプレフィックス
        content: ファイルに書き込むオプションのコンテンツ
        encoding: コンテンツ書き込み時に使用するエンコーディング

    Returns:
        一時ファイルへのパス
    """
    try:
        fd, temp_path = tempfile.mkstemp(suffix=suffix, prefix=prefix)
        os.close(fd)

        if content is not None:
            write_text_file(temp_path, content, encoding)

        return temp_path
    except Exception as e:
        logger.error(f"Error creating temporary file: {e}")
        raise


def list_files(directory_path: str, pattern: Optional[str] = None,
              recursive: bool = False) -> List[str]:
    """
    ディレクトリ内のファイルを一覧表示し、オプションでパターンに一致するものを抽出します。

    Args:
        directory_path: ファイルを一覧表示するディレクトリのパス
        pattern: ファイルを照合するオプションのglobパターン
        recursive: ファイルを再帰的に一覧表示するかどうか

    Returns:
        ファイルパスのリスト
    """
    try:
        if not os.path.exists(directory_path):
            logger.warning(f"Directory not found: {directory_path}")
            return []

        if not os.path.isdir(directory_path):
            logger.warning(f"Not a directory: {directory_path}")
            return []

        path_obj = Path(directory_path)

        if pattern:
            if recursive:
                return [str(p) for p in path_obj.glob(f"**/{pattern}") if p.is_file()]
            else:
                return [str(p) for p in path_obj.glob(pattern) if p.is_file()]
        else:
            if recursive:
                return [str(p) for p in path_obj.glob("**/*") if p.is_file()]
            else:
                return [str(p) for p in path_obj.glob("*") if p.is_file()]
    except Exception as e:
        logger.error(f"Error listing files in {directory_path}: {e}")
        raise


def process_files(directory_path: str, processor: Callable[[str], Any],
                 pattern: Optional[str] = None, recursive: bool = False) -> List[Any]:
    """
    提供された関数を使用してディレクトリ内のファイルを処理します。

    Args:
        directory_path: 処理するファイルを含むディレクトリのパス
        processor: 各ファイルパスに適用する関数
        pattern: ファイルを照合するオプションのglobパターン
        recursive: ファイルを再帰的に処理するかどうか

    Returns:
        各ファイルの処理結果のリスト
    """
    files = list_files(directory_path, pattern, recursive)
    results = []

    for file_path in files:
        try:
            result = processor(file_path)
            results.append(result)
        except Exception as e:
            logger.error(f"Error processing file {file_path}: {e}")
            # 他のファイルの処理を続行

    return results


def read_file_chunks(file_path: str, chunk_size: int = 8192,
                    encoding: str = 'utf-8') -> Iterator[str]:
    """
    大きなファイルをメモリに読み込まないように、ファイルをチャンクで読み込みます。

    Args:
        file_path: 読み込むファイルのパス
        chunk_size: 各チャンクのサイズ（バイト単位）
        encoding: ファイル読み込み時に使用するエンコーディング

    Yields:
        ファイルコンテンツのチャンク
    """
    try:
        with open(file_path, 'r', encoding=encoding) as file:
            while True:
                chunk = file.read(chunk_size)
                if not chunk:
                    break
                yield chunk
    except Exception as e:
        logger.error(f"Error reading file chunks from {file_path}: {e}")
        raise

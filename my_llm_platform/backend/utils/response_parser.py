"""
レスポンスパーサーユーティリティ

LLMの生成結果を構造化された形式に変換するためのユーティリティクラスを提供します。
"""

import re
from typing import Dict, Any, List, Optional


class ResponseParser:
    """
    LLMの生成結果を構造化された辞書に変換するクラス。
    
    このクラスは、LLMの生成結果からthinking部分、コード部分、説明部分などを
    抽出し、構造化された辞書として返します。
    """

    @staticmethod
    def parse_response(text: str) -> Dict[str, Any]:
        """
        LLMの生成結果を解析して構造化された辞書に変換します。
        
        Args:
            text: LLMの生成結果テキスト
            
        Returns:
            構造化された辞書（thinking, code, explanation, changes）
        """
        result = {
            "thinking": "",
            "code": "",
            "explanation": "",
            "changes": []
        }
        
        # thinkingの抽出
        thinking_match = re.search(r"<think>(.*?)</think>", text, re.DOTALL)
        if thinking_match:
            result["thinking"] = thinking_match.group(1).strip()
        
        # コードの抽出
        code_match = re.search(r"```(?:python)?\s*(.*?)\s*```", text, re.DOTALL)
        if code_match:
            result["code"] = code_match.group(1).strip()
        
        # 変更点の抽出
        changes_section = re.search(r"変更点：(.*?)(?:\n\n|$)", text, re.DOTALL)
        if changes_section:
            changes_text = changes_section.group(1)
            # 番号付きリストの各項目を抽出
            changes_items = re.findall(r"\d+\.\s*(.*?)(?:\n|$)", changes_text)
            result["changes"] = changes_items
        
        # 説明部分（コードと変更点以外のテキスト）
        # thinkingとコードブロックを除去
        explanation_text = re.sub(r"<think>.*?</think>", "", text, flags=re.DOTALL)
        explanation_text = re.sub(r"```.*?```", "", explanation_text, flags=re.DOTALL)
        # 変更点セクションを除去
        explanation_text = re.sub(r"変更点：.*?(?:\n\n|$)", "", explanation_text, flags=re.DOTALL)
        # 空行を削除して整形
        explanation_lines = [line.strip() for line in explanation_text.split("\n") if line.strip()]
        result["explanation"] = "\n".join(explanation_lines)
        
        return result

    @staticmethod
    def extract_code(text: str) -> str:
        """
        テキストからコードブロックを抽出します。
        
        Args:
            text: 処理するテキスト
            
        Returns:
            抽出されたコード（見つからない場合は空文字列）
        """
        code_match = re.search(r"```(?:python)?\s*(.*?)\s*```", text, re.DOTALL)
        return code_match.group(1).strip() if code_match else ""

    @staticmethod
    def extract_thinking(text: str) -> str:
        """
        テキストからthinking部分を抽出します。
        
        Args:
            text: 処理するテキスト
            
        Returns:
            抽出されたthinking（見つからない場合は空文字列）
        """
        thinking_match = re.search(r"<think>(.*?)</think>", text, re.DOTALL)
        return thinking_match.group(1).strip() if thinking_match else ""
"""
レスポンスフィルタリングユーティリティ

モデルからのレスポンスを適切に処理するためのユーティリティ関数を提供します。
"""

import re
import logging
from typing import Dict, Any, List, Optional

logger = logging.getLogger(__name__)

# 特殊トークンのパターン（<|xxx|>形式）
SPECIAL_TOKEN_PATTERN = re.compile(r'<\|[^|]+\|>')

def clean_special_tokens(text: str) -> str:
    """
    テキストから特殊トークン（<|xxx|>形式）を削除します。

    Args:
        text: 処理するテキスト

    Returns:
        特殊トークンが削除されたテキスト
    """
    # 特殊トークンを削除
    cleaned_text = SPECIAL_TOKEN_PATTERN.sub('', text)

    # 連続する空白を1つにまとめる
    cleaned_text = re.sub(r'\s+', ' ', cleaned_text)

    # 前後の空白を削除
    cleaned_text = cleaned_text.strip()

    return cleaned_text

def extract_first_response(text: str) -> str:
    """
    複数の回答候補から最初の有効な回答を抽出します。
    特殊トークンで区切られた最初のセグメントを返します。

    Args:
        text: 処理するテキスト

    Returns:
        最初の有効な回答
    """
    # 特殊トークンで分割
    segments = SPECIAL_TOKEN_PATTERN.split(text)

    # 空でない最初のセグメントを返す
    for segment in segments:
        segment = segment.strip()
        if segment:
            return segment

    # 有効なセグメントがない場合は元のテキストを返す
    return text

def filter_chat_completion_response(response: Dict[str, Any]) -> Dict[str, Any]:
    """
    チャット完了レスポンスをフィルタリングします。
    特殊トークンを削除し、最初の有効な回答のみを保持します。

    Args:
        response: vLLM APIからのレスポンス

    Returns:
        フィルタリングされたレスポンス
    """
    if "choices" not in response:
        logger.warning("レスポンスに 'choices' フィールドがありません")
        return response

    filtered_response = response.copy()

    for i, choice in enumerate(filtered_response["choices"]):
        if "message" in choice and "content" in choice["message"]:
            original_content = choice["message"]["content"]

            # 最初の有効な回答を抽出
            first_response = extract_first_response(original_content)

            # 特殊トークンを削除
            cleaned_content = clean_special_tokens(first_response)

            logger.info(f"元のコンテンツ: {original_content[:100]}...")
            logger.info(f"フィルタリング後: {cleaned_content[:100]}...")

            # フィルタリングされたコンテンツで置き換え
            filtered_response["choices"][i]["message"]["content"] = cleaned_content

    return filtered_response

def filter_completion_response(response: Dict[str, Any]) -> Dict[str, Any]:
    """
    テキスト補完レスポンスをフィルタリングします。
    特殊トークンを削除し、最初の有効な回答のみを保持します。

    Args:
        response: vLLM APIからのレスポンス

    Returns:
        フィルタリングされたレスポンス
    """
    if "choices" not in response:
        logger.warning("レスポンスに 'choices' フィールドがありません")
        return response

    filtered_response = response.copy()

    for i, choice in enumerate(filtered_response["choices"]):
        if "text" in choice:
            original_text = choice["text"]

            # 最初の有効な回答を抽出
            first_response = extract_first_response(original_text)

            # 特殊トークンを削除
            cleaned_text = clean_special_tokens(first_response)

            logger.info(f"元のテキスト: {original_text[:100]}...")
            logger.info(f"フィルタリング後: {cleaned_text[:100]}...")

            # フィルタリングされたテキストで置き換え
            filtered_response["choices"][i]["text"] = cleaned_text

    return filtered_response

def filter_streaming_chunk(chunk: Dict[str, Any], is_chat_completion: bool = True) -> Dict[str, Any]:
    """
    ストリーミングチャンクをフィルタリングします。
    特殊トークンを削除します。

    Args:
        chunk: ストリーミングチャンク
        is_chat_completion: チャット完了APIかどうか

    Returns:
        フィルタリングされたチャンク
    """
    if "choices" not in chunk:
        return chunk

    filtered_chunk = chunk.copy()

    for i, choice in enumerate(filtered_chunk["choices"]):
        if is_chat_completion and "delta" in choice and "content" in choice["delta"]:
            # チャット完了APIの場合
            original_content = choice["delta"]["content"]

            # 特殊トークンを含むかチェック
            contains_special_token = bool(SPECIAL_TOKEN_PATTERN.search(original_content))

            # 特殊トークンを削除
            cleaned_content = clean_special_tokens(original_content)

            # 特殊トークンが含まれている場合はログに出力
            if original_content != cleaned_content:
                logger.debug(f"特殊トークンを削除: '{original_content}' -> '{cleaned_content}'")

            # 特殊トークンが含まれていて、コンテンツが空になる場合はスキップ
            if contains_special_token and not cleaned_content.strip():
                logger.debug(f"特殊トークンのみのチャンクをスキップ: '{original_content}'")
                # 空のコンテンツを設定
                filtered_chunk["choices"][i]["delta"]["content"] = ""
            else:
                # フィルタリングされたコンテンツで置き換え
                filtered_chunk["choices"][i]["delta"]["content"] = cleaned_content
        elif not is_chat_completion and "text" in choice:
            # テキスト補完APIの場合
            original_text = choice["text"]

            # 特殊トークンを含むかチェック
            contains_special_token = bool(SPECIAL_TOKEN_PATTERN.search(original_text))

            # 特殊トークンを削除
            cleaned_text = clean_special_tokens(original_text)

            # 特殊トークンが含まれている場合はログに出力
            if original_text != cleaned_text:
                logger.debug(f"特殊トークンを削除: '{original_text}' -> '{cleaned_text}'")

            # 特殊トークンが含まれていて、テキストが空になる場合はスキップ
            if contains_special_token and not cleaned_text.strip():
                logger.debug(f"特殊トークンのみのチャンクをスキップ: '{original_text}'")
                # 空のテキストを設定
                filtered_chunk["choices"][i]["text"] = ""
            else:
                # フィルタリングされたテキストで置き換え
                filtered_chunk["choices"][i]["text"] = cleaned_text

    return filtered_chunk

version: 1
formatters:
  simple:
    format: "%(asctime)s [%(levelname)s] %(name)s: %(message)s"
  detailed:
    format: "%(asctime)s [%(levelname)s] %(name)s (%(filename)s:%(lineno)d): %(message)s"
handlers:
  console:
    class: logging.StreamHandler
    formatter: detailed
    level: DEBUG
loggers:
  myllm:
    handlers: [console]
    level: DEBUG
    propagate: no
  uvicorn:
    handlers: [console]
    level: INFO
    propagate: no
root:
  level: DEBUG
  handlers: [console]

"""
メトリクス収集モジュール
Prometheusメトリクスを定義し、アプリケーションのパフォーマンスを監視します。
"""

from prometheus_client import Counter, Histogram, Gauge

# リクエスト数カウンター
request_count = Counter(
    'llm_api_requests_total', 
    'Total number of requests to the LLM API',
    ['model', 'endpoint']
)

# レスポンス時間ヒストグラム
response_time = Histogram(
    'llm_api_response_time_seconds', 
    'Response time in seconds',
    ['model', 'endpoint']
)

# 現在のリクエスト数ゲージ
active_requests = Gauge(
    'llm_api_active_requests', 
    'Number of currently active requests',
    ['model', 'endpoint']
)

# トークン使用量カウンター
token_usage = Counter(
    'llm_api_token_usage_total', 
    'Total number of tokens used',
    ['model', 'type']  # type: prompt, completion
)

# エラー数カウンター
error_count = Counter(
    'llm_api_errors_total', 
    'Total number of errors',
    ['model', 'error_type']
)

import logging
import logging.config
import yaml
import os

def setup_logger():
    config_file = os.path.join(os.path.dirname(__file__), "log_config.yaml")
    if os.path.exists(config_file):
        with open(config_file, "r") as f:
            config = yaml.safe_load(f.read())
        logging.config.dictConfig(config)
    else:
        logging.basicConfig(level=logging.INFO)

logger = logging.getLogger("myllm")

def get_request_logger(request_id):
    """特定のリクエストのロガー"""
    return logging.getLogger(f"myllm.request.{request_id}")

def log_request(request, response, processing_time):
    """APIリクエストとレスポンスを記録"""
    logger.info(f"リクエスト: {request.method} {request.url.path} - レスポンス: {response.status_code} - 処理時間: {processing_time}ms")

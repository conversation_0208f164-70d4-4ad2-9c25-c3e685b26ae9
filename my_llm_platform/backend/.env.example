# アプリケーション設定
PROJECT_NAME="My Custom LLM Platform"
DEBUG=true

#プラグインシステム有効化フラグ
ENABLE_PLUGINS=true

#ベクトルデータベース設定

VECTOR_ENABLED_FLAG=true
VECTOR_DATABASE_TYPE=supabase #"qdrant", "weaviate", "faiss", "supabase"のいずれか
# データベース設定
# オプション: sqlite, postgresql, mysql
DATABASE_TYPE=sqlite
# 以下の設定はPostgreSQLまたはMySQLを使用する場合にのみ必要です
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_USER=postgres
DATABASE_PASSWORD=password
DATABASE_NAME=llm_platform
# 設定されている場合、上記のデータベース設定を上書きします
# DATABASE_URL=postgresql://postgres:password@localhost:5432/llm_platform
# データベースを強制的に初期化するかどうか（デフォルトはfalse）
FORCE_DB_INIT=false

# LLM設定
DEFAULT_LLM_BACKEND=ollama #vllm, ollama, x-inference（未対応）,sglang（未対応）,
DEFAULT_LLM_MODEL=qwen3:latest #deepseek-r1:7b

# バックエンドエンドポイント設定
VLLM_ENDPOINT=http://localhost:8001
SGLANG_ENDPOINT=http://localhost:8002
OLLAMA_ENDPOINT=http://localhost:11434
X_INFERENCE_ENDPOINT=http://localhost:9999
OPENAI_ENDPOINT=https://api.openai.com

# OpenAI設定
OPENAI_API_KEY=
OPENAI_ORGANIZATION=
OPENAI_DEFAULT_MODEL=gpt-3.5-turbo

# モデルマッピング設定
# 特定のモデルが特定のバックエンドを使用
MODEL_BACKEND_MAPPING={"facebook/opt-125m": "vllm", "gpt-4o": "openai", "qwen3:latest": "ollama"}

# 認証設定
# 認証モード: "self_hosted" (自前アカウント), "self_hosted_test" (テストモード) または "enterprise" (企業SSO)
AUTH_MODE=self_hosted_test

# テストモードのユーザー設定
TEST_USER_ID=1
TEST_USER_NAME=demo
TEST_USER_EMAIL=<EMAIL>
TEST_USER_ROLES=["admin", "sales", "user"]

# Keycloak設定
KEYCLOAK_SERVER_URL=http://localhost:8080
KEYCLOAK_REALM=myrealm
KEYCLOAK_CLIENT_ID=myclient
KEYCLOAK_CLIENT_SECRET=mysecret
KEYCLOAK_CALLBACK_URI=http://localhost:8000/auth/callback

# 企業SSO設定
# trueに設定すると企業SSOモードが有効になります。AUTH_MODEも"enterprise"に設定する必要があります。
ENABLE_ENTERPRISE_SSO=false
# "saml" または "ldap"
ENTERPRISE_SSO_TYPE=saml
# 企業ロールとプラットフォームロールのマッピング
# 企業のロール名をプラットフォームのロールにマッピングします
ENTERPRISE_ROLE_MAPPING={"admin":["enterprise_admin","admin_group","system_admin"],"sales":["enterprise_sales","sales_team","business_dev"],"user":["enterprise_user","employee","staff"]}

# その他のサービス

# Redisの有効/無効設定
# セッション管理やキャッシュ機能で使用されます
ENABLE_REDIS=false
# RabbitMQの有効/無効設定
# メッセージキューを使用した非同期処理やタスクキューで使用されます
ENABLE_RABBITMQ=false
# 以下の設定は削除されました。代わりにVECTOR_ENABLED_FLAGを使用してください
# Keycloakの有効/無効設定
# 認証と認可、特にSSO（シングルサインオン）機能で使用されます
ENABLE_KEYCLOAK=false

# Redis設定
REDIS_URL=redis://localhost:6379

# RabbitMQ設定
RABBITMQ_URL=amqp://user:password@localhost:5672//

# Qdrantベクトルデータベース設定
QDRANT_HOST=localhost
QDRANT_PORT=6333

# Weaviateベクトルデータベース設定
WEAVIATE_HOST=localhost
WEAVIATE_PORT=8087
WEAVIATE_URL=http://localhost:8087
WEAVIATE_API_KEY=

# Supabaseベクトルデータベース設定
SUPABASE_CONNECTION_STRING=postgresql://postgres:postgres@localhost:54322/postgres

# CORS設定
FRONTEND_URL=http://localhost:3000
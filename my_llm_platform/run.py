#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Run the LLM platform with optional plugin system.
"""

import os
import sys
import uvicorn
import argparse

# Import settings
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
try:
    from backend.config import settings
except ImportError:
    settings = None

if __name__ == "__main__":
    # Parse command line arguments
    parser = argparse.ArgumentParser(description="My LLM Platform API Server")
    parser.add_argument("--port", type=int, default=8000, help="Server port number")
    parser.add_argument("--host", type=str, default="0.0.0.0", help="Server host")
    parser.add_argument("--reload", action="store_true", help="Enable auto-reload")
    parser.add_argument("--enable-plugins", action="store_true", help="Enable plugin system")
    args = parser.parse_args()

    # Set environment variables and update settings if needed
    if args.enable_plugins:
        os.environ["ENABLE_PLUGINS"] = "true"  # For backward compatibility

        # Update settings if available
        if settings is not None:
            settings.ENABLE_PLUGINS = True

        print("Plugin system enabled")

    # Run the server
    reload = args.reload
    print(f"Starting server on {args.host}:{args.port} (reload={reload})")
    uvicorn.run(
        "backend.main:app",
        host=args.host,
        port=args.port,
        reload=reload
    )

accelerate==1.4.0
aiofiles==24.1.0
aiohappyeyeballs==2.4.6
aiohttp==3.11.13
aiosignal==1.3.2
aiosqlite==0.21.0
airportsdata==20250224
amqp==5.3.1
annotated-types==0.7.0
anyio==4.8.0
astor==0.8.1
async-property==0.2.2
async-timeout==4.0.3
attrs==25.1.0
bcrypt==4.3.0
billiard==4.2.1
blake3==1.0.4
cachetools==5.5.2
celery==5.5.0
certifi==2025.1.31
cffi==1.17.1
charset-normalizer==3.4.1
click==8.1.8
click-didyoumean==0.3.1
click-plugins==1.1.1
click-repl==0.3.0
cloudpickle==3.1.1
compressed-tensors==0.9.2
cryptography==44.0.2
cupy-cuda12x==13.4.1
dataclasses-json==0.6.7
deprecation==2.1.0
depyf==0.18.0
dill==0.3.9
diskcache==5.6.3
distro==1.9.0
dnspython==2.7.0
ecdsa==0.19.1
einops==0.8.1
email_validator==2.2.0
faiss-gpu==1.7.2
fastapi==0.115.12
fastapi-cli==0.0.7
fastrlock==0.8.3
filelock==3.18.0
frozenlist==1.5.0
fsspec==2024.6.1
gguf==0.10.0
greenlet==3.1.1
grpcio==1.71.0
grpcio-tools==1.71.0
h11==0.14.0
h2==4.2.0
hpack==4.1.0
httpcore==1.0.7
httptools==0.6.4
httpx==0.28.1
httpx-sse==0.4.0
huggingface-hub==0.29.1
hyperframe==6.1.0
idna==3.10
importlib_metadata==8.6.1
interegular==0.3.3
ipywidgets==8.1.5
Jinja2==3.1.6
jiter==0.8.2
joblib==1.4.2
jsonpatch==1.33
jsonpointer==3.0.0
jsonschema==4.23.0
jsonschema-specifications==2024.10.1
jupyterlab_widgets==3.0.13
jwcrypto==1.5.6
kombu==5.5.2
langchain==0.3.22
langchain-core==0.3.49
langchain-huggingface==0.1.2
langchain-openai==0.3.7
langchain-text-splitters==0.3.7
langsmith==0.3.11
lark==1.2.2
llguidance==0.7.13
llvmlite==0.43.0
lm-format-enforcer==0.10.11
markdown-it-py==3.0.0
MarkupSafe==2.1.5
marshmallow==3.26.1
mdurl==0.1.2
mistral_common==1.5.4
mpmath==1.3.0
msgpack==1.1.0
msgspec==0.19.0
multidict==6.1.0
mypy-extensions==1.0.0
networkx==3.3
ninja==********
numba==0.60.0
numpy==1.26.4
nvidia-cublas-cu12==********
nvidia-cuda-cupti-cu12==12.6.80
nvidia-cuda-nvrtc-cu12==12.6.77
nvidia-cuda-runtime-cu12==12.6.77
nvidia-cudnn-cu12==********
nvidia-cufft-cu12==********
nvidia-curand-cu12==*********
nvidia-cusolver-cu12==********
nvidia-cusparse-cu12==********
nvidia-cusparselt-cu12==0.6.3
nvidia-nccl-cu12==2.21.5
nvidia-nvjitlink-cu12==12.6.85
nvidia-nvtx-cu12==12.6.77
openai==1.64.0
opencv-python-headless==*********
orjson==3.10.15
outlines==0.1.11
outlines_core==0.1.26
partial-json-parser==*******.post5
passlib==1.7.4
peft==0.14.0
pillow==11.0.0
portalocker==2.10.1
prometheus-fastapi-instrumentator==7.1.0
prometheus_client==0.21.1
propcache==0.3.0
protobuf==5.29.4
py-cpuinfo==9.0.0
pyasn1==0.4.8
pycountry==24.6.1
pycparser==2.22
pydantic==2.11.1
pydantic-settings==2.8.0
pydantic_core==2.33.0
python-dotenv==1.0.1
python-jose==3.4.0
python-json-logger==3.3.0
python-keycloak==5.3.1
python-multipart==0.0.20
PyYAML==6.0.2
qdrant-client==1.13.3
ray==2.44.1
redis==5.2.1
referencing==0.36.2
regex==2024.11.6
requests==2.32.3
requests-toolbelt==1.0.0
rich==14.0.0
rich-toolkit==0.14.1
rpds-py==0.24.0
rsa==4.9
torch --index-url https://download.pytorch.org/whl/cu124
torchvision --index-url https://download.pytorch.org/whl/cu124
torchaudio --index-url https://download.pytorch.org/whl/cu124
safetensors==0.5.2
scikit-learn==1.6.1
scipy==1.15.2
sentence-transformers==3.4.1
sentencepiece==0.2.0
setproctitle==1.3.5
sglang==0.4.4.post3
shellingham==1.5.4
sniffio==1.3.1
SQLAlchemy==2.0.38
starlette==0.45.3
sympy==1.13.1
tenacity==9.0.0
threadpoolctl==3.5.0
tiktoken==0.9.0
tokenizers==0.21.0
tqdm==4.67.1
transformers==4.49.0
triton==3.2.0
typer==0.15.2
typing-inspect==0.9.0
typing-inspection==0.4.0
tzdata==2025.2
urllib3==2.3.0
uv==0.6.3
uvicorn==0.34.0
uvloop==0.21.0
vine==5.1.0
vllm==0.8.2
watchfiles==1.0.4
websockets==15.0.1
widgetsnbextension==4.0.13
xformers==0.0.29.post2
xgrammar==0.1.16
yarl==1.18.3
zipp==3.21.0
zstandard==0.23.0

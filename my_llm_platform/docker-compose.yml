services:
  # サンプル: バックエンド (FastAPI)
  llm-backend:
    build: ./backend
    container_name: llm-backend
    volumes:
      - ./backend:/app
    ports:
      - 8000:8000
    environment:
      - NODE_ENV=production
      - DB_TYPE=postgresql
      - ENABLE_AUDIT_LOG=true
      - ENABLE_SSO=true
    depends_on:
      - postgres
      - redis
      - qdrant
      - weaviate
      # - keycloak  # Keycloakが必要な場合はコメントを解除
    command: uvicorn main:app --host 0.0.0.0 --port 8000 --reload

  # サンプル: Redis
  redis:
    image: redis:6
    container_name: llm-redis
    ports:
      - 6379:6379

  # Weaviate ベクトルデータベース
  weaviate:
    image: semitechnologies/weaviate:1.24.5
    container_name: llm-weaviate
    ports:
      - 8087:8080
    environment:
      - QUERY_DEFAULTS_LIMIT=20
      - AUTHENTICATION_ANONYMOUS_ACCESS_ENABLED=true
      - PERSISTENCE_DATA_PATH=/var/lib/weaviate
      - DEFAULT_VECTORIZER_MODULE=none
      - CLUSTER_HOSTNAME=node1
    volumes:
      - weaviate_data:/var/lib/weaviate

  # サンプル: RabbitMQ
  rabbitmq:
    image: rabbitmq:3-management
    container_name: llm-rabbitmq
    environment:
      RABBITMQ_DEFAULT_USER: user
      RABBITMQ_DEFAULT_PASS: password
    ports:
      - 5672:5672
      - 15672:15672

  # サンプル: Qdrant ベクトルデータベース
  qdrant:
    image: qdrant/qdrant
    container_name: llm-qdrant
    ports:
      - 6333:6333

  # サンプル: Keycloak
  # 有効化する場合は以下を設定
  # keycloak:
  #   image: quay.io/keycloak/keycloak:21.1.2
  #   container_name: llm-keycloak
  #   environment:
  #     KEYCLOAK_ADMIN: admin
  #     KEYCLOAK_ADMIN_PASSWORD: admin
  #   ports:
  #     - 8080:8080
  #   command: >
  #     start-dev --http-enabled=true

  postgres:
    image: postgres:14
    environment:
      POSTGRES_DB: llm_platform
      POSTGRES_USER: admin
      POSTGRES_PASSWORD: ${DB_PASSWORD}

  monitoring:
    image: grafana/grafana
    ports:
      - "3000:3000"

  # Text Generation Inference (X-Inference の後継)
  x-inference:
    image: ghcr.io/huggingface/text-generation-inference:latest
    container_name: llm-x-inference
    ports:
      - "9999:80"
    volumes:
      - ./models:/data
    environment:
      - MODEL_ID=meta-llama/Llama-3.2-3B-Instruct
      - NUM_SHARD=1
      - MAX_BATCH_SIZE=16
      - MAX_INPUT_LENGTH=4096
      - MAX_TOTAL_TOKENS=8192
      - QUANTIZE=bitsandbytes-nf4

    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]

volumes:
  weaviate_data:
    driver: local

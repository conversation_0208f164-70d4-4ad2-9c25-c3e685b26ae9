<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>Supabase TODO</title>
</head>
<body>
  <h1>📝 Supabase TODO</h1>

  <input type="text" id="taskInput" placeholder="任务...">
  <button onclick="addTask()">添加</button>

  <ul id="taskList"></ul>

  <script type="module">
    import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

    const supabase = createClient(
      'http://localhost:54321',
      'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0'  // 从 Studio -> Project Settings -> API 获取 anon 公钥
    )

    async function addTask() {
      const input = document.getElementById('taskInput')
      await supabase.from('todos').insert({ task: input.value })
      input.value = ''
      loadTasks()
    }

    async function loadTasks() {
      const { data } = await supabase.from('todos').select()
      const list = document.getElementById('taskList')
      list.innerHTML = ''
      data.forEach(item => {
        const li = document.createElement('li')
        li.textContent = (item.is_complete ? '✅ ' : '🕒 ') + item.task
        list.appendChild(li)
      })
    }

    loadTasks()
  </script>
</body>
</html>

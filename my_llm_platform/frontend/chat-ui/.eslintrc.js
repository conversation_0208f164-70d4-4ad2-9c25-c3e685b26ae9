module.exports = {
  root: true,
  env: {
    browser: true,
    es2021: true,
    node: true
  },
  globals: {
    performance: 'readonly',
    PerformanceObserver: 'readonly'
  },
  extends: [
    'eslint:recommended',
    'plugin:react/recommended',
    'plugin:@typescript-eslint/recommended'
  ],
  parser: '@typescript-eslint/parser',
  parserOptions: {
    ecmaFeatures: {
      jsx: true
    },
    ecmaVersion: 'latest',
    sourceType: 'module'
  },
  plugins: [
    'react',
    '@typescript-eslint'
  ],
  rules: {
    // 禁用 React Router 的 Future Flag 警告
    'no-warning-comments': 'off',
    // 禁用 zustand 的 default export 警告
    'import/no-default-export': 'off'
  },
  settings: {
    react: {
      version: 'detect'
    }
  }
};

import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import tailwindcssPostcss from '@tailwindcss/postcss'
import autoprefixer from 'autoprefixer'

// https://vitejs.dev/config/
export default defineConfig({

  plugins: [react()],
  optimizeDeps: {
    include: [
      'react-syntax-highlighter',
      'react-syntax-highlighter/dist/cjs/styles/hljs'
    ]
  },
  server: {
    port: 5173,
    strictPort: false,
    watch: {
      usePolling: true,  // ファイル変更を監視するためのポーリング方式を使用
      interval: 100,     // ポーリング間隔（ミリ秒）
    },
    proxy: {
      // バックエンドFastAPIプロキシ、必要に応じて変更可能
      '/v1': 'http://localhost:8000'
    }
  },
  css: {
    postcss: {
      plugins: [
        tailwindcssPostcss,
        autoprefixer,
      ],
    },
  }
})
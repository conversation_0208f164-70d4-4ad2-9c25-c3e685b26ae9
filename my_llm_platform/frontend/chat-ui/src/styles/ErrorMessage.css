/* エラーメッセージのスタイル */

.error-message-container {
  width: 100%;
  display: flex;
  justify-content: center;
  margin: 1rem 0;
  animation: errorFadeIn 0.3s ease-in-out;
}

@keyframes errorFadeIn {
  from { opacity: 0; transform: translateY(-10px); }
  to { opacity: 1; transform: translateY(0); }
}

.error-message {
  display: flex;
  align-items: center;
  background-color: rgba(239, 65, 70, 0.1);
  border: 1px solid var(--error-color);
  border-radius: 10px;
  padding: 0.75rem 1rem;
  max-width: 80%;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.error-icon {
  margin-right: 0.75rem;
  font-size: 1.25rem;
}

.error-text {
  color: var(--error-color);
  font-size: 0.95rem;
  line-height: 1.4;
}

.error-help-text {
  margin-top: 0.5rem;
  font-size: 0.85rem;
  opacity: 0.8;
  color: var(--error-color);
}

/* ダークモード対応 */
@media (prefers-color-scheme: dark) {
  .error-message {
    background-color: rgba(239, 65, 70, 0.15);
    border-color: #ff6b6b;
  }

  .error-text {
    color: #ff6b6b;
  }
}

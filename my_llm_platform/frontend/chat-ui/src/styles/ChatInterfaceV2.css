/* チャットコンテナのスタイル */
.chat-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 100%;
  box-sizing: border-box;
  margin: 0 auto;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  overflow-x: hidden;
}

/* チャットヘッダーのスタイル */
.chat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding: 1rem;
  border-bottom: 1px solid #e0e0e0;
  background-color: #f8f9fa;
  box-sizing: border-box;
}

/* モデル情報のスタイル */
.model-info {
  display: flex;
  align-items: center;
  font-size: 0.9rem;
  color: #666;
  font-weight: 500;
  text-align: left;
  margin-right: auto;
}

/* ヘッダー右側のスタイル */
.header-right {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

/* 設定ボタンのスタイル */
.settings-button {
  background: none;
  border: none;
  font-size: 1.2rem;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 50%;
  transition: background-color 0.2s;
}

/* 設定ボタンのホバースタイル */
.settings-button:hover {
  background-color: #f0f0f0;
}

/* 設定オーバーレイのスタイル */
.settings-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.settings-modal {
  background-color: white;
  border-radius: 10px;
  width: 90%;
  max-width: 800px;
  height: 600px;
  display: flex;
  flex-direction: column;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.3);
  animation: modalFadeIn 0.3s ease-out;
  overflow: hidden;
}

@keyframes modalFadeIn {
  from { opacity: 0; transform: translateY(-20px); }
  to { opacity: 1; transform: translateY(0); }
}

.settings-header {
  padding: 1.25rem 1.5rem;
  border-bottom: 1px solid #e0e0e0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #10a37f;
  color: white;
  height: 50px;
  box-sizing: border-box;
}

.settings-header h2 {
  margin: 0;
  color: white;
  font-size: 1.25rem;
}

.close-btn {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: white;
  opacity: 0.8;
  transition: opacity 0.2s;
}

.close-btn:hover {
  opacity: 1;
}

.settings-content {
  display: flex;
  flex: 1;
  overflow: hidden;
  height: calc(100% - 120px);
}

.settings-sidebar {
  width: 200px;
  min-width: 200px;
  border-right: 1px solid #e0e0e0;
  overflow-y: auto;
  background-color: #f5f5f5;
  height: 100%;
}

.settings-sidebar ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.settings-sidebar li {
  padding: 1rem 1.5rem;
  cursor: pointer;
  border-bottom: 1px solid #e0e0e0;
  transition: all 0.3s ease;
  font-weight: 500;
}

.settings-sidebar li:hover {
  background-color: #f0f0f0;
}

.settings-sidebar li.active {
  background-color: #10a37f;
  color: white;
  border-left: 4px solid #0a7d62;
  padding-left: calc(1.5rem - 4px);
}

.settings-panel {
  flex: 1;
  padding: 1.5rem 2rem;
  overflow-y: auto;
  height: 100%;
  min-height: 400px;
}

.settings-panel h3 {
  margin-top: 0;
  color: #10a37f;
}

.settings-footer {
  padding: 1rem 1.5rem;
  border-top: 1px solid #e0e0e0;
  display: flex;
  justify-content: space-between;
  background-color: #f9f9f9;
  height: 70px;
  box-sizing: border-box;
}

.close-settings:hover {
  background-color: #0e906f;
}

.clear-chat {
  padding: 0.75rem 1.5rem;
  background-color: #f5f5f5;
  color: #666;
  border: 1px solid #ddd;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s;
}

.clear-chat:hover {
  background-color: #e5e5e5;
  color: #333;
}

.settings-section {
  margin-bottom: 1rem;
}

.settings-section h4 {
  margin-top: 0;
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
  color: #666;
}

.settings-section select,
.settings-section button {
  width: 100%;
  padding: 0.5rem;
  border-radius: 10px;
  border: 1px solid #e0e0e0;
  font-size: 0.9rem;
}

.settings-section button {
  background-color: #f0f0f0;
  cursor: pointer;
  transition: background-color 0.2s;
}

.settings-section button:hover {
  background-color: #e0e0e0;
}

.messages-container {
  flex: 1;
  overflow-y: auto;
  padding: 1rem 0;
  display: flex;
  flex-direction: column;
  width: 100%;
}

.welcome-message {
  text-align: center;
  margin: auto;
  max-width: 600px;
}

.welcome-message h2 {
  font-size: 1.5rem;
  margin-bottom: 1rem;
  color: #333;
}

.welcome-message p {
  color: #666;
  line-height: 1.5;
}

.message {
  display: flex;
  margin-bottom: 1rem;
  animation: fadeIn 0.3s ease-in-out;
}

.message-content-container {
  display: flex;
  max-width: 90%; /* メッセージ内容コンテナの最大幅を増加、80%から90%に変更 */
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.user-message {
  justify-content: flex-end;
}

.assistant-message {
  justify-content: flex-start;
}

.message-avatar {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 0.5rem;
  font-size: 1rem;
  color: white;
}

.user-avatar {
  background-color: #2196f3;
}

.assistant-avatar {
  background-color: #757575;
}

.user-message .message-avatar {
  order: 2;
  margin-right: 0;
  margin-left: 0.5rem;
  background-color: #e1f5fe;
}

.assistant-message .message-avatar {
  background-color: #f5f5f5;
}

.message-content {
  padding: 0.75rem 1rem;
  border-radius: 10px;
  max-width: 85%; /* メッセージ内容の最大幅を増加、70%から85%に変更 */
  white-space: pre-wrap;
  line-height: 1.5;
}

.user-message .message-content {
  background-color: #e1f5fe;
  color: #01579b;
  text-align: right;
}

.assistant-message .message-content {
  background-color: #f5f5f5;
  color: #333;
}

.error-message {
  color: #d32f2f;
  text-align: center;
  margin: 1rem 0;
  padding: 0.5rem;
  border-radius: 10px;
  background-color: #ffebee;
}

.input-box-container {
  margin-top: 1rem;
  border-top: 1px solid #e0e0e0;
  padding-top: 1rem;
  width: 100%;
}

.input-wrapper {
  display: flex;
  flex-direction: column;
  max-width: 800px;
  margin: 0 auto;
  width: 100%;
}

.input-box {
  display: flex;
  gap: 0.5rem;
  width: 100%;
  border: 1px solid #e0e0e0;
  border-radius: 10px;
  padding: 0.5rem;
  background-color: white;
}

.input-box textarea {
  flex: 1;
  padding: 0.75rem 1rem;
  border: none;
  outline: none;
  resize: none;
  font-family: inherit;
  font-size: 1rem;
  min-height: 26px;
  max-height: 120px;
  line-height: 1.5;
  overflow-y: auto;
  background-color: transparent;
  color: #333;
}

.input-box button {
  width: 32px;
  height: 32px;
  background-color: #10a37f;
  color: white;
  border: none;
  border-radius: 10px;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.2s, transform 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.input-box button:hover {
  transform: scale(1.05);
  background-color: #0e906f;
}

.input-box button:disabled {
  background-color: #e0e0e0;
  cursor: not-allowed;
}

.disclaimer {
  text-align: center;
  font-size: 0.8rem;
  color: #9e9e9e;
  margin-top: 1rem;
  padding-bottom: 10px;
}

.chat-content {
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow: hidden;
  width: 100%;
}

.chat-layout {
  display: flex;
  flex: 1;
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  overflow-x: hidden;
}

.chat-left-column {
  width: 20px; /* 左側列の幅を減少、30pxから20pxに変更 */
  flex-shrink: 0;
}

.chat-center-column {
  flex: 1;
  display: flex;
  flex-direction: column;
  max-width: 1000px; /* 最大幅を増加、800pxから1000pxに変更 */
  margin: 0 auto;
  box-sizing: border-box;
  overflow-x: hidden;
}

.chat-right-column {
  width: 20px; /* 右側列の幅を減少、30pxから20pxに変更 */
  flex-shrink: 0;
}

.message-list {
  display: flex;
  flex-direction: column;
  width: 100%;
}

@media (max-width: 768px) {
  .chat-container {
    padding: 0.5rem;
  }

  .message-content {
    max-width: 80%;
  }

  .settings-menu {
    width: 250px;
  }
}

@media (prefers-color-scheme: dark) {
  .chat-container {
    background-color: #121212;
    color: #e0e0e0;
  }

  .chat-header {
    border-bottom-color: #333;
    background-color: #1e1e1e;
  }

  .model-info {
    color: #bdbdbd;
  }

  .settings-button:hover {
    background-color: #333;
  }

  .settings-modal {
    background-color: #1e1e1e;
    color: #e0e0e0;
  }

  .settings-sidebar {
    background-color: #2a2a2a;
  }

  .settings-sidebar li {
    border-bottom-color: #444;
    color: #e0e0e0;
  }

  .settings-sidebar li:hover {
    background-color: #3a3a3a;
  }

  .settings-panel {
    background-color: #1e1e1e;
    color: #e0e0e0;
  }

  .settings-panel h3 {
    color: #bdbdbd;
  }

  .settings-footer {
    background-color: #2a2a2a;
    border-top-color: #444;
  }

  .clear-chat {
    background-color: #3a3a3a;
    color: #bdbdbd;
    border-color: #555;
  }

  .clear-chat:hover {
    background-color: #444;
    color: #e0e0e0;
  }

  .settings-section h4 {
    color: #bdbdbd;
  }

  .settings-section select,
  .settings-section button {
    background-color: #333;
    border-color: #444;
    color: #e0e0e0;
  }

  .settings-section button:hover {
    background-color: #444;
  }

  .welcome-message h2 {
    color: #e0e0e0;
  }

  .welcome-message p {
    color: #bdbdbd;
  }

  .user-message .message-avatar {
    background-color: #01579b;
  }

  .assistant-message .message-avatar {
    background-color: #333;
  }

  .user-message .message-content {
    background-color: #01579b;
    color: #e1f5fe;
  }

  .assistant-message .message-content {
    background-color: #333;
    color: #e0e0e0;
  }

  .error-message {
    background-color: #4a0000;
    color: #ffcdd2;
  }

  .input-box {
    border-color: #333;
    background-color: #1e1e1e;
  }

  .input-box textarea {
    background-color: transparent;
    color: #e0e0e0;
  }

  .input-box button {
    background-color: #10a37f;
  }

  .input-box button:hover {
    background-color: #0e906f;
  }

  .input-box button:disabled {
    background-color: #444;
    color: #888;
  }

  .disclaimer {
    color: #757575;
  }
}

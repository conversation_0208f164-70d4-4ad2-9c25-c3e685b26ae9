/* モデルセレクタースタイル */
.model-selector {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 1rem;
  width: 100%;
  max-width: 600px;
}

.model-selector.in-settings {
  max-width: 100%;
}

.model-dropdown {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.model-dropdown label {
  font-weight: 500;
  color: var(--text-color);
}

.select-input {
  padding: 0.75rem;
  border: 1px solid var(--border-color);
  border-radius: 6px;
  background-color: var(--card-background);
  color: var(--text-color);
  font-size: 1rem;
  width: 100%;
  cursor: pointer;
  transition: border-color 0.2s;
}

.select-input:hover {
  border-color: var(--primary-color);
}

.select-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(16, 163, 127, 0.2);
}

/* 無効化されたオプションのスタイル */
.select-input option:disabled {
  color: var(--light-text);
  background-color: var(--card-background);
  font-style: italic;
  opacity: 0.7;
}

/* 無効化されたバックエンドのスタイル */
.unavailable-backend {
  color: var(--light-text) !important;
  font-style: italic;
  opacity: 0.7;
}

/* Firefoxのための特別なスタイル */
@-moz-document url-prefix() {
  .select-input option:disabled {
    color: #999;
    background-color: #f5f5f5;
  }
}

/* Safariのための特別なスタイル */
@media screen and (-webkit-min-device-pixel-ratio:0) {
  _::-webkit-full-page-media, _:future, :root .select-input option:disabled {
    color: #999;
    background-color: #f5f5f5;
  }
}

/* Base URL 入力ボックスのスタイル */
.base-url-input {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.base-url-input label {
  font-weight: 500;
  color: var(--text-color);
}

.text-input {
  padding: 0.75rem;
  border: 1px solid var(--border-color);
  border-radius: 6px;
  background-color: var(--card-background);
  color: var(--text-color);
  font-size: 1rem;
  width: 100%;
  transition: border-color 0.2s;
}

.text-input:hover {
  border-color: var(--primary-color);
}

.text-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(16, 163, 127, 0.2);
}

.base-url-help {
  font-size: 0.8rem;
  color: var(--light-text);
  margin-top: 0.25rem;
}

.base-url-note {
  margin-top: 0.5rem;
  padding: 0.5rem;
  border-radius: 4px;
  background-color: rgba(255, 229, 100, 0.2);
  border-left: 3px solid #ffe564;
  font-size: 0.8rem;
  color: var(--text-color);
}

/* バックエンドセレクタースタイル */
.advanced-options {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-top: 0.5rem;
}

.advanced-toggle {
  background: none;
  border: none;
  color: var(--primary-color);
  font-size: 0.9rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.advanced-toggle:hover {
  background-color: rgba(16, 163, 127, 0.1);
}

.backend-selector {
  margin-top: 1rem;
  padding: 1rem;
  border: 1px solid var(--border-color);
  border-radius: 6px;
  background-color: var(--card-background);
}

.backend-selector h4 {
  margin-top: 0;
  margin-bottom: 0.5rem;
  color: var(--text-color);
}

.backend-options {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.backend-option {
  display: flex;
  align-items: center;
  padding: 0.5rem;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.backend-option:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.backend-option.selected {
  background-color: rgba(16, 163, 127, 0.1);
}

.backend-option input[type="radio"] {
  margin-right: 0.5rem;
}

.backend-option-label {
  display: flex;
  flex-direction: column;
}

.backend-name {
  font-weight: 500;
  color: var(--text-color);
}

.backend-description {
  font-size: 0.8rem;
  color: var(--light-text);
}


/* エラーステータス */
.model-selector-error {
  width: 100%;
}

.error-message {
  color: var(--error-color);
  margin-bottom: 1rem;
  padding: 0.75rem;
  border: 1px solid var(--error-color);
  border-radius: 6px;
  background-color: rgba(239, 65, 70, 0.1);
}


/* ロード状態 */
.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem;
  color: var(--light-text);
}


/* ダークモード対応 */
@media (prefers-color-scheme: dark) {
  .backend-option:hover {
    background-color: rgba(255, 255, 255, 0.1);
  }

  .advanced-toggle:hover {
    background-color: rgba(16, 163, 127, 0.2);
  }
}

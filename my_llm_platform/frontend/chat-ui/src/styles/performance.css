/* パフォーマンス最適化関連のスタイル */

/* 遅延読み込み時のローディングインジケーター */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  width: 100%;
  padding: 2rem;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top-color: #3b82f6;
  animation: spin 1s ease-in-out infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* 画像読み込みの最適化 */
img.lazy-load {
  opacity: 0;
  transition: opacity 0.3s ease-in-out;
}

img.lazy-load.loaded {
  opacity: 1;
}

/* アニメーションパフォーマンスの最適化 */
.hardware-accelerated {
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}

/* スクロールパフォーマンスの最適化 */
.optimize-scroll {
  -webkit-overflow-scrolling: touch;
  scroll-behavior: smooth;
  overscroll-behavior: contain;
}

/* コンテンツプレースホルダー */
.content-placeholder {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: placeholder-shimmer 1.5s infinite;
  height: 1em;
  margin-bottom: 0.5em;
  border-radius: 4px;
}

@keyframes placeholder-shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* レンダリングパフォーマンスを最適化するクラス */
.render-optimized {
  contain: content;
  will-change: transform;
}

/* 再描画を減らすクラス */
.reduce-repaints {
  transform: translateZ(0);
  backface-visibility: hidden;
}

/* アニメーションパフォーマンスの最適化 */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

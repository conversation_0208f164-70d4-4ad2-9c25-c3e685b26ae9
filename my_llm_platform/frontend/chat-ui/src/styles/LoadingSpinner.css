/* ローディングスピナーのアニメーション */
.loading-spinner {
  display: inline-block;
  border: 2px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top-color: var(--primary-color);
  animation: spin 0.8s linear infinite;
  vertical-align: middle;
  margin: 0 4px;
}

/* サイズバリエーション */
.loading-spinner.small {
  width: 16px;
  height: 16px;
}

.loading-spinner.medium {
  width: 24px;
  height: 24px;
}

.loading-spinner.large {
  width: 32px;
  height: 32px;
}

/* スクリーンリーダー用テキスト */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}

/* 回転アニメーション */
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

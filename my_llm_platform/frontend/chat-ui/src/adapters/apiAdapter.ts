import axios, { AxiosInstance } from "axios";
// 以下はインポートされているが使用されていない型にアンダースコアを付ける
// eslint-disable-next-line @typescript-eslint/no-unused-vars
import type { AxiosRequestConfig as _AxiosRequestConfig, AxiosResponse as _AxiosResponse } from "axios";
// eslint-disable-next-line @typescript-eslint/no-unused-vars
import { Message, ChatOptions, ChatResponse, StreamChunk as _StreamChunk } from "../types/chat";
import { LogService } from "../services/logService";
import { API_ENDPOINTS, API_CONFIG, buildApiUrl } from '../config/api';

/**
 * API アダプターインターフェース
 * バックエンド API とのインタラクションの標準インターフェースを定義する
 */
export interface ApiAdapter {
  sendChatRequest(
    _messages: Message[],
    _options: ChatOptions
  ): Promise<ChatResponse | Response>;
  sendCompletionRequest(
    _prompt: string,
    _options: ChatOptions,
  ): Promise<ChatResponse | Response>;
  fetchModels(): Promise<any[]>;
  fetchBackends(): Promise<any>;
  fetchChatHistory(): Promise<any[]>;
  fetchChatMessages(_sessionId: string): Promise<any[]>;
  createChatSession(_title: string): Promise<any>;
  addChatMessage(
    _sessionId: string,
    _role: string,
    _content: string,
  ): Promise<any>;
  deleteChatSession(_sessionId: string): Promise<boolean>;
  updateSessionTitle(_sessionId: string, _title: string): Promise<boolean>;
  fetchPlugins(): Promise<any[]>;
  fetchPluginSystemStatus(): Promise<any>;
  enablePlugin(_pluginId: string): Promise<boolean>;
  disablePlugin(_pluginId: string): Promise<boolean>;
  configurePlugin(_pluginId: string, _config: Record<string, any>): Promise<boolean>;
  fetchBackendConfig(): Promise<any>;
}

/**
 * 基本 API アダプター
 * 汎用的な API 呼び出し機能を提供
 */
export abstract class BaseApiAdapter implements ApiAdapter {
  protected axiosInstance: AxiosInstance;
  protected apiBaseUrl: string;
  protected apiPrefix: string;
  protected logger: LogService;

  constructor(logger: LogService) {
    this.apiBaseUrl = import.meta.env.VITE_API_BASE_URL || "";
    this.apiPrefix = import.meta.env.VITE_API_PREFIX || "";
    this.logger = logger;

    // デバッグ用に環境変数をログに出力
    console.log(`初期化時のAPIベースURL: ${this.apiBaseUrl}`);
    console.log(`初期化時のAPIプレフィックス: ${this.apiPrefix}`);
    console.log(`環境変数: ${import.meta.env.VITE_API_BASE_URL}`);

    // CORSチェック用にテストリクエストを送信
    this.testConnection();

    this.axiosInstance = axios.create({
      baseURL: this.apiBaseUrl,
      timeout: 30000, // 30秒のタイムアウト
      headers: {
        "Content-Type": "application/json",
      },
    });

    // リクエストインターセプター
    this.axiosInstance.interceptors.request.use(
      (config) => {
        // 認証トークンを追加
        const token = localStorage.getItem("token");
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        } else {
          config.headers.Authorization = "Bearer demo-token";
        }

        // ユーザー設定のベースURLを確認
        const userBaseUrl = localStorage.getItem("apiBaseUrl");
        if (userBaseUrl && userBaseUrl !== this.apiBaseUrl) {
          // ベースURLが変更された場合、新しいURLを使用
          this.logger.info(`ユーザー設定のベースURLを使用: ${userBaseUrl} (元: ${this.apiBaseUrl})`);

          // 相対URLの場合は、新しいベースURLを使用
          if (config.url && !config.url.startsWith("http")) {
            // 現在のベースURLを新しいベースURLに置き換え
            const fullUrl = `${userBaseUrl}${config.url.replace(this.apiBaseUrl, "")}`;
            config.url = fullUrl;
            this.logger.debug(`URL更新: ${fullUrl}`);
          }
        }

        this.logger.debug("API Request", {
          url: config.url,
          method: config.method,
          data: config.data,
          baseUrl: userBaseUrl || this.apiBaseUrl
        });

        return config;
      },
      (error) => {
        this.logger.error("API Request Error", error);
        return Promise.reject(error);
      },
    );

    // レスポンスインターセプター
    this.axiosInstance.interceptors.response.use(
      (response) => {
        this.logger.debug("API Response", {
          url: response.config.url,
          status: response.status,
          data: response.data,
        });
        return response;
      },
      (error) => {
        this.logger.error("API Response Error", {
          url: error.config?.url,
          status: error.response?.status,
          data: error.response?.data,
          message: error.message,
        });

        // 401 Unauthorized エラーの場合、ログイン画面にリダイレクト
        if (error.response && error.response.status === 401) {
          this.logger.warn("認証エラー: ユーザーが認証されていません。ログイン画面にリダイレクトします。");

          // ローカルストレージからトークンを削除
          localStorage.removeItem("token");
          localStorage.removeItem("user");

          // 現在のURLを保存（ログイン後にリダイレクトするため）
          const currentPath = window.location.pathname;
          if (currentPath !== "/login") {
            localStorage.setItem("redirectAfterLogin", currentPath);
          }

          // ログイン画面にリダイレクト
          window.location.href = "/login";
        }

        return Promise.reject(error);
      },
    );
  }

  /**
   * 認証ヘッダーを取得
   */
  protected getAuthHeaders(): Record<string, string> {
    const token = localStorage.getItem("token");
    return {
      Authorization: `Bearer ${token || "demo-token"}`,
      "Content-Type": "application/json",
    };
  }

  /**
   * 接続テスト
   */
  async testConnection(): Promise<void> {
    try {
      // 直接URLを指定してテスト
      const directUrl = 'http://localhost:8000/v1/models';
      console.log(`直接URLで接続テスト: ${directUrl}`);

      try {
        const directResponse = await fetch(directUrl, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
          }
        });

        if (!directResponse.ok) {
          console.error(`直接URL接続テストエラー: ${directResponse.status} ${directResponse.statusText}`);
        } else {
          const directData = await directResponse.json();
          console.log(`直接URL接続テスト成功: `, directData);
        }
      } catch (directError) {
        console.error(`直接URL接続テスト例外: `, directError);
      }

      // 環境変数からのURLでテスト
      const url = `${this.apiBaseUrl}${this.apiPrefix}/v1/models`;
      console.log(`環境変数URLで接続テスト: ${url}`);

      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });

      if (!response.ok) {
        console.error(`環境変数URL接続テストエラー: ${response.status} ${response.statusText}`);
      } else {
        const data = await response.json();
        console.log(`環境変数URL接続テスト成功: `, data);
      }
    } catch (error) {
      console.error(`接続テスト例外: `, error);
    }
  }

  /**
   * チャットリクエストを送信
   * @param messages メッセージ配列
   * @param options オプション
   */
  abstract sendChatRequest(
    _messages: Message[],
    _options: ChatOptions,
  ): Promise<ChatResponse | Response>;

  /**
   * テキスト生成リクエストを送信
   * @param prompt プロンプトテキスト
   * @param options オプション
   */
  abstract sendCompletionRequest(
    _prompt: string,
    _options: ChatOptions,
  ): Promise<ChatResponse | Response>;

  /**
   * 利用可能なモデルリストを取得
   */
  abstract fetchModels(): Promise<any[]>;

  /**
   * 利用可能なバックエンドリストを取得
   */
  abstract fetchBackends(): Promise<any>;

  /**
   * チャット履歴を取得
   */
  abstract fetchChatHistory(): Promise<any[]>;

  /**
   * チャットセッションのメッセージを取得
   * @param sessionId セッションID
   */
  abstract fetchChatMessages(_sessionId: string): Promise<any[]>;

  /**
   * 新しいチャットセッションを作成
   * @param title セッションタイトル
   */
  abstract createChatSession(_title: string): Promise<any>;

  /**
   * チャットセッションにメッセージを追加
   * @param sessionId セッションID
   * @param role メッセージロール
   * @param content メッセージ内容
   */
  abstract addChatMessage(
    _sessionId: string,
    _role: string,
    _content: string,
  ): Promise<any>;

  /**
   * チャットセッションを削除
   * @param sessionId セッションID
   */
  abstract deleteChatSession(_sessionId: string): Promise<boolean>;

  /**
   * セッションタイトルを更新
   * @param sessionId セッションID
   * @param title 新しいタイトル
   */
  abstract updateSessionTitle(
    _sessionId: string,
    _title: string,
  ): Promise<boolean>;

  /**
   * バックエンド設定情報を取得
   */
  abstract fetchBackendConfig(): Promise<any>;

  /**
   * プラグインリストを取得
   */
  abstract fetchPlugins(): Promise<any[]>;

  /**
   * プラグインシステムの状態を取得
   */
  abstract fetchPluginSystemStatus(): Promise<any>;

  /**
   * プラグインを有効化
   */
  abstract enablePlugin(_pluginId: string): Promise<boolean>;

  /**
   * プラグインを無効化
   */
  abstract disablePlugin(_pluginId: string): Promise<boolean>;

  /**
   * プラグインを設定
   */
  abstract configurePlugin(_pluginId: string, _config: Record<string, any>): Promise<boolean>;
}

/**
 * 標準 API アダプター
 * 標準 API との対話を実装
 */
export class StandardApiAdapter extends BaseApiAdapter {
  constructor(logger: LogService) {
    super(logger);
    this.apiBaseUrl = API_CONFIG.BASE_URL;
    this.apiPrefix = API_CONFIG.PREFIX;
  }

  /**
   * メッセージ配列を検証
   * @param messages メッセージ配列
   * @throws メッセージ配列が無効な場合にエラーをスロー
   */
  private validateMessages(messages: any): void {
    // メッセージ配列が存在するか確認
    if (!messages) {
      this.logger.error("メッセージ配列が存在しません");
      throw new Error("メッセージ配列が存在しません。少なくとも1つのメッセージが必要です。");
    }

    // メッセージ配列が空でないか確認
    if (messages.length === 0) {
      this.logger.error("メッセージ配列が空です");
      throw new Error("メッセージ配列が空です。少なくとも1つのメッセージが必要です。");
    }

    // メッセージ配列にユーザーメッセージがあるか確認
    const hasUserMessage = messages.some((msg: any) => msg.role === "user");
    if (!hasUserMessage) {
      this.logger.warn("メッセージ配列にユーザーメッセージがありません");
      // 警告のみを出力し、エラーはスローしない
    }

    // 各メッセージの形式を確認
    for (let i = 0; i < messages.length; i++) {
      const msg = messages[i];
      if (!msg.role || !msg.content) {
        this.logger.error(`メッセージ[${i}]の形式が無効です`, { message: msg });
        throw new Error(`メッセージ[${i}]の形式が無効です。roleとcontentプロパティが必要です。`);
      }

      // roleが有効な値か確認
      if (!["system", "user", "assistant", "function"].includes(msg.role)) {
        this.logger.warn(`メッセージ[${i}]のrole値が無効です: ${msg.role}`);
        // 警告のみを出力し、エラーはスローしない
      }
    }
  }

  /**
   * チャットリクエストを送信
   * @param messages メッセージ配列
   * @param options オプション
   */
  async sendChatRequest(
    messages: Message[],
    options: ChatOptions = {},
  ): Promise<ChatResponse | Response> {
    const {
      model = "llama3",
      backend = null,
      temperature = 0.7,
      top_p = 0.9,
      top_k = 50,
      max_tokens = 500,
      stream = false,
      backend_options = null,
    } = options;

    try {
      const payload: Record<string, any> = {
        model,
        messages,
        temperature,
        top_p,
        top_k,
        max_tokens,
        stream,
      };

      // 明確にバックエンドが指定されている場合のみ追加
      if (backend) {
        payload.backend = backend;
      }

      // バックエンドオプションが提供されている場合のみ追加
      if (backend_options) {
        payload.backend_options = backend_options;
      }

      this.logger.info("Sending chat request", {
        model,
        stream,
        messagesCount: messages.length,
      });

      // チャット画面の送信ボタン：設定画面のURLを使用
      // ユーザーが設定したURLがあるか確認
      const userBaseUrl = localStorage.getItem("apiBaseUrl");
      // 使用するベースURL
      const baseUrl = userBaseUrl || this.apiBaseUrl;

      this.logger.info(`チャットリクエスト送信時のベースURL: ${baseUrl}`);

      if (stream) {
        // ストリーミングレスポンス処理
        // 設定画面のURLを使用し、v1は自動的に追加
        const url = `${baseUrl}/v1/chat/completions`;

        // ペイロードの詳細をログに出力
        this.logger.info("ストリーミングチャットリクエストペイロード", {
          model: payload.model,
          messagesCount: payload.messages ? payload.messages.length : 0,
          stream: payload.stream,
          backend: payload.backend,
          url: url
        });

        // メッセージ配列の検証
        this.validateMessages(payload.messages);

        // メッセージの内容をログに出力（デバッグ用）
        if (payload.messages && payload.messages.length > 0) {
          this.logger.debug("送信するメッセージのロール", {
            roles: payload.messages.map((msg: any) => msg.role).join(", ")
          });

          // ユーザーメッセージがあるか確認
          const hasUserMessage = payload.messages.some((msg: any) => msg.role === "user");
          if (!hasUserMessage) {
            this.logger.warn("メッセージ配列にユーザーメッセージがありません");
          }
        }

        try {
          this.logger.info(`${url} にリクエストを送信します`);

          // リクエストボディを文字列化
          const requestBody = JSON.stringify(payload);
          this.logger.debug("リクエストボディのサイズ", {
            size: requestBody.length,
            preview: requestBody.substring(0, 100) + (requestBody.length > 100 ? "..." : "")
          });

          const response = await fetch(url, {
            method: "POST",
            headers: this.getAuthHeaders(),
            body: requestBody,
          });

          if (!response.ok) {
            const errorText = await response.text();
            this.logger.error(`${url} からのAPIエラー`, {
              status: response.status,
              statusText: response.statusText,
              error: errorText,
            });

            // エラーレスポンスのJSON解析を試みる
            try {
              const errorJson = JSON.parse(errorText);
              if (errorJson.error) {
                throw new Error(`APIエラー: ${response.status}, ${errorJson.error.message || errorJson.error}`);
              }
            } catch (parseErr) {
              // JSON解析に失敗した場合は、テキストをそのまま使用
              this.logger.warn("エラーレスポンスのJSON解析に失敗しました", { parseErr });
            }

            throw new Error(`APIエラー: ${response.status}, ${errorText}`);
          }

          // 成功したレスポンスを取得
          this.logger.info("ストリームレスポンスを正常に受信しました", {
            status: response.status,
            contentType: response.headers.get("content-type"),
          });

          // レスポンスのヘッダーをログに出力
          const headers = {};
          response.headers.forEach((value, key) => {
            headers[key] = value;
          });
          this.logger.debug("レスポンスヘッダー", headers);

          return response;
        } catch (err) {
          this.logger.error(`${url} からのフェッチエラー`, err);
          if (err instanceof TypeError && err.message.includes("Failed to fetch")) {
            throw new Error(`サーバーに接続できません。バックエンドサーバーが起動しているか確認してください。`);
          }
          throw new Error(`APIリクエスト失敗: ${err instanceof Error ? err.message : String(err)}`);
        }
      } else {
        // 非ストリーミングレスポンス
        // 設定画面のURLを使用し、v1は自動的に追加
        const url = `${baseUrl}/v1/chat/completions`;

        // ペイロードの詳細をログに出力
        this.logger.info("非ストリーミングチャットリクエストペイロード", {
          model: payload.model,
          messagesCount: payload.messages ? payload.messages.length : 0,
          stream: payload.stream,
          backend: payload.backend,
          url: url
        });

        // メッセージ配列の検証
        this.validateMessages(payload.messages);

        try {
          this.logger.info(`${url} にリクエストを送信します`);

          // リクエストボディを文字列化
          const requestBody = JSON.stringify(payload);
          this.logger.debug("リクエストボディのサイズ", {
            size: requestBody.length,
            preview: requestBody.substring(0, 100) + (requestBody.length > 100 ? "..." : "")
          });

          const response = await this.axiosInstance.post<ChatResponse>(
            url,
            payload,
          );

          this.logger.info("レスポンスを受信しました", {
            status: response.status,
            dataType: typeof response.data,
            hasChoices: response.data && response.data.choices ? response.data.choices.length : 0,
          });

          return response.data;
        } catch (err) {
          this.logger.error(`${url} からのフェッチエラー`, err);

          // Axiosエラーの詳細をログに出力
          if (err.response) {
            // サーバーからのレスポンスがある場合
            this.logger.error("サーバーからのエラーレスポンス", {
              status: err.response.status,
              statusText: err.response.statusText,
              data: err.response.data
            });

            // エラーレスポンスの形式を確認
            if (err.response.data && err.response.data.error) {
              const errorMsg = err.response.data.error.message || err.response.data.error;
              throw new Error(`APIエラー: ${err.response.status}, ${errorMsg}`);
            }

            throw new Error(`APIエラー: ${err.response.status}, ${err.response.statusText}`);
          } else if (err.request) {
            // リクエストは送信されたが、レスポンスがない場合
            this.logger.error("レスポンスがありません", { request: err.request });
            throw new Error(`サーバーからのレスポンスがありません。バックエンドサーバーが起動しているか確認してください。`);
          } else {
            // その他のエラー
            this.logger.error("リクエスト設定中のエラー", { message: err.message });
            throw new Error(`APIリクエスト失敗: ${err instanceof Error ? err.message : String(err)}`);
          }
        }
      }
    } catch (error) {
      this.logger.error("Error sending chat request", error);
      throw error;
    }
  }

  /**
   * テキスト生成リクエストを送信
   * @param prompt プロンプトテキスト
   * @param options オプション
   */
  async sendCompletionRequest(
    prompt: string,
    options: ChatOptions = {},
  ): Promise<ChatResponse | Response> {
    const {
      model = "llama3",
      backend = null,
      temperature = 0.7,
      top_p = 0.9,
      top_k = 50,
      max_tokens = 500,
      stream = false,
      backend_options = null,
    } = options;

    try {
      const payload: Record<string, any> = {
        model,
        prompt,
        temperature,
        top_p,
        top_k,
        max_tokens,
        stream,
      };

      // 明確にバックエンドが指定されている場合のみ追加
      if (backend) {
        payload.backend = backend;
      }

      // バックエンドオプションが提供されている場合のみ追加
      if (backend_options) {
        payload.backend_options = backend_options;
      }

      this.logger.info("Sending completion request", {
        model,
        stream,
        promptLength: prompt.length,
      });

      // チャット画面の送信ボタン：設定画面のURLを使用
      // ユーザーが設定したURLがあるか確認
      const userBaseUrl = localStorage.getItem("apiBaseUrl");
      // 使用するベースURL
      const baseUrl = userBaseUrl || this.apiBaseUrl;

      this.logger.info(`テキスト生成リクエスト送信時のベースURL: ${baseUrl}`);

      if (stream) {
        // ストリーミングレスポンス処理
        // 設定画面のURLを使用し、v1は自動的に追加
        const url = `${baseUrl}/v1/completions`;
        const response = await fetch(url, {
          method: "POST",
          headers: this.getAuthHeaders(),
          body: JSON.stringify(payload),
        });

        if (!response.ok) {
          const errorText = await response.text();
          this.logger.error("Completion API error", {
            status: response.status,
            error: errorText,
          });
          throw new Error(`API error: ${response.status}, ${errorText}`);
        }

        return response; // レスポンスオブジェクトを返し、呼び出し元がストリームを処理
      } else {
        // 非ストリーミングレスポンス
        // 設定画面のURLを使用し、v1は自動的に追加
        const url = `${baseUrl}/v1/completions`;
        const response = await this.axiosInstance.post<ChatResponse>(
          url,
          payload,
        );
        return response.data;
      }
    } catch (error) {
      this.logger.error("Error sending completion request", error);
      throw error;
    }
  }

  /**
   * 利用可能なモデルリストを取得
   */
  async fetchModels(): Promise<any[]> {
    try {
      // 統一されたAPIエンドポイントを使用
      const url = buildApiUrl(API_ENDPOINTS.MODELS);

      // デバッグ用にリクエスト情報をログに出力
      console.log(`モデル取得リクエストURL: ${url}`);
      console.log(`APIベースURL: ${this.apiBaseUrl}`);
      console.log(`APIプレフィックス: ${this.apiPrefix}`);
      console.log(`環境変数: ${import.meta.env.VITE_API_BASE_URL}`);
      console.log(`デフォルトモデル: ${import.meta.env.VITE_DEFAULT_MODEL}`);
      console.log(`デフォルトバックエンド: ${import.meta.env.VITE_DEFAULT_BACKEND}`);

      // 直接fetchを使用してリクエストを送信
      console.log(`fetchを使用してリクエストを送信します: ${url}`);
      const fetchResponse = await fetch(url, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });

      if (!fetchResponse.ok) {
        console.error(`fetchリクエストエラー: ${fetchResponse.status} ${fetchResponse.statusText}`);
        throw new Error(`fetchリクエストエラー: ${fetchResponse.status} ${fetchResponse.statusText}`);
      }

      const fetchData = await fetchResponse.json();
      console.log(`fetchレスポンス受信: `, fetchData);

      if (fetchData && fetchData.data) {
        console.log(`fetchレスポンスにデータがあります: `, fetchData.data);
        return fetchData.data;
      }

      try {
        this.logger.info(`Fetching models from ${url}`);
        console.log(`モデル取得リクエスト送信中: ${url}`);

        // リクエストヘッダーをログに出力
        console.log(`リクエストヘッダー: `, this.axiosInstance.defaults.headers);

        // 直接fetchを使用してリクエストを送信
        console.log(`fetchを使用してリクエストを送信します: ${url}`);
        const fetchResponse = await fetch(url, {
          method: 'GET',
          headers: this.getAuthHeaders()
        });

        if (!fetchResponse.ok) {
          console.error(`fetchリクエストエラー: ${fetchResponse.status} ${fetchResponse.statusText}`);
          throw new Error(`fetchリクエストエラー: ${fetchResponse.status} ${fetchResponse.statusText}`);
        }

        const fetchData = await fetchResponse.json();
        console.log(`fetchレスポンス受信: `, fetchData);

        // axiosも使用してみる
        const response = await this.axiosInstance.get(url);
        console.log(`axiosレスポンス受信: `, response.data);

        console.log(`レスポンスデータの形式: `, response.data);
        console.log(`レスポンスデータのJSON文字列: `, JSON.stringify(response.data));

        if (response.data && response.data.data) {
          console.log(`response.data.dataが存在します: `, response.data.data);
          return response.data.data;
        } else if (response.data && Array.isArray(response.data)) {
          // 配列が直接返される場合
          console.log(`response.dataは配列です: `, response.data);
          return response.data;
        } else if (response.data && typeof response.data === "object") {
          console.log(`response.dataはオブジェクトです: `, response.data);
          // オブジェクトが返される場合、モデルリストを抽出
          this.logger.info(
            "Received object response, trying to extract models",
          );
          if (response.data.models) {
            return response.data.models;
          }
          // models 属性がない場合、オブジェクトを配列に変換を試みる
          const possibleModels = Object.values(response.data).filter(
            (item) =>
              item &&
              typeof item === "object" &&
              ("id" in item || "name" in item),
          );
          if (possibleModels.length > 0) {
            this.logger.info("Extracted possible models from object", {
              models: possibleModels,
            });
            // 抽出したモデルを Model 型に変換
            return possibleModels.map((item) => {
              const modelItem = item as Record<string, any>;
              return {
                id:
                  "id" in modelItem
                    ? String(modelItem.id)
                    : String(modelItem.name),
                name:
                  "name" in modelItem
                    ? String(modelItem.name)
                    : String(modelItem.id),
                default:
                  "default" in modelItem ? Boolean(modelItem.default) : false,
                description:
                  "description" in modelItem
                    ? String(modelItem.description)
                    : "",
                backend:
                  "backend" in modelItem
                    ? String(modelItem.backend)
                    : undefined,
              };
            });
          }
        }
      } catch (err) {
        this.logger.warn(`Failed to fetch models from ${url}`, err);
        console.error(`モデル取得エラー: `, err);
        if (err instanceof Error) {
          console.error(`エラーメッセージ: ${err.message}`);
          console.error(`エラースタック: ${err.stack}`);
        }
      }

      this.logger.warn("All model fetch attempts failed, trying to use environment variables");
      // 環境変数からデフォルトモデルを取得
      const defaultModel = import.meta.env.VITE_DEFAULT_MODEL || "";
      const defaultBackend = import.meta.env.VITE_DEFAULT_BACKEND || "";

      if (defaultModel && defaultBackend) {
        this.logger.info(`Using default model from environment: ${defaultModel} (${defaultBackend})`);
        return [
          {
            id: defaultModel,
            name: defaultModel,
            default: true,
            description: `Default model from environment variables`,
            backend: defaultBackend,
          }
        ];
      }

      this.logger.warn("No models found in environment variables, returning empty list");
      return [];
    } catch (error) {
      this.logger.error("Error fetching models", error);
      // エラーが発生した場合、環境変数からデフォルトモデルを取得
      const defaultModel = import.meta.env.VITE_DEFAULT_MODEL || "";
      const defaultBackend = import.meta.env.VITE_DEFAULT_BACKEND || "";

      if (defaultModel && defaultBackend) {
        this.logger.info(`Using default model from environment: ${defaultModel} (${defaultBackend})`);
        return [
          {
            id: defaultModel,
            name: defaultModel,
            default: true,
            description: `Default model from environment variables`,
            backend: defaultBackend,
          }
        ];
      }

      this.logger.warn("No models found in environment variables, returning empty list");
      return [];
    }
  }

  /**
   * 利用可能なバックエンドリストを取得
   */
  async fetchBackends(): Promise<any> {
    try {
      // 統一されたAPIエンドポイントを使用
      const url = buildApiUrl(API_ENDPOINTS.BACKENDS);

      try {
        this.logger.info(`バックエンドを取得しています: ${url}`);
        console.log(`バックエンド取得URL: ${url}`);

        const response = await this.axiosInstance.get(url);

        // レスポンスの詳細をログに出力
        console.log(`バックエンドレスポンス:`, response.data);
        this.logger.info(`バックエンドレスポンス受信: ${JSON.stringify(response.data, null, 2)}`);

        // 各バックエンドの利用可能性を確認
        if (response.data && response.data.backends) {
          console.log(`バックエンド数: ${response.data.backends.length}`);
          response.data.backends.forEach((backend: any) => {
            console.log(`バックエンド ${backend.id}: available=${backend.available}, endpoint=${backend.endpoint}`);
          });
          return response.data;
        } else if (response.data && typeof response.data === "object") {
          // オブジェクトが返される場合、バックエンドリストを抽出
          this.logger.info(
            "オブジェクトレスポンスを受信、バックエンドの抽出を試みます",
          );
          console.log(`オブジェクトレスポンス:`, response.data);

          // バックエンド属性リストがある場合、抽出を試みる
          const possibleBackends = Object.values(response.data).filter(
            (item) =>
              item &&
              typeof item === "object" &&
              ("id" in item || "name" in item),
          );

          if (possibleBackends.length > 0) {
            this.logger.info("オブジェクトから可能なバックエンドを抽出しました", {
              backends: possibleBackends,
            });
            console.log(`抽出されたバックエンド:`, possibleBackends);

            // バックエンドレスポンスを構築
            const backends = possibleBackends.map((item) => {
              const backendItem = item as Record<string, any>;
              const backend = {
                id:
                  "id" in backendItem
                    ? String(backendItem.id)
                    : String(backendItem.name),
                name:
                  "name" in backendItem
                    ? String(backendItem.name)
                    : String(backendItem.id),
                description:
                  "description" in backendItem
                    ? String(backendItem.description)
                    : "",
                supports_streaming:
                  "supports_streaming" in backendItem
                    ? Boolean(backendItem.supports_streaming)
                    : true,
                available:
                  "available" in backendItem
                    ? Boolean(backendItem.available)
                    : true,
              };
              console.log(`構築されたバックエンド:`, backend);
              return backend;
            });

            const result = {
              backends,
              default_backend: backends[0].id,
            };
            console.log(`構築されたレスポンス:`, result);
            return result;
          }
        }
      } catch (err) {
        this.logger.warn(`Failed to fetch backends from ${url}`, err);
      }

      this.logger.warn(
        "All backend fetch attempts failed, trying to use environment variables",
      );
      // 環境変数からデフォルトバックエンドを取得
      const defaultBackend = import.meta.env.VITE_DEFAULT_BACKEND || "";

      if (defaultBackend) {
        this.logger.info(`Using default backend from environment: ${defaultBackend}`);
        return {
          backends: [
            {
              id: defaultBackend,
              name: defaultBackend,
              description: `Default backend from environment variables`,
              supports_streaming: true,
            }
          ],
          default_backend: defaultBackend,
        };
      }

      this.logger.warn("No backends found in environment variables, returning empty list");
      return {
        backends: [],
        default_backend: "",
      };
    } catch (error) {
      this.logger.error("Error fetching backends", error);
      // エラーが発生した場合、環境変数からデフォルトバックエンドを取得
      const defaultBackend = import.meta.env.VITE_DEFAULT_BACKEND || "";

      if (defaultBackend) {
        this.logger.info(`Using default backend from environment: ${defaultBackend}`);
        return {
          backends: [
            {
              id: defaultBackend,
              name: defaultBackend,
              description: `Default backend from environment variables`,
              supports_streaming: true,
            }
          ],
          default_backend: defaultBackend,
        };
      }

      this.logger.warn("No backends found in environment variables, returning empty list");
      return {
        backends: [],
        default_backend: "",
      };
    }
  }

  /**
   * チャット履歴を取得
   */
  async fetchChatHistory(): Promise<any[]> {
    try {
      this.logger.info("Fetching chat history");

      // fetchを使用してリクエストを送信
      const response = await fetch(
        `${this.apiBaseUrl}${this.apiPrefix}/chat/history`,
        {
          method: "GET",
          headers: this.getAuthHeaders(),
        },
      );

      if (!response.ok) {
        // ステータスコードに基づいて異なるエラーを処理
        if (response.status === 401) {
          this.logger.error("Authentication error: User not authenticated");
          // サービス内でエラーをスローし、認証失敗を呼び出し元に通知
          throw new Error("Authentication failed: Please log in");
        } else {
          this.logger.error(`API error: ${response.status}`);
          // その他のエラーは空の配列を返す
          return [];
        }
      }

      const data = await response.json();
      this.logger.debug("Chat history fetched", { count: data.length });
      return data;
    } catch (error) {
      this.logger.error("Error fetching chat history", error);
      return [];
    }
  }

  /**
   * チャットセッションのメッセージを取得
   * @param sessionId セッションID
   */
  async fetchChatMessages(sessionId: string): Promise<any[]> {
    try {
      this.logger.info("Fetching chat messages", { sessionId });

      // リクエストURLをログに出力
      const url = `${this.apiBaseUrl}${this.apiPrefix}/chat/sessions/${sessionId}`;
      this.logger.debug(`Request URL: ${url}`);

      // axiosを使用してリクエストを送信
      const response = await this.axiosInstance.get(url);

      // レスポンスの詳細をログに出力
      this.logger.debug("Chat messages response received", {
        sessionId,
        status: response.status,
        dataType: typeof response.data,
        isArray: Array.isArray(response.data),
        dataLength: Array.isArray(response.data) ? response.data.length : 'not an array'
      });

      // レスポンスデータが配列でない場合の処理
      if (!Array.isArray(response.data)) {
        this.logger.warn("Response data is not an array", {
          sessionId,
          dataType: typeof response.data,
          data: response.data
        });

        // レスポンスデータにmessagesプロパティがある場合
        if (response.data && response.data.messages && Array.isArray(response.data.messages)) {
          this.logger.info("Using messages property from response data", {
            count: response.data.messages.length
          });
          return response.data.messages;
        }

        // それ以外の場合は空の配列を返す
        return [];
      }

      // 各メッセージの詳細をログに出力
      if (response.data.length > 0) {
        this.logger.debug("First message sample:", {
          sample: response.data[0]
        });
      }

      this.logger.info("Chat messages fetched successfully", {
        sessionId,
        count: response.data.length,
      });

      return response.data;
    } catch (error) {
      this.logger.error(
        `Error fetching chat messages for session ${sessionId}`,
        error,
      );

      // エラーの詳細をログに出力
      if (error.response) {
        this.logger.error("Error response details:", {
          status: error.response.status,
          statusText: error.response.statusText,
          data: error.response.data
        });
      }

      return [];
    }
  }

  /**
   * 新しいチャットセッションを作成
   * @param title セッションタイトル
   */
  async createChatSession(title: string = "New Chat"): Promise<any> {
    try {
      this.logger.info("Creating chat session", { title });

      // axiosを使用してリクエストを送信
      const response = await this.axiosInstance.post(
        `${this.apiBaseUrl}${this.apiPrefix}/chat/sessions`,
        { title }, // JSONリクエストボディを使用
        {
          params: { title }, // クエリパラメータとしても送信し、両方の方法に対応
        },
      );

      this.logger.debug("Chat session created successfully", {
        sessionId: response.data.id,
        title,
      });
      return response.data;
    } catch (error) {
      this.logger.error("Error creating chat session", error);

      // フォールバックとしてfetchを試みる
      try {
        this.logger.info("Trying with fetch as fallback...");

        const response = await fetch(
          `${this.apiBaseUrl}${this.apiPrefix}/chat/sessions?title=${encodeURIComponent(title)}`,
          {
            method: "POST",
            headers: this.getAuthHeaders(),
          },
        );

        if (!response.ok) {
          throw new Error(`API error: ${response.status}`);
        }

        const data = await response.json();
        this.logger.debug("Fetch fallback succeeded", { sessionId: data.id });
        return data;
      } catch (fetchError) {
        this.logger.error("Fetch fallback also failed", fetchError);

        // 開発/テスト環境でモックデータを使用
        if (import.meta.env.DEV || import.meta.env.MODE === "development") {
          // モックのセッションオブジェクトを返し、フロントエンドが動作を続けられるようにする
          const mockSession = {
            id: `mock-${Date.now()}`,
            title,
            created_at: new Date().toISOString(),
            last_message: "",
          };
          this.logger.debug(
            "Returning mock session in development mode",
            mockSession,
          );
          return mockSession;
        }

        // 本番環境ではnullを返し、フロントエンドにエラーを表示
        return null;
      }
    }
  }

  /**
   * チャットセッションにメッセージを追加
   * @param sessionId セッションID
   * @param role メッセージロール
   * @param content メッセージ内容
   */
  async addChatMessage(
    sessionId: string,
    role: string,
    content: string,
  ): Promise<any> {
    try {
      this.logger.info("Adding message to session", { sessionId, role });

      const response = await this.axiosInstance.post(
        buildApiUrl(API_ENDPOINTS.CHAT.MESSAGES(sessionId)),
        { role, content },
      );

      this.logger.debug("Message added successfully", {
        sessionId,
        messageId: response.data.id,
      });
      return response.data;
    } catch (error) {
      this.logger.error(`Error adding message to session ${sessionId}`, error);

      // 開発/テスト環境でモックデータを使用
      if (import.meta.env.DEV || import.meta.env.MODE === "development") {
        const mockMessage = {
          id: `mock-${Date.now()}`,
          session_id: sessionId,
          role,
          content,
          created_at: new Date().toISOString(),
        };
        this.logger.debug(
          "Returning mock message in development mode",
          mockMessage,
        );
        return mockMessage;
      }

      // 本番環境ではnullを返し、フロントエンドにエラーを表示
      return null;
    }
  }

  /**
   * チャットセッションを削除
   * @param sessionId セッションID
   */
  async deleteChatSession(sessionId: string): Promise<boolean> {
    try {
      this.logger.info("Deleting chat session", { sessionId });

      await this.axiosInstance.delete(
        `${this.apiBaseUrl}${this.apiPrefix}/chat/sessions/${sessionId}`,
      );

      this.logger.debug("Chat session deleted successfully", { sessionId });
      return true;
    } catch (error) {
      this.logger.error(`Error deleting chat session ${sessionId}`, error);
      return false;
    }
  }

  /**
   * セッションタイトルを更新
   * @param sessionId セッションID
   * @param title 新しいタイトル
   */
  async updateSessionTitle(sessionId: string, title: string): Promise<boolean> {
    try {
      this.logger.info("Updating session title", { sessionId, title });

      const response = await fetch(
        `${this.apiBaseUrl}${this.apiPrefix}/chat/sessions/${sessionId}`,
        {
          method: "PUT",
          headers: this.getAuthHeaders(),
          body: JSON.stringify({ title }),
        },
      );

      if (response.ok) {
        this.logger.debug("Session title updated successfully", {
          sessionId,
          title,
        });
        return true;
      } else {
        this.logger.error("Failed to update session title", {
          sessionId,
          status: response.status,
          statusText: response.statusText,
        });
        return false;
      }
    } catch (error) {
      this.logger.error("Error updating session title", { sessionId, error });
      return false;
    }
  }

  /**
   * プラグイン一覧を取得
   */
  async fetchPlugins(): Promise<any[]> {
    try {
      this.logger.info("Fetching plugins");

      // プラグインシステムのステータスを確認
      const status = await this.fetchPluginSystemStatus().catch(() => ({ enabled: false }));
      if (!status.enabled) {
        this.logger.info("Plugin system is disabled. Returning empty list.");
        return [];
      }

      const response = await this.axiosInstance.get(
        buildApiUrl(API_ENDPOINTS.PLUGINS.LIST)
      );

      this.logger.debug("Plugins fetched successfully", {
        count: response.data.length,
      });
      return response.data;
    } catch (error) {
      this.logger.error("Error fetching plugins", error);

      // 開発/テスト環境でモックデータを使用
      if (import.meta.env.DEV || import.meta.env.MODE === "development") {
        const mockPlugins = [
          {
            id: "simple_rag",
            name: "Simple RAG",
            description: "A simple RAG plugin that adds retrieved documents to the system message",
            type: "rag",
            version: "0.1.0",
            author: "LLM Platform",
            config: {
              enabled: true,
              priority: 100,
              config: {}
            },
            dependencies: [],
            tags: ["rag", "retrieval"]
          },
          {
            id: "simple_agent",
            name: "Simple Agent",
            description: "A simple agent that implements a basic ReAct pattern",
            type: "agent",
            version: "0.1.0",
            author: "LLM Platform",
            config: {
              enabled: true,
              priority: 100,
              config: {}
            },
            dependencies: [],
            tags: ["agent", "tools"]
          }
        ];
        this.logger.debug(
          "Returning mock plugins in development mode",
          mockPlugins
        );
        return mockPlugins;
      }

      return [];
    }
  }

  /**
   * プラグインシステムのステータスを取得
   */
  async fetchPluginSystemStatus(): Promise<any> {
    try {
      this.logger.info("Fetching plugin system status");

      const response = await this.axiosInstance.get(
        buildApiUrl(API_ENDPOINTS.PLUGINS.STATUS)
      );

      this.logger.debug("Plugin system status fetched successfully", response.data);
      return response.data;
    } catch (error) {
      this.logger.error("Error fetching plugin system status", error);

      // 開発/テスト環境でモックデータを使用
      if (import.meta.env.DEV || import.meta.env.MODE === "development") {
        const mockStatus = {
          enabled: true,
          plugin_count: 2,
          enabled_plugins: 2,
          system_setting: true
        };
        this.logger.debug(
          "Returning mock plugin system status in development mode",
          mockStatus
        );
        return mockStatus;
      }

      return { enabled: false, plugin_count: 0, enabled_plugins: 0, system_setting: false };
    }
  }

  /**
   * プラグインを有効化
   * @param pluginId プラグインID
   */
  async enablePlugin(pluginId: string): Promise<boolean> {
    try {
      this.logger.info("Enabling plugin", { pluginId });

      const response = await this.axiosInstance.post(
        buildApiUrl(API_ENDPOINTS.PLUGINS.ENABLE(pluginId))
      );

      this.logger.debug("Plugin enabled successfully", { pluginId });
      return true;
    } catch (error) {
      this.logger.error(`Error enabling plugin ${pluginId}`, error);
      return false;
    }
  }

  /**
   * プラグインを無効化
   * @param pluginId プラグインID
   */
  async disablePlugin(pluginId: string): Promise<boolean> {
    try {
      this.logger.info("Disabling plugin", { pluginId });

      const response = await this.axiosInstance.post(
        buildApiUrl(API_ENDPOINTS.PLUGINS.DISABLE(pluginId))
      );

      this.logger.debug("Plugin disabled successfully", { pluginId });
      return true;
    } catch (error) {
      this.logger.error(`Error disabling plugin ${pluginId}`, error);
      return false;
    }
  }

  /**
   * プラグインを設定
   * @param pluginId プラグインID
   * @param config 設定
   */
  async configurePlugin(pluginId: string, config: Record<string, any>): Promise<boolean> {
    try {
      this.logger.info("Configuring plugin", { pluginId });

      const response = await this.axiosInstance.post(
        buildApiUrl(API_ENDPOINTS.PLUGINS.CONFIGURE(pluginId)),
        { config }
      );

      this.logger.debug("Plugin configured successfully", { pluginId });
      return true;
    } catch (error) {
      this.logger.error(`Error configuring plugin ${pluginId}`, error);
      return false;
    }
  }

  /**
   * バックエンド設定情報を取得
   */
  async fetchBackendConfig(): Promise<any> {
    try {
      this.logger.info("バックエンド設定情報を取得しています");

      // 統一されたAPIエンドポイントを使用
      const url = buildApiUrl(API_ENDPOINTS.BACKEND_CONFIG);

      this.logger.debug(`バックエンド設定情報取得URL: ${url}`);

      const response = await this.axiosInstance.get(url);

      if (response.data) {
        this.logger.info("バックエンド設定情報を取得しました");
        this.logger.debug("バックエンド設定情報:", response.data);
        return response.data;
      } else {
        this.logger.warn("バックエンド設定情報が空です");
        return {
          backend_endpoints: {},
          default_backend: ""
        };
      }
    } catch (error) {
      this.logger.error("バックエンド設定情報の取得に失敗しました", error);

      // エラーの場合は空のオブジェクトを返す
      return {
        backend_endpoints: {},
        default_backend: ""
      };
    }
  }
}

/**
 * API アダプター工場
 * 適切な API アダプターインスタンスを作成するために使用
 */
export class ApiAdapterFactory {
  static createAdapter(
    type: string = "standard",
    logger: LogService,
  ): ApiAdapter {
    switch (type.toLowerCase()) {
      case "standard":
      default:
        return new StandardApiAdapter(logger);
    }
  }
}

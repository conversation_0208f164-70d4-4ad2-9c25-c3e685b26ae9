// Test import to verify react-syntax-highlighter works
try {
  console.log('Testing react-syntax-highlighter import...');
  
  // Test basic import
  const SyntaxHighlighter = require('react-syntax-highlighter');
  console.log('✅ Basic import successful:', typeof SyntaxHighlighter);
  
  // Test style import
  try {
    const { docco } = require('react-syntax-highlighter/dist/cjs/styles/hljs');
    console.log('✅ CJS style import successful:', typeof docco);
  } catch (e) {
    console.log('❌ CJS style import failed:', e.message);
    
    // Try alternative import
    try {
      const { docco } = require('react-syntax-highlighter/dist/esm/styles/hljs');
      console.log('✅ ESM style import successful:', typeof docco);
    } catch (e2) {
      console.log('❌ ESM style import failed:', e2.message);
    }
  }
  
} catch (error) {
  console.error('❌ Import test failed:', error.message);
}

import i18n from "i18next";
import { initReactI18next } from "react-i18next";
import LanguageDetector from "i18next-browser-languagedetector";

// 翻訳ファイルをインポート
import enTranslation from "./locales/en.json";
import jaTranslation from "./locales/ja.json";
import zhTranslation from "./locales/zh.json";

// 翻訳リソース
const resources = {
  en: {
    translation: enTranslation,
  },
  ja: {
    translation: jaTranslation,
  },
  zh: {
    translation: zhTranslation,
  },
};

// 保存された言語設定を取得
const savedLanguage = localStorage.getItem("i18nextLng");

// i18nを初期化
i18n
  .use(LanguageDetector) // ユーザーの言語を自動検出
  .use(initReactI18next) // i18nをreact-i18nextに渡す
  .init({
    resources,
    lng: savedLanguage || "ja", // 保存された言語を使用、またはデフォルトで日本語
    fallbackLng: "ja", // デフォルト言語は日本語
    interpolation: {
      escapeValue: false, // HTMLをエスケープしない
    },
    detection: {
      order: ["localStorage", "navigator"],
      caches: ["localStorage"],
    },
  });

// 言語切り替え後、即座にlocalStorageに保存
i18n.on("languageChanged", (lng) => {
  localStorage.setItem("i18nextLng", lng);
});

export default i18n;

import { create } from "zustand";
import { Model, Backend } from "../types/chat";

interface ModelState {
  models: Model[];
  backends: Backend[];
  selectedModel: string;
  selectedBackend: string | null;
  setModels: (_models: Model[]) => void;
  setBackends: (_backends: Backend[]) => void;
  setSelectedModel: (_modelId: string) => void;
  setSelectedBackend: (_backendId: string | null) => void;
}

export const useModelStore = create<ModelState>((set) => ({
  models: [],
  backends: [],
  selectedModel: localStorage.getItem("selectedModel") || "llama3",
  selectedBackend: localStorage.getItem("selectedBackend") || null,

  setModels: (_models: Model[]) => set({ models: _models }),
  setBackends: (_backends: Backend[]) => set({ backends: _backends }),

  setSelectedModel: (_modelId: string) => {
    localStorage.setItem("selectedModel", _modelId);
    set({ selectedModel: _modelId });
  },

  setSelectedBackend: (_backendId: string | null) => {
    if (_backendId) {
      localStorage.setItem("selectedBackend", _backendId);
    } else {
      localStorage.removeItem("selectedBackend");
    }
    set({ selectedBackend: _backendId });
  },
}));

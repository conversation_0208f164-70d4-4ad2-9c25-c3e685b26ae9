import { create } from "zustand";
import { serviceFactory } from "../services/serviceFactory";

interface User {
  id: string;
  username: string;
  role: "user" | "admin";
}

interface AuthStore {
  user: User | null;
  token: string | null;
  isAuthenticated: boolean;
  login: (_username: string, _password: string) => Promise<void>;
  logout: () => void;
}

// localStorageからユーザー情報を読み込む
const loadUserFromStorage = () => {
  const token = localStorage.getItem("token");
  if (token) {
    // トークンがあれば、localStorageからユーザー情報を読み込む
    const storedUser = localStorage.getItem("user");
    if (storedUser) {
      try {
        return JSON.parse(storedUser);
      } catch (e) {
        console.error("Failed to parse user from localStorage:", e);
      }
    }
    // ユーザー情報がない場合は、デフォルトユーザーを作成
    return {
      id: "1",
      username: "User",
      role: "user",
    };
  }
  return null;
};

export const useAuthStore = create<AuthStore>((set) => ({
  user: loadUserFromStorage(),
  token: localStorage.getItem("token"),
  isAuthenticated: !!localStorage.getItem("token"),
  login: async (_username: string, _password: string) => {
    const logger = serviceFactory.getLogger();

    try {
      // バックエンドAPIを呼び出す
      try {
        logger.info("Attempting to login with backend API");

        // OAuth2のパスワードフローの形式でリクエストを送信
        const formData = new URLSearchParams();
        formData.append("username", _username);
        formData.append("password", _password);

        const response = await fetch(
          `${import.meta.env.VITE_API_BASE_URL || ""}/auth/login`,
          {
            method: "POST",
            headers: { "Content-Type": "application/x-www-form-urlencoded" },
            body: formData.toString(),
          },
        );

        // バックエンドAPIが利用可能な場合、バックエンドからのレスポンスを使用
        if (response.ok) {
          const data = await response.json();
          logger.info("Login successful with backend API");

          localStorage.setItem("token", data.token);
          localStorage.setItem("user", JSON.stringify(data.user));
          set({ user: data.user, token: data.token, isAuthenticated: true });
          return;
        } else {
          // エラーレスポンスの場合
          const errorData = await response.json().catch(() => ({ detail: "Unknown error" }));
          logger.error("Login failed with backend API", errorData);
          throw new Error(errorData.detail || "Login failed");
        }
      } catch (apiError) {
        logger.warn("Backend API not available, using mock login:", apiError);

        // 開発環境でのみモックログインを使用
        if (import.meta.env.DEV) {
          const mockUser: User = {
            id: "1",
            username: _username || "demo",
            role: _username.toLowerCase().includes("admin") ? "admin" : "user",
          };
          const mockToken = "demo-token";

          logger.info(`Logging in with mock user: ${JSON.stringify(mockUser)}`);
          localStorage.setItem("token", mockToken);
          localStorage.setItem("user", JSON.stringify(mockUser));
          set({ user: mockUser, token: mockToken, isAuthenticated: true });
          return;
        }

        // 本番環境ではエラーをスロー
        throw apiError;
      }
    } catch (error) {
      logger.error("Login failed:", error);
      throw error;
    }
  },
  logout: () => {
    const logger = serviceFactory.getLogger();
    logger.info("Logging out");

    // バックエンドのログアウトAPIを呼び出すことも可能
    // fetch(`${import.meta.env.VITE_API_BASE_URL || ""}/auth/logout`, {
    //   method: "POST",
    //   headers: { "Authorization": `Bearer ${localStorage.getItem("token")}` }
    // }).catch(err => logger.warn("Logout API call failed:", err));

    localStorage.removeItem("token");
    localStorage.removeItem("user");
    set({ user: null, token: null, isAuthenticated: false });
  },
}));

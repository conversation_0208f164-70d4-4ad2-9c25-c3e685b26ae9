import { LogService } from "./logService";

/**
 * エラータイプの列挙型
 */
export enum ErrorType {
  NETWORK = "network",
  API = "api",
  AUTHENTICATION = "authentication",
  VALIDATION = "validation",
  TIMEOUT = "timeout",
  UNKNOWN = "unknown",
}

/**
 * カスタムエラークラス
 */
export class AppError extends Error {
  type: ErrorType;
  statusCode?: number;
  details?: any;
  retryable: boolean;

  constructor(
    message: string,
    type: ErrorType = ErrorType.UNKNOWN,
    statusCode?: number,
    details?: any,
    retryable: boolean = false,
  ) {
    super(message);
    this.name = "AppError";
    this.type = type;
    this.statusCode = statusCode;
    this.details = details;
    this.retryable = retryable;

    // instanceof が正常に動作することを保証
    Object.setPrototypeOf(this, AppError.prototype);
  }

  /**
   * ユーザーフレンドリーなエラーメッセージを取得
   */
  getUserFriendlyMessage(): string {
    switch (this.type) {
      case ErrorType.NETWORK:
        return "ネットワーク接続エラーです。ネットワーク接続を確認して再試行してください。";
      case ErrorType.API:
        return `API エラー${this.statusCode ? ` (${this.statusCode})` : ""}: ${this.message}`;
      case ErrorType.AUTHENTICATION:
        return "認証エラー：再ログインしてください。";
      case ErrorType.VALIDATION:
        return `検証エラー: ${this.message}`;
      case ErrorType.TIMEOUT:
        return "リクエストがタイムアウトしました。後でもう一度お試しください。";
      case ErrorType.UNKNOWN:
      default:
        return `エラーが発生しました: ${this.message}`;
    }
  }

  /**
   * エラーの詳細を取得
   */
  getDetails(): string {
    if (!this.details) {
      return "";
    }

    if (typeof this.details === "string") {
      return this.details;
    }

    try {
      return JSON.stringify(this.details, null, 2);
    } catch (_e) {
      return String(this.details);
    }
  }
}

/**
 * リトライ設定インターフェース
 */
export interface RetryConfig {
  maxRetries: number;
  initialDelay: number;
  maxDelay: number;
  backoffFactor: number;
  retryableStatusCodes: number[];
  retryableErrorTypes: ErrorType[];
}

/**
 * エラー処理サービス
 * エラー処理とリトライ機能を提供
 */
export class ErrorService {
  private logger: LogService;
  private retryConfig: RetryConfig;
  private static instance: ErrorService | null = null;

  /**
   * エラー処理サービスのインスタンスを取得（シングルトンパターン）
   */
  public static getInstance(
    logger: LogService,
    config?: Partial<RetryConfig>,
  ): ErrorService {
    if (!ErrorService.instance) {
      ErrorService.instance = new ErrorService(logger, config);
    } else if (config) {
      // 既存のインスタンスの設定を更新
      ErrorService.instance.updateConfig(config);
    }

    return ErrorService.instance;
  }

  /**
   * コンストラクタ
   * @param logger ログサービス
   * @param config リトライ設定
   */
  private constructor(logger: LogService, config?: Partial<RetryConfig>) {
    this.logger = logger;

    // デフォルトのリトライ設定
    this.retryConfig = {
      maxRetries: 3,
      initialDelay: 1000, // 1 秒
      maxDelay: 10000, // 10 秒
      backoffFactor: 2,
      retryableStatusCodes: [408, 429, 500, 502, 503, 504],
      retryableErrorTypes: [ErrorType.NETWORK, ErrorType.TIMEOUT],
    };

    // カスタム設定を適用
    if (config) {
      this.updateConfig(config);
    }

    this.logger.info("ErrorService initialized", { config: this.retryConfig });
  }

  /**
   * リトライ設定を更新
   * @param config 新しい設定
   */
  public updateConfig(config: Partial<RetryConfig>): void {
    this.retryConfig = { ...this.retryConfig, ...config };
    this.logger.debug("ErrorService configuration updated", {
      config: this.retryConfig,
    });
  }

  /**
   * エラーを処理
   * @param error エラーオブジェクト
   * @param source エラーの発生元
   */
  public handleError(error: any, source?: string): AppError {
    let appError: AppError;

    if (error instanceof AppError) {
      appError = error;
    } else {
      appError = this.createAppError(error);
    }

    // エラーを記録
    this.logger.error(
      `Error in ${source || "unknown"}: ${appError.message}`,
      {
        type: appError.type,
        statusCode: appError.statusCode,
        details: appError.details,
        stack: appError.stack,
      },
      source,
    );

    return appError;
  }

  /**
   * アプリケーションエラーを作成
   * @param error 元のエラー
   */
  private createAppError(error: any): AppError {
    // Axios エラーを処理
    if (error.isAxiosError) {
      const statusCode = error.response?.status;
      const data = error.response?.data;

      // ステータスコードに基づいてエラータイプを決定
      let type = ErrorType.API;
      let retryable = false;

      if (!error.response) {
        type = ErrorType.NETWORK;
        retryable = true;
      } else if (statusCode === 401 || statusCode === 403) {
        type = ErrorType.AUTHENTICATION;
      } else if (statusCode === 408 || statusCode === 429) {
        type = ErrorType.TIMEOUT;
        retryable = true;
      } else if (this.retryConfig.retryableStatusCodes.includes(statusCode)) {
        retryable = true;
      }

      return new AppError(
        error.message || "API request failed",
        type,
        statusCode,
        data,
        retryable,
      );
    }

    // ネットワークエラーを処理
    if (error instanceof TypeError && error.message.includes("Network")) {
      return new AppError(
        "Network error occurred",
        ErrorType.NETWORK,
        undefined,
        error.message,
        true,
      );
    }

    // タイムアウトエラーを処理
    if (error.message && error.message.includes("timeout")) {
      return new AppError(
        "Request timed out",
        ErrorType.TIMEOUT,
        undefined,
        error.message,
        true,
      );
    }

    // その他のエラーを処理
    return new AppError(
      error.message || "An unknown error occurred",
      ErrorType.UNKNOWN,
      undefined,
      error,
    );
  }

  /**
   * リトライロジックを使用して非同期関数を実行
   * @param fn 実行する非同期関数
   * @param context コンテキスト情報
   */
  public async withRetry<T>(
    fn: () => Promise<T>,
    context: { source?: string; description?: string } = {},
  ): Promise<T> {
    const { source, description } = context;
    let lastError: any;

    for (
      let attempt = 1;
      attempt <= this.retryConfig.maxRetries + 1;
      attempt++
    ) {
      try {
        if (attempt > 1) {
          this.logger.info(
            `Retry attempt ${attempt - 1}/${this.retryConfig.maxRetries}${description ? ` for ${description}` : ""}`,
            { source },
          );
        }

        return await fn();
      } catch (error) {
        lastError = error;
        const appError = this.handleError(error, source);

        // リトライすべきかどうかを確認
        const shouldRetry =
          attempt <= this.retryConfig.maxRetries &&
          (appError.retryable ||
            this.retryConfig.retryableErrorTypes.includes(appError.type) ||
            (appError.statusCode &&
              this.retryConfig.retryableStatusCodes.includes(
                appError.statusCode,
              )));

        if (!shouldRetry) {
          this.logger.warn(
            `Not retrying${description ? ` ${description}` : ""}: ${appError.message}`,
            {
              attempt,
              maxRetries: this.retryConfig.maxRetries,
              error: appError,
            },
            source,
          );
          break;
        }

        // 遅延時間を計算（指数バックオフ）
        const delay = Math.min(
          this.retryConfig.initialDelay *
            Math.pow(this.retryConfig.backoffFactor, attempt - 1),
          this.retryConfig.maxDelay,
        );

        this.logger.info(
          `Waiting ${delay}ms before retry${description ? ` for ${description}` : ""}`,
          { attempt, delay },
          source,
        );

        // 一定時間待機してからリトライ
        await new Promise((resolve) => setTimeout(resolve, delay));
      }
    }

    // すべてのリトライが失敗
    throw lastError;
  }

  /**
   * ネットワークエラーを作成
   * @param message エラーメッセージ
   * @param details エラーの詳細
   */
  public createNetworkError(message: string, details?: any): AppError {
    return new AppError(message, ErrorType.NETWORK, undefined, details, true);
  }

  /**
   * API エラーを作成
   * @param message エラーメッセージ
   * @param statusCode HTTP ステータスコード
   * @param details エラーの詳細
   * @param retryable リトライ可能かどうか
   */
  public createApiError(
    message: string,
    statusCode?: number,
    details?: any,
    retryable: boolean = false,
  ): AppError {
    return new AppError(message, ErrorType.API, statusCode, details, retryable);
  }

  /**
   * 認証エラーを作成
   * @param message エラーメッセージ
   * @param details エラーの詳細
   */
  public createAuthError(message: string, details?: any): AppError {
    return new AppError(message, ErrorType.AUTHENTICATION, 401, details, false);
  }

  /**
   * 検証エラーを作成
   * @param message エラーメッセージ
   * @param details エラーの詳細
   */
  public createValidationError(message: string, details?: any): AppError {
    return new AppError(message, ErrorType.VALIDATION, 400, details, false);
  }

  /**
   * タイムアウトエラーを作成
   * @param message エラーメッセージ
   * @param details エラーの詳細
   */
  public createTimeoutError(message: string, details?: any): AppError {
    return new AppError(message, ErrorType.TIMEOUT, 408, details, true);
  }
}

/**
 * デフォルトのエラー処理サービスインスタンスを作成
 */
export const createDefaultErrorService = (logger: LogService): ErrorService => {
  return ErrorService.getInstance(logger, {
    maxRetries: process.env.NODE_ENV === "production" ? 3 : 1,
    initialDelay: 1000,
    maxDelay: 10000,
    backoffFactor: 2,
    retryableStatusCodes: [408, 429, 500, 502, 503, 504],
    retryableErrorTypes: [ErrorType.NETWORK, ErrorType.TIMEOUT],
  });
};

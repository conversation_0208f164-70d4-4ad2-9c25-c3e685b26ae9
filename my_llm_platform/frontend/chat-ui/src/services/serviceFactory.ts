import { ApiAdapter, ApiAdapterFactory } from "../adapters/apiAdapter";
import { BackendStrategyContext } from "../strategies/backendStrategies";
import { LogService, defaultLogger, LogLevel } from "./logService";
import { ErrorService, createDefaultErrorService } from "./errorService";
import type { ApiService } from "./apiService";

/**
 * サービスファクトリー
 * アプリケーションサービスの作成と管理に使用
 */
export class ServiceFactory {
  private static instance: ServiceFactory | null = null;

  private logger: LogService;
  private errorService: ErrorService;
  private apiAdapter: ApiAdapter;
  private backendContext: BackendStrategyContext;
  private apiService: ApiService | null = null;

  /**
   * サービスファクトリーインスタンスを取得（シングルトンパターン）
   */
  public static getInstance(): ServiceFactory {
    if (!ServiceFactory.instance) {
      ServiceFactory.instance = new ServiceFactory();
    }

    return ServiceFactory.instance;
  }

  /**
   * コンストラクタ
   */
  private constructor() {
    // ログサービスを初期化
    this.logger = defaultLogger;

    // エラーハンドリングサービスを初期化
    this.errorService = createDefaultErrorService(this.logger);

    // APIアダプターを初期化
    this.apiAdapter = ApiAdapterFactory.createAdapter("standard", this.logger);

    // バックエンド戦略コンテキストを初期化
    this.backendContext = new BackendStrategyContext(
      this.logger,
      this.apiAdapter,
    );

    this.logger.info("ServiceFactory initialized");
  }

  /**
   * ログサービスを取得
   */
  public getLogger(): LogService {
    return this.logger;
  }

  /**
   * エラーハンドリングサービスを取得
   */
  public getErrorService(): ErrorService {
    return this.errorService;
  }

  /**
   * APIアダプターを取得
   */
  public getApiAdapter(): ApiAdapter {
    return this.apiAdapter;
  }

  /**
   * バックエンド戦略コンテキストを取得
   */
  public getBackendContext(): BackendStrategyContext {
    return this.backendContext;
  }

  /**
   * APIサービスを取得
   */
  public getApiService(): ApiService {
    if (!this.apiService) {
      // APIサービスの遅延初期化、循環依存を回避
      // 動的インポートを使用して循環依存を回避
      const { apiService } = require("./apiService");
      this.apiService = apiService;
    }
    return this.apiService;
  }

  /**
   * ログレベルを設定
   * @param level ログレベル
   */
  public setLogLevel(level: LogLevel): void {
    this.logger.updateConfig({ minLevel: level });
    this.logger.info(`Log level set to ${LogLevel[level]}`);
  }

  /**
   * 現在のバックエンド戦略を設定
   * @param backendName バックエンド名
   */
  public setBackendStrategy(backendName: string): boolean {
    return this.backendContext.setStrategy(backendName);
  }

  /**
   * 登録済みのすべてのバックエンド戦略名を取得
   */
  public getAvailableBackends(): string[] {
    return this.backendContext.getRegisteredStrategyNames();
  }

  /**
   * API設定を取得
   */
  public getApiConfig() {
    // API設定をインポートして返す
    const { API_CONFIG, API_ENDPOINTS } = require("../config/api");
    return { API_CONFIG, API_ENDPOINTS };
  }
}

/**
 * デフォルトのサービスファクトリーインスタンスを作成
 */
export const serviceFactory = ServiceFactory.getInstance();

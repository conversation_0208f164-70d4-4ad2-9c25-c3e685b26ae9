import {
  Message,
  ChatOptions,
  ChatResponse,
  StreamChunk,
  Model,
  Backend,
  ChatHistoryItem,
  ChatMessage,
  ChatSession,
} from "../types/chat";
import { serviceFactory } from "./serviceFactory";
import { ErrorType } from "./errorService";
import { performanceMonitor } from "../utils/performanceMonitor";

/**
 * API サービス
 * バックエンド API とのインタラクションの高レベルインターフェースを提供
 */
export class ApiService {
  private static instance: ApiService | null = null;

  /**
   * API サービスインスタンスを取得（シングルトンパターン）
   */
  public static getInstance(): ApiService {
    if (!ApiService.instance) {
      ApiService.instance = new ApiService();
    }

    return ApiService.instance;
  }

  /**
   * コンストラクタ
   */
  private constructor() {
    const logger = serviceFactory.getLogger();
    logger.info("ApiService initialized");
  }

  /**
   * チャットリクエストを送信
   * @param messages メッセージ配列
   * @param options オプション
   */
  public async sendChatRequest(
    messages: Message[],
    options: ChatOptions = {},
  ): Promise<ChatResponse | Response> {
    const logger = serviceFactory.getLogger();
    const errorService = serviceFactory.getErrorService();
    const backendContext = serviceFactory.getBackendContext();

    logger.info("Sending chat request", {
      model: options.model,
      backend: options.backend,
      messagesCount: messages.length,
      stream: options.stream,
    });

    // パフォーマンスモニタリング開始
    performanceMonitor.startMeasure("sendChatRequest");

    try {
      const result = await errorService.withRetry(
        async () => {
          return await backendContext.sendChatRequest(messages, options);
        },
        { source: "ApiService", description: "chat request" },
      );

      // パフォーマンスモニタリング終了
      const duration = performanceMonitor.endMeasure("sendChatRequest");
      if (duration) {
        performanceMonitor.logApiCall("chat", duration, "success");
      }

      return result;
    } catch (error) {
      // パフォーマンスモニタリング終了（エラーの場合）
      const duration = performanceMonitor.endMeasure("sendChatRequest");
      if (duration) {
        performanceMonitor.logApiCall("chat", duration, "error");
      }

      // エラー処理
      const appError = errorService.handleError(
        error,
        "ApiService.sendChatRequest",
      );

      // ユーザーフレンドリーなエラーメッセージを作成
      const friendlyMessage = appError.getUserFriendlyMessage();

      // ストリームリクエストの場合、モックストリームレスポンスを作成
      if (options.stream) {
        logger.warn("Creating mock stream response for failed request", {
          error: appError,
        });

        // ReadableStreamを作成
        const encoder = new TextEncoder();
        const stream = new ReadableStream({
          start(controller) {
            // エラーメッセージを送信
            const errorChunk = {
              choices: [
                {
                  delta: { content: friendlyMessage },
                  index: 0,
                  finish_reason: "error",
                },
              ],
            };

            controller.enqueue(
              encoder.encode(`data: ${JSON.stringify(errorChunk)}\n\n`),
            );
            controller.enqueue(encoder.encode("data: [DONE]\n\n"));
            controller.close();
          },
        });

        // Responseオブジェクトを作成
        return new Response(stream, {
          headers: {
            "Content-Type": "text/event-stream",
            "Cache-Control": "no-cache",
            Connection: "keep-alive",
          },
        });
      } else {
        // 非ストリームリクエストの場合、モックレスポンスオブジェクトを作成
        logger.warn("Creating mock response for failed request", {
          error: appError,
        });

        return {
          choices: [
            {
              message: {
                role: "assistant",
                content: friendlyMessage,
              },
              finish_reason: "error",
            },
          ],
          created: Date.now(),
          model: options.model || "unknown",
        };
      }
    }
  }

  /**
   * テキスト生成リクエストを送信
   * @param prompt プロンプトテキスト
   * @param options オプション
   */
  public async sendCompletionRequest(
    prompt: string,
    options: ChatOptions = {},
  ): Promise<ChatResponse | Response> {
    const logger = serviceFactory.getLogger();
    const errorService = serviceFactory.getErrorService();
    const backendContext = serviceFactory.getBackendContext();

    logger.info("Sending completion request", {
      model: options.model,
      backend: options.backend,
      promptLength: prompt.length,
      stream: options.stream,
    });

    // パフォーマンスモニタリング開始
    performanceMonitor.startMeasure("sendCompletionRequest");

    try {
      const result = await errorService.withRetry(
        async () => {
          return await backendContext.sendCompletionRequest(prompt, options);
        },
        { source: "ApiService", description: "completion request" },
      );

      // パフォーマンスモニタリング終了
      const duration = performanceMonitor.endMeasure("sendCompletionRequest");
      if (duration) {
        performanceMonitor.logApiCall("completion", duration, "success");
      }

      return result;
    } catch (error) {
      // パフォーマンスモニタリング終了（エラーの場合）
      const duration = performanceMonitor.endMeasure("sendCompletionRequest");
      if (duration) {
        performanceMonitor.logApiCall("completion", duration, "error");
      }

      // エラー処理
      const appError = errorService.handleError(
        error,
        "ApiService.sendCompletionRequest",
      );

      // ユーザーフレンドリーなエラーメッセージを作成
      const friendlyMessage = appError.getUserFriendlyMessage();

      // ストリームリクエストの場合、モックストリームレスポンスを作成
      if (options.stream) {
        logger.warn("Creating mock stream response for failed request", {
          error: appError,
        });

        // ReadableStreamを作成
        const encoder = new TextEncoder();
        const stream = new ReadableStream({
          start(controller) {
            // エラーメッセージを送信
            const errorChunk = {
              choices: [
                {
                  delta: { content: friendlyMessage },
                  index: 0,
                  finish_reason: "error",
                },
              ],
            };

            controller.enqueue(
              encoder.encode(`data: ${JSON.stringify(errorChunk)}\n\n`),
            );
            controller.enqueue(encoder.encode("data: [DONE]\n\n"));
            controller.close();
          },
        });

        // Responseオブジェクトを作成
        return new Response(stream, {
          headers: {
            "Content-Type": "text/event-stream",
            "Cache-Control": "no-cache",
            Connection: "keep-alive",
          },
        });
      } else {
        // 非ストリームリクエストの場合、モックレスポンスオブジェクトを作成
        logger.warn("Creating mock response for failed request", {
          error: appError,
        });

        return {
          choices: [
            {
              message: {
                role: "assistant",
                content: friendlyMessage,
              },
              finish_reason: "error",
            },
          ],
          created: Date.now(),
          model: options.model || "unknown",
        };
      }
    }
  }

  /**
   * ストリームレスポンスを処理
   * @param response レスポンスオブジェクト
   * @param onChunk チャンク処理コールバック
   * @param onComplete 完了コールバック
   * @param onError エラーコールバック
   * @param backendName バックエンド名
   */
  public handleStreamResponse(
    response: Response,
    onChunk: (_chunk: StreamChunk) => void,
    onComplete: () => void,
    onError: (_error: Error) => void,
    backendName?: string,
  ): void {
    const logger = serviceFactory.getLogger();
    const backendContext = serviceFactory.getBackendContext();

    logger.info("Handling stream response", {
      status: response.status,
      statusText: response.statusText,
      contentType: response.headers.get("content-type"),
      backend: backendName || "unknown"
    });

    // レスポンスヘッダーの詳細をログに出力
    const headers: Record<string, string> = {};
    response.headers.forEach((value, key) => {
      headers[key] = value;
    });
    logger.debug("レスポンスヘッダー:", headers);

    // レスポンスのステータスを確認
    if (!response.ok) {
      logger.error("レスポンスステータスが不正です", {
        status: response.status,
        statusText: response.statusText
      });
    }

    // レスポンスのコンテントタイプを確認
    const contentType = response.headers.get("content-type");
    if (!contentType || !contentType.includes("text/event-stream")) {
      logger.warn(`予期しないコンテンツタイプ: ${contentType}`);
    }

    // パフォーマンスモニタリング開始
    performanceMonitor.startMeasure("streamResponse");

    // バックエンド戦略を使用してストリームを処理
    backendContext.handleStreamResponse(
      response,
      (chunk) => {
        // チャンクの詳細をログに出力
        logger.debug("Stream chunk received", {
          chunkType: typeof chunk,
          hasChoices: chunk && chunk.choices ? true : false,
          choicesLength: chunk && chunk.choices ? chunk.choices.length : 0,
          hasError: chunk && chunk.error ? true : false,
          errorType: chunk && chunk.error ? chunk.error.type : null,
          id: chunk && chunk.id ? chunk.id : null,
          object: chunk && chunk.object ? chunk.object : null,
        });

        // チャンクが有効かチェック
        if (!chunk) {
          logger.warn("空のチャンクを受信しました");
        } else if (chunk.error) {
          logger.warn("エラー情報を含むチャンクを受信しました", {
            errorMessage: chunk.error.message,
            errorType: chunk.error.type
          });
        } else if (!chunk.choices || chunk.choices.length === 0) {
          logger.warn("選択肢がないチャンクを受信しました", { chunk });
        }

        // コールバックを呼び出す
        onChunk(chunk);
      },
      () => {
        logger.info("Stream completed");
        // パフォーマンスモニタリング終了
        const duration = performanceMonitor.endMeasure("streamResponse");
        if (duration) {
          performanceMonitor.logApiCall("streamResponse", duration, "success");
        }
        onComplete();
      },
      (error) => {
        logger.error("Stream error", {
          errorMessage: error.message,
          errorName: error.name,
          errorStack: error.stack
        });
        // パフォーマンスモニタリング終了（エラーの場合）
        const duration = performanceMonitor.endMeasure("streamResponse");
        if (duration) {
          performanceMonitor.logApiCall("streamResponse", duration, "error");
        }
        onError(error);
      },
      backendName,
    );
  }

  /**
   * 利用可能なモデルリストを取得
   * @returns モデルリスト
   */
  public async fetchModels(): Promise<Model[]> {
    const logger = serviceFactory.getLogger();
    const errorService = serviceFactory.getErrorService();
    const apiAdapter = serviceFactory.getApiAdapter();

    logger.info("Fetching models");

    // パフォーマンスモニタリング開始
    performanceMonitor.startMeasure("fetchModels");

    try {
      const result = await errorService.withRetry(
        async () => {
          return await apiAdapter.fetchModels();
        },
        { source: "ApiService", description: "fetch models" },
      );

      // パフォーマンスモニタリング終了
      const duration = performanceMonitor.endMeasure("fetchModels");
      if (duration) {
        performanceMonitor.logApiCall("fetchModels", duration, "success");
      }

      return result;
    } catch (error) {
      // パフォーマンスモニタリング終了（エラーの場合）
      const duration = performanceMonitor.endMeasure("fetchModels");
      if (duration) {
        performanceMonitor.logApiCall("fetchModels", duration, "error");
      }

      // エラー処理
      const appError = errorService.handleError(
        error,
        "ApiService.fetchModels",
      );

      logger.warn("Using default models due to error", { error: appError });

      // 環境変数からデフォルトモデルを取得
      const defaultModel = import.meta.env.VITE_DEFAULT_MODEL || "";
      const defaultBackend = import.meta.env.VITE_DEFAULT_BACKEND || "";

      if (defaultModel && defaultBackend) {
        logger.info(`Using default model from environment: ${defaultModel} (${defaultBackend})`);
        return [
          {
            id: defaultModel,
            name: defaultModel,
            default: true,
            description: `Default model from environment variables`,
            backend: defaultBackend,
          }
        ];
      }

      // 環境変数が設定されていない場合は空のリストを返す
      logger.warn("No models found in environment variables, returning empty list");
      return [];
    }
  }

  /**
   * 利用可能なバックエンドリストを取得
   * @returns バックエンドリストとデフォルトバックエンド
   */
  public async fetchBackends(): Promise<{
    backends: Backend[];
    default_backend: string;
  }> {
    const logger = serviceFactory.getLogger();
    const errorService = serviceFactory.getErrorService();
    const apiAdapter = serviceFactory.getApiAdapter();

    logger.info("Fetching backends");

    // パフォーマンスモニタリング開始
    performanceMonitor.startMeasure("fetchBackends");

    try {
      const result = await errorService.withRetry(
        async () => {
          return await apiAdapter.fetchBackends();
        },
        { source: "ApiService", description: "fetch backends" },
      );

      // パフォーマンスモニタリング終了
      const duration = performanceMonitor.endMeasure("fetchBackends");
      if (duration) {
        performanceMonitor.logApiCall("fetchBackends", duration, "success");
      }

      return result;
    } catch (error) {
      // パフォーマンスモニタリング終了（エラーの場合）
      const duration = performanceMonitor.endMeasure("fetchBackends");
      if (duration) {
        performanceMonitor.logApiCall("fetchBackends", duration, "error");
      }

      // エラー処理
      const appError = errorService.handleError(
        error,
        "ApiService.fetchBackends",
      );

      logger.warn("Using default backends due to error", { error: appError });

      // 環境変数からデフォルトバックエンドを取得
      const defaultBackend = import.meta.env.VITE_DEFAULT_BACKEND || "";

      if (defaultBackend) {
        logger.info(`Using default backend from environment: ${defaultBackend}`);
        return {
          backends: [
            {
              id: defaultBackend,
              name: defaultBackend,
              description: `Default backend from environment variables`,
              supports_streaming: true,
            }
          ],
          default_backend: defaultBackend,
        };
      }

      // 環境変数が設定されていない場合は空のリストを返す
      logger.warn("No backends found in environment variables, returning empty list");
      return {
        backends: [],
        default_backend: "",
      };
    }
  }

  /**
   * チャット履歴を取得
   */
  public async fetchChatHistory(): Promise<ChatHistoryItem[]> {
    const logger = serviceFactory.getLogger();
    const errorService = serviceFactory.getErrorService();
    const apiAdapter = serviceFactory.getApiAdapter();

    logger.info("Fetching chat history");

    // パフォーマンスモニタリング開始
    performanceMonitor.startMeasure("fetchChatHistory");

    try {
      const result = await errorService.withRetry(
        async () => {
          return await apiAdapter.fetchChatHistory();
        },
        { source: "ApiService", description: "fetch chat history" },
      );

      // パフォーマンスモニタリング終了
      const duration = performanceMonitor.endMeasure("fetchChatHistory");
      if (duration) {
        performanceMonitor.logApiCall("fetchChatHistory", duration, "success");
      }

      return result;
    } catch (error) {
      // パフォーマンスモニタリング終了（エラーの場合）
      const duration = performanceMonitor.endMeasure("fetchChatHistory");
      if (duration) {
        performanceMonitor.logApiCall("fetchChatHistory", duration, "error");
      }

      // エラー処理
      const appError = errorService.handleError(
        error,
        "ApiService.fetchChatHistory",
      );

      // 認証エラーの場合、エラーをスロー
      if (appError.type === ErrorType.AUTHENTICATION) {
        throw appError;
      }

      logger.warn("Returning empty chat history due to error", {
        error: appError,
      });
      return [];
    }
  }

  /**
   * チャットセッションのメッセージを取得
   * @param sessionId セッションID
   */
  public async fetchChatMessages(sessionId: string): Promise<ChatMessage[]> {
    const logger = serviceFactory.getLogger();
    const errorService = serviceFactory.getErrorService();
    const apiAdapter = serviceFactory.getApiAdapter();

    logger.info("Fetching chat messages", { sessionId });

    // パフォーマンスモニタリング開始
    performanceMonitor.startMeasure(`fetchChatMessages_${sessionId}`);

    try {
      const result = await errorService.withRetry(
        async () => {
          return await apiAdapter.fetchChatMessages(sessionId);
        },
        {
          source: "ApiService",
          description: `fetch chat messages for session ${sessionId}`,
        },
      );

      // パフォーマンスモニタリング終了
      const duration = performanceMonitor.endMeasure(
        `fetchChatMessages_${sessionId}`,
      );
      if (duration) {
        performanceMonitor.logApiCall(
          `fetchChatMessages_${sessionId}`,
          duration,
          "success",
        );
      }

      return result;
    } catch (error) {
      // パフォーマンスモニタリング終了（エラーの場合）
      const duration = performanceMonitor.endMeasure(
        `fetchChatMessages_${sessionId}`,
      );
      if (duration) {
        performanceMonitor.logApiCall(
          `fetchChatMessages_${sessionId}`,
          duration,
          "error",
        );
      }

      // エラー処理
      const appError = errorService.handleError(
        error,
        "ApiService.fetchChatMessages",
      );

      logger.warn("Returning empty chat messages due to error", {
        error: appError,
        sessionId,
      });
      return [];
    }
  }

  /**
   * 新しいチャットセッションを作成
   * @param title セッションタイトル
   */
  public async createChatSession(title: string = "New Chat"): Promise<ChatSession> {
    const logger = serviceFactory.getLogger();
    const errorService = serviceFactory.getErrorService();
    const apiAdapter = serviceFactory.getApiAdapter();

    logger.info("Creating chat session", { title });

    // パフォーマンスモニタリング開始
    performanceMonitor.startMeasure("createChatSession");

    try {
      const result = await errorService.withRetry(
        async () => {
          return await apiAdapter.createChatSession(title);
        },
        { source: "ApiService", description: "create chat session" },
      );

      // パフォーマンスモニタリング終了
      const duration = performanceMonitor.endMeasure("createChatSession");
      if (duration) {
        performanceMonitor.logApiCall("createChatSession", duration, "success");
      }

      return result;
    } catch (error) {
      // パフォーマンスモニタリング終了（エラーの場合）
      const duration = performanceMonitor.endMeasure("createChatSession");
      if (duration) {
        performanceMonitor.logApiCall("createChatSession", duration, "error");
      }

      // エラー処理
      const appError = errorService.handleError(
        error,
        "ApiService.createChatSession",
      );

      // 開発環境でモックセッションを返す
      if (import.meta.env.DEV || import.meta.env.MODE === "development") {
        logger.warn("Returning mock session in development mode", {
          error: appError,
        });

        return {
          id: `mock-${Date.now()}`,
          title,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        };
      }

      // 本番環境でエラーをスロー
      throw appError;
    }
  }

  /**
   * チャットセッションにメッセージを追加
   * @param sessionId セッションID
   * @param role メッセージの役割
   * @param content メッセージ内容
   */
  public async addChatMessage(
    sessionId: string,
    role: string,
    content: string,
  ): Promise<ChatMessage> {
    const logger = serviceFactory.getLogger();
    const errorService = serviceFactory.getErrorService();
    const apiAdapter = serviceFactory.getApiAdapter();

    logger.info("Adding chat message", { sessionId, role });

    // パフォーマンスモニタリング開始
    performanceMonitor.startMeasure(`addChatMessage_${sessionId}`);

    try {
      const result = await errorService.withRetry(
        async () => {
          return await apiAdapter.addChatMessage(sessionId, role, content);
        },
        {
          source: "ApiService",
          description: `add message to session ${sessionId}`,
        },
      );

      // パフォーマンスモニタリング終了
      const duration = performanceMonitor.endMeasure(
        `addChatMessage_${sessionId}`,
      );
      if (duration) {
        performanceMonitor.logApiCall(
          `addChatMessage_${sessionId}`,
          duration,
          "success",
        );
      }

      return result;
    } catch (error) {
      // パフォーマンスモニタリング終了（エラーの場合）
      const duration = performanceMonitor.endMeasure(
        `addChatMessage_${sessionId}`,
      );
      if (duration) {
        performanceMonitor.logApiCall(
          `addChatMessage_${sessionId}`,
          duration,
          "error",
        );
      }

      // エラー処理
      const appError = errorService.handleError(
        error,
        "ApiService.addChatMessage",
      );

      // 開発環境でモックメッセージを返す
      if (import.meta.env.DEV || import.meta.env.MODE === "development") {
        logger.warn("Returning mock message in development mode", {
          error: appError,
        });

        return {
          id: `mock-${Date.now()}`,
          session_id: sessionId,
          role,
          content,
          created_at: new Date().toISOString(),
        };
      }

      // 本番環境でエラーをスロー
      throw appError;
    }
  }

  /**
   * チャットセッションを削除
   * @param sessionId セッションID
   */
  public async deleteChatSession(sessionId: string): Promise<boolean> {
    const logger = serviceFactory.getLogger();
    const errorService = serviceFactory.getErrorService();
    const apiAdapter = serviceFactory.getApiAdapter();

    logger.info("Deleting chat session", { sessionId });

    // パフォーマンスモニタリング開始
    performanceMonitor.startMeasure(`deleteChatSession_${sessionId}`);

    try {
      const result = await errorService.withRetry(
        async () => {
          return await apiAdapter.deleteChatSession(sessionId);
        },
        { source: "ApiService", description: `delete session ${sessionId}` },
      );

      // パフォーマンスモニタリング終了
      const duration = performanceMonitor.endMeasure(
        `deleteChatSession_${sessionId}`,
      );
      if (duration) {
        performanceMonitor.logApiCall(
          `deleteChatSession_${sessionId}`,
          duration,
          "success",
        );
      }

      return result;
    } catch (error) {
      // パフォーマンスモニタリング終了（エラーの場合）
      const duration = performanceMonitor.endMeasure(
        `deleteChatSession_${sessionId}`,
      );
      if (duration) {
        performanceMonitor.logApiCall(
          `deleteChatSession_${sessionId}`,
          duration,
          "error",
        );
      }

      // エラー処理
      const appError = errorService.handleError(
        error,
        "ApiService.deleteChatSession",
      );

      // 開発環境で成功を返す
      if (import.meta.env.DEV || import.meta.env.MODE === "development") {
        logger.warn("Returning success in development mode", {
          error: appError,
        });
        return true;
      }

      // 本番環境で失敗を返す
      return false;
    }
  }

  /**
   * セッションタイトルを更新
   * @param sessionId セッションID
   * @param title 新しいタイトル
   */
  public async updateSessionTitle(
    sessionId: string,
    title: string,
  ): Promise<boolean> {
    const logger = serviceFactory.getLogger();
    const errorService = serviceFactory.getErrorService();
    const apiAdapter = serviceFactory.getApiAdapter();

    logger.info("Updating session title", { sessionId, title });

    // パフォーマンスモニタリング開始
    performanceMonitor.startMeasure(`updateSessionTitle_${sessionId}`);

    try {
      const result = await errorService.withRetry(
        async () => {
          return await apiAdapter.updateSessionTitle(sessionId, title);
        },
        {
          source: "ApiService",
          description: `update title for session ${sessionId}`,
        },
      );

      // パフォーマンスモニタリング終了
      const duration = performanceMonitor.endMeasure(
        `updateSessionTitle_${sessionId}`,
      );
      if (duration) {
        performanceMonitor.logApiCall(
          `updateSessionTitle_${sessionId}`,
          duration,
          "success",
        );
      }

      return result;
    } catch (error) {
      // パフォーマンスモニタリング終了（エラーの場合）
      const duration = performanceMonitor.endMeasure(
        `updateSessionTitle_${sessionId}`,
      );
      if (duration) {
        performanceMonitor.logApiCall(
          `updateSessionTitle_${sessionId}`,
          duration,
          "error",
        );
      }

      // エラー処理
      const appError = errorService.handleError(
        error,
        "ApiService.updateSessionTitle",
      );

      // 開発環境で成功を返す
      if (import.meta.env.DEV || import.meta.env.MODE === "development") {
        logger.warn("Returning success in development mode", {
          error: appError,
        });
        return true;
      }

      // 本番環境で失敗を返す
      return false;
    }
  }

  /**
   * AI を使用してセッションタイトルを生成
   * @param messages メッセージ配列
   * @param selectedModel 選択されたモデル名（オプション）
   */
  public async generateSessionTitle(messages: Message[], selectedModel?: string): Promise<string> {
    const logger = serviceFactory.getLogger();

    // パフォーマンスモニタリング開始
    performanceMonitor.startMeasure("generateSessionTitle");

    try {
      // システムメッセージを作成し、AI に簡潔なタイトルを生成するよう依頼
      const systemMessage: Message = {
        role: "system",
        content:
          "この会話の簡潔なタイトルを生成してください。15文字以内でお願いします。タイトルテキストのみを返し、他の内容は不要です。",
      };

      // タイトル生成のため、最初の数件のメッセージのみを使用し、リクエストサイズを削減
      const contextMessages = messages.slice(0, Math.min(3, messages.length));

      // モデル名を取得
      // 選択されたモデルが指定されている場合はそれを使用、そうでない場合はデフォルトモデルを使用
      const modelToUse = selectedModel || localStorage.getItem("selectedModel") || import.meta.env.VITE_DEFAULT_MODEL || "llama3.2:3b";

      logger.info(`セッションタイトル生成に使用するモデル: ${modelToUse}`);

      // AI にリクエストを送信
      const response = (await this.sendChatRequest(
        [systemMessage, ...contextMessages],
        {
          model: modelToUse, // 選択されたモデルまたはデフォルトモデルを使用
          max_tokens: 30, // 出力長を制限
          temperature: 0.3, // 創造性を下げ、タイトルをより正確に
          stream: false, // ストリームレスポンスを使用しない
        },
      )) as ChatResponse;

      // 生成されたタイトルを抽出
      let title = response.choices[0].message.content.trim();

      // タイトルが15文字を超えないようにする
      if (title.length > 15) {
        title = title.substring(0, 15);
      }

      logger.info("Generated title", { title });

      // パフォーマンスモニタリング終了
      const duration = performanceMonitor.endMeasure("generateSessionTitle");
      if (duration) {
        performanceMonitor.logApiCall(
          "generateSessionTitle",
          duration,
          "success",
        );
      }

      return title;
    } catch (error) {
      // パフォーマンスモニタリング終了（エラーの場合）
      const duration = performanceMonitor.endMeasure("generateSessionTitle");
      if (duration) {
        performanceMonitor.logApiCall(
          "generateSessionTitle",
          duration,
          "error",
        );
      }

      // エラー処理
      const errorService = serviceFactory.getErrorService();
      const appError = errorService.handleError(
        error,
        "ApiService.generateSessionTitle",
      );

      logger.warn("Using default title due to error", { error: appError });
      return `Chat ${new Date().toLocaleString()}`;
    }
  }

  /**
   * プラグイン一覧を取得
   * @returns プラグイン一覧
   */
  public async fetchPlugins(): Promise<any[]> {
    const logger = serviceFactory.getLogger();
    const errorService = serviceFactory.getErrorService();
    const apiAdapter = serviceFactory.getApiAdapter();

    logger.info("Fetching plugins");

    // プラグインシステムのステータスを確認
    const status = await this.fetchPluginSystemStatus().catch(() => ({ enabled: false }));
    if (!status.enabled) {
      logger.info("Plugin system is disabled. Returning empty list.");
      return [];
    }

    // パフォーマンスモニタリング開始
    performanceMonitor.startMeasure("fetchPlugins");

    try {
      const result = await errorService.withRetry(
        async () => {
          return await apiAdapter.fetchPlugins();
        },
        { source: "ApiService", description: "fetch plugins" },
      );

      // パフォーマンスモニタリング終了
      const duration = performanceMonitor.endMeasure("fetchPlugins");
      if (duration) {
        performanceMonitor.logApiCall("fetchPlugins", duration, "success");
      }

      return result;
    } catch (error) {
      // パフォーマンスモニタリング終了（エラーの場合）
      const duration = performanceMonitor.endMeasure("fetchPlugins");
      if (duration) {
        performanceMonitor.logApiCall("fetchPlugins", duration, "error");
      }

      // エラー処理
      const appError = errorService.handleError(
        error,
        "ApiService.fetchPlugins",
      );

      logger.warn("Error fetching plugins", { error: appError });
      return [];
    }
  }

  /**
   * プラグインシステムのステータスを取得
   * @returns プラグインシステムのステータス
   */
  public async fetchPluginSystemStatus(): Promise<{ enabled: boolean; plugin_count: number; enabled_plugins: number; system_setting: boolean }> {
    const logger = serviceFactory.getLogger();
    const errorService = serviceFactory.getErrorService();
    const apiAdapter = serviceFactory.getApiAdapter();

    logger.info("Fetching plugin system status");

    try {
      const result = await errorService.withRetry(
        async () => {
          return await apiAdapter.fetchPluginSystemStatus();
        },
        { source: "ApiService", description: "fetch plugin system status" },
      );

      return result;
    } catch (error) {
      // エラー処理
      const appError = errorService.handleError(
        error,
        "ApiService.fetchPluginSystemStatus",
      );

      logger.warn("Error fetching plugin system status", { error: appError });
      return { enabled: false, plugin_count: 0, enabled_plugins: 0, system_setting: false };
    }
  }

  /**
   * プラグインを有効化
   * @param pluginId プラグインID
   * @returns 成功したかどうか
   */
  public async enablePlugin(pluginId: string): Promise<boolean> {
    const logger = serviceFactory.getLogger();
    const errorService = serviceFactory.getErrorService();
    const apiAdapter = serviceFactory.getApiAdapter();

    logger.info("Enabling plugin", { pluginId });

    try {
      const result = await errorService.withRetry(
        async () => {
          return await apiAdapter.enablePlugin(pluginId);
        },
        { source: "ApiService", description: "enable plugin" },
      );

      return result;
    } catch (error) {
      // エラー処理
      const appError = errorService.handleError(
        error,
        "ApiService.enablePlugin",
      );

      logger.warn("Error enabling plugin", { pluginId, error: appError });
      return false;
    }
  }

  /**
   * プラグインを無効化
   * @param pluginId プラグインID
   * @returns 成功したかどうか
   */
  public async disablePlugin(pluginId: string): Promise<boolean> {
    const logger = serviceFactory.getLogger();
    const errorService = serviceFactory.getErrorService();
    const apiAdapter = serviceFactory.getApiAdapter();

    logger.info("Disabling plugin", { pluginId });

    try {
      const result = await errorService.withRetry(
        async () => {
          return await apiAdapter.disablePlugin(pluginId);
        },
        { source: "ApiService", description: "disable plugin" },
      );

      return result;
    } catch (error) {
      // エラー処理
      const appError = errorService.handleError(
        error,
        "ApiService.disablePlugin",
      );

      logger.warn("Error disabling plugin", { pluginId, error: appError });
      return false;
    }
  }

  /**
   * プラグインを設定
   * @param pluginId プラグインID
   * @param config 設定
   * @returns 成功したかどうか
   */
  public async configurePlugin(pluginId: string, config: Record<string, any>): Promise<boolean> {
    const logger = serviceFactory.getLogger();
    const errorService = serviceFactory.getErrorService();
    const apiAdapter = serviceFactory.getApiAdapter();

    logger.info("Configuring plugin", { pluginId });

    try {
      const result = await errorService.withRetry(
        async () => {
          return await apiAdapter.configurePlugin(pluginId, config);
        },
        { source: "ApiService", description: "configure plugin" },
      );

      return result;
    } catch (error) {
      // エラー処理
      const appError = errorService.handleError(
        error,
        "ApiService.configurePlugin",
      );

      logger.warn("Error configuring plugin", { pluginId, error: appError });
      return false;
    }
  }

  /**
   * モデルリストとデフォルトモデルを取得
   * @returns モデルリストとデフォルトモデル
   */
  public async getModelsWithDefault(): Promise<{
    models: Model[];
    default_model: string;
  }> {
    try {
      const models = await this.fetchModels();

      // デフォルトモデルを見つける
      const defaultModel =
        models.find((model) => model.default)?.id ||
        models[0]?.id ||
        "llama3.2:3b";

      return {
        models: models,
        default_model: defaultModel,
      };
    } catch (error) {
      const logger = serviceFactory.getLogger();
      logger.error("Error getting models with default", error);

      // デフォルトモデルを返す
      return {
        models: [
          {
            id: "llama3.2:3b",
            name: "Llama 3.2 (3B)",
            default: true,
            description: "Meta Llama 3.2 model (3B)",
            backend: "ollama",
          },
          {
            id: "mistral",
            name: "Mistral",
            default: false,
            description: "Mistral AI model",
            backend: "ollama",
          },
          {
            id: "command-r7b",
            name: "Command R-7B",
            default: false,
            description: "Command R-7B model",
            backend: "ollama",
          },
        ],
        default_model: "llama3.2:3b",
      };
    }
  }

  /**
   * 特定のバックエンドがサポートするモデルを取得
   * @param backendId バックエンドID
   * @returns そのバックエンドがサポートするモデルリスト
   */
  public async getModelsForBackend(backendId: string): Promise<Model[]> {
    try {
      const models = await this.fetchModels();
      return models.filter((model) => model.backend === backendId);
    } catch (error) {
      const logger = serviceFactory.getLogger();
      logger.error(`Error getting models for backend ${backendId}:`, error);
      return [];
    }
  }
}

/**
 * デフォルトの API サービスインスタンスを作成
 */
export const apiService = ApiService.getInstance();

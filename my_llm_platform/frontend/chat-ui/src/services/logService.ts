/**
 * ログレベルの列挙型
 */
export enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3,
  NONE = 4,
}

/**
 * ログエントリのインターフェース
 */
export interface LogEntry {
  timestamp: string;
  level: LogLevel;
  message: string;
  context?: any;
  source?: string;
}

/**
 * ログサービス設定のインターフェース
 */
export interface LogServiceConfig {
  minLevel: LogLevel;
  maxEntries?: number;
  enableConsole?: boolean;
  enableStorage?: boolean;
  storageKey?: string;
  context?: Record<string, any>;
}

/**
 * ログサービス
 * アプリケーションのログ記録機能を提供
 */
export class LogService {
  private config: LogServiceConfig;
  private logs: LogEntry[] = [];
  private static instance: LogService | null = null;

  /**
   * ログサービスインスタンスを取得（シングルトンパターン）
   */
  public static getInstance(config?: Partial<LogServiceConfig>): LogService {
    if (!LogService.instance) {
      LogService.instance = new LogService(config);
    } else if (config) {
      // 既存のインスタンスの設定を更新
      LogService.instance.updateConfig(config);
    }

    return LogService.instance;
  }

  /**
   * コンストラクタ
   * @param config ログサービス設定
   */
  private constructor(config?: Partial<LogServiceConfig>) {
    // デフォルト設定
    this.config = {
      minLevel: LogLevel.INFO,
      maxEntries: 1000,
      enableConsole: true,
      enableStorage: false,
      storageKey: "app_logs",
      context: {},
    };

    // カスタム設定を適用
    if (config) {
      this.updateConfig(config);
    }

    // ストレージからログを読み込む
    if (this.config.enableStorage) {
      this.loadLogs();
    }

    // 初期化ログを記録
    this.info("LogService initialized", { config: this.config });
  }

  /**
   * ログサービス設定を更新
   * @param config 新しい設定
   */
  public updateConfig(config: Partial<LogServiceConfig>): void {
    this.config = { ...this.config, ...config };

    // ストレージが有効化されている場合、ログを読み込む
    if (this.config.enableStorage && !config.enableStorage) {
      this.loadLogs();
    }

    this.debug("LogService configuration updated", { config: this.config });
  }

  /**
   * デバッグレベルのログを記録
   * @param message ログメッセージ
   * @param context コンテキストデータ
   * @param source ログのソース
   */
  public debug(message: string, context?: any, source?: string): void {
    this.log(LogLevel.DEBUG, message, context, source);
  }

  /**
   * 情報レベルのログを記録
   * @param message ログメッセージ
   * @param context コンテキストデータ
   * @param source ログのソース
   */
  public info(message: string, context?: any, source?: string): void {
    this.log(LogLevel.INFO, message, context, source);
  }

  /**
   * 警告レベルのログを記録
   * @param message ログメッセージ
   * @param context コンテキストデータ
   * @param source ログのソース
   */
  public warn(message: string, context?: any, source?: string): void {
    this.log(LogLevel.WARN, message, context, source);
  }

  /**
   * エラーレベルのログを記録
   * @param message ログメッセージ
   * @param context コンテキストデータ
   * @param source ログのソース
   */
  public error(message: string, context?: any, source?: string): void {
    this.log(LogLevel.ERROR, message, context, source);
  }

  /**
   * ログを記録
   * @param level ログレベル
   * @param message ログメッセージ
   * @param context コンテキストデータ
   * @param source ログのソース
   */
  private log(
    level: LogLevel,
    message: string,
    context?: any,
    source?: string,
  ): void {
    // ログレベルをチェック
    if (level < this.config.minLevel) {
      return;
    }

    // エラーオブジェクトを処理
    if (context instanceof Error) {
      // Error.causeを処理するために型アサーションを使用
      const errorWithCause = context as Error & { cause?: unknown };
      context = {
        name: context.name,
        message: context.message,
        stack: context.stack,
        cause: errorWithCause.cause,
      };
    }

    // グローバルコンテキストをマージ
    const mergedContext = { ...this.config.context, ...context };

    // ログエントリを作成
    const entry: LogEntry = {
      timestamp: new Date().toISOString(),
      level,
      message,
      context: mergedContext,
      source,
    };

    // ログ配列に追加
    this.logs.push(entry);

    // ログエントリ数を制限
    if (this.config.maxEntries && this.logs.length > this.config.maxEntries) {
      this.logs = this.logs.slice(-this.config.maxEntries);
    }

    // コンソールに出力
    if (this.config.enableConsole) {
      this.consoleLog(entry);
    }

    // ストレージに保存
    if (this.config.enableStorage) {
      this.saveLogs();
    }
  }

  /**
   * ログをコンソールに出力
   * @param entry ログエントリ
   */
  private consoleLog(entry: LogEntry): void {
    const timestamp = entry.timestamp.split("T")[1].split(".")[0]; // 時間部分を抽出
    const prefix = `[${timestamp}] [${LogLevel[entry.level]}]${entry.source ? ` [${entry.source}]` : ""}`;

    switch (entry.level) {
      case LogLevel.DEBUG:
        console.debug(prefix, entry.message, entry.context || "");
        break;
      case LogLevel.INFO:
        console.info(prefix, entry.message, entry.context || "");
        break;
      case LogLevel.WARN:
        console.warn(prefix, entry.message, entry.context || "");
        break;
      case LogLevel.ERROR:
        console.error(prefix, entry.message, entry.context || "");
        break;
    }
  }

  /**
   * ログをローカルストレージに保存
   */
  private saveLogs(): void {
    try {
      localStorage.setItem(this.config.storageKey!, JSON.stringify(this.logs));
    } catch (error) {
      console.error("Failed to save logs to storage:", error);
    }
  }

  /**
   * ローカルストレージからログを読み込む
   */
  private loadLogs(): void {
    try {
      const storedLogs = localStorage.getItem(this.config.storageKey!);

      if (storedLogs) {
        this.logs = JSON.parse(storedLogs);
      }
    } catch (error) {
      console.error("Failed to load logs from storage:", error);
    }
  }

  /**
   * すべてのログを取得
   */
  public getLogs(): LogEntry[] {
    return [...this.logs];
  }

  /**
   * 特定のレベルのログを取得
   * @param level ログレベル
   */
  public getLogsByLevel(level: LogLevel): LogEntry[] {
    return this.logs.filter((entry) => entry.level === level);
  }

  /**
   * 特定のソースのログを取得
   * @param source ログのソース
   */
  public getLogsBySource(source: string): LogEntry[] {
    return this.logs.filter((entry) => entry.source === source);
  }

  /**
   * すべてのログをクリア
   */
  public clearLogs(): void {
    this.logs = [];

    if (this.config.enableStorage) {
      localStorage.removeItem(this.config.storageKey!);
    }

    this.info("Logs cleared");
  }

  /**
   * ログをJSON文字列としてエクスポート
   */
  public exportLogs(): string {
    return JSON.stringify(this.logs, null, 2);
  }

  /**
   * ログをインポート
   * @param logsJson ログのJSON文字列
   */
  public importLogs(logsJson: string): void {
    try {
      const importedLogs = JSON.parse(logsJson) as LogEntry[];
      this.logs = importedLogs;

      if (this.config.enableStorage) {
        this.saveLogs();
      }

      this.info("Logs imported", { count: importedLogs.length });
    } catch (error) {
      this.error("Failed to import logs", error);
    }
  }
}

/**
 * デフォルトのログサービスインスタンスを作成
 */
export const defaultLogger = LogService.getInstance({
  minLevel:
    process.env.NODE_ENV === "production" ? LogLevel.INFO : LogLevel.DEBUG,
  enableConsole: true,
  enableStorage: process.env.NODE_ENV === "production",
  context: {
    app: "my-llm-platform",
    version: import.meta.env.VITE_APP_VERSION || "1.0.0",
    environment: process.env.NODE_ENV,
  },
});

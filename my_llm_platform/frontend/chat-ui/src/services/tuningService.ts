import axios from "axios";
import {
  TuningProfile,
  TestProfileRequest,
  TestProfileResponse,
} from "../types/chat";

// TypeScriptの静的型チェック問題を型アサーションで解決
const API_BASE_URL = (import.meta as any).env.VITE_API_BASE_URL || "";

interface ProfilesResponse {
  profiles: TuningProfile[];
}

interface ProfileResponse {
  message: string;
  profile: TuningProfile;
}

interface DeleteResponse {
  message: string;
}

interface InitResponse {
  message: string;
  profiles: string[];
}

/**
 * すべてのチューニングプロファイルを取得
 * @returns プロファイルリストを含むオブジェクト
 */
export const fetchProfiles = async (): Promise<ProfilesResponse> => {
  try {
    const response = await axios.get<ProfilesResponse>(
      `${API_BASE_URL}/tuning/profiles`,
    );
    return response.data;
  } catch (error) {
    console.error("Error fetching tuning profiles:", error);
    throw error;
  }
};

/**
 * 特定のチューニングプロファイルを取得
 * @param name プロファイル名
 * @returns プロファイルの詳細
 */
export const fetchProfile = async (name: string): Promise<TuningProfile> => {
  try {
    const response = await axios.get<TuningProfile>(
      `${API_BASE_URL}/tuning/profiles/${name}`,
    );
    return response.data;
  } catch (error) {
    console.error(`Error fetching tuning profile ${name}:`, error);
    throw error;
  }
};

/**
 * 新しいチューニングプロファイルを作成
 * @param profile プロファイルオブジェクト
 * @returns 作成結果
 */
export const createProfile = async (
  profile: TuningProfile,
): Promise<ProfileResponse> => {
  try {
    const response = await axios.post<ProfileResponse>(
      `${API_BASE_URL}/tuning/profiles`,
      profile,
    );
    return response.data;
  } catch (error) {
    console.error("Error creating tuning profile:", error);
    throw error;
  }
};

/**
 * チューニングプロファイルを更新
 * @param name プロファイル名
 * @param profile 更新後のプロファイルオブジェクト
 * @returns 更新結果
 */
export const updateProfile = async (
  name: string,
  profile: TuningProfile,
): Promise<ProfileResponse> => {
  try {
    const response = await axios.put<ProfileResponse>(
      `${API_BASE_URL}/tuning/profiles/${name}`,
      profile,
    );
    return response.data;
  } catch (error) {
    console.error(`Error updating tuning profile ${name}:`, error);
    throw error;
  }
};

/**
 * チューニングプロファイルを削除
 * @param name プロファイル名
 * @returns 削除結果
 */
export const deleteProfile = async (name: string): Promise<DeleteResponse> => {
  try {
    const response = await axios.delete<DeleteResponse>(
      `${API_BASE_URL}/tuning/profiles/${name}`,
    );
    return response.data;
  } catch (error) {
    console.error(`Error deleting tuning profile ${name}:`, error);
    throw error;
  }
};

/**
 * チューニングプロファイルをテスト
 * @param profileName プロファイル名
 * @param prompt テストプロンプト
 * @param maxTokens 最大生成トークン数
 * @returns テスト結果
 */
export const testProfile = async (
  profileName: string,
  prompt: string,
  maxTokens: number = 128,
): Promise<TestProfileResponse> => {
  try {
    const request: TestProfileRequest = {
      profile_name: profileName,
      prompt,
      max_tokens: maxTokens,
    };

    const response = await axios.post<TestProfileResponse>(
      `${API_BASE_URL}/tuning/test`,
      request,
    );
    return response.data;
  } catch (error) {
    console.error(`Error testing profile ${profileName}:`, error);
    throw error;
  }
};

/**
 * デフォルトのチューニングプロファイルを初期化
 * @returns 初期化結果
 */
export const initDefaultProfiles = async (): Promise<InitResponse> => {
  try {
    const response = await axios.post<InitResponse>(
      `${API_BASE_URL}/tuning/init-default-profiles`,
    );
    return response.data;
  } catch (error) {
    console.error("Error initializing default profiles:", error);
    throw error;
  }
};

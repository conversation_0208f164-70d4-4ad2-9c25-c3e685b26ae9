// APIエンドポイント
export const API_ENDPOINTS = {
  CHAT: {
    COMPLETIONS: '/chat/completions',
    SESSIONS: '/chat/sessions',
    MESSAGES: (sessionId: string) => `/chat/sessions/${sessionId}/messages`,
  },
  MODELS: '/v1/models',
  BACKENDS: '/v1/backends',
  BACKEND_CONFIG: '/v1/backend-config',
  PLUGINS: {
    LIST: '/plugins',
    GET: (pluginId: string) => `/plugins/${pluginId}`,
    ENABLE: (pluginId: string) => `/plugins/${pluginId}/enable`,
    DISABLE: (pluginId: string) => `/plugins/${pluginId}/disable`,
    CONFIGURE: (pluginId: string) => `/plugins/${pluginId}/configure`,
    BY_TYPE: (pluginType: string) => `/plugins/types/${pluginType}`,
    UI_COMPONENTS: '/plugins/ui/components',
    TOOL_DEFINITIONS: '/plugins/tools/definitions',
    STATUS: '/plugins/status',
    DOCS: (pluginId: string) => `/plugins/${pluginId}/docs`
  }
};

// 設定オブジェクトをインポート
import { settings } from './settings';

// API基本設定
export const API_CONFIG = {
  BASE_URL: settings.API_BASE_URL,
  PREFIX: settings.API_PREFIX,
  DEFAULT_MODEL: settings.DEFAULT_MODEL,
  DEFAULT_BACKEND: settings.DEFAULT_BACKEND,
};

// 完全なAPI URLを構築
export const buildApiUrl = (endpoint: string): string => {
  // ユーザー設定のベースURLを確認
  const userBaseUrl = localStorage.getItem("apiBaseUrl");

  // ユーザー設定のベースURLがある場合はそれを使用、なければデフォルト値を使用
  const baseUrl = userBaseUrl || API_CONFIG.BASE_URL;

  // デバッグ用にURLの構築情報をログに出力
  if (userBaseUrl) {
    console.log(`ユーザー設定のベースURLを使用: ${userBaseUrl}`);
  }

  console.log(`API URL構築: ${baseUrl}${API_CONFIG.PREFIX}${endpoint}`);

  return `${baseUrl}${API_CONFIG.PREFIX}${endpoint}`;
};
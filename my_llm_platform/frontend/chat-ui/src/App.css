/* グローバルスタイル - OpenAIスタイル */
:root, .theme-light {
  --primary-color: #10a37f; /* OpenAIグリーン */
  --secondary-color: #0e906f;
  --accent-color: #1a7f64;
  --background-color: #f7f7f8; /* ライトグレー背景 */
  --card-background: #ffffff;
  --text-color: #343541; /* ダークグレーテキスト */
  --light-text: #8e8ea0; /* ライトグレーテキスト */
  --border-color: #e5e5e5;
  --user-message-bg: #343541; /* ユーザーメッセージダーク背景 */
  --user-message-text: #ffffff; /* ユーザーメッセージホワイトテキスト */
  --assistant-message-bg: #ffffff; /* アシスタントメッセージホワイト背景 */
  --assistant-message-text: #343541; /* アシスタントメッセージダークテキスト */
  --error-color: #ef4146; /* エラーレッド */
  --success-color: #10a37f; /* 成功グリーン */
  --sidebar-color: #202123; /* サイドバーダーク */
  --sidebar-text: #ffffff; /* サイドバーテキスト */
  --sidebar-hover: #2a2b32; /* サイドバーホバー */
  --code-bg: #f2f2f2; /* コードブロック背景 */
  --code-text: #343541; /* コードブロックテキスト */
  --inline-code-bg: rgba(0, 0, 0, 0.05); /* インラインコード背景 */
}

/* ダークモード変数 */
.theme-dark {
  --background-color: #343541;
  --card-background: #444654;
  --text-color: #ffffff;
  --light-text: #c5c5d2;
  --border-color: #565869;
  --user-message-bg: #343541;
  --user-message-text: #ffffff;
  --assistant-message-bg: #444654;
  --assistant-message-text: #ffffff;
  --code-bg: #1e1e1e;
  --code-text: #e6e6e6;
  --inline-code-bg: rgba(255, 255, 255, 0.1);
}

/* 系统主题检测 */
@media (prefers-color-scheme: dark) {
  :root:not(.theme-light):not(.theme-dark) {
    --background-color: #343541;
    --card-background: #444654;
    --text-color: #ffffff;
    --light-text: #c5c5d2;
    --border-color: #565869;
    --user-message-bg: #343541;
    --user-message-text: #ffffff;
    --assistant-message-bg: #444654;
    --assistant-message-text: #ffffff;
    --code-bg: #1e1e1e;
    --code-text: #e6e6e6;
    --inline-code-bg: rgba(255, 255, 255, 0.1);
  }
}

body {
  font-family: 'Söhne', 'Söhne Mono', ui-sans-serif, system-ui, -apple-system, 'Segoe UI', Roboto, Ubuntu, Cantarell, 'Noto Sans', sans-serif;
  margin: 0;
  padding: 0;
  background-color: var(--background-color);
  color: var(--text-color);
  line-height: 1.5;
}

.App {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

.app-container {
  display: flex;
  height: 100vh;
  width: 100%;
  overflow: hidden;
}

.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  position: relative;
  background-color: var(--background-color);
  transition: margin-left 0.3s ease;
  margin-left: 260px; /* 与侧边栏宽度相同 */
}

.main-content.sidebar-collapsed {
  margin-left: 60px;
}

/* 侧边栏样式 */
.sidebar {
  width: 260px;
  height: 100vh;
  background-color: var(--sidebar-color);
  color: var(--sidebar-text);
  display: flex;
  flex-direction: column;
  position: fixed;
  left: 0;
  top: 0;
  z-index: 100;
  transition: width 0.3s ease, transform 0.3s ease;
  overflow-x: hidden;
  border-right: none; /* 移除边框 */
}

.sidebar.collapsed {
  width: 60px;
}

.sidebar-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.75rem 1rem; /* 与中间区域的填充一致 */
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  position: relative;
  height: 48px; /* 与中间区域的高度一致 */
  box-sizing: border-box; /* 确保高度包含填充和边框 */
}

.sidebar-title {
  font-size: 1.2rem;
  font-weight: bold;
  margin: 0 auto;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  text-align: center;
}

.toggle-sidebar-button {
  background: none;
  border: none;
  color: var(--sidebar-text);
  cursor: pointer;
  padding: 0.25rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 0;
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
}

.toggle-sidebar-button:hover {
  background-color: var(--sidebar-hover);
}

.new-chat-button {
  margin: 1rem;
  padding: 0.75rem 1rem;
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 500;
  transition: background-color 0.2s;
}

.new-chat-button:hover {
  background-color: var(--secondary-color);
}

.chat-sessions {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 1rem;
  overflow-y: auto;
  max-height: calc(100vh - 180px); /* 留出空间给头部和底部 */
}

.sessions-title {
  font-size: 0.9rem;
  color: var(--light-text);
  margin-bottom: 0.5rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.sessions-list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.session-item {
  padding: 0.75rem;
  border-radius: 6px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.session-item:hover {
  background-color: var(--sidebar-hover);
}

.session-title {
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.session-date {
  font-size: 0.8rem;
  color: var(--light-text);
  margin-top: 0.25rem;
}

.no-sessions {
  color: var(--light-text);
  font-size: 0.9rem;
  padding: 0.5rem;
  text-align: center;
}

.admin-section {
  padding: 1rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.admin-link {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem;
  border-radius: 6px;
  color: var(--sidebar-text);
  text-decoration: none;
  transition: background-color 0.2s;
}

.admin-link:hover, .admin-link.active {
  background-color: var(--sidebar-hover);
}

/* 聊天界面 - OpenAI风格 */
.chat-interface {
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 100%;
  position: relative;
  background-color: var(--background-color);
  overflow: hidden; /* 防止整体滚动 */
}

/* 新增的聊天内容容器 */
.chat-content {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 48px); /* 减去头部高度 */
  position: relative;
  overflow: hidden;
}

/* 可滚动的消息区域 */
.messages-scroll-area {
  flex: 1;
  overflow-y: auto;
  padding-bottom: 120px; /* 留出足够的空间给底部区域 */
  position: relative; /* 添加相对定位 */
}

/* 固定在底部的输入区域 */
.fixed-bottom-area {
  position: fixed; /* 改为固定定位，确保始终在底部 */
  bottom: 25px; /* 保持原来的位置 */
  left: 260px; /* 与侧边栏宽度相同 */
  right: 0;
  background-color: var(--background-color);
  padding: 10px 0;
  border-top: 1px solid var(--border-color);
  z-index: 100; /* 增加z-index值，确保始终显示在最上层 */
  transition: left 0.3s ease; /* 添加过渡效果 */
  display: flex;
  flex-direction: column;
}

/* 当侧边栏收起时调整输入区域位置 */
.sidebar-collapsed + .main-content .fixed-bottom-area {
  left: 0; /* 改为0，覆盖整个宽度 */
}

/* 底部容器，用于布局设置按钮和输入框 */
.bottom-container {
  display: flex;
  align-items: center;
  max-width: 1000px; /* 增加最大宽度，从800px增加到1000px */
  margin: 0 auto;
  width: 100%;
  padding: 0 1rem;
  justify-content: flex-end; /* 将内容移到右侧 */
}

.chat-header {
  background-color: var(--background-color);
  color: var(--text-color);
  padding: 0.75rem 1rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid var(--border-color);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  height: 48px;
  position: sticky;
  top: 0;
  z-index: 10;
}

/* 模型名称显示 */
.model-name-display {
  font-size: 1rem;
  font-weight: 500;
  color: var(--text-color);
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  background-color: var(--hover-color);
}

.model-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  justify-content: flex-start;
}

/* 用户控制区域 */
.user-controls {
  display: flex;
  align-items: center;
  gap: 3px;
}

.user-avatar-text {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: var(--primary-color);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  font-weight: 500;
  margin-right: 3px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

.model-label {
  font-size: 0.9rem;
  color: var(--light-text);
  background-color: rgba(0, 0, 0, 0.05);
  padding: 0.25rem 0.75rem;
  border-radius: 16px;
}

@media (prefers-color-scheme: dark) {
  .model-label {
    background-color: rgba(255, 255, 255, 0.1);
  }
}

.chat-header h1 {
  margin: 0;
  font-size: 1.5rem;
}



/* 用户设置区域 */
.user-settings-area {
  display: flex;
  align-items: center;
  gap: 10px;
}

/* 用户头像圆圈 */
.user-avatar-circle {
  width: 36px;
  height: 36px;
  border-radius: 18px;
  background-color: var(--primary-color);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 16px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

/* 设置按钮 */
.settings-button {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: var(--primary-color);
  color: white;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  transition: background-color 0.2s, transform 0.2s;
  padding: 0;
}

.settings-button:hover {
  background-color: var(--secondary-color);
  transform: scale(1.05);
}

.settings-button svg {
  width: 24px;
  height: 24px;
}

/* 流式/非流式切换开关 */
.stream-mode-selector {
  margin-top: 10px;
}

.toggle-container {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.toggle-switch {
  position: relative;
  display: inline-block;
  width: 50px;
  height: 24px;
  margin-right: 10px;
}

.toggle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: .4s;
  border-radius: 24px;
}

.toggle-slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 3px;
  bottom: 3px;
  background-color: white;
  transition: .4s;
  border-radius: 50%;
}

input:checked + .toggle-slider {
  background-color: var(--primary-color);
}

input:checked + .toggle-slider:before {
  transform: translateX(26px);
}

.toggle-label {
  font-weight: 500;
}

.mode-description {
  font-size: 0.85rem;
  color: var(--light-text);
  margin-top: 5px;
  line-height: 1.4;
}

/* 用户设置区域 */
.user-settings-area {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

/* 用户头像圆圈 */
.user-avatar-circle {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: var(--primary-color);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 16px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

/* 活动配置文件指示器 */
.active-profile-indicator {
  position: fixed;
  bottom: 20px;
  right: 20px;
  background-color: var(--primary-color);
  color: white;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 0.9rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  z-index: 100;
}

.chat-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  max-width: 1000px; /* 增加最大宽度，从800px增加到1000px */
  margin: 0 auto;
  width: 100%;
}

.messages-container {
  flex: 1;
  overflow-y: auto;
  padding: 0 0 150px 0; /* 增加底部填充，确保有足够的空间给输入框 */
  display: flex;
  flex-direction: column;
  scroll-behavior: smooth;
  position: relative; /* 添加相对定位 */
  margin-bottom: 20px; /* 添加底部边距，增加与输入框的间距 */
}

/* 欢迎消息容器 */
.welcome-message-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 100%;
  max-width: 1000px; /* 增加最大宽度，从800px增加到1000px */
  margin: 0 auto;
  padding-bottom: 20px;
  position: relative; /* 改为相对定位，避免定位问题 */
  margin-top: 30vh; /* 使用视口高度的百分比来定位，更灵活 */
}

/* 欢迎消息样式 */
.welcome-message {
  font-size: 1.5rem;
  font-weight: 500;
  color: var(--text-color);
  text-align: center;
  padding: 1rem 2rem;
  margin-bottom: 1.5rem;
  background-color: transparent;
  border-radius: 10px;
}

/* 欢迎输入框容器 */
.welcome-input-container {
  width: 100%;
  max-width: 600px;
  margin: 0 auto;
}

/* 当没有消息时也显示底部输入框，并居中显示 */
.messages-container:empty ~ .fixed-bottom-area {
  display: flex; /* 始终显示输入框 */
  bottom: 40%; /* 将输入框向上移动，与欢迎信息协调 */
}

/* 当有消息时输入框回到底部 */
.chat-interface:has(.messages-list) .fixed-bottom-area {
  bottom: 25px; /* 回到原来的底部位置 */
}

.message-list {
  flex: 1;
  display: flex;
  flex-direction: column;
  width: 100%;
}

.message {
  padding: 1rem 1.5rem;
  width: 100%;
  animation: fadeIn 0.3s ease-in-out;
  display: flex;
  border-bottom: 1px solid var(--border-color);
  border-radius: 10px; /* 增加圆角到10px */
  margin: 2px auto; /* 添加一点间距，让圆角更明显，并居中 */
  max-width: 700px; /* 限制最大宽度 */
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.user-message {
  background-color: var(--user-message-bg);
  color: var(--user-message-text);
}

.assistant-message {
  background-color: var(--assistant-message-bg);
  color: var(--assistant-message-text);
}

/* 代码块样式 */
.message-content pre {
  background-color: var(--code-bg);
  border-radius: 6px;
  padding: 1rem;
  margin: 0.5rem 0;
  overflow-x: auto;
  color: var(--code-text);
  font-size: 0.9rem;
  position: relative;
}

.message-content pre::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  background-color: rgba(0, 0, 0, 0.1);
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
  border-bottom-left-radius: 6px;
  opacity: 0.7;
}

.message-content code {
  font-family: 'Söhne Mono', Monaco, 'Andale Mono', 'Ubuntu Mono', monospace;
  font-size: 0.9rem;
  background-color: var(--inline-code-bg);
  padding: 0.2rem 0.4rem;
  border-radius: 3px;
  color: var(--text-color);
}

.message-content p {
  margin: 0.5rem 0;
  line-height: 1.6;
}

.message-content ul, .message-content ol {
  margin: 0.5rem 0;
  padding-left: 1.5rem;
}

.message-content li {
  margin: 0.25rem 0;
}

.message-content a {
  color: var(--primary-color);
  text-decoration: none;
}

.message-content a:hover {
  text-decoration: underline;
}

.message-content blockquote {
  border-left: 3px solid var(--border-color);
  margin: 0.5rem 0;
  padding-left: 1rem;
  color: var(--light-text);
}

.message-content table {
  border-collapse: collapse;
  margin: 0.5rem 0;
  width: 100%;
}

.message-content th, .message-content td {
  border: 1px solid var(--border-color);
  padding: 0.5rem;
  text-align: left;
}

.message-content th {
  background-color: rgba(0, 0, 0, 0.05);
}

/* 暗色模式下的代码块 */
@media (prefers-color-scheme: dark) {
  .message-content pre {
    background-color: #1e1e1e;
  }

  .message-content code {
    background-color: rgba(255, 255, 255, 0.1);
  }

  .user-message {
    color: white;
  }

  /* 设置界面深色模式 */
  .settings-modal {
    background-color: #1e1e1e;
    color: #e0e0e0;
  }

  .settings-sidebar {
    background-color: #2a2a2a;
  }

  .settings-sidebar li {
    border-bottom-color: #444;
    color: #e0e0e0;
  }

  .settings-sidebar li:hover {
    background-color: #3a3a3a;
  }

  .settings-sidebar li.active {
    background-color: var(--primary-color);
    color: white;
    border-left-color: #0a7d62;
  }

  .settings-panel {
    background-color: #444654;
    color: #ffffff;
  }

  .settings-panel h3 {
    color: #bdbdbd;
  }

  .settings-footer {
    background-color: #2a2a2a;
    border-top-color: #444;
  }

  .clear-chat {
    background-color: #3a3a3a;
    color: #bdbdbd;
    border-color: #555;
  }

  .clear-chat:hover {
    background-color: #444;
    color: #e0e0e0;
  }

  .close-settings {
    background-color: var(--primary-color);
    color: white;
  }

  .close-settings:hover {
    background-color: var(--secondary-color);
  }

  .theme-selector button, .language-selector button {
    background-color: #333;
    color: #e0e0e0;
    border-color: #555;
  }

  .theme-selector button:hover, .language-selector button:hover {
    background-color: #444;
  }
}

.message-content-container {
  max-width: 700px;
  margin: 0 auto;
  width: 100%;
  display: flex;
  gap: 1rem;
}

.message-avatar {
  width: 30px;
  height: 30px;
  border-radius: 15px; /* 改为圆形头像 */
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  font-weight: bold;
  font-size: 14px;
}

.user-avatar {
  background-color: var(--user-message-bg);
  color: var(--user-message-text);
}

.assistant-avatar {
  background-color: var(--primary-color);
  color: white;
}

.message-header {
  margin-bottom: 0.5rem;
  font-weight: bold;
}

.message-content {
  line-height: 1.5;
  flex: 1;
  overflow-wrap: break-word;
}

/* ローディングコンテナ */
.loading-container {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 4px 0;
}

/* ローディングテキスト */
.loading-text {
  font-size: 14px;
  color: var(--light-text);
  animation: pulse 1.5s infinite ease-in-out;
}

@keyframes pulse {
  0%, 100% { opacity: 0.6; }
  50% { opacity: 1; }
}

/* 思考プロセス関連のスタイル */
.think-container {
  margin-top: 8px;
}

.think-toggle {
  background-color: var(--background-color);
  border: 1px solid var(--border-color);
  border-radius: 4px;
  padding: 4px 8px;
  font-size: 12px;
  color: var(--light-text);
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 4px;
}

.think-toggle:hover {
  background-color: var(--hover-color, rgba(0, 0, 0, 0.05));
}

.think-toggle.expanded {
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
  border-bottom: none;
}

.think-toggle::before {
  content: '';
  display: inline-block;
  width: 0;
  height: 0;
  border-left: 4px solid transparent;
  border-right: 4px solid transparent;
  border-top: 4px solid var(--light-text);
  transition: transform 0.2s ease;
}

.think-toggle.collapsed::before {
  transform: rotate(-90deg);
}

.think-box {
  border: 1px solid var(--border-color);
  border-radius: 4px;
  border-top-left-radius: 0;
  background-color: var(--code-bg);
  overflow: hidden;
  margin-top: -1px;
}

.think-header {
  background-color: var(--border-color);
  padding: 4px 8px;
  font-size: 12px;
  color: var(--text-color);
  font-weight: 500;
}

.think-body {
  padding: 8px 12px;
  font-size: 14px;
  color: var(--text-color);
  white-space: pre-wrap;
  overflow-x: auto;
}

.message-content p {
  margin: 0.5rem 0;
}

.message-content pre {
  background-color: #2d2d2d;
  border-radius: 4px;
  padding: 1rem;
  overflow-x: auto;
}

.message-content code {
  font-family: 'Fira Code', monospace;
  font-size: 0.9rem;
}

.input-container {
  position: relative;
  padding: 0.75rem 1rem;
  background-color: var(--card-background);
  border: 1px solid var(--border-color);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  border-radius: 10px; /* 添加圆角 */
  flex: 1;
  backdrop-filter: blur(5px); /* 添加模糊效果 */
}

.input-wrapper {
  display: flex;
  flex-direction: column;
  max-width: 1000px; /* 增加最大宽度，从800px增加到1000px */
  margin: 0 auto;
  width: 100%;
  transform: translateY(-5px); /* 上移输入框 */
  order: 1; /* 确保输入框在免责声明之前 */
}

.input-box {
  display: flex;
  gap: 0.5rem;
  width: 100%;
}

.disclaimer-container {
  position: absolute;
  bottom: 10px; /* 从0改为10px，向下移动10px */
  left: 0;
  right: 0;
  padding: 5px 0;
  z-index: 5;
  pointer-events: none; /* 允许点击穿透 */
}

.disclaimer, .ai-disclaimer {
  font-size: 0.75rem;
  color: var(--light-text);
  text-align: center;
  padding: 0 1rem;
  line-height: 1.2;
  max-width: 1000px; /* 增加最大宽度，从800px增加到1000px */
  margin: 10px auto 0; /* 从0 auto改为10px auto 0，增加上边距 */
  width: 100%;
  order: 2; /* 确保免责声明在输入框之后 */
}

.input-box textarea {
  flex: 1;
  padding: 0.75rem 1rem;
  border: 1px solid var(--border-color);
  border-radius: 10px; /* 增加圆角到10px */
  resize: none;
  font-family: inherit;
  font-size: 1rem;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.05);
  min-height: 26px; /* 增加2px高度 */
  max-height: 120px; /* 允许最大高度增加，但不要太大 */
  line-height: 1.5;
  overflow-y: auto; /* 允许垂直滚动 */
  height: auto; /* 自动调整高度 */
  background-color: var(--card-background);
  color: var(--text-color);
}

.input-box button {
  width: 32px;
  height: 32px;
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: 10px; /* 增加圆角到10px */
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.2s, transform 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.input-box button:hover {
  transform: scale(1.05);
  background-color: var(--secondary-color);
}

.input-box button:disabled {
  background-color: var(--border-color);
  cursor: not-allowed;
}

.loading-indicator {
  margin-top: 0.5rem;
  text-align: center;
  color: var(--light-text);
  font-style: italic;
}

.error-message {
  color: var(--error-color);
  margin: 0.5rem 0;
  padding: 0.5rem;
  border: 1px solid var(--error-color);
  border-radius: 4px;
  background-color: rgba(229, 57, 53, 0.1);
}

.empty-messages {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: var(--light-text);
}

/* 模型选择器 */
.model-selector {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  width: 100%;
  max-width: 400px;
}

.model-selector-error {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  width: 100%;
  max-width: 400px;
}

.model-selector-error .error-message {
  color: #ff4d4f;
  background-color: rgba(255, 77, 79, 0.1);
  border: 1px solid #ff4d4f;
  border-radius: 10px;
  padding: 0.75rem 1rem;
  margin-bottom: 1rem;
  font-size: 0.9rem;
}

.model-selector.in-settings {
  max-width: 100%;
}

.model-dropdown, .backend-dropdown {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.model-dropdown label, .backend-dropdown label {
  white-space: nowrap;
  color: white;
}

.select-input {
  flex: 1;
  padding: 0.5rem;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  background-color: white;
}

.default-option {
  font-weight: bold;
}

.advanced-options {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.advanced-toggle {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  text-decoration: underline;
  padding: 0;
  font-size: 0.9rem;
}

.backend-info {
  margin-top: 0.25rem;
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.8);
}

.tuning-toggle {
  padding: 0.5rem 1rem;
  background-color: var(--secondary-color);
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: bold;
  transition: background-color 0.2s;
}

.tuning-toggle:hover {
  background-color: var(--accent-color);
}

/* 调优配置文件面板 */
.tuning-panel {
  flex: 1;
  overflow-y: auto;
  padding: 1rem;
  background-color: var(--card-background);
}

.tuning-profiles {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.tuning-profiles.in-settings {
  max-width: 100%;
  height: 100%;
  overflow-y: auto;
}

.tuning-profiles h2 {
  margin: 0 0 1rem 0;
  color: var(--primary-color);
}

.profiles-container {
  display: flex;
  gap: 2rem;
  height: 100%;
}

.profiles-list {
  width: 300px;
  border-right: 1px solid var(--border-color);
  padding-right: 1rem;
}

.profiles-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.profiles-list h3 {
  margin: 0;
}

.create-btn {
  padding: 0.5rem 1rem;
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.profiles-list ul {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.profiles-list li {
  padding: 0.75rem;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.profiles-list li:hover {
  background-color: var(--background-color);
}

.profiles-list li.selected {
  background-color: var(--user-message-bg);
  border-color: var(--primary-color);
}

.profile-name {
  font-weight: bold;
}

.profile-model {
  font-size: 0.8rem;
  color: var(--light-text);
  margin-top: 0.25rem;
}

.profile-details {
  flex: 1;
}

.profile-details h3 {
  margin-top: 0;
  color: var(--primary-color);
}

.profile-description {
  color: var(--light-text);
  margin-bottom: 1rem;
}

.profile-info {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
}

.info-item {
  display: flex;
  flex-direction: column;
}

.label {
  font-size: 0.8rem;
  color: var(--light-text);
}

.value {
  font-weight: bold;
}

.parameters-list {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.parameter-item {
  padding: 0.75rem;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  background-color: var(--background-color);
}

.param-name {
  font-weight: bold;
}

.param-value {
  margin-top: 0.25rem;
}

.param-description {
  font-size: 0.8rem;
  color: var(--light-text);
  margin-top: 0.25rem;
}

.profile-actions {
  display: flex;
  gap: 1rem;
  margin: 1rem 0;
}

.edit-btn, .delete-btn {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.edit-btn {
  background-color: var(--primary-color);
  color: white;
}

.delete-btn {
  background-color: var(--error-color);
  color: white;
}

.profile-test {
  margin-top: 2rem;
  border-top: 1px solid var(--border-color);
  padding-top: 1rem;
}

.test-input {
  margin: 1rem 0;
}

.test-input textarea {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  resize: vertical;
  min-height: 100px;
  font-family: inherit;
}

.test-btn {
  padding: 0.5rem 1rem;
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.test-btn:disabled {
  background-color: var(--border-color);
  cursor: not-allowed;
}

.test-result {
  margin-top: 1rem;
  padding: 1rem;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  background-color: var(--background-color);
}

.test-result h5 {
  margin-top: 0;
  color: var(--primary-color);
}

.result-prompt, .result-output {
  margin-bottom: 1rem;
}

.result-prompt strong, .result-output strong {
  display: block;
  margin-bottom: 0.5rem;
}

/* 配置文件编辑器 */
.profile-editor {
  width: 100%;
}

.profile-editor h3 {
  margin-top: 0;
  color: var(--primary-color);
}

.form-group {
  margin-bottom: 1rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: bold;
}

.form-group input, .form-group textarea {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  font-family: inherit;
}

.parameter-row {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
  align-items: flex-start;
}

.parameter-row .form-group {
  flex: 1;
  margin-bottom: 0;
}

.remove-btn {
  padding: 0.5rem;
  background-color: var(--error-color);
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  margin-top: 1.5rem;
}

.add-btn {
  padding: 0.5rem 1rem;
  background-color: var(--secondary-color);
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  margin-bottom: 1rem;
}

.form-actions {
  display: flex;
  gap: 1rem;
  margin-top: 2rem;
}

.save-btn, .cancel-btn {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: bold;
}

.save-btn {
  background-color: var(--success-color);
  color: white;
}

.cancel-btn {
  background-color: var(--light-text);
  color: white;
}

/* 设置菜单 */
.settings-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.settings-modal {
  background-color: var(--card-background);
  border-radius: 10px;
  width: 90%;
  max-width: 800px;
  height: 600px; /* 固定高度 */
  display: flex;
  flex-direction: column;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.3);
  animation: modalFadeIn 0.3s ease-out;
  overflow: hidden;
  transition: background-color 0.3s ease;
}

@keyframes modalFadeIn {
  from { opacity: 0; transform: translateY(-20px); }
  to { opacity: 1; transform: translateY(0); }
}

.settings-header {
  padding: 1.25rem 1.5rem;
  border-bottom: 1px solid var(--border-color);
  display: flex;
  justify-content: center; /* 将标题居中 */
  align-items: center;
  background-color: var(--primary-color);
  color: white;
  height: 50px; /* 固定高度 */
  box-sizing: border-box;
  position: relative; /* 添加相对定位，为关闭按钮的绝对定位提供参考点 */
}

.settings-header h2 {
  margin: 0;
  color: white;
  font-size: 1.25rem;
}

.close-btn {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: white;
  opacity: 0.8;
  transition: opacity 0.2s;
}

.close-btn-right {
  position: absolute !important;
  right: 15px !important;
  top: 50% !important;
  transform: translateY(-50%) !important;
}

.close-btn:hover {
  opacity: 1;
}

.settings-content {
  display: flex;
  flex: 1;
  overflow: hidden;
  height: calc(100% - 120px); /* 固定高度，减去头部和底部的高度 */
}

.settings-sidebar {
  width: 200px;
  min-width: 200px; /* 确保最小宽度 */
  border-right: 1px solid var(--border-color);
  overflow-y: auto;
  background-color: var(--sidebar-color); /* 使用与主侧边栏相同的颜色 */
  color: var(--sidebar-text); /* 使用与主侧边栏相同的文本颜色 */
  height: 100%; /* 确保高度填满父容器 */
  transition: background-color 0.3s ease, color 0.3s ease;
}

.settings-sidebar ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.settings-sidebar li {
  padding: 1rem 1.5rem;
  cursor: pointer;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
  font-weight: 500;
  color: var(--sidebar-text);
}

.settings-sidebar li:hover {
  background-color: var(--sidebar-hover);
}

.settings-sidebar li.active {
  background-color: var(--primary-color);
  color: white;
  border-left: 4px solid #0a7d62;
  padding-left: calc(1.5rem - 4px);
}

.settings-panel {
  flex: 1;
  padding: 1.5rem 2rem;
  overflow-y: auto;
  height: 100%; /* 确保高度填满父容器 */
  min-height: 400px; /* 确保最小高度 */
  background-color: var(--card-background);
  color: var(--text-color);
}

.settings-panel h3 {
  margin-top: 0;
  color: var(--primary-color);
}

.settings-footer {
  padding: 1rem 1.5rem;
  border-top: 1px solid var(--border-color);
  display: flex;
  justify-content: flex-end; /* 改为右对齐 */
  background-color: var(--background-color);
  height: 70px; /* 固定高度 */
  box-sizing: border-box;
  transition: background-color 0.3s ease, border-color 0.3s ease;
}

.close-settings {
  padding: 0.75rem 1.5rem;
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.2s;
  display: flex;
  align-items: center; /* 确保文字垂直居中 */
  justify-content: center; /* 确保文字水平居中 */
  height: 40px; /* 设置固定高度 */
  margin-top: 5px; /* 微调位置使其垂直居中 */
}

.close-settings:hover {
  background-color: var(--secondary-color);
}

.clear-chat {
  padding: 0.75rem 1.5rem;
  background-color: #f5f5f5;
  color: #666;
  border: 1px solid #ddd;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s;
}

.clear-chat:hover {
  background-color: #e5e5e5;
  color: #333;
}

/* 语言选择器 */
.language-selector {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
}

.language-selector button {
  padding: 0.5rem 1rem;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  background-color: var(--card-background);
  color: var(--text-color);
  cursor: pointer;
}

.language-selector button.active {
  background-color: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

/* 主题选择器和语言选择器 */
.theme-selector, .language-selector {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
  flex-wrap: wrap; /* 允许按钮换行 */
}

.theme-selector button, .language-selector button {
  padding: 0.75rem 1.25rem;
  border: 1px solid var(--border-color);
  border-radius: 10px; /* 增加圆角到10px */
  background-color: var(--card-background);
  color: var(--text-color);
  cursor: pointer;
  min-width: 100px; /* 确保按钮有足够的宽度 */
  font-size: 1rem;
  font-weight: 500;
  transition: all 0.2s ease;
}

.theme-selector button.active, .language-selector button.active {
  background-color: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  transform: translateY(-1px);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .chat-header {
    padding: 0.75rem;
  }

  .chat-header h1 {
    font-size: 1.2rem;
  }

  .model-selection {
    flex-direction: column;
  }

  .message {
    max-width: 90%;
  }

  .profiles-container {
    flex-direction: column;
  }

  .profiles-list {
    width: 100%;
    border-right: none;
    border-bottom: 1px solid var(--border-color);
    padding-right: 0;
    padding-bottom: 1rem;
    margin-bottom: 1rem;
  }

  .parameter-row {
    flex-direction: column;
    gap: 0.5rem;
  }

  .remove-btn {
    margin-top: 0;
  }

  .settings-content {
    flex-direction: column;
  }

  .settings-sidebar {
    width: 100%;
    border-right: none;
    border-bottom: 1px solid var(--border-color);
  }

  .settings-sidebar ul {
    display: flex;
    overflow-x: auto;
  }

  .settings-sidebar li {
    white-space: nowrap;
    border-bottom: none;
    border-right: 1px solid var(--border-color);
  }
}

/* 添加选择器样式 */
.select-container {
  margin-bottom: 1rem;
}

.select-container label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: var(--text-color);
}

.select-input {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 1px solid var(--border-color);
  border-radius: 10px; /* 增加圆角到10px */
  background-color: var(--card-background);
  color: var(--text-color);
  font-size: 1rem;
  margin-top: 0.25rem;
  appearance: none; /* 移除默认样式 */
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 1rem center;
  background-size: 1em;
}

.select-input option {
  background-color: var(--card-background);
  color: var(--text-color);
}

/* 配置文件选择器 */
.profile-select {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 1px solid var(--border-color);
  border-radius: 10px; /* 增加圆角到10px */
  background-color: var(--card-background);
  color: var(--text-color);
  font-size: 1rem;
  margin-top: 0.25rem;
  appearance: none; /* 移除默认样式 */
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 1rem center;
  background-size: 1em;
}

.profile-options {
  display: flex;
  gap: 1rem;
  margin-bottom: 1.5rem;
  align-items: center;
}

.create-profile-button {
  padding: 0.75rem 1.5rem;
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: 10px; /* 增加圆角到10px */
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.2s, transform 0.2s;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  white-space: nowrap;
}

.create-profile-button:hover {
  background-color: var(--secondary-color);
  transform: scale(1.02);
}

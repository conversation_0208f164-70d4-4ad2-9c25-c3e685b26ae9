export interface ToolCall {
  id: string;
  type: string;
  function: FunctionCall;
}

export interface FunctionCall {
  name: string;
  arguments: string;
}

export interface Message {
  role: "user" | "assistant" | "system" | "tool" | "function";
  content: string;
  name?: string;
  tool_call_id?: string;
  tool_calls?: ToolCall[];
  function_call?: FunctionCall;
}

export interface Model {
  id: string;
  name?: string;
  object?: string;
  created?: number | null;
  owned_by?: string;
  default?: boolean;
  backend?: string;
  description?: string;
}

export interface Backend {
  id: string;
  name: string;
  description: string;
  supports_streaming: boolean;
  endpoint?: string;
  available?: boolean;
}

export interface BackendsResponse {
  default_backend: string;
  backends: Backend[];
}

export interface TuningParameter {
  name: string;
  value: number | string | boolean;
  description?: string;
}

export interface TuningProfile {
  id: string;
  name: string;
  description: string;
  model: string;
  backend: string | null;
  parameters: TuningParameter[];
}

export interface ChatOptions {
  model?: string;
  backend?: string | null;
  temperature?: number;
  top_p?: number;
  top_k?: number;
  max_tokens?: number;
  stream?: boolean;
  backend_options?: Record<string, unknown>;
  plugins?: string[];
  use_tools?: boolean;
  [key: string]: string | number | boolean | Record<string, unknown> | undefined | null | string[];
}

export interface ChatResponse {
  choices: {
    message: {
      role: string;
      content: string;
      tool_calls?: ToolCall[];
      function_call?: FunctionCall;
    };
    finish_reason: string;
  }[];
  created: number | null;
  model: string;
}

export interface StreamChunk {
  // OpenAIフォーマット
  choices?: {
    delta?: {
      content?: string;
      role?: string;
      tool_calls?: ToolCall[];
      function_call?: FunctionCall;
    };
    index?: number;
    finish_reason?: string | null;
  }[];

  // 直接コンテンツフォーマット
  content?: string;

  // Ollamaフォーマット
  message?: {
    content: string;
    role?: string;
  };
  done?: boolean;

  // エラー情報
  error?: {
    message: string;
    type: string;
    code?: string;
    param?: string;
    details?: any;
  };

  // 他のプロパティを許可
  [key: string]: string | number | boolean | object | undefined | null;
}

export interface TestProfileRequest {
  profile_name: string;
  prompt: string;
  max_tokens?: number;
}

export interface TestProfileResponse {
  profile: TuningProfile;
  prompt: string;
  output: string;
  parameters_used: Record<string, string | number | boolean | null>;
}

export interface ChatHistoryItem {
  id: string;
  title: string;
  created_at: string;
  updated_at: string;
  message_count?: number;
  last_message?: string;
}

export interface ChatMessage {
  id: string;
  session_id: string;
  role: string;
  content: string;
  created_at: string;
  metadata?: Record<string, string | number | boolean | null>;
}

export interface ChatSession {
  id: string;
  title: string;
  created_at: string;
  updated_at: string;
  messages?: ChatMessage[];
}

export interface Plugin {
  id: string;
  name: string;
  description: string;
  type: string;
  version: string;
  author: string;
  config: {
    enabled: boolean;
    priority: number;
    config: Record<string, any>;
  };
  dependencies: string[];
  tags: string[];
}

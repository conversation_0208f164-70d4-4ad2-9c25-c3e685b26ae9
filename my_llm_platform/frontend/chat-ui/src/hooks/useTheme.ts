import { useState, useEffect } from "react";

type Theme = "light" | "dark" | "system";

export const useTheme = () => {
  const [theme, setTheme] = useState<Theme>(() => {
    // ローカルストレージからテーマ設定を取得し、存在しない場合はシステムデフォルトを使用
    const savedTheme = localStorage.getItem("theme") as Theme | null;
    return savedTheme || "system";
  });

  // テーマをドキュメントに適用
  useEffect(() => {
    const applyTheme = (newTheme: Theme) => {
      const root = document.documentElement;

      // すべてのテーマクラスを削除
      root.classList.remove("theme-light", "theme-dark");

      if (newTheme === "system") {
        // システムテーマを検出
        const prefersDark = window.matchMedia(
          "(prefers-color-scheme: dark)",
        ).matches;
        root.classList.add(prefersDark ? "theme-dark" : "theme-light");
      } else {
        // 指定されたテーマを適用
        root.classList.add(`theme-${newTheme}`);
      }
    };

    // 現在のテーマを適用
    applyTheme(theme);

    // ローカルストレージに保存
    localStorage.setItem("theme", theme);

    // システムテーマの変化を監視
    const mediaQuery = window.matchMedia("(prefers-color-scheme: dark)");
    const handleChange = () => {
      if (theme === "system") {
        applyTheme("system");
      }
    };

    mediaQuery.addEventListener("change", handleChange);
    return () => mediaQuery.removeEventListener("change", handleChange);
  }, [theme]);

  return { theme, setTheme };
};

/* eslint-disable no-undef */
import { lazy, Suspense, useState, useEffect } from "react";
import {
  createBrowserRouter,
  RouterProvider,
  Navigate,
  Outlet,
} from "react-router-dom";
import { useAuthStore } from "./store/authStore";
import { Sidebar } from "./components/Sidebar";
import { serviceFactory } from "./services/serviceFactory";

// React.lazy を使用してコンポーネントを遅延読み込み
const ChatInterfaceV2 = lazy(() => import("./components/ChatInterfaceV2"));
const AdminPanel = lazy(() =>
  import("./components/AdminPanel").then((module) => ({
    default: module.AdminPanel,
  })),
);
const Login = lazy(() => import("./components/Login"));

// ローディングインジケーターコンポーネント
const LoadingFallback = () => (
  <div className="loading-container">
    <div className="loading-spinner"></div>
    <p>Loading...</p>
  </div>
);

// レイアウトコンポーネント
const Layout = () => {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);

  const toggleSidebar = () => {
    setSidebarCollapsed(!sidebarCollapsed);
  };

  return (
    <div className="app-container">
      <Sidebar isCollapsed={sidebarCollapsed} toggleSidebar={toggleSidebar} />
      <div
        className={`main-content ${sidebarCollapsed ? "sidebar-collapsed" : ""}`}
      >
        <Suspense fallback={<LoadingFallback />}>
          <Outlet />
        </Suspense>
      </div>
    </div>
  );
};

// ルート設定を作成
export const createAppRouter = (
  isAuthenticated: boolean,
  userRole: string | undefined,
) => {
  return createBrowserRouter([
    {
      path: "/",
      element: <Layout />,
      children: [
        {
          index: true,
          element: <Navigate to="/chat" replace />,
        },
        {
          path: "chat",
          element: isAuthenticated ? (
            <Suspense fallback={<LoadingFallback />}>
              <ChatInterfaceV2 />
            </Suspense>
          ) : (
            <Suspense fallback={<LoadingFallback />}>
              <Login />
            </Suspense>
          ),
        },
        ...(userRole === "admin"
          ? [
              {
                path: "admin",
                element: (
                  <Suspense fallback={<LoadingFallback />}>
                    <AdminPanel />
                  </Suspense>
                ),
              },
            ]
          : []),
      ],
    },
  ]);
};

// ルーティングコンポーネント
export const AppRouter = () => {
  const { isAuthenticated, user } = useAuthStore();
  const logger = serviceFactory.getLogger();

  // アプリケーションを初期化
  useEffect(() => {
    logger.info("Initializing application");

    // パフォーマンス監視を追加
    if (process.env.NODE_ENV === "development") {
      // 初期ロード時間を記録
      // performance API が利用可能であることを確認
      if (typeof performance !== 'undefined') {
        const loadTime = performance.now();
        logger.info(`Initial app load time: ${loadTime.toFixed(2)}ms`);
      } else {
        logger.warn('Performance API not available');
      }

      // ルート変更のパフォーマンスを監視
      if (typeof PerformanceObserver !== "undefined") {
        try {
          const observer = new PerformanceObserver((list) => {
            list.getEntries().forEach((entry) => {
              logger.info(
                `Navigation timing: ${entry.name} - ${entry.duration.toFixed(2)}ms`,
              );
            });
          });

          observer.observe({ entryTypes: ["navigation", "resource"] });
        } catch (error) {
          // PerformanceObserver がサポートされていない、または失敗した場合の警告
          logger.warn("PerformanceObserver not supported or failed:", error);
        }
      }
    }
  }, [logger]);

  const router = createAppRouter(isAuthenticated, user?.role);
  return <RouterProvider router={router} />;
};

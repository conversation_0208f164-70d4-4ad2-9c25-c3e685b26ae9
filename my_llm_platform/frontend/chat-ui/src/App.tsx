import { useAuthStore } from "./store/authStore";
import Login from "./components/Login";
import "./App.css";
import { AppRouter } from "./router";
import { serviceFactory } from "./services/serviceFactory";

function App() {
  const { isAuthenticated } = useAuthStore();
  const logger = serviceFactory.getLogger();

  logger.debug("App rendered, authentication status:", isAuthenticated);

  // ユーザーが認証されていない場合、ログインページを表示
  if (!isAuthenticated) {
    return <Login />;
  }

  // 認証済みの場合、AppRouterを使用
  return <AppRouter />;
}

export default App;

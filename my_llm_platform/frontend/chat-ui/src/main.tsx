import React from "react";
import ReactDOM from "react-dom/client";
import App from "./App";
import "./index.css";
import "./i18n";
import { StagewiseToolbar } from '@stagewise/toolbar-react';

ReactDOM.createRoot(document.getElementById("root") as HTMLElement).render(
  <React.StrictMode>
    <App />
  </React.StrictMode>,
);

if (import.meta.env.MODE === 'development') {
  const toolbarConfig = { plugins: [] };
  const toolbarDiv = document.createElement('div');
  toolbarDiv.id = 'stagewise-toolbar-root';
  document.body.appendChild(toolbarDiv);
  ReactDOM.createRoot(toolbarDiv).render(
    <StagewiseToolbar config={toolbarConfig} />
  );
}

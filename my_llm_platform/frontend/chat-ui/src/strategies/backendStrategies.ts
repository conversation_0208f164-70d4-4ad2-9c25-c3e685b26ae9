import { Message, ChatOptions, ChatResponse, StreamChunk } from "../types/chat";
import { LogService } from "../services/logService";
import { ApiAdapter } from "../adapters/apiAdapter";

/**
 * バックエンド戦略インターフェース
 * 異なるバックエンドの処理戦略を定義
 */
export interface BackendStrategy {
  /**
   * 戦略名を取得
   */
  getName(): string;

  /**
   * チャットリクエストパラメータを準備
   * @param messages メッセージ配列
   * @param options オプション
   */
  prepareChatRequest(
    _messages: Message[],
    _options: ChatOptions,
  ): { messages: Message[]; options: ChatOptions };

  /**
   * テキスト生成リクエストパラメータを準備
   * @param prompt プロンプト
   * @param options オプション
   */
  prepareCompletionRequest(
    _prompt: string,
    _options: ChatOptions,
  ): { prompt: string; options: ChatOptions };

  /**
   * ストリームレスポンスを処理
   * @param response レスポンスオブジェクト
   * @param onChunk チャンク処理コールバック
   * @param onComplete 完了コールバック
   * @param onError エラーコールバック
   */
  handleStreamResponse(
    _response: Response,
    _onChunk: (_chunk: StreamChunk) => void,
    _onComplete: () => void,
    _onError: (_error: Error) => void,
  ): void;

  /**
   * 非ストリームレスポンスを処理
   * @param response レスポンスオブジェクト
   */
  handleResponse(_response: ChatResponse): ChatResponse;

  /**
   * 特定の機能をサポートしているか確認
   * @param feature 機能名
   */
  supportsFeature(_feature: string): boolean;
}

/**
 * 基本バックエンド戦略
 * 一般的なバックエンド処理機能を提供
 */
export abstract class BaseBackendStrategy implements BackendStrategy {
  protected logger: LogService;

  constructor(logger: LogService) {
    this.logger = logger;
  }

  abstract getName(): string;

  prepareChatRequest(
    messages: Message[],
    options: ChatOptions,
  ): { messages: Message[]; options: ChatOptions } {
    // デフォルト実装、元のメッセージとオプションをそのまま返す
    return { messages, options };
  }

  prepareCompletionRequest(
    prompt: string,
    options: ChatOptions,
  ): { prompt: string; options: ChatOptions } {
    // デフォルト実装、元のプロンプトとオプションをそのまま返す
    return { prompt, options };
  }

  handleStreamResponse(
    response: Response,
    onChunk: (_chunk: StreamChunk) => void,
    onComplete: () => void,
    onError: (_error: Error) => void,
  ): void {
    // デフォルトのストリーム処理実装
    const reader = response.body?.getReader();
    const decoder = new TextDecoder();

    if (!reader) {
      onError(new Error("Response body is null or undefined"));
      return;
    }

    const processStream = async () => {
      try {
        while (true) {
          const { done, value } = await reader.read();

          if (done) {
            onComplete();
            break;
          }

          const chunk = decoder.decode(value, { stream: true });

          // SSE形式のレスポンスを処理
          const lines = chunk.split("\n").filter((line) => line.trim() !== "");

          for (const line of lines) {
            if (line.startsWith("data: ")) {
              const data = line.slice(6);

              if (data === "[DONE]") {
                onComplete();
                continue;
              }

              try {
                // エラー情報を含む可能性があるかチェック
                if (data.includes('"error"')) {
                  this.logger.warn("ストリームデータにエラー情報が含まれています", { data });

                  try {
                    const errorObj = JSON.parse(data);
                    if (errorObj.error) {
                      // ユーザーフレンドリーなエラーメッセージを作成
                      const errorMessage = errorObj.error.message || "不明なエラーが発生しました";
                      const errorType = errorObj.error.type || "server_error";

                      // エラー情報を含む特殊なチャンクを作成
                      const errorChunk: StreamChunk = {
                        choices: [{
                          delta: { content: `エラー: ${errorMessage}` },
                          index: 0,
                          finish_reason: "error"
                        }],
                        error: {
                          message: errorMessage,
                          type: errorType,
                          details: errorObj.error
                        }
                      };

                      this.logger.error("フォーマットされたエラーチャンク", { errorChunk });
                      onChunk(errorChunk);
                      onError(new Error(errorMessage));
                      return;
                    }
                  } catch (parseErr) {
                    this.logger.error("エラーJSONの解析に失敗しました", { parseErr, data });
                  }
                }

                // 正常なデータ処理
                const parsedData = JSON.parse(data);
                onChunk(parsedData);
              } catch (e) {
                this.logger.warn("ストリームチャンクの解析に失敗しました", {
                  data,
                  error: e,
                });

                // ユーザーフレンドリーなエラーメッセージを含むチャンクを作成
                const errorChunk: StreamChunk = {
                  choices: [{
                    delta: { content: `レスポンスの解析エラー: ${e instanceof Error ? e.message : '不明なエラー'}` },
                    index: 0,
                    finish_reason: "error"
                  }],
                  error: {
                    message: e instanceof Error ? e.message : '不明なエラー',
                    type: "parsing_error"
                  }
                };

                onChunk(errorChunk);
              }
            }
          }
        }
      } catch (error) {
        this.logger.error("Error processing stream", error);
        onError(error instanceof Error ? error : new Error(String(error)));
      }
    };

    processStream();
  }

  handleResponse(response: ChatResponse): ChatResponse {
    // デフォルト実装、元のレスポンスをそのまま返す
    return response;
  }

  supportsFeature(feature: string): boolean {
    // デフォルト実装、基本機能をサポート
    switch (feature.toLowerCase()) {
      case "streaming":
      case "chat":
      case "completion":
        return true;
      default:
        return false;
    }
  }
}

/**
 * Ollama バックエンド戦略
 * Ollama 特定のリクエストとレスポンス形式を処理
 */
export class OllamaBackendStrategy extends BaseBackendStrategy {
  getName(): string {
    return "ollama";
  }

  prepareChatRequest(
    messages: Message[],
    options: ChatOptions,
  ): { messages: Message[]; options: ChatOptions } {
    // Ollama 特定のメッセージ処理
    const processedMessages = [...messages];
    const processedOptions = { ...options };

    // オプションに Ollama 特定のパラメータを含めることを確認
    if (!processedOptions.backend_options) {
      processedOptions.backend_options = {};
    }

    // Ollama 特定のオプションを追加
    processedOptions.backend_options.format = "json";

    this.logger.debug("Prepared Ollama chat request", {
      messagesCount: processedMessages.length,
      options: processedOptions,
    });

    return { messages: processedMessages, options: processedOptions };
  }

  prepareCompletionRequest(
    prompt: string,
    options: ChatOptions,
  ): { prompt: string; options: ChatOptions } {
    // Ollama 特定のプロンプト処理
    const processedOptions = { ...options };

    // オプションに Ollama 特定のパラメータを含めることを確認
    if (!processedOptions.backend_options) {
      processedOptions.backend_options = {};
    }

    // Ollama 特定のオプションを追加
    processedOptions.backend_options.format = "json";

    this.logger.debug("Prepared Ollama completion request", {
      promptLength: prompt.length,
      options: processedOptions,
    });

    return { prompt, options: processedOptions };
  }

  handleStreamResponse(
    response: Response,
    onChunk: (_chunk: StreamChunk) => void,
    onComplete: () => void,
    onError: (_error: Error) => void,
  ): void {
    // Ollama 特定のストリーム処理
    const reader = response.body?.getReader();
    const decoder = new TextDecoder();

    if (!reader) {
      onError(new Error("Response body is null or undefined"));
      return;
    }

    const processStream = async () => {
      try {
        let buffer = "";

        while (true) {
          const { done, value } = await reader.read();

          if (done) {
            onComplete();
            break;
          }

          const chunk = decoder.decode(value, { stream: true });
          buffer += chunk;

          // 完全な行を処理
          const lines = buffer.split("\n");
          buffer = lines.pop() || ""; // 最後の不完全な行を保持

          for (const line of lines) {
            if (line.trim() === "") continue;

            // SSE形式のレスポンスを処理
            if (line.startsWith("data: ")) {
              const data = line.substring(6).trim();

              // [DONE]シグナルの処理
              if (data === "[DONE]") {
                this.logger.info("Received [DONE] signal");
                onComplete();
                continue;
              }

              try {
                // JSONデータの処理
                const parsedData = JSON.parse(data);
                this.logger.debug("Parsed SSE JSON data", { parsedData });

                // エラー情報を含むかチェック
                if (parsedData.error) {
                  const errorMessage = typeof parsedData.error === 'string'
                    ? parsedData.error
                    : (parsedData.error.message || "不明なエラーが発生しました");

                  const errorChunk: StreamChunk = {
                    choices: [{
                      delta: { content: `エラー: ${errorMessage}` },
                      index: 0,
                      finish_reason: "error"
                    }],
                    error: {
                      message: errorMessage,
                      type: "server_error",
                      details: parsedData.error
                    }
                  };

                  onChunk(errorChunk);
                  continue;
                }

                // 標準形式に変換
                const standardChunk: StreamChunk = {};

                if (parsedData.choices && parsedData.choices.length > 0) {
                  // すでにOpenAI形式の場合はそのまま使用
                  standardChunk.choices = parsedData.choices;
                  standardChunk.id = parsedData.id;
                  standardChunk.model = parsedData.model;
                  standardChunk.created = parsedData.created;
                } else if (parsedData.message && parsedData.message.content) {
                  // Ollama形式の場合は変換
                  standardChunk.content = parsedData.message.content;
                  standardChunk.choices = [
                    {
                      delta: { content: parsedData.message.content },
                      index: 0,
                      finish_reason: parsedData.done ? "stop" : null,
                    },
                  ];
                } else if (parsedData.response) {
                  // 別のOllama形式の場合も変換
                  standardChunk.content = parsedData.response;
                  standardChunk.choices = [
                    {
                      delta: { content: parsedData.response },
                      index: 0,
                      finish_reason: parsedData.done ? "stop" : null,
                    },
                  ];
                }

                // 参照用に元データを追加
                standardChunk.raw = parsedData;

                onChunk(standardChunk);

                if (parsedData.done) {
                  onComplete();
                }
              } catch (e) {
                this.logger.warn("SSEデータの解析に失敗しました", {
                  data,
                  error: e,
                });

                const errorChunk: StreamChunk = {
                  choices: [{
                    delta: { content: `SSEデータの解析エラー: ${e instanceof Error ? e.message : '不明なエラー'}` },
                    index: 0,
                    finish_reason: "error"
                  }],
                  error: {
                    message: e instanceof Error ? e.message : '不明なエラー',
                    type: "parsing_error"
                  }
                };

                onChunk(errorChunk);
              }
            } else {
              // 直接JSONとして解析を試みる（Ollamaの一部のレスポンス形式）
              try {
                const parsedData = JSON.parse(line);
                this.logger.debug("Parsed direct JSON line", { parsedData });

                // エラー情報を含むかチェック
                if (parsedData.error) {
                  const errorMessage = typeof parsedData.error === 'string'
                    ? parsedData.error
                    : (parsedData.error.message || "不明なエラーが発生しました");

                  const errorChunk: StreamChunk = {
                    choices: [{
                      delta: { content: `エラー: ${errorMessage}` },
                      index: 0,
                      finish_reason: "error"
                    }],
                    error: {
                      message: errorMessage,
                      type: "server_error",
                      details: parsedData.error
                    }
                  };

                  onChunk(errorChunk);
                  continue;
                }

                // 標準形式に変換
                const standardChunk: StreamChunk = {};

                if (parsedData.message && parsedData.message.content) {
                  standardChunk.content = parsedData.message.content;
                  standardChunk.choices = [
                    {
                      delta: { content: parsedData.message.content },
                      index: 0,
                      finish_reason: parsedData.done ? "stop" : null,
                    },
                  ];
                } else if (parsedData.response) {
                  standardChunk.content = parsedData.response;
                  standardChunk.choices = [
                    {
                      delta: { content: parsedData.response },
                      index: 0,
                      finish_reason: parsedData.done ? "stop" : null,
                    },
                  ];
                }

                // 参照用に元データを追加
                standardChunk.raw = parsedData;

                onChunk(standardChunk);

                if (parsedData.done) {
                  onComplete();
                }
              } catch (e) {
                // JSONでない場合は無視
                this.logger.debug("Line is not JSON, ignoring", { line });
              }
            }
          }
        }
      } catch (error) {
        this.logger.error("Error processing Ollama stream", error);
        onError(error instanceof Error ? error : new Error(String(error)));
      }
    };

    processStream();
  }

  handleResponse(response: ChatResponse): ChatResponse {
    // Ollama 特定のレスポンス処理
    const processedResponse = { ...response };

    // レスポンス形式が期待通りであることを確認
    if (!processedResponse.choices || processedResponse.choices.length === 0) {
      this.logger.warn(
        "Invalid Ollama response format, creating default choice",
        { response },
      );

      // デフォルトの選択肢を作成
      processedResponse.choices = [
        {
          message: {
            role: "assistant",
            content: "I apologize, but I couldn't generate a proper response.",
          },
          finish_reason: "error",
        },
      ];
    }

    return processedResponse;
  }

  supportsFeature(feature: string): boolean {
    // Ollama がサポートする機能
    switch (feature.toLowerCase()) {
      case "streaming":
      case "chat":
      case "completion":
      case "json_response":
        return true;
      case "function_calling":
      case "tools":
        return false;
      default:
        return super.supportsFeature(feature);
    }
  }
}

/**
 * vLLM バックエンド戦略
 * vLLM 特定のリクエストとレスポンス形式を処理
 */
export class VLLMBackendStrategy extends BaseBackendStrategy {
  getName(): string {
    return "vllm";
  }

  prepareChatRequest(
    messages: Message[],
    options: ChatOptions,
  ): { messages: Message[]; options: ChatOptions } {
    // vLLM 特定のメッセージ処理
    const processedMessages = [...messages];
    const processedOptions = { ...options };

    // オプションに vLLM 特定のパラメータを含めることを確認
    if (!processedOptions.backend_options) {
      processedOptions.backend_options = {};
    }

    // vLLM は特定のフォーマットが必要な場合がある
    processedOptions.backend_options.use_beam_search = false;

    this.logger.debug("Prepared vLLM chat request", {
      messagesCount: processedMessages.length,
      options: processedOptions,
    });

    return { messages: processedMessages, options: processedOptions };
  }

  prepareCompletionRequest(
    prompt: string,
    options: ChatOptions,
  ): { prompt: string; options: ChatOptions } {
    // vLLM 特定のプロンプト処理
    const processedOptions = { ...options };

    // オプションに vLLM 特定のパラメータを含めることを確認
    if (!processedOptions.backend_options) {
      processedOptions.backend_options = {};
    }

    // vLLM は特定のフォーマットが必要な場合がある
    processedOptions.backend_options.use_beam_search = false;

    this.logger.debug("Prepared vLLM completion request", {
      promptLength: prompt.length,
      options: processedOptions,
    });

    return { prompt, options: processedOptions };
  }

  // vLLM は標準のストリーム処理を使用するため、handleStreamResponse をオーバーライドする必要はない

  handleResponse(response: ChatResponse): ChatResponse {
    // vLLM 特定のレスポンス処理
    const processedResponse = { ...response };

    // レスポンス形式が期待通りであることを確認
    if (!processedResponse.choices || processedResponse.choices.length === 0) {
      this.logger.warn(
        "Invalid vLLM response format, creating default choice",
        { response },
      );

      // デフォルトの選択肢を作成
      processedResponse.choices = [
        {
          message: {
            role: "assistant",
            content: "I apologize, but I couldn't generate a proper response.",
          },
          finish_reason: "error",
        },
      ];
    }

    return processedResponse;
  }

  supportsFeature(feature: string): boolean {
    // vLLM がサポートする機能
    switch (feature.toLowerCase()) {
      case "streaming":
      case "chat":
      case "completion":
      case "json_response":
        return true;
      case "function_calling":
      case "tools":
        return false;
      default:
        return super.supportsFeature(feature);
    }
  }
}

/**
 * SGLang バックエンド戦略
 * SGLang 特定のリクエストとレスポンス形式を処理
 */
export class SGLangBackendStrategy extends BaseBackendStrategy {
  getName(): string {
    return "sglang";
  }

  prepareChatRequest(
    messages: Message[],
    options: ChatOptions,
  ): { messages: Message[]; options: ChatOptions } {
    // SGLang 特定のメッセージ処理
    const processedMessages = [...messages];
    const processedOptions = { ...options };

    // オプションに SGLang 特定のパラメータを含めることを確認
    if (!processedOptions.backend_options) {
      processedOptions.backend_options = {};
    }

    this.logger.debug("Prepared SGLang chat request", {
      messagesCount: processedMessages.length,
      options: processedOptions,
    });

    return { messages: processedMessages, options: processedOptions };
  }

  prepareCompletionRequest(
    prompt: string,
    options: ChatOptions,
  ): { prompt: string; options: ChatOptions } {
    // SGLang 特定のプロンプト処理
    const processedOptions = { ...options };

    // オプションに SGLang 特定のパラメータを含めることを確認
    if (!processedOptions.backend_options) {
      processedOptions.backend_options = {};
    }

    this.logger.debug("Prepared SGLang completion request", {
      promptLength: prompt.length,
      options: processedOptions,
    });

    return { prompt, options: processedOptions };
  }

  // SGLang は特定のストリーム処理方式を持つ可能性があるが、ここでは標準実装を使用

  handleResponse(response: ChatResponse): ChatResponse {
    // SGLang 特定のレスポンス処理
    const processedResponse = { ...response };

    // レスポンス形式が期待通りであることを確認
    if (!processedResponse.choices || processedResponse.choices.length === 0) {
      this.logger.warn(
        "Invalid SGLang response format, creating default choice",
        { response },
      );

      // デフォルトの選択肢を作成
      processedResponse.choices = [
        {
          message: {
            role: "assistant",
            content: "I apologize, but I couldn't generate a proper response.",
          },
          finish_reason: "error",
        },
      ];
    }

    return processedResponse;
  }

  supportsFeature(feature: string): boolean {
    // SGLang がサポートする機能
    switch (feature.toLowerCase()) {
      case "streaming":
      case "chat":
      case "completion":
      case "structured_generation":
        return true;
      default:
        return super.supportsFeature(feature);
    }
  }
}

/**
 * X-Inference バックエンド戦略
 * X-Inference 特定のリクエストとレスポンス形式を処理
 */
export class XInferenceBackendStrategy extends BaseBackendStrategy {
  getName(): string {
    return "x-inference";
  }

  prepareChatRequest(
    messages: Message[],
    options: ChatOptions,
  ): { messages: Message[]; options: ChatOptions } {
    // X-Inference 特定のメッセージ処理
    const processedMessages = [...messages];
    const processedOptions = { ...options };

    // オプションに X-Inference 特定のパラメータを含めることを確認
    if (!processedOptions.backend_options) {
      processedOptions.backend_options = {};
    }

    this.logger.debug("Prepared X-Inference chat request", {
      messagesCount: processedMessages.length,
      options: processedOptions,
    });

    return { messages: processedMessages, options: processedOptions };
  }

  prepareCompletionRequest(
    prompt: string,
    options: ChatOptions,
  ): { prompt: string; options: ChatOptions } {
    // X-Inference 特定のプロンプト処理
    const processedOptions = { ...options };

    // オプションに X-Inference 特定のパラメータを含めることを確認
    if (!processedOptions.backend_options) {
      processedOptions.backend_options = {};
    }

    this.logger.debug("Prepared X-Inference completion request", {
      promptLength: prompt.length,
      options: processedOptions,
    });

    return { prompt, options: processedOptions };
  }

  // X-Inference は特定のストリーム処理方式を持つ可能性があるが、ここでは標準実装を使用

  handleResponse(response: ChatResponse): ChatResponse {
    // X-Inference 特定のレスポンス処理
    const processedResponse = { ...response };

    // レスポンス形式が期待通りであることを確認
    if (!processedResponse.choices || processedResponse.choices.length === 0) {
      this.logger.warn(
        "Invalid X-Inference response format, creating default choice",
        { response },
      );

      // デフォルトの選択肢を作成
      processedResponse.choices = [
        {
          message: {
            role: "assistant",
            content: "I apologize, but I couldn't generate a proper response.",
          },
          finish_reason: "error",
        },
      ];
    }

    return processedResponse;
  }

  supportsFeature(feature: string): boolean {
    // X-Inference がサポートする機能
    switch (feature.toLowerCase()) {
      case "streaming":
      case "chat":
      case "completion":
        return true;
      default:
        return super.supportsFeature(feature);
    }
  }
}

/**
 * バックエンド戦略コンテキスト
 * バックエンド戦略を動的に選択して使用
 */
export class BackendStrategyContext {
  private strategies: Map<string, BackendStrategy>;
  private currentStrategy: BackendStrategy | null = null;
  private logger: LogService;
  private apiAdapter: ApiAdapter;

  constructor(logger: LogService, apiAdapter: ApiAdapter) {
    this.strategies = new Map();
    this.logger = logger;
    this.apiAdapter = apiAdapter;

    // デフォルト戦略を登録
    this.registerStrategy(new OllamaBackendStrategy(logger));
    this.registerStrategy(new VLLMBackendStrategy(logger));
    this.registerStrategy(new SGLangBackendStrategy(logger));
    this.registerStrategy(new XInferenceBackendStrategy(logger));

    // デフォルトで Ollama 戦略を使用
    this.setStrategy("ollama");
  }

  /**
   * バックエンド戦略を登録
   * @param strategy 戦略インスタンス
   */
  registerStrategy(strategy: BackendStrategy): void {
    this.strategies.set(strategy.getName(), strategy);
    this.logger.debug(`Registered backend strategy: ${strategy.getName()}`);
  }

  /**
   * 現在の戦略を設定
   * @param backendName バックエンド名
   */
  setStrategy(backendName: string): boolean {
    const strategy = this.strategies.get(backendName.toLowerCase());

    if (strategy) {
      this.currentStrategy = strategy;
      this.logger.info(`Set current backend strategy to: ${backendName}`);
      return true;
    } else {
      this.logger.warn(
        `Backend strategy not found: ${backendName}, using default`,
      );
      // デフォルト戦略を使用
      this.currentStrategy = this.strategies.get("ollama") || null;
      return false;
    }
  }

  /**
   * 現在の戦略を取得
   */
  getCurrentStrategy(): BackendStrategy {
    if (!this.currentStrategy) {
      this.logger.warn("No current strategy set, using Ollama as default");
      this.currentStrategy =
        this.strategies.get("ollama") || new OllamaBackendStrategy(this.logger);
    }

    return this.currentStrategy;
  }

  /**
   * チャットリクエストを送信
   * @param messages メッセージ配列
   * @param options オプション
   */
  async sendChatRequest(
    messages: Message[],
    options: ChatOptions = {},
  ): Promise<ChatResponse | Response> {
    const strategy = options.backend
      ? this.strategies.get(options.backend.toLowerCase()) ||
        this.getCurrentStrategy()
      : this.getCurrentStrategy();

    this.logger.info(
      `Using backend strategy: ${strategy.getName()} for chat request`,
    );

    // 戦略を使用してリクエストを準備
    const { messages: processedMessages, options: processedOptions } =
      strategy.prepareChatRequest(messages, options);

    // リクエストを送信
    return this.apiAdapter.sendChatRequest(processedMessages, processedOptions);
  }

  /**
   * テキスト生成リクエストを送信
   * @param prompt プロンプト
   * @param options オプション
   */
  async sendCompletionRequest(
    prompt: string,
    options: ChatOptions = {},
  ): Promise<ChatResponse | Response> {
    const strategy = options.backend
      ? this.strategies.get(options.backend.toLowerCase()) ||
        this.getCurrentStrategy()
      : this.getCurrentStrategy();

    this.logger.info(
      `Using backend strategy: ${strategy.getName()} for completion request`,
    );

    // 戦略を使用してリクエストを準備
    const { prompt: processedPrompt, options: processedOptions } =
      strategy.prepareCompletionRequest(prompt, options);

    // リクエストを送信
    return this.apiAdapter.sendCompletionRequest(
      processedPrompt,
      processedOptions,
    );
  }

  /**
   * ストリームレスポンスを処理
   * @param response レスポンスオブジェクト
   * @param onChunk チャンク処理コールバック
   * @param onComplete 完了コールバック
   * @param onError エラーコールバック
   * @param backendName バックエンド名
   */
  handleStreamResponse(
    response: Response,
    onChunk: (_chunk: StreamChunk) => void,
    onComplete: () => void,
    onError: (_error: Error) => void,
    backendName?: string,
  ): void {
    const strategy = backendName
      ? this.strategies.get(backendName.toLowerCase()) ||
        this.getCurrentStrategy()
      : this.getCurrentStrategy();

    this.logger.info(
      `Using backend strategy: ${strategy.getName()} for stream handling`,
    );

    // 戦略を使用してストリームを処理
    strategy.handleStreamResponse(response, onChunk, onComplete, onError);
  }

  /**
   * 非ストリームレスポンスを処理
   * @param response レスポンスオブジェクト
   * @param backendName バックエンド名
   */
  handleResponse(response: ChatResponse, backendName?: string): ChatResponse {
    const strategy = backendName
      ? this.strategies.get(backendName.toLowerCase()) ||
        this.getCurrentStrategy()
      : this.getCurrentStrategy();

    this.logger.info(
      `Using backend strategy: ${strategy.getName()} for response handling`,
    );

    // 戦略を使用してレスポンスを処理
    return strategy.handleResponse(response);
  }

  /**
   * バックエンドが特定の機能をサポートしているか確認
   * @param feature 機能名
   * @param backendName バックエンド名
   */
  supportsFeature(feature: string, backendName?: string): boolean {
    const strategy = backendName
      ? this.strategies.get(backendName.toLowerCase()) ||
        this.getCurrentStrategy()
      : this.getCurrentStrategy();

    return strategy.supportsFeature(feature);
  }

  /**
   * 登録済みのすべての戦略名を取得
   */
  getRegisteredStrategyNames(): string[] {
    return Array.from(this.strategies.keys());
  }
}

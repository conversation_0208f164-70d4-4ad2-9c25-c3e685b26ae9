/**
 * API関連のユーティリティ関数
 */
import { serviceFactory } from "../services/serviceFactory";

/**
 * 認証エラー（401）を処理する関数
 * ログイン画面にリダイレクトし、現在のパスを保存する
 */
export const handleAuthError = () => {
  const logger = serviceFactory.getLogger();
  logger.warn("認証エラー: ユーザーが認証されていません。ログイン画面にリダイレクトします。");
  
  // ローカルストレージからトークンを削除
  localStorage.removeItem("token");
  localStorage.removeItem("user");
  
  // 現在のURLを保存（ログイン後にリダイレクトするため）
  const currentPath = window.location.pathname;
  if (currentPath !== "/login") {
    localStorage.setItem("redirectAfterLogin", currentPath);
  }
  
  // ログイン画面にリダイレクト
  window.location.href = "/login";
};

/**
 * 認証ヘッダーを取得する関数
 * @returns 認証ヘッダーを含むオブジェクト
 */
export const getAuthHeaders = (): Record<string, string> => {
  const token = localStorage.getItem("token");
  return {
    Authorization: `Bearer ${token || "demo-token"}`,
    "Content-Type": "application/json",
  };
};

/**
 * APIリクエストを送信する関数（fetchラッパー）
 * 認証エラーを自動的に処理する
 * @param url リクエストURL
 * @param options fetchオプション
 * @returns レスポンス
 */
export const fetchWithAuth = async (url: string, options: RequestInit = {}): Promise<Response> => {
  const logger = serviceFactory.getLogger();
  
  // 認証ヘッダーを追加
  const headers = {
    ...getAuthHeaders(),
    ...(options.headers || {}),
  };
  
  try {
    const response = await fetch(url, {
      ...options,
      headers,
    });
    
    // 401エラーの場合、ログイン画面にリダイレクト
    if (response.status === 401) {
      handleAuthError();
      throw new Error("認証エラー: ユーザーが認証されていません");
    }
    
    return response;
  } catch (error) {
    logger.error("APIリクエストエラー:", error);
    throw error;
  }
};

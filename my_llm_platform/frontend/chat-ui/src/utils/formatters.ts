/**
 * 日付をフォーマット
 * @param date 日付オブジェクト、タイムスタンプまたは日付文字列
 * @param options フォーマットオプション
 * @returns フォーマット後の日時文字列
 */
export const formatDateTime = (
  date: Date | number | string,
  options: Intl.DateTimeFormatOptions = {},
): string => {
  const dateObj = date instanceof Date ? date : new Date(date);

  const defaultOptions: Intl.DateTimeFormatOptions = {
    year: "numeric",
    month: "short",
    day: "numeric",
    hour: "2-digit",
    minute: "2-digit",
    second: "2-digit",
    ...options,
  };

  return new Intl.DateTimeFormat("en-US", defaultOptions).format(dateObj);
};

/**
 * 数字をフォーマット
 * @param num 数字
 * @param options フォーマットオプション
 * @returns フォーマット後の数字文字列
 */
export const formatNumber = (
  num: number,
  options: Intl.NumberFormatOptions = {},
): string => {
  const defaultOptions: Intl.NumberFormatOptions = {
    minimumFractionDigits: 0,
    maximumFractionDigits: 2,
    ...options,
  };

  return new Intl.NumberFormat("en-US", defaultOptions).format(num);
};

/**
 * テキストを切り捨て
 * @param text テキスト
 * @param maxLength 最大長さ
 * @param suffix サフィックス
 * @returns 切り捨て後のテキスト
 */
export const truncateText = (
  text: string,
  maxLength: number = 100,
  suffix: string = "...",
): string => {
  if (!text) return "";
  if (text.length <= maxLength) return text;

  return text.substring(0, maxLength - suffix.length) + suffix;
};

/**
 * ファイルサイズをフォーマット
 * @param bytes バイト数
 * @param decimals 小数点以下の桁数
 * @returns フォーマット後のファイルサイズ
 */
export const formatFileSize = (bytes: number, decimals: number = 2): string => {
  if (bytes === 0) return "0 Bytes";

  const k = 1024;
  const dm = decimals < 0 ? 0 : decimals;
  const sizes = ["Bytes", "KB", "MB", "GB", "TB", "PB", "EB", "ZB", "YB"];

  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + " " + sizes[i];
};

/* eslint-disable no-undef */
/**
 * パフォーマンス監視ツール
 * アプリケーションのパフォーマンス指標を測定および記録するためのツール
 */
class PerformanceMonitor {
  private static instance: PerformanceMonitor | null = null;
  private measurements: Map<string, number> = new Map();
  private enabled: boolean = true;

  /**
   * パフォーマンス監視インスタンスを取得（シングルトンパターン）
   */
  public static getInstance(): PerformanceMonitor {
    if (!PerformanceMonitor.instance) {
      PerformanceMonitor.instance = new PerformanceMonitor();
    }
    return PerformanceMonitor.instance;
  }

  /**
   * コンストラクタ
   */
  private constructor() {
    // 開発環境であるかを確認
    this.enabled =
      import.meta.env.DEV || import.meta.env.MODE === "development";

    if (this.enabled) {
      console.info("パフォーマンス監視が有効化されました");
    }
  }

  /**
   * 測定を開始
   * @param id 測定ID
   */
  public startMeasure(id: string): void {
    if (!this.enabled) return;

    // performance API が利用可能であることを確認
    if (typeof performance !== 'undefined') {
      this.measurements.set(id, performance.now());
    } else {
      console.warn('Performance API が利用できないため、Date.now() を代替として使用します');
      this.measurements.set(id, Date.now());
    }
  }

  /**
   * 測定を終了し、継続時間（ミリ秒）を返す
   * @param id 測定ID
   * @returns 継続時間（ミリ秒）、開始時間が見つからない場合は null を返す
   */
  public endMeasure(id: string): number | null {
    if (!this.enabled) return null;

    const startTime = this.measurements.get(id);
    if (startTime === undefined) {
      console.warn(`測定の開始時間が見つかりません: ${id}`);
      return null;
    }

    // performance API が利用可能であることを確認
    let endTime: number;
    if (typeof performance !== 'undefined') {
      endTime = performance.now();
    } else {
      endTime = Date.now();
    }
    const duration = endTime - startTime;

    // 測定をクリア
    this.measurements.delete(id);

    return duration;
  }

  /**
   * API 呼び出しのパフォーマンスを記録
   * @param apiName API 名称
   * @param duration 継続時間（ミリ秒）
   * @param status ステータス（成功/失敗）
   */
  public logApiCall(
    apiName: string,
    duration: number,
    status: "success" | "error",
  ): void {
    if (!this.enabled) return;

    console.info(`API 呼び出し [${status}] ${apiName}: ${duration.toFixed(2)}ms`);

    // ここに、パフォーマンスデータを分析サービスに送信するロジックを追加可能
  }

  /**
   * コンポーネントのレンダリングパフォーマンスを記録
   * @param componentName コンポーネント名
   * @param duration 継続時間（ミリ秒）
   */
  public logComponentRender(componentName: string, duration: number): void {
    if (!this.enabled) return;

    console.info(`コンポーネントレンダリング ${componentName}: ${duration.toFixed(2)}ms`);
  }

  /**
   * 操作のパフォーマンスを記録
   * @param operationName 操作名
   * @param duration 継続時間（ミリ秒）
   */
  public logOperation(operationName: string, duration: number): void {
    if (!this.enabled) return;

    console.info(`操作 ${operationName}: ${duration.toFixed(2)}ms`);
  }
}

// シングルトンインスタンスをエクスポート
export const performanceMonitor = PerformanceMonitor.getInstance();

/**
 * レスポンスフィルタリングユーティリティ
 *
 * モデルからのレスポンスを適切に処理するためのユーティリティ関数を提供します。
 */

/**
 * 特殊トークン（<|xxx|>形式）を削除します。
 *
 * @param text 処理するテキスト
 * @returns 特殊トークンが削除されたテキスト
 */
export const cleanSpecialTokens = (text: string): string => {
  if (!text) return text;

  // 特殊トークンを削除（<|xxx|>形式）
  const cleanedText = text.replace(/<\|[^|]+\|>/g, '');

  // 連続する空白を1つにまとめる
  const normalizedText = cleanedText.replace(/\s+/g, ' ');

  // 前後の空白を削除
  return normalizedText.trim();
};

/**
 * 会話の履歴から最新のアシスタントの応答のみを抽出します。
 *
 * @param text 処理するテキスト
 * @returns 最新のアシスタントの応答
 */
export const extractLatestAssistantResponse = (text: string): string => {
  if (!text) return text;

  // <|assistant|>タグで分割
  const parts = text.split('<|assistant|>');

  // 最後のアシスタント応答を取得（最新の応答）
  if (parts.length > 1) {
    // 最後のアシスタント応答を取得
    let lastResponse = parts[parts.length - 1];

    // ユーザータグがある場合はそこまでを取得
    const userTagIndex = lastResponse.indexOf('<|user|>');
    if (userTagIndex !== -1) {
      lastResponse = lastResponse.substring(0, userTagIndex);
    }

    return lastResponse.trim();
  }

  // アシスタント応答がない場合は元のテキストを返す
  return text;
};

/**
 * 複数の回答候補から最初の有効な回答を抽出します。
 * 特殊トークンで区切られた最初のセグメントを返します。
 *
 * @param text 処理するテキスト
 * @returns 最初の有効な回答
 */
export const extractFirstResponse = (text: string): string => {
  if (!text) return text;

  // 特殊トークンで分割
  const segments = text.split(/<\|[^|]+\|>/);

  // 空でない最初のセグメントを返す
  for (const segment of segments) {
    const trimmed = segment.trim();
    if (trimmed) {
      return trimmed;
    }
  }

  // 有効なセグメントがない場合は元のテキストを返す
  return text;
};

/**
 * 重複する文章を削除します。
 *
 * @param text 処理するテキスト
 * @returns 重複が削除されたテキスト
 */
export const removeDuplicates = (text: string): string => {
  if (!text) return text;

  // 文章を文単位で分割
  const sentences = text.match(/[^。.!?！？]+[。.!?！？]?/g) || [text];

  // 重複を削除
  const uniqueSentences: string[] = [];
  const seen = new Set<string>();

  for (const sentence of sentences) {
    const trimmed = sentence.trim();
    if (trimmed && !seen.has(trimmed)) {
      seen.add(trimmed);
      uniqueSentences.push(trimmed);
    }
  }

  // 文章を再結合
  return uniqueSentences.join(' ');
};

/**
 * モデルからのレスポンスを処理します。
 * 特殊トークンを削除し、最新の有効な回答のみを返します。
 *
 * @param text 処理するテキスト
 * @returns 処理されたテキスト
 */
export const filterModelResponse = (text: string): string => {
  if (!text) return text;

  // コンソールに元のテキストを出力（デバッグ用）
  console.log('元のレスポンス:', text);

  // 最新のアシスタント応答を抽出
  const latestResponse = extractLatestAssistantResponse(text);
  console.log('最新の応答:', latestResponse);

  // 特殊トークンを削除
  const cleanedResponse = cleanSpecialTokens(latestResponse);
  console.log('特殊トークン削除後:', cleanedResponse);

  // 重複を削除
  const deduplicatedResponse = removeDuplicates(cleanedResponse);
  console.log('重複削除後:', deduplicatedResponse);

  return deduplicatedResponse;
};

import { StreamChunk } from "../types/chat";

/**
 * SSEストリームデータを解析する
 * @param stream 読み取り可能なストリーム
 * @param onChunk 各チャンクのコールバック関数
 * @param onDone 完了時のコールバック関数
 * @param onError エラー時のコールバック関数
 */
export const parseSSEStream = async (
  stream: ReadableStream<Uint8Array>,
  onChunk?: (_chunk: StreamChunk) => void,
  onDone?: () => void,
  onError?: (_error: Error) => void,
): Promise<void> => {
  const reader = stream.getReader();
  const decoder = new TextDecoder();
  let buffer = ""; // 不完全なデータを保持するバッファ

  try {
    console.log("Stream parsing started");

    while (true) {
      const { done, value } = await reader.read();
      if (done) {
        console.log("Stream reading complete");
        if (onDone) onDone();
        break;
      }

      const chunk = decoder.decode(value, { stream: true });
      console.log("Received chunk:", chunk);

      // バッファに新しいデータを追加
      buffer += chunk;

      // 完全な行を処理
      const lines = buffer.split("\n");
      // 最後の行は不完全な可能性があるのでバッファに保持
      buffer = lines.pop() || "";

      for (const line of lines) {
        if (line.trim() === "") continue;

        console.log("Processing line:", line);

        // data: プレフィックスを処理
        if (line.startsWith("data: ")) {
          const data = line.substring(6).trim();
          console.log("Extracted data:", data);

          // [DONE]シグナルの処理
          if (data === "[DONE]") {
            console.log("Received [DONE] signal");
            if (onDone) onDone();
            continue;
          }

          // JSONデータの処理
          try {
            const parsed = JSON.parse(data);
            console.log("Parsed JSON:", parsed);

            // チャンクが有効か確認
            if (parsed && parsed.choices && parsed.choices.length > 0) {
              if (onChunk) onChunk(parsed);
            } else if (parsed.error) {
              // エラー情報を含むチャンク
              console.warn("エラー情報を含むチャンク:", parsed);
              
              // エラーチャンクを作成
              const errorChunk: StreamChunk = {
                id: parsed.id || `error-${Date.now()}`,
                object: "chat.completion.chunk",
                created: parsed.created || Math.floor(Date.now() / 1000),
                model: parsed.model || "unknown",
                choices: [{
                  delta: { content: `エラー: ${parsed.error.message || "不明なエラー"}` },
                  index: 0,
                  finish_reason: "error"
                }],
                error: parsed.error
              };
              
              if (onChunk) onChunk(errorChunk);
              if (onError) onError(new Error(parsed.error.message || "不明なエラー"));
            } else {
              // Ollama形式をOpenAI形式に変換
              const standardChunk: StreamChunk = {
                id: parsed.id || `chunk-${Date.now()}`,
                object: "chat.completion.chunk",
                created: parsed.created || Math.floor(Date.now() / 1000),
                model: parsed.model || "unknown",
                choices: []
              };

              if (parsed.message && parsed.message.content) {
                standardChunk.choices = [{
                  delta: { content: parsed.message.content },
                  index: 0,
                  finish_reason: parsed.done ? "stop" : null
                }];
              } else if (parsed.response) {
                standardChunk.choices = [{
                  delta: { content: parsed.response },
                  index: 0,
                  finish_reason: parsed.done ? "stop" : null
                }];
              }

              if (standardChunk.choices && standardChunk.choices.length > 0) {
                console.log("Converted to standard format:", standardChunk);
                if (onChunk) onChunk(standardChunk);
              } else {
                console.warn("有効なチャンクを生成できませんでした:", parsed);
              }
            }
          } catch (e) {
            console.error("Error parsing JSON:", e, "Raw data:", data);
            
            // エラーメッセージを含むチャンクを作成
            const errorChunk: StreamChunk = {
              id: `error-${Date.now()}`,
              object: "chat.completion.chunk",
              created: Math.floor(Date.now() / 1000),
              model: "unknown",
              choices: [{
                delta: { content: `解析エラー: ${e instanceof Error ? e.message : '不明なエラー'}` },
                index: 0,
                finish_reason: "error"
              }],
              error: {
                message: e instanceof Error ? e.message : '不明なエラー',
                type: "parsing_error"
              }
            };
            
            if (onChunk) onChunk(errorChunk);
            if (onError && e instanceof Error) onError(e);
          }
        } else {
          // Ollamaのレスポンス形式の場合、各行が直接JSON
          try {
            // 行がJSONかどうかを確認
            const parsed = JSON.parse(line);
            console.log("Parsed direct JSON:", parsed);

            // Ollama形式をOpenAI形式に変換
            const standardChunk: StreamChunk = {};

            if (parsed.message && parsed.message.content) {
              standardChunk.choices = [{
                delta: { content: parsed.message.content },
                index: 0,
                finish_reason: parsed.done ? "stop" : null
              }];
            } else if (parsed.response) {
              standardChunk.choices = [{
                delta: { content: parsed.response },
                index: 0,
                finish_reason: parsed.done ? "stop" : null
              }];
            }

            if (standardChunk.choices) {
              console.log("Converted to standard format:", standardChunk);
              if (onChunk) onChunk(standardChunk);

              if (parsed.done) {
                console.log("Ollama format done signal");
                if (onDone) onDone();
              }
            }
          } catch (e) {
            // JSONでない場合は無視
            console.log("Line is not JSON, ignoring:", line);
          }
        }
      }
    }
  } catch (e) {
    console.error("Error reading stream:", e);
    
    // エラーメッセージを含むチャンクを作成
    const errorChunk: StreamChunk = {
      id: `error-${Date.now()}`,
      object: "chat.completion.chunk",
      created: Math.floor(Date.now() / 1000),
      model: "unknown",
      choices: [{
        delta: { content: `ストリーム読み取りエラー: ${e instanceof Error ? e.message : '不明なエラー'}` },
        index: 0,
        finish_reason: "error"
      }],
      error: {
        message: e instanceof Error ? e.message : '不明なエラー',
        type: "stream_error"
      }
    };
    
    if (onChunk) onChunk(errorChunk);
    if (onError && e instanceof Error) onError(e);
  } finally {
    reader.releaseLock();
    console.log("Stream reader released");
  }
};

/**
 * チャット完了ストリームからコンテンツを抽出
 * @param chunk データチャンク
 * @returns 抽出されたコンテンツまたはnull
 */
export const extractContentFromChatChunk = (
  chunk: StreamChunk,
): string | null => {
  if (
    chunk.choices &&
    chunk.choices[0].delta &&
    chunk.choices[0].delta.content
  ) {
    return chunk.choices[0].delta.content;
  }
  return null;
};

/**
 * テキスト完了ストリームからコンテンツを抽出
 * @param chunk データチャンク
 * @returns 抽出されたコンテンツまたはnull
 */
export const extractContentFromCompletionChunk = (
  chunk: StreamChunk,
): string | null => {
  // OpenAIの完了レスポンス形式をチェック
  if (chunk.choices && chunk.choices[0] && 'text' in chunk.choices[0]) {
    return (chunk.choices[0] as any).text;
  }
  // 代替形式をチェック
  if (chunk.choices && chunk.choices[0].delta && chunk.choices[0].delta.content) {
    return chunk.choices[0].delta.content;
  }
  return null;
};

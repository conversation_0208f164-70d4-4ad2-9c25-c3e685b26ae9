import React, { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { useAuthStore } from "../store/authStore";

interface UserStats {
  total_users: number;
  active_users: number;
  total_conversations: number;
  messages_today: number;
}

interface SystemStatus {
  cpu_usage: number;
  memory_usage: number;
  api_latency: number;
}

export const AdminPanel: React.FC = () => {
  const { t } = useTranslation();
  const { token, user } = useAuthStore();
  const [stats, setStats] = useState<UserStats | null>(null);
  const [status, setStatus] = useState<SystemStatus | null>(null);

  useEffect(() => {
    if (user?.role === "admin") {
      fetchStats();
      fetchSystemStatus();
      const interval = setInterval(fetchSystemStatus, 30000);
      return () => clearInterval(interval);
    }
  }, [token]);

  const fetchStats = async () => {
    try {
      const response = await fetch("/api/admin/stats", {
        headers: { Authorization: `Bearer ${token}` },
      });
      const data = await response.json();
      setStats(data);
    } catch (error) {
      console.error("Failed to fetch stats:", error);
    }
  };

  const fetchSystemStatus = async () => {
    try {
      const response = await fetch("/api/admin/system-status", {
        headers: { Authorization: `Bearer ${token}` },
      });
      const data = await response.json();
      setStatus(data);
    } catch (error) {
      console.error("Failed to fetch system status:", error);
    }
  };

  if (user?.role !== "admin") {
    return null;
  }

  return (
    <div className="p-6">
      <h1 className="text-2xl mb-6">{t("admin.dashboard")}</h1>

      <div className="grid grid-cols-2 gap-6">
        <div className="bg-white p-4 rounded-lg shadow">
          <h2 className="text-xl mb-4">{t("admin.userStats")}</h2>
          {stats && (
            <div className="space-y-2">
              <div>総ユーザー数: {stats.total_users}</div>
              <div>アクティブユーザー: {stats.active_users}</div>
              <div>総会話数: {stats.total_conversations}</div>
              <div>本日のメッセージ数: {stats.messages_today}</div>
            </div>
          )}
        </div>

        <div className="bg-white p-4 rounded-lg shadow">
          <h2 className="text-xl mb-4">{t("admin.systemStatus")}</h2>
          {status && (
            <div className="space-y-2">
              <div>CPU使用率: {status.cpu_usage}%</div>
              <div>メモリ使用率: {status.memory_usage}%</div>
              <div>APIレイテンシー: {status.api_latency}ms</div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

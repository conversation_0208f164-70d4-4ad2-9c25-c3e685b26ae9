import React, { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { serviceFactory } from "../services/serviceFactory";
import { Backend } from "../types/chat";

interface BackendSelectorProps {
  selectedBackend: string | null;
  onBackendSelect: (_backendId: string) => void;
}

const BackendSelector: React.FC<BackendSelectorProps> = ({
  selectedBackend,
  onBackendSelect,
}) => {
  const { t } = useTranslation();
  const [backends, setBackends] = useState<Backend[]>([]);
  const [defaultBackend, setDefaultBackend] = useState<string>("");
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [showSelector, setShowSelector] = useState<boolean>(false);

  useEffect(() => {
    const loadBackends = async (): Promise<void> => {
      setLoading(true);
      try {
        // バックエンドリストを取得
        console.log("Fetching backends...");
        const apiService = serviceFactory.getApiService();
        const backendsData = await apiService.fetchBackends();
        console.log("Backends loaded:", backendsData);
        setBackends(backendsData.backends);
        setDefaultBackend(backendsData.default_backend);

        // 選択されたバックエンドがない場合、デフォルトのバックエンドを選択
        if (!selectedBackend && backendsData.default_backend) {
          onBackendSelect(backendsData.default_backend);
        }

        setLoading(false);
        setError(null);
      } catch (err) {
        console.error("Error loading backends:", err);
        setError("Failed to load backends");
        setLoading(false);

        // エラーが発生してもデフォルト値を設定
        if (!selectedBackend) {
          onBackendSelect("ollama");
        }
      }
    };

    loadBackends();
  }, [selectedBackend, onBackendSelect]);

  // バックエンド選択を処理
  const handleBackendChange = (backendId: string): void => {
    onBackendSelect(backendId);
  };

  // セレクターの表示を切り替える
  const toggleSelector = (): void => {
    setShowSelector(!showSelector);
  };

  if (loading) return <div className="loading">{t("loading")}</div>;
  if (error)
    return (
      <div className="error">
        {t("error")}: {error}
      </div>
    );

  return (
    <div className="backend-selector">
      <div className="backend-display" onClick={toggleSelector}>
        <span className="backend-label">
          {selectedBackend || defaultBackend || "ollama"}
        </span>
        <span className="toggle-icon">{showSelector ? "▲" : "▼"}</span>
      </div>

      {showSelector && (
        <div className="backend-dropdown-menu">
          {backends.map((backend) => (
            <div
              key={backend.id}
              className={`backend-option ${backend.id === selectedBackend ? "selected" : ""}`}
              onClick={() => handleBackendChange(backend.id)}
            >
              <span className="backend-name">{backend.name}</span>
              <span className="backend-description">{backend.description}</span>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default BackendSelector;

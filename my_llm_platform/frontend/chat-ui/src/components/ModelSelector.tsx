import React, { useState, useEffect, ChangeEvent } from "react";
import { useTranslation } from "react-i18next";

import { apiService } from "../services/apiService";
import { serviceFactory } from "../services/serviceFactory";
import { API_CONFIG } from '../config/api';
import { Model, Backend, BackendsResponse } from "../types/chat";
import "../styles/ModelSelector.css";

// 重複するモデルIDを除去する関数
const removeDuplicateModels = (models: Model[]): Model[] => {
  const uniqueModels: Model[] = [];
  const modelIds = new Set<string>();

  for (const model of models) {
    if (!modelIds.has(model.id)) {
      modelIds.add(model.id);
      uniqueModels.push(model);
    } else {
      console.log(`重複するモデルIDを除外: ${model.id}`);
    }
  }

  return uniqueModels;
};

interface ModelSelectorProps {
  onModelSelect: (_modelId: string) => void;
  onBackendSelect: (_backendId: string) => void;
  selectedModel: string | null;
  selectedBackend: string | null;
  inSettingsMenu?: boolean;
}

const ModelSelector: React.FC<ModelSelectorProps> = ({
  onModelSelect,
  onBackendSelect,
  selectedModel,
  selectedBackend,
  inSettingsMenu = false,
}) => {
  const { t } = useTranslation();
  const [models, setModels] = useState<Model[]>([]);
  const [backends, setBackends] = useState<Backend[]>([]);
  const [defaultBackend, setDefaultBackend] = useState<string>("");
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [showBackendSelector, setShowBackendSelector] =
    useState<boolean>(false);

  useEffect(() => {
    const loadData = async (): Promise<void> => {
      // 常にモデルとバックエンドをロードして最新の情報を取得
      setLoading(true);
      setError(null);

      console.log("モデルとバックエンドを読み込んでいます...");

      // APIアダプターを取得
      const apiAdapter = serviceFactory.getApiAdapter();

      try {
        // バックエンド設定情報を取得
        try {
          console.log("バックエンド設定情報を取得しています...");
          // 設定ファイルからAPIのベースURLを取得
          console.log("設定ファイルからAPIのベースURLを取得します");
          console.log(`設定ファイルのAPIベースURL: ${API_CONFIG.BASE_URL}`);

          // ユーザーが設定したURLがあるか確認（チャット画面用）
          const userBaseUrl = localStorage.getItem("apiBaseUrl");
          if (!userBaseUrl) {
            // 設定ファイルのバックエンドAPIのURLを設定（チャット画面用）
            console.log(`デフォルトのバックエンドAPIのURLを設定: ${API_CONFIG.BASE_URL}`);
            localStorage.setItem("apiBaseUrl", API_CONFIG.BASE_URL);

            // 入力フィールドの値を更新（存在する場合）
            const baseUrlInput = document.getElementById("base-url-input") as HTMLInputElement;
            if (baseUrlInput) {
              baseUrlInput.value = API_CONFIG.BASE_URL;
            }
          }
        } catch (error) {
          console.error("バックエンド設定情報の取得に失敗しました:", error);
        }

        // 先にデフォルト値を設定し、長時間のロードを避ける
        if (!selectedBackend) {
          console.log("Setting default backend: ollama");
          onBackendSelect("ollama");
        }
        if (!selectedModel) {
          console.log("Setting default model: llama3.2:3b");
          onModelSelect("llama3.2:3b");
        }

        // バックエンドリストを非同期で取得
        console.log("バックエンドを取得しています...");
        let backendsLoaded = false;
        let retryCount = 0;
        const maxRetries = 3;

        while (!backendsLoaded && retryCount < maxRetries) {
          try {
            console.log("バックエンド取得試行 #" + (retryCount + 1));
            const backendsData: BackendsResponse =
              await apiService.fetchBackends();
            console.log("バックエンド読み込み完了:", backendsData);

            // バックエンドデータの詳細をログに出力
            if (backendsData && backendsData.backends) {
              console.log("バックエンド詳細情報:", JSON.stringify(backendsData, null, 2));

              // 各バックエンドの利用可能性を確認
              backendsData.backends.forEach((backend: any) => {
                console.log(`バックエンド ${backend.id}: available=${backend.available}, endpoint=${backend.endpoint}`);
              });
            }

            if (
              backendsData &&
              backendsData.backends &&
              backendsData.backends.length > 0
            ) {
              setBackends(backendsData.backends);
              setDefaultBackend(backendsData.default_backend);
              console.log("利用可能なバックエンド:", backendsData.backends.map(b => b.id).join(", "));
              console.log("デフォルトバックエンド:", backendsData.default_backend);

              // 選択されたバックエンドがない場合、デフォルトのバックエンドを選択
              if (!selectedBackend && backendsData.default_backend) {
                console.log(
                  `Selecting default backend from API: ${backendsData.default_backend}`,
                );
                onBackendSelect(backendsData.default_backend);
              }
              backendsLoaded = true;
            } else {
              console.warn("Invalid backends data format:", backendsData);
              retryCount++;
              if (retryCount >= maxRetries) {
                // デフォルトのバックエンドを使用
                setBackends([
                  {
                    id: "ollama",
                    name: "Ollama",
                    description: "Local inference using Ollama",
                    supports_streaming: true,
                  },
                ]);
                setDefaultBackend("ollama");
              } else {
                await new Promise((resolve) => setTimeout(resolve, 1000)); // 1秒待ってから再試行
              }
            }
          } catch (err) {
            console.error(
              `Error loading backends (attempt ${retryCount + 1}/${maxRetries}):`,
              err,
            );
            retryCount++;
            if (retryCount >= maxRetries) {
              // デフォルトのバックエンドを使用
              setBackends([
                {
                  id: "ollama",
                  name: "Ollama",
                  description: "Local inference using Ollama",
                  supports_streaming: true,
                },
              ]);
              setDefaultBackend("ollama");
            } else {
              await new Promise((resolve) => setTimeout(resolve, 1000)); // 1秒待ってから再試行
            }
          }
        }

        // モデルリストを非同期で取得
        console.log("Fetching models...");
        let modelsLoaded = false;
        retryCount = 0;

        while (!modelsLoaded && retryCount < maxRetries) {
          try {
            console.log("Attempting to fetch models, attempt #" + (retryCount + 1));
            const modelsData = await apiService.fetchModels();
            console.log("Models loaded:", modelsData);
            if (modelsData && modelsData.length > 0) {
              // 重複するモデルIDを除去
              const uniqueModels = removeDuplicateModels(modelsData);
              console.log(`モデル数: 元の ${modelsData.length} -> 重複除去後 ${uniqueModels.length}`);

              setModels(uniqueModels);
              console.log("Available models:", uniqueModels.map(m => m.id).join(", "));
              console.log("Default model:", uniqueModels.find(m => m.default)?.id || "none");

              // 選択されたモデルがない場合、デフォルトのモデルを選択
              if (!selectedModel) {
                // findメソッドではなくループを使用してデフォルトモデルを見つける
                let defaultModel = modelsData[0];
                for (let i = 0; i < modelsData.length; i++) {
                  if (modelsData[i].default) {
                    defaultModel = modelsData[i];
                    break;
                  }
                }
                console.log(
                  `Selecting default model from API: ${defaultModel.id}`,
                );
                onModelSelect(defaultModel.id);
              }
              modelsLoaded = true;
            } else {
              console.warn("No models returned from API or invalid format");
              retryCount++;
              if (retryCount >= maxRetries) {
                // モデルが取得できない場合は空の配列を使用
                console.warn("モデルリストが取得できませんでした。空のリストを使用します。");
                setModels([]);
              } else {
                await new Promise((resolve) => setTimeout(resolve, 1000)); // 1秒待ってから再試行
              }
            }
          } catch (err) {
            console.error(
              `Error loading models (attempt ${retryCount + 1}/${maxRetries}):`,
              err,
            );
            retryCount++;
            if (retryCount >= maxRetries) {
              // モデルが取得できない場合は空の配列を使用
              console.warn("モデルリストが取得できませんでした。空のリストを使用します。");
              setModels([]);
            } else {
              await new Promise((resolve) => setTimeout(resolve, 1000)); // 1秒待ってから再試行
            }
          }
        }

        setLoading(false);
        setError(null);
      } catch (err) {
        console.error("Error in loadData:", err);
        setError("Failed to load models and backends");
        setLoading(false);
        // エラーが発生した場合は空の配列を使用
        console.warn("モデルリストの取得中にエラーが発生しました。空のリストを使用します。");
        setModels([]);
        setBackends([]);
      }
    };

    loadData();
  }, [selectedModel, selectedBackend, onModelSelect, onBackendSelect]);

  // 処理モデル選択
  const handleModelChange = (e: ChangeEvent<HTMLSelectElement>): void => {
    const modelId = e.target.value;

    // モデル選択をローカルストレージに保存（画面リフレッシュなしでも反映されるように）
    localStorage.setItem("selectedModel", modelId);

    // 親コンポーネントに通知
    onModelSelect(modelId);

    // 設定画面内での選択の場合は、画面リフレッシュを行わない
    console.log(`モデルを選択しました: ${modelId} (設定画面内: ${inSettingsMenu})`);
  };

  // 処理バックエンド選択
  const handleBackendChange = (e: ChangeEvent<HTMLSelectElement>): void => {
    const backendId = e.target.value;

    // 選択されたバックエンドが利用可能かどうかを確認
    if (backendId) {
      const selectedBackend = backends.find(b => b.id === backendId);
      if (selectedBackend) {
        // backend.availableがundefinedの場合はtrueとして扱う
        const isAvailable = selectedBackend.available !== false;
        console.log(`選択されたバックエンド ${backendId} の利用可能性:`, isAvailable);

        // 利用不可のバックエンドが選択された場合は処理しない
        if (!isAvailable) {
          console.warn(`利用不可のバックエンド ${backendId} が選択されました。選択を無視します。`);
          return;
        }
      }
    }

    onBackendSelect(backendId);

    // バックエンド選択時の処理
    // 注意: ここではbase URLを変更しない
    // フロントエンド→バックエンドAPIの通信は常に固定のURL（8000ポート）を使用
    // バックエンド→LLMサービスの通信は設定ファイルに基づいて異なるバックエンドのURLを使用
    // 設定画面のドロップダウンリスト切り替え時は画面のURLを使用せず、単なる値として扱う
    console.log(`バックエンド ${backendId} が選択されました。フロントエンド→バックエンドAPIの通信URLは変更されません。`);

    // 当バックエンド変化時、再ロードモデルリスト
    const loadModels = async () => {
      try {
        setLoading(true);
        setError(null);

        let retryCount = 0;
        const maxRetries = 3;
        let modelsLoaded = false;

        while (!modelsLoaded && retryCount < maxRetries) {
          try {
            const modelsData = await apiService.fetchModels();
            console.log("Models loaded for backend change:", modelsData);

            if (modelsData && modelsData.length > 0) {
              // 重複するモデルIDを除去
              const uniqueModels = removeDuplicateModels(modelsData);
              console.log(`モデル数: 元の ${modelsData.length} -> 重複除去後 ${uniqueModels.length}`);

              setModels(uniqueModels);

              // 選択適合現バックエンドのデフォルトモデル
              const backendModels = uniqueModels.filter(
                (m) => !m.backend || m.backend === backendId,
              );
              if (backendModels.length > 0) {
                // findメソッドではなくループを使用してデフォルトモデルを見つける
                let defaultModel = backendModels[0];

                for (let i = 0; i < backendModels.length; i++) {
                  if (backendModels[i].default) {
                    defaultModel = backendModels[i];
                    break;
                  }
                }

                console.log(
                  `Selecting model for backend ${backendId}: ${defaultModel.id}`,
                );
                onModelSelect(defaultModel.id);
              } else {
                console.warn(`No models available for backend ${backendId}`);
                // 選択されたバックエンドに適合するモデルがない場合、警告を表示
                console.warn(`バックエンド ${backendId} に適合するモデルが見つかりませんでした。`);
                setError(`バックエンド ${backendId} に適合するモデルが見つかりませんでした。`);
                // 既存のモデルデータはそのまま使用
                setModels(modelsData);
              }
              modelsLoaded = true;
            } else {
              console.warn("No models returned from API or invalid format");
              retryCount++;
              if (retryCount >= maxRetries) {
                // もしすべての試行が失敗した場合、空の配列を使用
                console.warn(`バックエンド ${backendId} のモデルリストが取得できませんでした。空のリストを使用します。`);
                setModels([]);
                setError(`バックエンド ${backendId} のモデルリストが取得できませんでした。`);
              } else {
                await new Promise((resolve) => setTimeout(resolve, 1000)); // 1秒待ってから再試行
              }
            }
          } catch (err) {
            console.error(
              `Error loading models for backend (attempt ${retryCount + 1}/${maxRetries}):`,
              err,
            );
            retryCount++;
            if (retryCount >= maxRetries) {
              // もしすべての試行が失敗した場合、空の配列を使用
              console.warn(`バックエンド ${backendId} のモデルリストが取得できませんでした。空のリストを使用します。`);
              setModels([]);
              setError(`バックエンド ${backendId} のモデルリストが取得できませんでした。エラー: ${err instanceof Error ? err.message : String(err)}`);
            } else {
              await new Promise((resolve) => setTimeout(resolve, 1000)); // 1秒待ってから再試行
            }
          }
        }

        setLoading(false);
      } catch (err) {
        console.error("Error in loadModels:", err);
        // もしエラーが発生した場合、空の配列を使用
        console.warn(`モデルリストの取得中にエラーが発生しました。空のリストを使用します。エラー: ${err instanceof Error ? err.message : String(err)}`);
        setModels([]);
        setError(`モデルリストの取得中にエラーが発生しました。エラー: ${err instanceof Error ? err.message : String(err)}`);
        setLoading(false);
      }
    };

    loadModels();
  };

  // 切り替え表示バックエンドセレクタ
  const toggleBackendSelector = (): void => {
    setShowBackendSelector(!showBackendSelector);
  };

  // 設定メニューにない場合、モデル名のみ表示
  if (!inSettingsMenu) {
    return (
      <div className="model-name-display">
        {selectedModel || "Loading model..."}
      </div>
    );
  }

  // 追加インラインスタイル
  const loadingContainerStyle = {
    display: "flex",
    flexDirection: "column" as const,
    alignItems: "center",
    justifyContent: "center",
    padding: "1rem",
    gap: "0.5rem",
  };

  const loadingStyle = {
    fontSize: "1rem",
    fontWeight: "bold" as const,
  };

  const loadingMessageStyle = {
    fontSize: "0.875rem",
    color: "var(--text-secondary)",
  };

  if (loading) {
    return (
      <div style={loadingContainerStyle}>
        <div style={loadingStyle}>{t("loading")}</div>
        <div style={loadingMessageStyle}>{t("loading_models")}</div>
      </div>
    );
  }

  if (error) {
    console.error("Error in ModelSelector:", error);
    // エラー情報を表示してもコンポーネントをレンダリング
    return (
      <div className="model-selector-error">
        <div className="error-message">{error}</div>
        <div className="model-selector">
          <div className="model-dropdown">
            <label htmlFor="model-select">{t("model")}:</label>
            <select
              id="model-select"
              value={selectedModel || ""}
              onChange={handleModelChange}
              className="select-input"
            >
              <option value="" disabled>
                {t("select_model")}
              </option>
              {models.map((model) => (
                <option
                  key={model.id}
                  value={model.id}
                  className={model.default ? "default-option" : ""}
                >
                  {model.id} {model.default ? "(Default)" : ""}
                </option>
              ))}
            </select>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`model-selector ${inSettingsMenu ? "in-settings" : ""}`}>
      {inSettingsMenu && (
        <div className="base-url-input">
          <label htmlFor="base-url-input">{t("base_url")} ({t("optional")}):</label>
          <input
            id="base-url-input"
            type="text"
            placeholder={`Default: ${API_CONFIG.BASE_URL}`}
            className="text-input"
            defaultValue={localStorage.getItem("apiBaseUrl") || ""}
            onChange={(e) => {
              const url = e.target.value.trim();
              if (url) {
                localStorage.setItem("apiBaseUrl", url);
                console.log(`バックエンドAPI URL設定: ${url}`);
              } else {
                localStorage.removeItem("apiBaseUrl");
                console.log(`バックエンドAPI URLをクリアしました。デフォルトURL(${API_CONFIG.BASE_URL})を使用します。`);
              }
            }}
          />
          <div className="base-url-help">
            {t("base_url_help")}
            <div className="base-url-note">
              注意: このURLはフロントエンドからバックエンドAPIへの通信に使用されます。
              デフォルトでは {API_CONFIG.BASE_URL} が使用されます。
            </div>
          </div>
        </div>
      )}
      <div className="model-dropdown">
        <label htmlFor="model-select">{t("model")}:</label>
        <select
          id="model-select"
          value={selectedModel || ""}
          onChange={handleModelChange}
          className="select-input"
        >
          <option value="" disabled>
            {t("select_model")}
          </option>
          {models
            .filter(
              (model) =>
                !selectedBackend ||
                !model.backend ||
                model.backend === selectedBackend,
            )
            .map((model) => (
              <option
                key={model.id}
                value={model.id}
                className={model.default ? "default-option" : ""}
              >
                {model.id} {model.default ? "(Default)" : ""}
              </option>
            ))}
        </select>
      </div>

      <div className="advanced-options">
        <button
          onClick={toggleBackendSelector}
          className="advanced-toggle"
          title="Advanced options"
          type="button"
        >
          {showBackendSelector
            ? `▲ ${t("hide_advanced")}`
            : `▼ ${t("show_advanced")}`}
        </button>

        {showBackendSelector && (
          <div className="backend-dropdown">
            <label htmlFor="backend-select">{t("backend")}:</label>
            <select
              id="backend-select"
              value={selectedBackend || ""}
              onChange={handleBackendChange}
              className="select-input"
            >
              <option value="">{t("auto_backend")}</option>
              {backends.map((backend) => {
                // backend.availableがundefinedの場合はtrueとして扱う
                const isAvailable = backend.available !== false;
                console.log(`バックエンド ${backend.id} の利用可能性:`, isAvailable, backend);

                // 利用可能性に基づいてクラス名を設定
                const optionClassName = !isAvailable ? "unavailable-backend" : "";

                return (
                  <option
                    key={backend.id}
                    value={backend.id}
                    disabled={!isAvailable} // 利用不可のバックエンドは無効化
                    className={optionClassName}
                  >
                    {backend.name} {!isAvailable && '(現在利用不可)'}
                  </option>
                );
              })}
            </select>

            <div className="backend-info">
              {selectedBackend &&
                backends.find((b) => b.id === selectedBackend) && (
                  <p>
                    {
                      backends.find((b) => b.id === selectedBackend)
                        ?.description
                    }
                  </p>
                )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ModelSelector;

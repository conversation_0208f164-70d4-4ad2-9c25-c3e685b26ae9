import React from "react";
// import { useTranslation } from "react-i18next";
import ReactMarkdown from "react-markdown";
import { Message } from "../types/chat";
import { useAuthStore } from "../store/authStore";
import CodeBlock from "./CodeBlock";
import "../styles/CodeBlock.css";

interface MessageListProps {
  messages: Message[];
}

const MessageList: React.FC<MessageListProps> = ({ messages }) => {
  // 暫定的に翻訳機能は不要
  // const { t } = useTranslation();
  const user = useAuthStore((state) => state.user);

  // デバッグログを追加して、レンダリングされるメッセージを確認
  console.log("MessageList render - messages count:", messages?.length || 0);
  console.log("MessageList render - messages details:", messages?.map((msg, index) => ({
    index,
    role: msg.role,
    contentLength: msg.content?.length || 0,
    contentPreview: msg.content?.substring(0, 50) + (msg.content?.length > 50 ? "..." : "")
  })));

  if (!messages || messages.length === 0) {
    console.log("表示するメッセージがありません");
    return (
      <div className="empty-messages">
        {/* 空のメッセージの場合、何も表示しません。ウェルカムメッセージはチャット画面に表示されます */}
      </div>
    );
  }

  return (
    <div className="message-list">
      {messages.map((message, index) => (
        <div
          key={`${message.role}-${index}-${message.content.substring(0, 20)}`}
          className={`message ${message.role === "user" ? "user-message" : "assistant-message"}`}
        >
          <div className="message-content-container">
            <div
              className={`message-avatar ${message.role === "user" ? "user-avatar" : "assistant-avatar"}`}
            >
              {message.role === "user"
                ? user?.username
                  ? user.username.substring(0, 1).toUpperCase()
                  : "U"
                : "A"}
            </div>
            <div className="message-content">
              <ReactMarkdown
                components={{
                  code({ className, children, ...props }) {
                    const match = /language-(\w+)/.exec(className || "");
                    return match ? (
                      <CodeBlock
                        language={match[1]}
                        className={className}
                        {...props}
                      >
                        {String(children).replace(/\n$/, "")}
                      </CodeBlock>
                    ) : (
                      <code className={className} {...props}>
                        {children}
                      </code>
                    );
                  },
                }}
              >
                {message.content}
              </ReactMarkdown>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};

export default MessageList;

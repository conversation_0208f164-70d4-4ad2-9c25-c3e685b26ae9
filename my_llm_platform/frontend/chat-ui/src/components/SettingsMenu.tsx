import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import ModelSelector from "./ModelSelector";
import TuningSettings from "./TuningSettings";
import ChatPluginSelector from "./ChatPluginSelector";
import { useTheme } from "../hooks/useTheme";
import { TuningProfile } from "../types/chat";

interface SettingsMenuProps {
  isOpen: boolean;
  onClose: () => void;
  selectedModel: string | null;
  selectedBackend: string | null;
  onModelSelect: (_modelId: string) => void;
  onBackendSelect: (_backendId: string) => void;
  selectedProfile: TuningProfile | null;
  onProfileSelect: (_profile: TuningProfile | null) => void;
  streamMode?: boolean;
  onStreamModeChange?: (_enabled: boolean) => void;
  selectedPlugins?: string[];
  onPluginsChange?: (_plugins: string[]) => void;
  useTools?: boolean;
  onUseToolsChange?: (_enabled: boolean) => void;
}

type SettingsTab = "model" | "tuning" | "plugins" | "interface" | "about";

const SettingsMenu: React.FC<SettingsMenuProps> = ({
  isOpen,
  onClose,
  selectedModel,
  selectedBackend,
  onModelSelect,
  onBackendSelect,
  selectedProfile,
  onProfileSelect,
  streamMode = true,
  onStreamModeChange,
  selectedPlugins = [],
  onPluginsChange,
  useTools = true,
  onUseToolsChange,
}) => {
  const { t, i18n } = useTranslation();
  const { theme, setTheme } = useTheme();
  const [activeTab, setActiveTab] = useState<SettingsTab>("model");

  // 言語を切り替える
  const changeLanguage = (lng: string) => {
    console.log(`Changing language to: ${lng}`);
    i18n.changeLanguage(lng);
    // すべてのコンポーネントが新しい言語を適用することを確認するためにページを強制的にリロード
    setTimeout(() => {
      window.location.reload();
    }, 100);
  };

  if (!isOpen) return null;

  return (
    <div className="settings-overlay">
      <div className="settings-modal">
        <div className="settings-header">
          <div style={{ width: "100%", display: "flex", justifyContent: "center", position: "relative" }}>
            <h2 style={{ margin: 0 }}>{t("settings_label")}</h2>
            <div style={{ position: "absolute", right: "0", top: "0" }}>
              <button
                onClick={onClose}
                style={{
                  background: "none",
                  border: "none",
                  fontSize: "1.5rem",
                  cursor: "pointer",
                  color: "white",
                  opacity: 0.8,
                  padding: "0",
                  margin: "0",
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                  width: "40px",
                  height: "40px"
                }}
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <line x1="18" y1="6" x2="6" y2="18"></line>
                  <line x1="6" y1="6" x2="18" y2="18"></line>
                </svg>
              </button>
            </div>
          </div>
        </div>

        <div className="settings-content">
          <div className="settings-sidebar">
            <ul>
              <li
                className={activeTab === "model" ? "active" : ""}
                onClick={() => setActiveTab("model")}
              >
                {t("model_settings")}
              </li>
              <li
                className={activeTab === "tuning" ? "active" : ""}
                onClick={() => setActiveTab("tuning")}
              >
                {t("tuning_settings")}
              </li>
              <li
                className={activeTab === "plugins" ? "active" : ""}
                onClick={() => setActiveTab("plugins")}
              >
                {t("plugins.title")}
              </li>
              <li
                className={activeTab === "interface" ? "active" : ""}
                onClick={() => setActiveTab("interface")}
              >
                {t("interface_settings")}
              </li>
              <li
                className={activeTab === "about" ? "active" : ""}
                onClick={() => setActiveTab("about")}
              >
                {t("about")}
              </li>
            </ul>
          </div>

          <div className="settings-panel">
            {activeTab === "model" && (
              <div className="model-settings">
                <h3>{t("model")}</h3>
                <ModelSelector
                  onModelSelect={onModelSelect}
                  onBackendSelect={onBackendSelect}
                  selectedModel={selectedModel}
                  selectedBackend={selectedBackend}
                  inSettingsMenu={true}
                />
              </div>
            )}

            {activeTab === "tuning" && (
              <div className="tuning-settings-container">
                <TuningSettings
                  onProfileSelect={onProfileSelect}
                  selectedProfile={selectedProfile}
                />
              </div>
            )}

            {activeTab === "plugins" && (
              <div className="plugins-settings-container">
                <h3>{t("plugins.title")}</h3>
                <ChatPluginSelector
                  selectedPlugins={selectedPlugins}
                  onPluginsChange={onPluginsChange || (() => {})}
                />
                <div className="mt-4">
                  <h4>{t("plugins.toolsSettings")}</h4>
                  <div className="toggle-container">
                    <label className="toggle-switch">
                      <input
                        type="checkbox"
                        checked={useTools}
                        onChange={(e) => onUseToolsChange && onUseToolsChange(e.target.checked)}
                      />
                      <span className="toggle-slider"></span>
                    </label>
                    <span className="toggle-label">
                      {useTools ? t("plugins.toolsEnabled") : t("plugins.toolsDisabled")}
                    </span>
                  </div>
                  <div className="mode-description">
                    {useTools
                      ? t("plugins.toolsEnabledDescription")
                      : t("plugins.toolsDisabledDescription")}
                  </div>
                </div>
              </div>
            )}

            {activeTab === "interface" && (
              <div className="interface-settings">
                <h3>{t("language")}</h3>
                <div className="language-selector">
                  <button
                    className={i18n.language === "en" ? "active" : ""}
                    onClick={() => changeLanguage("en")}
                  >
                    English
                  </button>
                  <button
                    className={i18n.language === "ja" ? "active" : ""}
                    onClick={() => changeLanguage("ja")}
                  >
                    日本語
                  </button>
                  <button
                    className={i18n.language === "zh" ? "active" : ""}
                    onClick={() => changeLanguage("zh")}
                  >
                    中文
                  </button>
                </div>

                <h3>{t("theme")}</h3>
                <div className="theme-selector">
                  <button
                    className={theme === "light" ? "active" : ""}
                    onClick={() => setTheme("light")}
                  >
                    {t("light")}
                  </button>
                  <button
                    className={theme === "dark" ? "active" : ""}
                    onClick={() => setTheme("dark")}
                  >
                    {t("dark")}
                  </button>
                  <button
                    className={theme === "system" ? "active" : ""}
                    onClick={() => setTheme("system")}
                  >
                    {t("system")}
                  </button>
                </div>

                <h3>{t("response_mode")}</h3>
                <div className="stream-mode-selector">
                  <div className="toggle-container">
                    <label className="toggle-switch">
                      <input
                        type="checkbox"
                        checked={streamMode}
                        onChange={(e) => onStreamModeChange && onStreamModeChange(e.target.checked)}
                      />
                      <span className="toggle-slider"></span>
                    </label>
                    <span className="toggle-label">
                      {streamMode ? t("stream_mode") : t("complete_mode")}
                    </span>
                  </div>
                  <div className="mode-description">
                    {streamMode
                      ? t("stream_mode_description")
                      : t("complete_mode_description")}
                  </div>
                </div>
              </div>
            )}

            {activeTab === "about" && (
              <div className="about-settings">
                <h3>{t("app_name")}</h3>
                <p>{t("version")}: 1.0.0</p>
                <p>© 2023 My LLM Platform</p>
              </div>
            )}
          </div>
        </div>

        <div className="settings-footer">
          <button className="close-settings" onClick={onClose}>
            {t("close")}
          </button>
        </div>
      </div>
    </div>
  );
};

export default SettingsMenu;

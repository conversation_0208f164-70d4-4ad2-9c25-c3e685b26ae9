import React, { useState, useEffect, FormEvent, ChangeEvent } from "react";
import { useTranslation } from "react-i18next";
import {
  fetchProfiles,
  createProfile,
  updateProfile,
  deleteProfile,
  testProfile,
} from "../services/tuningService";
import {
  TuningProfile,
  TuningParameter,
  TestProfileResponse,
} from "../types/chat";

interface TuningProfilesProps {
  onProfileSelect: (_profile: TuningProfile) => void;
  selectedModel: string | null;
  inSettingsMenu?: boolean;
}

interface FormData {
  name: string;
  description: string;
  model: string;
  backend: string;
  parameters: TuningParameter[];
}

const TuningProfiles: React.FC<TuningProfilesProps> = ({
  onProfileSelect,
  selectedModel,
  inSettingsMenu = false,
}) => {
  const { t } = useTranslation();
  const [profiles, setProfiles] = useState<TuningProfile[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedProfile, setSelectedProfile] = useState<TuningProfile | null>(
    null,
  );
  const [isEditing, setIsEditing] = useState<boolean>(false);
  const [isTesting, setIsTesting] = useState<boolean>(false);
  const [testPrompt, setTestPrompt] = useState<string>("");
  const [testResult, setTestResult] = useState<TestProfileResponse | null>(
    null,
  );

  // 編集フォームの状態
  const [formData, setFormData] = useState<FormData>({
    name: "",
    description: "",
    model: selectedModel || "",
    backend: "",
    parameters: [
      { name: "temperature", value: 0.7, description: "Controls randomness" },
      { name: "top_p", value: 0.9, description: "Nucleus sampling parameter" },
      { name: "top_k", value: 50, description: "Top-k sampling parameter" },
    ],
  });

  // プロファイルをロード
  useEffect(() => {
    const loadProfiles = async (): Promise<void> => {
      setLoading(true);
      try {
        const data = await fetchProfiles();
        setProfiles(data.profiles);
        setLoading(false);
      } catch (err) {
        setError("Failed to load tuning profiles");
        setLoading(false);
        console.error(err);
      }
    };

    loadProfiles();
  }, []);

  // 選択されたモデルが変更されたときにフォームデータを更新
  useEffect(() => {
    if (selectedModel && isEditing) {
      setFormData((prev) => ({ ...prev, model: selectedModel }));
    }
  }, [selectedModel, isEditing]);

  // プロファイル選択を処理
  const handleProfileSelect = (profile: TuningProfile): void => {
    setSelectedProfile(profile);
    if (onProfileSelect) {
      onProfileSelect(profile);
    }
  };

  // 新しいプロファイルを作成
  const handleCreateProfile = (): void => {
    setIsEditing(true);
    setSelectedProfile(null);
    setFormData({
      name: "",
      description: "",
      model: selectedModel || "",
      backend: "",
      parameters: [
        { name: "temperature", value: 0.7, description: "Controls randomness" },
        {
          name: "top_p",
          value: 0.9,
          description: "Nucleus sampling parameter",
        },
        { name: "top_k", value: 50, description: "Top-k sampling parameter" },
      ],
    });
  };

  // 既存のプロファイルを編集
  const handleEditProfile = (): void => {
    if (!selectedProfile) return;

    setIsEditing(true);
    setFormData({
      name: selectedProfile.name,
      description: selectedProfile.description || "",
      model: selectedProfile.model,
      backend: selectedProfile.backend || "",
      parameters: selectedProfile.parameters,
    });
  };

  // プロファイルを削除
  const handleDeleteProfile = async (): Promise<void> => {
    if (!selectedProfile) return;

    if (
      window.confirm(
        `Are you sure you want to delete the profile "${selectedProfile.name}"?`,
      )
    ) {
      try {
        await deleteProfile(selectedProfile.name);
        setProfiles(profiles.filter((p) => p.name !== selectedProfile.name));
        setSelectedProfile(null);
      } catch (err) {
        setError(
          `Failed to delete profile: ${err instanceof Error ? err.message : String(err)}`,
        );
        console.error(err);
      }
    }
  };

  // 处理表单提交
  const handleSubmit = async (e: FormEvent<HTMLFormElement>): Promise<void> => {
    e.preventDefault();

    try {
      let result;
      if (selectedProfile) {
        // 更新现有配置文件
        result = await updateProfile(
          selectedProfile.name,
          formData as TuningProfile,
        );
      } else {
        // 创建新配置文件
        result = await createProfile(formData as TuningProfile);
      }

      // 更新配置文件列表
      setProfiles((prev) => {
        const filtered = prev.filter((p) => p.name !== formData.name);
        return [...filtered, result.profile];
      });

      setSelectedProfile(result.profile);
      setIsEditing(false);
    } catch (err) {
      setError(
        `Failed to save profile: ${err instanceof Error ? err.message : String(err)}`,
      );
      console.error(err);
    }
  };

  // 处理表单输入变化
  const handleInputChange = (
    e: ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
  ): void => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  // 处理参数变化
  const handleParameterChange = (
    index: number,
    field: string,
    value: string | number,
  ): void => {
    setFormData((prev) => {
      const newParams = [...prev.parameters];
      newParams[index] = {
        ...newParams[index],
        [field]: field === "value" ? parseFloat(value as string) : value,
      };
      return { ...prev, parameters: newParams };
    });
  };

  // 添加新参数
  const handleAddParameter = (): void => {
    setFormData((prev) => ({
      ...prev,
      parameters: [...prev.parameters, { name: "", value: 0, description: "" }],
    }));
  };

  // 删除参数
  const handleRemoveParameter = (index: number): void => {
    setFormData((prev) => ({
      ...prev,
      parameters: prev.parameters.filter((_, i) => i !== index),
    }));
  };

  // 测试配置文件
  const handleTestProfile = async (): Promise<void> => {
    if (!selectedProfile || !testPrompt) return;

    setIsTesting(true);
    try {
      const result = await testProfile(selectedProfile.name, testPrompt);
      setTestResult(result);
    } catch (err) {
      setError(
        `Test failed: ${err instanceof Error ? err.message : String(err)}`,
      );
      console.error(err);
    } finally {
      setIsTesting(false);
    }
  };

  if (loading) return <div className="loading">{t("loading")}</div>;
  if (error)
    return (
      <div className="error">
        {t("error")}: {error}
      </div>
    );

  return (
    <div className={`tuning-profiles ${inSettingsMenu ? "in-settings" : ""}`}>
      <h2>{t("tuning_profiles")}</h2>

      {isEditing ? (
        <div className="profile-editor">
          <h3>{selectedProfile ? "Edit Profile" : "Create New Profile"}</h3>
          <form onSubmit={handleSubmit}>
            <div className="form-group">
              <label htmlFor="name">Name:</label>
              <input
                type="text"
                id="name"
                name="name"
                value={formData.name}
                onChange={handleInputChange}
                required
              />
            </div>

            <div className="form-group">
              <label htmlFor="description">Description:</label>
              <textarea
                id="description"
                name="description"
                value={formData.description}
                onChange={handleInputChange}
              />
            </div>

            <div className="form-group">
              <label htmlFor="model">Model:</label>
              <input
                type="text"
                id="model"
                name="model"
                value={formData.model}
                onChange={handleInputChange}
                required
              />
            </div>

            <div className="form-group">
              <label htmlFor="backend">Backend (optional):</label>
              <input
                type="text"
                id="backend"
                name="backend"
                value={formData.backend}
                onChange={handleInputChange}
              />
            </div>

            <h4>Parameters</h4>
            {formData.parameters.map((param, index) => (
              <div key={index} className="parameter-row">
                <div className="form-group">
                  <label>Name:</label>
                  <input
                    type="text"
                    value={param.name}
                    onChange={(e) =>
                      handleParameterChange(index, "name", e.target.value)
                    }
                    required
                  />
                </div>

                <div className="form-group">
                  <label>Value:</label>
                  <input
                    type="number"
                    step="0.1"
                    value={String(param.value)}
                    onChange={(e) =>
                      handleParameterChange(index, "value", e.target.value)
                    }
                    required
                  />
                </div>

                <div className="form-group">
                  <label>Description:</label>
                  <input
                    type="text"
                    value={param.description || ""}
                    onChange={(e) =>
                      handleParameterChange(
                        index,
                        "description",
                        e.target.value,
                      )
                    }
                  />
                </div>

                <button
                  type="button"
                  className="remove-btn"
                  onClick={() => handleRemoveParameter(index)}
                >
                  Remove
                </button>
              </div>
            ))}

            <button
              type="button"
              className="add-btn"
              onClick={handleAddParameter}
            >
              Add Parameter
            </button>

            <div className="form-actions">
              <button type="submit" className="save-btn">
                Save Profile
              </button>
              <button
                type="button"
                className="cancel-btn"
                onClick={() => setIsEditing(false)}
              >
                Cancel
              </button>
            </div>
          </form>
        </div>
      ) : (
        <div className="profiles-container">
          <div className="profiles-list">
            <div className="profiles-header">
              <h3>Available Profiles</h3>
              <button className="create-btn" onClick={handleCreateProfile}>
                Create New
              </button>
            </div>

            <ul>
              {profiles.map((profile) => (
                <li
                  key={profile.name}
                  className={
                    selectedProfile?.name === profile.name ? "selected" : ""
                  }
                  onClick={() => handleProfileSelect(profile)}
                >
                  <div className="profile-name">{profile.name}</div>
                  <div className="profile-model">{profile.model}</div>
                </li>
              ))}
            </ul>
          </div>

          {selectedProfile && (
            <div className="profile-details">
              <h3>{selectedProfile.name}</h3>
              {selectedProfile.description && (
                <p className="profile-description">
                  {selectedProfile.description}
                </p>
              )}

              <div className="profile-info">
                <div className="info-item">
                  <span className="label">Model:</span>
                  <span className="value">{selectedProfile.model}</span>
                </div>

                {selectedProfile.backend && (
                  <div className="info-item">
                    <span className="label">Backend:</span>
                    <span className="value">{selectedProfile.backend}</span>
                  </div>
                )}
              </div>

              <h4>Parameters</h4>
              <ul className="parameters-list">
                {selectedProfile.parameters.map((param, index) => (
                  <li key={index} className="parameter-item">
                    <div className="param-name">{param.name}</div>
                    <div className="param-value">{param.value}</div>
                    {param.description && (
                      <div className="param-description">
                        {param.description}
                      </div>
                    )}
                  </li>
                ))}
              </ul>

              <div className="profile-actions">
                <button className="edit-btn" onClick={handleEditProfile}>
                  Edit
                </button>
                <button className="delete-btn" onClick={handleDeleteProfile}>
                  Delete
                </button>
              </div>

              <div className="profile-test">
                <h4>Test Profile</h4>
                <div className="test-input">
                  <textarea
                    placeholder="Enter a prompt to test this profile..."
                    value={testPrompt}
                    onChange={(e) => setTestPrompt(e.target.value)}
                  />
                </div>

                <button
                  className="test-btn"
                  onClick={handleTestProfile}
                  disabled={isTesting || !testPrompt}
                >
                  {isTesting ? "Testing..." : "Test Profile"}
                </button>

                {testResult && (
                  <div className="test-result">
                    <h5>Test Result</h5>
                    <div className="result-prompt">
                      <strong>Prompt:</strong>
                      <p>{testResult.prompt}</p>
                    </div>
                    <div className="result-output">
                      <strong>Output:</strong>
                      <p>{testResult.output}</p>
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default TuningProfiles;

import React, { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { useAuthStore } from "../store/authStore";
import { useChatStore } from "../store/chatStore";
import { Link, useLocation, useNavigate } from "react-router-dom";

interface ChatSession {
  id: string;
  title: string;
  created_at: string;
  last_message: string;
}

interface SidebarProps {
  isCollapsed: boolean;
  toggleSidebar: () => void;
}

export const Sidebar: React.FC<SidebarProps> = ({
  isCollapsed,
  toggleSidebar,
}) => {
  const { t } = useTranslation();
  const { token, user } = useAuthStore();
  const resetChat = useChatStore((state) => state.resetChat);
  const setCurrentSessionId = useChatStore(
    (state) => state.setCurrentSessionId,
  );
  const [sessions, setSessions] = useState<ChatSession[]>([]);
  const location = useLocation();
  const navigate = useNavigate();

  useEffect(() => {
    // コンポーネントの初期化時とサイドバーが展開されたときのみセッション履歴を取得
    // 更新イベントを監視せず、初期化時とF5リフレッシュ時にのみセッション履歴を取得
    if (!isCollapsed) {
      console.log("Sidebar expanded or initialized, fetching chat history...");
      fetchChatHistory();
    }
  }, [token, isCollapsed]);

  const fetchChatHistory = async () => {
    try {
      // 新しく実装されたチャット履歴APIを使用
      // OPTIONSリクエストを手動で送信する必要はなく、ブラウザが自動的に処理

      // 実際のGETリクエストを送信
      // 環境変数を安全に使用
      // TypeScriptの静的型チェック問題を解決するために型アサーションを使用
      const apiBaseUrl = (import.meta as any).env.VITE_API_BASE_URL || "";
      const apiPrefix = (import.meta as any).env.VITE_API_PREFIX || "";
      const response = await fetch(`${apiBaseUrl}${apiPrefix}/chat/history`, {
        method: "GET",
        headers: {
          Authorization: `Bearer ${token || "demo-token"}`,
          "Content-Type": "application/json",
          Accept: "application/json",
        },
      });

      if (!response.ok) {
        // ステータスコードに基づいて異なるエラーを処理
        if (response.status === 401) {
          console.error("Authentication error: User not authenticated");
          // ログインプロンプトを表示
          alert(t("login_required"));
          // ログインページにリダイレクト
          navigate("/login");
        } else {
          console.error(`API error: ${response.status}`);
        }
        setSessions([]);
        return;
      }

      const data = await response.json();
      console.log("Fetched chat history:", data.length, "sessions");

      // 作成時間でソートし、最新のものを前に
      const sortedData = data.sort((a: ChatSession, b: ChatSession) => {
        return (
          new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
        );
      });

      setSessions(sortedData);
    } catch (error) {
      console.error("Failed to fetch chat history:", error);
      // エラーが発生した場合、空の配列を使用
      setSessions([]);
    }
  };

  const createNewChat = async () => {
    try {
      console.log("Creating new chat session...");

      // 現在のメッセージ状態を取得
      const currentMessages = useChatStore.getState().messages;
      const currentSessionId = useChatStore.getState().currentSessionId;

      // 意味のある会話があるかどうかを確認（少なくとも1つのユーザーメッセージと1つのアシスタントメッセージ）
      // ES5互換のためにsomeメソッドではなくループを使用
      let hasUserMessage = false;
      let hasAssistantMessage = false;

      for (let i = 0; i < currentMessages.length; i++) {
        const msg = currentMessages[i];
        if (msg.role === "user") {
          hasUserMessage = true;
        }
        if (msg.role === "assistant" && msg.content.trim() !== "") {
          hasAssistantMessage = true;
        }
        // ユーザーメッセージとアシスタントメッセージが見つかった場合、ループを早期終了
        if (hasUserMessage && hasAssistantMessage) {
          break;
        }
      }

      const hasRealConversation = hasUserMessage && hasAssistantMessage;

      // 現在のセッションに実質的な内容がない場合、新しいセッションを作成せずにリセット
      if (!hasRealConversation) {
        console.log("No real conversation in current session, just resetting");
        resetChat();
        navigate("/chat");
        return;
      }

      // 現在のセッションに意味がある場合、まず現在のセッションを保存
      if (currentSessionId && hasRealConversation) {
        console.log(
          "Current session has real conversation, saving before creating new one",
        );
        // 追加の保存ロジックは不要、メッセージは送信時にすでに保存されているため
      }

      // チャット状態をリセット
      resetChat();

      // 新しいセッションを作成するが、すぐに履歴に保存しない
      // ユーザーが最初のメッセージを送信し、アシスタントの応答を受け取ったときにのみ実際のセッション記録が作成される
      // ここでは新しいチャットインターフェースを初期化するだけ
      navigate("/chat");
    } catch (error) {
      console.error("Error creating new chat:", error);
      // エラーが発生してもチャットをリセットしてナビゲート
      resetChat();
      navigate("/chat");
    }
  };

  return (
    <div className={`sidebar ${isCollapsed ? "collapsed" : ""}`}>
      <div className="sidebar-header">
        <h1 className="sidebar-title">
          {isCollapsed ? "" : "My LLM Platform"}
        </h1>
        <button className="toggle-sidebar-button" onClick={toggleSidebar}>
          {isCollapsed ? (
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="16"
              height="16"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <polyline points="9 18 15 12 9 6"></polyline>
            </svg>
          ) : (
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="16"
              height="16"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <polyline points="15 18 9 12 15 6"></polyline>
            </svg>
          )}
        </button>
      </div>

      {!isCollapsed && (
        <>
          <button className="new-chat-button" onClick={createNewChat}>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="16"
              height="16"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <line x1="12" y1="5" x2="12" y2="19"></line>
              <line x1="5" y1="12" x2="19" y2="12"></line>
            </svg>
            {t("new_chat")}
          </button>

          <div className="chat-sessions">
            <h2 className="sessions-title">{t("chat.history")}</h2>
            <div className="sessions-list">
              {sessions.length > 0 ? (
                sessions
                  // 过滤掉没有实质内容的会话
                  .filter((session) => {
                    // 如果标题是 'New Chat' 或者空白，可能是没有实质内容的会话
                    return session.title && session.title !== "New Chat";
                  })
                  .map((session) => (
                    <div
                      key={session.id}
                      className="session-item"
                      onClick={() => {
                        // 设置当前会话ID
                        console.log(`Clicked on session: ${session.id}`);
                        setCurrentSessionId(session.id);
                        // 导航到聊天页面
                        navigate("/chat");
                      }}
                    >
                      <div className="session-title" title={session.title}>
                        {session.title}
                      </div>
                      <div className="session-date">
                        {new Date(session.created_at).toLocaleDateString()}
                      </div>
                    </div>
                  ))
              ) : (
                <div className="no-sessions">{t("no_chat_history")}</div>
              )}
            </div>
          </div>

          {user?.role === "admin" && (
            <div className="admin-section">
              <Link
                to="/admin"
                className={`admin-link ${location.pathname === "/admin" ? "active" : ""}`}
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <path d="M12 2a10 10 0 1 0 0 20 10 10 0 1 0 0-20z"></path>
                  <path d="M12 16v-4"></path>
                  <path d="M12 8h.01"></path>
                </svg>
                {t("admin_panel")}
              </Link>
            </div>
          )}
        </>
      )}
    </div>
  );
};

// src/components/LanguageSwitcher.tsx
import i18n from "i18next";

export default function LanguageSwitcher() {
  const changeLang = (lang: "ja" | "en") => i18n.changeLanguage(lang);

  return (
    <div className="flex gap-2 p-2 text-sm">
      <button
        onClick={() => changeLang("ja")}
        className="px-2 py-1 border rounded"
      >
        日本語
      </button>
      <button
        onClick={() => changeLang("en")}
        className="px-2 py-1 border rounded"
      >
        English
      </button>
    </div>
  );
}

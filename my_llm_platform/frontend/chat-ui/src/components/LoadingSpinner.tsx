import React from 'react';
import '../styles/LoadingSpinner.css';

/**
 * ローディングスピナーのプロパティインターフェース
 */
interface LoadingSpinnerProps {
  /** スピナーのサイズ */
  size?: 'small' | 'medium' | 'large';
  /** スピナーの色 */
  color?: string;
}

/**
 * ローディングスピナーコンポーネント
 * 読み込み中や処理中の状態を表示するための回転アニメーション付きスピナー
 */
const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  size = 'small',
  color = 'var(--primary-color)'
}) => {
  return (
    <div className={`loading-spinner ${size}`} style={{ borderTopColor: color }}>
      <span className="sr-only">Loading...</span>
    </div>
  );
};

export default LoadingSpinner;

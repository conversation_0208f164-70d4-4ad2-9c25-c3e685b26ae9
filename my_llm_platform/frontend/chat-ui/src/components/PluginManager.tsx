import React, { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { apiService } from "../services/apiService";
import { serviceFactory } from "../services/serviceFactory";

interface Plugin {
  id: string;
  name: string;
  description: string;
  type: string;
  version: string;
  author: string;
  config: {
    enabled: boolean;
    priority: number;
    config: Record<string, any>;
  };
  dependencies: string[];
  tags: string[];
}

const PluginManager: React.FC = () => {
  const { t } = useTranslation();
  const logger = serviceFactory.getLogger();
  const [plugins, setPlugins] = useState<Plugin[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedPlugin, setSelectedPlugin] = useState<Plugin | null>(null);
  const [configJson, setConfigJson] = useState<string>("");

  // プラグイン一覧を取得
  const fetchPlugins = async () => {
    setLoading(true);
    setError(null);
    try {
      const response = await apiService.fetchPlugins();
      setPlugins(response);
      logger.info("プラグイン一覧を取得しました", { count: response.length });
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Unknown error";
      setError(`プラグイン一覧の取得に失敗しました: ${errorMessage}`);
      logger.error("プラグイン一覧の取得に失敗しました", { error: err });
    } finally {
      setLoading(false);
    }
  };

  // プラグインを有効化
  const enablePlugin = async (pluginId: string) => {
    try {
      await apiService.enablePlugin(pluginId);
      logger.info(`プラグイン ${pluginId} を有効化しました`);
      fetchPlugins(); // 一覧を更新
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Unknown error";
      setError(`プラグインの有効化に失敗しました: ${errorMessage}`);
      logger.error("プラグインの有効化に失敗しました", { pluginId, error: err });
    }
  };

  // プラグインを無効化
  const disablePlugin = async (pluginId: string) => {
    try {
      await apiService.disablePlugin(pluginId);
      logger.info(`プラグイン ${pluginId} を無効化しました`);
      fetchPlugins(); // 一覧を更新
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Unknown error";
      setError(`プラグインの無効化に失敗しました: ${errorMessage}`);
      logger.error("プラグインの無効化に失敗しました", { pluginId, error: err });
    }
  };

  // プラグインを設定
  const configurePlugin = async (pluginId: string, config: Record<string, any>) => {
    try {
      await apiService.configurePlugin(pluginId, config);
      logger.info(`プラグイン ${pluginId} を設定しました`, { config });
      fetchPlugins(); // 一覧を更新
      setSelectedPlugin(null); // 設定モーダルを閉じる
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Unknown error";
      setError(`プラグインの設定に失敗しました: ${errorMessage}`);
      logger.error("プラグインの設定に失敗しました", { pluginId, config, error: err });
    }
  };

  // プラグイン選択時の処理
  const handleSelectPlugin = (plugin: Plugin) => {
    setSelectedPlugin(plugin);
    setConfigJson(JSON.stringify(plugin.config.config, null, 2));
  };

  // 設定保存時の処理
  const handleSaveConfig = () => {
    if (!selectedPlugin) return;
    
    try {
      const config = JSON.parse(configJson);
      configurePlugin(selectedPlugin.id, config);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Unknown error";
      setError(`設定の解析に失敗しました: ${errorMessage}`);
      logger.error("設定の解析に失敗しました", { configJson, error: err });
    }
  };

  // 初期化時にプラグイン一覧を取得
  useEffect(() => {
    fetchPlugins();
  }, []);

  // プラグインタイプに応じたアイコンを取得
  const getPluginTypeIcon = (type: string) => {
    switch (type) {
      case "preprocessor":
        return "⬅️";
      case "postprocessor":
        return "➡️";
      case "command":
        return "🔍";
      case "tool":
        return "🔧";
      case "ui":
        return "🖼️";
      case "agent":
        return "🤖";
      case "rag":
        return "📚";
      default:
        return "🔌";
    }
  };

  return (
    <div className="plugin-manager">
      <h2 className="text-xl font-bold mb-4">{t("plugins.title")}</h2>
      
      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          {error}
        </div>
      )}
      
      <div className="mb-4">
        <button
          onClick={fetchPlugins}
          className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
          disabled={loading}
        >
          {loading ? t("plugins.loading") : t("plugins.refresh")}
        </button>
      </div>
      
      {loading ? (
        <div className="text-center py-4">{t("plugins.loading")}</div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {plugins.map((plugin) => (
            <div
              key={plugin.id}
              className="border rounded-lg p-4 hover:shadow-md transition-shadow"
            >
              <div className="flex justify-between items-start mb-2">
                <h3 className="text-lg font-semibold">
                  {getPluginTypeIcon(plugin.type)} {plugin.name}
                </h3>
                <div className="flex space-x-2">
                  <button
                    onClick={() => handleSelectPlugin(plugin)}
                    className="text-blue-500 hover:text-blue-700"
                    title={t("plugins.configure")}
                  >
                    ⚙️
                  </button>
                  {plugin.config.enabled ? (
                    <button
                      onClick={() => disablePlugin(plugin.id)}
                      className="text-red-500 hover:text-red-700"
                      title={t("plugins.disable")}
                    >
                      🔴
                    </button>
                  ) : (
                    <button
                      onClick={() => enablePlugin(plugin.id)}
                      className="text-green-500 hover:text-green-700"
                      title={t("plugins.enable")}
                    >
                      🟢
                    </button>
                  )}
                </div>
              </div>
              <p className="text-sm text-gray-600 mb-2">{plugin.description}</p>
              <div className="text-xs text-gray-500">
                <p>
                  {t("plugins.version")}: {plugin.version} | {t("plugins.author")}: {plugin.author}
                </p>
                <p>
                  {t("plugins.type")}: {plugin.type} | {t("plugins.priority")}: {plugin.config.priority}
                </p>
                {plugin.dependencies.length > 0 && (
                  <p>
                    {t("plugins.dependencies")}: {plugin.dependencies.join(", ")}
                  </p>
                )}
                {plugin.tags.length > 0 && (
                  <p>
                    {t("plugins.tags")}: {plugin.tags.join(", ")}
                  </p>
                )}
              </div>
            </div>
          ))}
        </div>
      )}
      
      {/* プラグイン設定モーダル */}
      {selectedPlugin && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-2xl w-full max-h-[80vh] overflow-y-auto">
            <h3 className="text-xl font-bold mb-4">
              {t("plugins.configureTitle", { name: selectedPlugin.name })}
            </h3>
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                {t("plugins.configJson")}
              </label>
              <textarea
                value={configJson}
                onChange={(e) => setConfigJson(e.target.value)}
                className="w-full h-64 p-2 border rounded font-mono text-sm"
              />
            </div>
            <div className="flex justify-end space-x-2">
              <button
                onClick={() => setSelectedPlugin(null)}
                className="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
              >
                {t("plugins.cancel")}
              </button>
              <button
                onClick={handleSaveConfig}
                className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
              >
                {t("plugins.save")}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default PluginManager;

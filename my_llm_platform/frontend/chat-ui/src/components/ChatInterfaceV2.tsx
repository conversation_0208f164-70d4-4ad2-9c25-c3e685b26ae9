import React, { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { useChatLogicV2 } from "../hooks/useChatLogicV2";
import { apiService } from "../services/apiService";
import { serviceFactory } from "../services/serviceFactory";
import { Message, TuningProfile } from "../types/chat";
import { useAuthStore } from "../store/authStore";
import SettingsMenu from "./SettingsMenu";
import InputBox from "./InputBox";
import LoadingSpinner from "./LoadingSpinner";
import ThinkContent from "./ThinkContent";

/**
 * チャットインターフェース V2 コンポーネント
 * 改良されたアーキテクチャを使用してチャット機能を実装
 */
const ChatInterfaceV2: React.FC = () => {
  const { t } = useTranslation();
  const logger = serviceFactory.getLogger();

  // チャットロジックフックを使用
  const {
    messages,
    loading,
    error,
    selectedModel,
    selectedBackend,
    selectedProfile,
    selectedPlugins,
    useTools,
    streamMode,
    messagesEndRef,
    sendMessage,
    setModel,
    setBackend,
    setProfile,
    setPlugins,
    toggleUseTools,
    toggleStreamMode,
  } = useChatLogicV2();

  // ローカル状態
  const [settingsOpen, setSettingsOpen] = useState<boolean>(false);
  const user = useAuthStore((state) => state.user);

  // モデルとバックエンドをロードし、初期化する
  useEffect(() => {
    // タイムアウト処理を追加
    const fetchWithTimeout = async (promise, timeoutMs, fallbackValue) => {
      let timeoutId;
      try {
        const timeoutPromise = new Promise((_, reject) => {
          timeoutId = setTimeout(() => {
            reject(new Error(`Request timed out after ${timeoutMs}ms`));
          }, timeoutMs);
        });

        // Promise.race を使用してタイムアウトを実装
        const result = await Promise.race([promise, timeoutPromise]);
        clearTimeout(timeoutId);
        return result;
      } catch (error) {
        clearTimeout(timeoutId);
        logger.warn(`Request timed out or failed, using fallback value`, { error });
        return fallbackValue;
      }
    };

    const fetchData = async () => {
      try {
        // ローカルストレージから保存された値を取得
        const savedModel = localStorage.getItem("selectedModel");
        const savedBackend = localStorage.getItem("selectedBackend");
        const defaultModel = import.meta.env.VITE_DEFAULT_MODEL || "llama3.2:3b";
        const defaultBackend = import.meta.env.VITE_DEFAULT_BACKEND || "ollama";

        // タイムアウト付きでモデルを取得（5秒）
        logger.info("Fetching models with timeout");
        const models = await fetchWithTimeout(
          apiService.fetchModels(),
          5000,
          [{ id: defaultModel, name: defaultModel, default: true, backend: defaultBackend }]
        );

        // タイムアウト付きでバックエンドを取得（5秒）
        logger.info("Fetching backends with timeout");
        const backendsData = await fetchWithTimeout(
          apiService.fetchBackends(),
          5000,
          { backends: [{ id: defaultBackend, name: defaultBackend, description: "Default backend", supports_streaming: true }], default_backend: defaultBackend }
        );

        logger.info("Models and backends loaded", {
          modelsCount: models.length,
          backendsCount: backendsData.backends.length
        });

        // モデルを設定
        if (savedModel && models.some(model => model.id === savedModel)) {
          // 保存されたモデルが利用可能な場合は使用
          setModel(savedModel);
          logger.info(`Using saved model: ${savedModel}`);
        } else if (import.meta.env.VITE_DEFAULT_MODEL) {
          // 環境変数のデフォルトモデルを使用
          const envModel = import.meta.env.VITE_DEFAULT_MODEL;
          if (models.some(model => model.id === envModel)) {
            setModel(envModel);
            logger.info(`Using environment default model: ${envModel}`);
          } else if (models.length > 0) {
            // デフォルトモデルが利用不可な場合は最初のモデルを使用
            setModel(models[0].id);
            logger.info(`Using first available model: ${models[0].id}`);
          }
        } else if (models.length > 0) {
          // デフォルトモデルが設定されていない場合は最初のモデルを使用
          setModel(models[0].id);
          logger.info(`Using first available model: ${models[0].id}`);
        } else {
          // モデルが取得できなかった場合はデフォルト値を使用
          setModel(defaultModel);
          logger.warn(`Using hardcoded default model: ${defaultModel}`);
        }

        // バックエンドを設定
        if (savedBackend && backendsData.backends.some(backend => backend.id === savedBackend)) {
          // 保存されたバックエンドが利用可能な場合は使用
          setBackend(savedBackend);
          logger.info(`Using saved backend: ${savedBackend}`);
        } else if (import.meta.env.VITE_DEFAULT_BACKEND) {
          // 環境変数のデフォルトバックエンドを使用
          const envBackend = import.meta.env.VITE_DEFAULT_BACKEND;
          if (backendsData.backends.some(backend => backend.id === envBackend)) {
            setBackend(envBackend);
            logger.info(`Using environment default backend: ${envBackend}`);
          } else if (backendsData.backends.length > 0) {
            // デフォルトバックエンドが利用不可な場合は最初のバックエンドを使用
            setBackend(backendsData.backends[0].id);
            logger.info(`Using first available backend: ${backendsData.backends[0].id}`);
          }
        } else if (backendsData.backends.length > 0) {
          // デフォルトバックエンドが設定されていない場合は最初のバックエンドを使用
          setBackend(backendsData.backends[0].id);
          logger.info(`Using first available backend: ${backendsData.backends[0].id}`);
        } else {
          // バックエンドが取得できなかった場合はデフォルト値を使用
          setBackend(defaultBackend);
          logger.warn(`Using hardcoded default backend: ${defaultBackend}`);
        }
      } catch (error) {
        logger.error("Error fetching data", error);

        // エラー発生時はデフォルト値を使用
        const defaultModel = import.meta.env.VITE_DEFAULT_MODEL || "llama3.2:3b";
        const defaultBackend = import.meta.env.VITE_DEFAULT_BACKEND || "ollama";

        setModel(defaultModel);
        setBackend(defaultBackend);
        logger.warn(`Using default values due to error: model=${defaultModel}, backend=${defaultBackend}`);
      }
    };

    fetchData();
  }, []);

  // 設定メニューを処理
  const handleOpenSettings = () => {
    setSettingsOpen(true);
  };

  const handleCloseSettings = () => {
    setSettingsOpen(false);
  };

  const handleModelSelect = (modelId: string) => {
    // モデルを設定（ローカルストレージへの保存はModelSelector内で行われる）
    setModel(modelId);

    // 設定画面を閉じない（モデル選択時は画面リフレッシュを行わない）
    console.log(`チャットインターフェースでモデルを選択しました: ${modelId}`);
  };

  const handleBackendSelect = (backendId: string) => {
    setBackend(backendId);
  };

  const handleProfileSelect = (profile: TuningProfile | null) => {
    setProfile(profile);
  };

  // メッセージをレンダリング
  const renderMessages = () => {
    if (!messages || messages.length === 0) {
      return (
        <div className="welcome-message">
          <h2>{t("chat.welcomeTitle")}</h2>
          <p>{t("chat.welcomeMessage")}</p>
        </div>
      );
    }

    return (
      <div className="message-list">
        {messages.map((message: Message, index: number) => (
          <div
            key={index}
            className={`message ${message.role === "user" ? "user-message" : "assistant-message"}`}
          >
            <div className="message-content-container">
              <div
                className={`message-avatar ${message.role === "user" ? "user-avatar" : "assistant-avatar"}`}
              >
                {message.role === "user"
                  ? user?.username
                    ? user.username.substring(0, 1).toUpperCase()
                    : "U"
                  : "A"}
              </div>
              <div className="message-content">
                {message.content ? (
                  <ThinkContent content={message.content} />
                ) : loading && index === messages.length - 1 ? (
                  <div className="loading-container">
                    <LoadingSpinner size="small" />
                    <span className="loading-text">{t("ai_thinking")}</span>
                  </div>
                ) : (
                  ""
                )}
              </div>
            </div>
          </div>
        ))}
      </div>
    );
  };

  return (
    <div className="chat-container">
      <div className="chat-header">
        <div className="model-info">
          {selectedModel} ({selectedBackend})
        </div>
        <div className="header-right">
          <button className="settings-button" onClick={handleOpenSettings}>
            ⚙️
          </button>
        </div>
      </div>

      {/* 設定メニュー */}
      <SettingsMenu
        isOpen={settingsOpen}
        onClose={handleCloseSettings}
        selectedModel={selectedModel}
        selectedBackend={selectedBackend}
        onModelSelect={handleModelSelect}
        onBackendSelect={handleBackendSelect}
        selectedProfile={selectedProfile}
        onProfileSelect={handleProfileSelect}
        streamMode={streamMode}
        onStreamModeChange={toggleStreamMode}
        selectedPlugins={selectedPlugins}
        onPluginsChange={setPlugins}
        useTools={useTools}
        onUseToolsChange={toggleUseTools}
      />

      <div className="chat-content">
        <div className="chat-layout">
          {/* 左側の空白エリア */}
          <div className="chat-left-column"></div>

          {/* 中央のコンテンツエリア */}
          <div className="chat-center-column">
            {/* メッセージリスト */}
            <div className="messages-container">
              {renderMessages()}
              {error && (
                <div className="error-message-container">
                  <div className="error-message">
                    <div className="error-icon">⚠️</div>
                    <div className="error-text">
                      {error.includes('Error:') ? error : `${t('chat.errorPrefix')}: ${error}`}
                      <div className="error-help-text">
                        {t('chat.errorHelpText')}
                      </div>
                    </div>
                  </div>
                </div>
              )}
              <div ref={messagesEndRef} />
            </div>

            {/* 入力ボックス */}
            <div className="input-box-container">
              <InputBox
                onSendMessage={sendMessage}
                disabled={loading}
                placeholder={t("chat.inputPlaceholder")}
              />
              <div className="disclaimer">{t("chat.disclaimer")}</div>
            </div>
          </div>

          {/* 右側の空白エリア */}
          <div className="chat-right-column"></div>
        </div>
      </div>
    </div>
  );
};

export default ChatInterfaceV2;

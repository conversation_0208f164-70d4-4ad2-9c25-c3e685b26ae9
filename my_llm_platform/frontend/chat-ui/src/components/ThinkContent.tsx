import React, { useState } from 'react';
import ReactMarkdown from 'react-markdown';
import { useTranslation } from 'react-i18next';
import CodeBlock from './CodeBlock';
import '../styles/CodeBlock.css';

interface ThinkContentProps {
  content: string;
}

/**
 * 思考内容コンポーネント
 * モデルの思考プロセスを表示・非表示できるコンポーネント
 */
const ThinkContent: React.FC<ThinkContentProps> = ({ content }) => {
  const { t } = useTranslation();
  const [isExpanded, setIsExpanded] = useState(false);

  // 思考内容を抽出する
  const extractThinkContent = (text: string): { thinkContent: string | null; visibleContent: string } => {
    // グローバルフラグを使用して複数の<think>タグに対応
    const thinkRegex = /<think>([\s\S]*?)<\/think>/g;
    const matches = [...text.matchAll(thinkRegex)];

    if (matches.length > 0) {
      // 最初の<think>タグの内容を取得
      const thinkContent = matches[0][1].trim();
      // すべての<think>タグを削除
      const visibleContent = text.replace(/<think>[\s\S]*?<\/think>/g, '').trim();
      return { thinkContent, visibleContent };
    }

    // <think>タグがない場合
    return { thinkContent: null, visibleContent: text };
  };

  const { thinkContent, visibleContent } = extractThinkContent(content);

  // ReactMarkdownのコンポーネント設定
  const markdownComponents = {
    code({ className, children, ...props }: any) {
      const match = /language-(\w+)/.exec(className || "");
      return match ? (
        <CodeBlock
          language={match[1]}
          className={className}
          {...props}
        >
          {String(children).replace(/\n$/, "")}
        </CodeBlock>
      ) : (
        <code className={className} {...props}>
          {children}
        </code>
      );
    },
  };

  // 思考内容がない場合は通常のマークダウンを表示
  if (!thinkContent) {
    return <ReactMarkdown components={markdownComponents}>{content}</ReactMarkdown>;
  }

  return (
    <div className="think-content-wrapper">
      <ReactMarkdown components={markdownComponents}>{visibleContent}</ReactMarkdown>

      {thinkContent && (
        <div className="think-container">
          <button
            className={`think-toggle ${isExpanded ? 'expanded' : 'collapsed'}`}
            onClick={() => setIsExpanded(!isExpanded)}
          >
            {isExpanded ? t('hide_thinking') : t('show_thinking')}
          </button>

          {isExpanded && (
            <div className="think-box">
              <div className="think-header">{t('model_thinking')}</div>
              <div className="think-body">
                <ReactMarkdown components={markdownComponents}>{thinkContent}</ReactMarkdown>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default ThinkContent;

import React, { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { apiService } from "../services/apiService";
import { Plugin } from "../types/chat";
import { settings } from "../config/settings";
import { serviceFactory } from "../services/serviceFactory";

interface ChatPluginSelectorProps {
  selectedPlugins: string[];
  onPluginsChange: (plugins: string[]) => void;
}

const ChatPluginSelector: React.FC<ChatPluginSelectorProps> = ({
  selectedPlugins,
  onPluginsChange,
}) => {
  const { t } = useTranslation();
  const [plugins, setPlugins] = useState<Plugin[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [systemEnabled, setSystemEnabled] = useState<boolean>(settings.ENABLE_PLUGINS);

  // プラグインシステムのステータスを取得
  useEffect(() => {
    const fetchPluginStatus = async () => {
      try {
        const status = await apiService.fetchPluginSystemStatus();
        setSystemEnabled(status.enabled);

        if (!status.enabled) {
          setError(t("plugins.systemDisabled"));
          setLoading(false);
          return;
        }

        // プラグインシステムが有効な場合のみプラグイン一覧を取得
        // 注意: ここでは status.enabled を直接使用し、systemEnabled 状態変数に依存しない
        await fetchPlugins();
      } catch (err) {
        // ステータス取得に失敗した場合、設定値を使用
        setSystemEnabled(settings.ENABLE_PLUGINS);
        if (!settings.ENABLE_PLUGINS) {
          setError(t("plugins.systemDisabled"));
          setLoading(false);
          return;
        }

        // 設定値が有効な場合はプラグイン一覧を取得
        await fetchPlugins();
      }
    };

    // プラグイン一覧を取得
    const fetchPlugins = async () => {
      setLoading(true);
      setError(null);
      try {
        const pluginList = await apiService.fetchPlugins();
        // 有効なプラグインのみをフィルタリング
        const enabledPlugins = pluginList.filter(
          (plugin) => plugin.config.enabled
        );
        setPlugins(enabledPlugins);
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : "Unknown error";
        setError(`${t("plugins.fetchError")}: ${errorMessage}`);
      } finally {
        setLoading(false);
      }
    };

    fetchPluginStatus();
  }, [t]);

  // プラグイン選択の切り替え
  const togglePlugin = (pluginId: string) => {
    if (selectedPlugins.includes(pluginId)) {
      onPluginsChange(selectedPlugins.filter((id) => id !== pluginId));
    } else {
      onPluginsChange([...selectedPlugins, pluginId]);
    }
  };

  // プラグインドキュメントを開く
  const openPluginDocs = (pluginId: string) => {
    const { API_CONFIG, API_ENDPOINTS } = serviceFactory.getApiConfig();
    const baseUrl = API_CONFIG.BASE_URL + API_CONFIG.PREFIX;
    const docsUrl = baseUrl + API_ENDPOINTS.PLUGINS.DOCS(pluginId);
    window.open(docsUrl, '_blank');
  };

  // プラグインタイプに応じたアイコンを取得
  const getPluginTypeIcon = (type: string) => {
    switch (type) {
      case "preprocessor":
        return "⬅️";
      case "postprocessor":
        return "➡️";
      case "command":
        return "🔍";
      case "tool":
        return "🔧";
      case "ui":
        return "🖼️";
      case "agent":
        return "🤖";
      case "rag":
        return "📚";
      default:
        return "🔌";
    }
  };

  // プラグインシステムが無効な場合
  if (!systemEnabled) {
    return (
      <div className="text-gray-500 p-4">
        <p>{t("plugins.systemDisabled")}</p>
        <p className="text-sm mt-2">{t("plugins.enableInstructions")}</p>
      </div>
    );
  }

  // ローディング中
  if (loading) {
    return (
      <div className="flex items-center justify-center p-4">
        <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-gray-500"></div>
        <span className="ml-2 text-gray-500">{t("plugins.loading")}</span>
      </div>
    );
  }

  // エラー発生時
  if (error) {
    return (
      <div className="text-red-500 p-4">
        <p>{error}</p>
        <button
          onClick={() => window.location.reload()}
          className="mt-2 text-blue-500 underline"
        >
          {t("plugins.retry")}
        </button>
      </div>
    );
  }

  // プラグインが存在しない場合
  if (plugins.length === 0) {
    return (
      <div className="text-gray-500 p-4">
        <p>{t("plugins.noPluginsAvailable")}</p>
      </div>
    );
  }

  return (
    <div className="p-4">
      <h3 className="text-lg font-medium mb-2">{t("plugins.selectPlugins")}</h3>
      <div className="space-y-2">
        {plugins.map((plugin) => (
          <div
            key={plugin.id}
            className="flex items-center justify-between p-2 hover:bg-gray-100 rounded-md"
          >
            <div className="flex items-center">
              <input
                type="checkbox"
                checked={selectedPlugins.includes(plugin.id)}
                onChange={() => togglePlugin(plugin.id)}
                className="mr-2"
              />
              <div>
                <div className="font-medium">
                  {plugin.name}
                </div>
                <div className="text-sm text-gray-500">{plugin.description}</div>
              </div>
            </div>
            <div className="flex items-center">
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  openPluginDocs(plugin.id);
                }}
                className="mr-2 text-blue-500 hover:text-blue-700 text-sm"
                title={t("plugins.viewDocs")}
              >
                📄
              </button>
              <span className="text-xs text-gray-500 ml-2">
                {getPluginTypeIcon(plugin.type)} {plugin.type}
              </span>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default ChatPluginSelector;

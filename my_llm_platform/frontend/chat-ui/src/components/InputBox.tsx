import React, {
  useState,
  useRef,
  useEffect,
  KeyboardEvent,
  FormEvent,
} from "react";
import { useTranslation } from "react-i18next";

interface InputBoxProps {
  onSendMessage: (message: string) => void;
  disabled?: boolean;
  placeholder?: string;
}

const InputBox: React.FC<InputBoxProps> = ({
  onSendMessage,
  disabled = false,
  placeholder,
}) => {
  const { t } = useTranslation();
  const [message, setMessage] = useState<string>("");
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // テキストエリアの高さを自動調整
  useEffect(() => {
    const textarea = textareaRef.current;
    if (textarea) {
      // 正確なscrollHeightを取得するために高さをリセット
      textarea.style.height = "auto";
      // 高さをscrollHeightに設定
      const newHeight = Math.min(textarea.scrollHeight, 120); // 最大高さを制限
      textarea.style.height = `${newHeight}px`;
    }
  }, [message]);

  const handleSubmit = (e: FormEvent<HTMLFormElement>): void => {
    e.preventDefault(); // フォーム送信によるページのリロードを防止
    console.log("Form submit prevented");
    if (message.trim() && !disabled) {
      // メッセージ送信関数を非同期で呼び出す
      setTimeout(() => {
        onSendMessage(message);
        setMessage("");
      }, 0);
    }
  };

  const handleKeyDown = (e: KeyboardEvent<HTMLTextAreaElement>): void => {
    // Enterキーでメッセージを送信、Shiftキーを押しながらの場合は改行を許可
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault(); // 改行を防止
      console.log("Enter key pressed, preventing default");
      if (message.trim() && !disabled) {
        // メッセージ送信関数を非同期で呼び出す
        setTimeout(() => {
          onSendMessage(message);
          setMessage("");
        }, 0);
      }
    }
    // Shift+Enterを押した場合、改行を許可し、デフォルトの動作を防止する必要はない
  };

  return (
    <div className="input-wrapper">
      <form className="input-box" onSubmit={handleSubmit}>
        <textarea
          ref={textareaRef}
          value={message}
          onChange={(e) => setMessage(e.target.value)}
          onKeyDown={handleKeyDown}
          placeholder={placeholder || t("chat_placeholder")}
          disabled={disabled}
          rows={1}
        />
        <button type="submit" disabled={!message.trim() || disabled}>
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="16"
            height="16"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          >
            <line x1="22" y1="2" x2="11" y2="13"></line>
            <polygon points="22 2 15 22 11 13 2 9 22 2"></polygon>
          </svg>
        </button>
      </form>
    </div>
  );
};

export default InputBox;

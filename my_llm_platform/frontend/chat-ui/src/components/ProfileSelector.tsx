import React, { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { TuningProfile } from "../types/chat";

// デフォルトのチューニングプロファイル
const defaultProfiles: TuningProfile[] = [
  {
    id: "creative",
    name: "Creative",
    description: "Higher temperature for more creative responses",
    model: "",
    backend: null,
    parameters: [
      {
        name: "temperature",
        value: 0.9,
        description: "Higher temperature for more randomness",
      },
      { name: "top_p", value: 0.95, description: "Nucleus sampling parameter" },
    ],
  },
  {
    id: "balanced",
    name: "Balanced",
    description: "Balanced settings for general use",
    model: "",
    backend: null,
    parameters: [
      {
        name: "temperature",
        value: 0.7,
        description: "Medium temperature for balanced responses",
      },
      { name: "top_p", value: 0.9, description: "Nucleus sampling parameter" },
    ],
  },
  {
    id: "precise",
    name: "Precise",
    description: "Lower temperature for more deterministic responses",
    model: "",
    backend: null,
    parameters: [
      {
        name: "temperature",
        value: 0.3,
        description: "Lower temperature for more deterministic responses",
      },
      { name: "top_p", value: 0.85, description: "Nucleus sampling parameter" },
    ],
  },
];

interface ProfileSelectorProps {
  selectedProfile: TuningProfile | null;
  onProfileSelect: (_profile: TuningProfile | null) => void;
}

const ProfileSelector: React.FC<ProfileSelectorProps> = ({
  selectedProfile,
  onProfileSelect,
}) => {
  const { t } = useTranslation();
  const [profiles, setProfiles] = useState<TuningProfile[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [showSelector, setShowSelector] = useState<boolean>(false);

  useEffect(() => {
    // チューニングプロファイルをロード
    const loadProfiles = async () => {
      setLoading(true);
      try {
        // ここでAPIからプロファイルをロードできますが、現在はデフォルトプロファイルを使用
        setProfiles(defaultProfiles);
        setLoading(false);
      } catch (err) {
        console.error("Error loading tuning profiles:", err);
        setError("Failed to load tuning profiles");
        setLoading(false);
      }
    };

    loadProfiles();
  }, []);

  const handleProfileSelect = (profile: TuningProfile | null) => {
    onProfileSelect(profile);
    setShowSelector(false);
  };

  // セレクターの表示を切り替える
  const toggleSelector = (): void => {
    setShowSelector(!showSelector);
  };

  if (loading) return <div className="loading">{t("loading")}</div>;
  if (error)
    return (
      <div className="error">
        {t("error")}: {error}
      </div>
    );

  return (
    <div className="profile-selector">
      <div className="profile-display" onClick={toggleSelector}>
        <span className="profile-label">
          {selectedProfile ? selectedProfile.name : t("select_profile")}
        </span>
        <span className="toggle-icon">{showSelector ? "▲" : "▼"}</span>
      </div>

      {showSelector && (
        <div className="profile-dropdown-menu">
          {profiles.map((profile) => (
            <div
              key={profile.id}
              className={`profile-option ${profile.id === selectedProfile?.id ? "selected" : ""}`}
              onClick={() => handleProfileSelect(profile)}
            >
              <span className="profile-name">{profile.name}</span>
              <span className="profile-description">{profile.description}</span>
            </div>
          ))}
          <div
            className="profile-option clear-option"
            onClick={() => handleProfileSelect(null)}
          >
            <span className="profile-name">{t("clear")}</span>
          </div>
        </div>
      )}
    </div>
  );
};

export default ProfileSelector;

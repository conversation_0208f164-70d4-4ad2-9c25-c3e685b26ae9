import React, { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { useAuthStore } from "../store/authStore";

interface ChatSession {
  id: string;
  title: string;
  created_at: string;
  last_message: string;
}

export const ChatHistory: React.FC = () => {
  const { t } = useTranslation();
  const { token } = useAuthStore();
  const [sessions, setSessions] = useState<ChatSession[]>([]);

  useEffect(() => {
    fetchChatHistory();
  }, [token]);

  const fetchChatHistory = async () => {
    try {
      // 使用新实现的聊天历史API
      // 使用类型断言解决TypeScript静态类型检查问题
      const apiBaseUrl = (import.meta as any).env.VITE_API_BASE_URL || "";
      const apiPrefix = (import.meta as any).env.VITE_API_PREFIX || "";
      const response = await fetch(`${apiBaseUrl}${apiPrefix}/chat/history`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        // 根据状态码处理不同的错误
        if (response.status === 401) {
          console.error("Authentication error: User not authenticated");
          // 显示登录提示
          alert(t("login_required"));
          // 重定向到登录页面
          window.location.href = "/login";
        } else {
          console.error(`API error: ${response.status}`);
        }
        setSessions([]);
        return;
      }

      const data = await response.json();
      setSessions(data);
    } catch (error) {
      console.error("Failed to fetch chat history:", error);
      // 如果出错，使用空数组
      setSessions([]);
    }
  };

  return (
    <div className="w-64 bg-gray-800 text-white p-4">
      <h2 className="text-xl mb-4">{t("chat.history")}</h2>
      <div className="space-y-2">
        {sessions.map((session) => (
          <div
            key={session.id}
            className="p-2 hover:bg-gray-700 rounded cursor-pointer"
          >
            <div className="font-medium">{session.title}</div>
            <div className="text-sm text-gray-400">
              {new Date(session.created_at).toLocaleDateString()}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

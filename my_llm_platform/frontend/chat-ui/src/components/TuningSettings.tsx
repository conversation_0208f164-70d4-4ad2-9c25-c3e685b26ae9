import React, { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { TuningProfile, TuningParameter } from "../types/chat";

interface TuningSettingsProps {
  selectedProfile: TuningProfile | null;
  onProfileSelect: (_profile: TuningProfile | null) => void;
}

const TuningSettings: React.FC<TuningSettingsProps> = ({
  selectedProfile,
  onProfileSelect,
}) => {
  const { t } = useTranslation();
  const [profiles, setProfiles] = useState<TuningProfile[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [showCreateForm, setShowCreateForm] = useState<boolean>(false);
  const [newProfile, setNewProfile] = useState<TuningProfile>({
    id: "",
    name: "",
    description: "",
    model: "",
    backend: null,
    parameters: [],
  });

  // デフォルトのチューニングプロファイル
  const defaultProfiles: TuningProfile[] = [
    {
      id: "creative",
      name: "Creative",
      description: "Higher temperature for more creative responses",
      model: "",
      backend: null,
      parameters: [
        {
          name: "temperature",
          value: 0.9,
          description: "Higher temperature for more randomness",
        },
        {
          name: "top_p",
          value: 0.95,
          description: "Nucleus sampling parameter",
        },
      ],
    },
    {
      id: "balanced",
      name: "Balanced",
      description: "Balanced settings for general use",
      model: "",
      backend: null,
      parameters: [
        {
          name: "temperature",
          value: 0.7,
          description: "Medium temperature for balanced responses",
        },
        {
          name: "top_p",
          value: 0.9,
          description: "Nucleus sampling parameter",
        },
      ],
    },
    {
      id: "precise",
      name: "Precise",
      description: "Lower temperature for more deterministic responses",
      model: "",
      backend: null,
      parameters: [
        {
          name: "temperature",
          value: 0.3,
          description: "Lower temperature for more deterministic responses",
        },
        {
          name: "top_p",
          value: 0.85,
          description: "Nucleus sampling parameter",
        },
      ],
    },
  ];

  useEffect(() => {
    // チューニングプロファイルをロード
    const loadProfiles = async () => {
      setLoading(true);
      try {
        // ここでAPIからプロファイルをロードできますが、現在はデフォルトプロファイルを使用
        setProfiles(defaultProfiles);
        setLoading(false);
      } catch (err) {
        console.error("Error loading tuning profiles:", err);
        setError("Failed to load tuning profiles");
        setLoading(false);
      }
    };

    loadProfiles();
  }, []);

  const handleProfileSelect = (profileId: string) => {
    const profile = profiles.find((p) => p.id === profileId) || null;
    onProfileSelect(profile);
  };

  const handleCreateProfile = () => {
    if (!newProfile.name) {
      setError("Profile name is required");
      return;
    }

    const profileId = newProfile.name.toLowerCase().replace(/\s+/g, "-");
    const profile: TuningProfile = {
      ...newProfile,
      id: profileId,
    };

    setProfiles([...profiles, profile]);
    onProfileSelect(profile);
    setShowCreateForm(false);
    setNewProfile({
      id: "",
      name: "",
      description: "",
      model: "",
      backend: null,
      parameters: [],
    });
  };

  const handleAddParameter = () => {
    setNewProfile({
      ...newProfile,
      parameters: [
        ...newProfile.parameters,
        { name: "", value: 0, description: "" },
      ],
    });
  };

  const handleParameterChange = (
    index: number,
    field: keyof TuningParameter,
    value: any,
  ) => {
    const updatedParameters = [...newProfile.parameters];
    updatedParameters[index] = {
      ...updatedParameters[index],
      [field]: field === "value" ? parseFloat(value) : value,
    };

    setNewProfile({
      ...newProfile,
      parameters: updatedParameters,
    });
  };

  const handleRemoveParameter = (index: number) => {
    const updatedParameters = [...newProfile.parameters];
    updatedParameters.splice(index, 1);

    setNewProfile({
      ...newProfile,
      parameters: updatedParameters,
    });
  };

  if (loading) return <div className="loading">{t("loading")}</div>;

  return (
    <div className="tuning-settings">
      <h2>{t("tuning_profiles")}</h2>

      {error && <div className="error">{error}</div>}

      <div className="profiles-list">
        <h3>{t("available_profiles")}</h3>
        <div className="profile-options">
          <select
            value={selectedProfile?.id || ""}
            onChange={(e) => handleProfileSelect(e.target.value)}
            className="profile-select"
          >
            <option value="">{t("select_model")}</option>
            {profiles.map((profile) => (
              <option key={profile.id} value={profile.id}>
                {profile.name}
              </option>
            ))}
          </select>

          <button
            type="button"
            className="create-profile-button"
            onClick={() => setShowCreateForm(!showCreateForm)}
          >
            {showCreateForm ? t("cancel") : t("create_new")}
          </button>
        </div>

        {selectedProfile && (
          <div className="selected-profile">
            <h4>{selectedProfile.name}</h4>
            <p>{selectedProfile.description}</p>

            <div className="parameters-list">
              <h5>{t("parameters")}</h5>
              <table>
                <thead>
                  <tr>
                    <th>{t("parameter_name")}</th>
                    <th>{t("parameter_value")}</th>
                    <th>{t("parameter_description")}</th>
                  </tr>
                </thead>
                <tbody>
                  {selectedProfile.parameters.map((param, index) => (
                    <tr key={index}>
                      <td>{param.name}</td>
                      <td>{param.value}</td>
                      <td>{param.description}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        )}

        {showCreateForm && (
          <div className="create-profile-form">
            <h4>{t("create_new")}</h4>

            <div className="form-group">
              <label htmlFor="profile-name">{t("profile_name")}:</label>
              <input
                id="profile-name"
                type="text"
                value={newProfile.name}
                onChange={(e) =>
                  setNewProfile({ ...newProfile, name: e.target.value })
                }
                required
              />
            </div>

            <div className="form-group">
              <label htmlFor="profile-description">
                {t("profile_description")}:
              </label>
              <textarea
                id="profile-description"
                value={newProfile.description}
                onChange={(e) =>
                  setNewProfile({ ...newProfile, description: e.target.value })
                }
              />
            </div>

            <div className="form-group">
              <label htmlFor="profile-model">{t("profile_model")}:</label>
              <input
                id="profile-model"
                type="text"
                value={newProfile.model}
                onChange={(e) =>
                  setNewProfile({ ...newProfile, model: e.target.value })
                }
                placeholder="Optional"
              />
            </div>

            <div className="form-group">
              <label htmlFor="profile-backend">{t("profile_backend")}:</label>
              <input
                id="profile-backend"
                type="text"
                value={newProfile.backend || ""}
                onChange={(e) =>
                  setNewProfile({
                    ...newProfile,
                    backend: e.target.value || null,
                  })
                }
                placeholder="Optional"
              />
            </div>

            <div className="parameters-section">
              <h5>{t("parameters")}</h5>

              {newProfile.parameters.map((param, index) => (
                <div key={index} className="parameter-item">
                  <div className="parameter-inputs">
                    <input
                      type="text"
                      placeholder={t("parameter_name")}
                      value={param.name}
                      onChange={(e) =>
                        handleParameterChange(index, "name", e.target.value)
                      }
                    />
                    <input
                      type="number"
                      placeholder={t("parameter_value")}
                      value={String(param.value)}
                      onChange={(e) =>
                        handleParameterChange(index, "value", e.target.value)
                      }
                      step="0.1"
                    />
                    <input
                      type="text"
                      placeholder={t("parameter_description")}
                      value={param.description}
                      onChange={(e) =>
                        handleParameterChange(
                          index,
                          "description",
                          e.target.value,
                        )
                      }
                    />
                  </div>
                  <button
                    type="button"
                    className="remove-parameter-button"
                    onClick={() => handleRemoveParameter(index)}
                  >
                    {t("remove")}
                  </button>
                </div>
              ))}

              <button
                type="button"
                className="add-parameter-button"
                onClick={handleAddParameter}
              >
                {t("add_parameter")}
              </button>
            </div>

            <div className="form-actions">
              <button
                type="button"
                className="save-profile-button"
                onClick={handleCreateProfile}
              >
                {t("save")}
              </button>
              <button
                type="button"
                className="cancel-button"
                onClick={() => setShowCreateForm(false)}
              >
                {t("cancel")}
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default TuningSettings;

import React, { useState } from "react";
import { useAuthStore } from "../store/authStore";
import { useTranslation } from "react-i18next";
import { serviceFactory } from "../services/serviceFactory";

const Login: React.FC = () => {
  const { t } = useTranslation();
  const [username, setUsername] = useState("");
  const [password, setPassword] = useState("");
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  const { login } = useAuthStore();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    const logger = serviceFactory.getLogger();

    // フォームのバリデーション
    if (!username.trim()) {
      setError(t("username_required"));
      return;
    }

    if (!password.trim()) {
      setError(t("password_required"));
      return;
    }

    setLoading(true);
    try {
      logger.info("Attempting to login", { username });
      await login(username, password);
      logger.info("Login successful");

      // ログイン成功後のリダイレクト処理
      // 保存されたリダイレクト先があれば、そこにリダイレクト
      const redirectPath = localStorage.getItem("redirectAfterLogin");
      if (redirectPath) {
        logger.info(`Redirecting to saved path: ${redirectPath}`);
        localStorage.removeItem("redirectAfterLogin"); // リダイレクト先を削除
        window.location.href = redirectPath;
      } else {
        // デフォルトはチャット画面
        logger.info("Redirecting to default path: /chat");
        window.location.href = "/chat";
      }
    } catch (error: any) {
      logger.error("Login failed:", error);
      setError(error.message || t("login_failed"));
    } finally {
      setLoading(false);
    }
  };

  // 管理者ログインのショートカット
  const loginAsAdmin = () => {
    setUsername("admin");
    setPassword("password");
  };

  // 一般ユーザーログインのショートカット
  const loginAsUser = () => {
    setUsername("user");
    setPassword("password");
  };

  return (
    <div className="flex items-center justify-center min-h-screen bg-gray-100">
      <div className="p-8 bg-white rounded-lg shadow-md w-full max-w-md">
        <h2 className="text-2xl mb-4 text-center">{t("login")}</h2>
        {error && (
          <div className="mb-4 p-2 bg-red-100 text-red-700 rounded">
            {error}
          </div>
        )}
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              {t("username")}
            </label>
            <input
              type="text"
              value={username}
              onChange={(e) => setUsername(e.target.value)}
              placeholder={t("username_placeholder")}
              className="w-full p-2 border rounded"
              disabled={loading}
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              {t("password")}
            </label>
            <input
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              placeholder={t("password_placeholder")}
              className="w-full p-2 border rounded"
              disabled={loading}
            />
          </div>
          <button
            type="submit"
            className={`w-full p-2 bg-blue-500 text-white rounded hover:bg-blue-600 ${loading ? "opacity-50 cursor-not-allowed" : ""}`}
            disabled={loading}
          >
            {loading ? t("logging_in") : t("login")}
          </button>
        </form>

        <div className="mt-6 pt-4 border-t border-gray-200">
          <p className="text-sm text-gray-600 mb-2">{t("quick_login")}:</p>
          <div className="flex space-x-2">
            <button
              onClick={loginAsUser}
              className="flex-1 p-2 bg-gray-200 text-gray-700 rounded hover:bg-gray-300"
              disabled={loading}
            >
              {t("login_as_user")}
            </button>
            <button
              onClick={loginAsAdmin}
              className="flex-1 p-2 bg-gray-200 text-gray-700 rounded hover:bg-gray-300"
              disabled={loading}
            >
              {t("login_as_admin")}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Login;

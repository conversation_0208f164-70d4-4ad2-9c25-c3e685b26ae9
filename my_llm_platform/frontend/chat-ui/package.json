{"name": "chat-ui", "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "tailwind:init": "tailwindcss init -p", "lint": "eslint './src/**/*.{ts,tsx,js,jsx}' --ext .js,.ts,.tsx"}, "dependencies": {"axios": "^1.8.4", "i18next": "^24.2.3", "i18next-browser-languagedetector": "^8.0.4", "react": "^19.1.0", "react-dom": "^19.1.0", "react-i18next": "^15.4.1", "react-markdown": "^10.1.0", "react-router-dom": "^7.5.0", "react-syntax-highlighter": "^5.8.0", "zustand": "^5.0.3"}, "devDependencies": {"@eslint/js": "^9.24.0", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/postcss": "^4.1.3", "@types/node": "^22.14.0", "@types/react": "^19.1.0", "@types/react-dom": "^19.1.2", "@types/react-syntax-highlighter": "^15.5.13", "@typescript-eslint/eslint-plugin": "^8.29.1", "@typescript-eslint/parser": "^8.29.1", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.21", "eslint": "^9.24.0", "eslint-config-prettier": "^10.1.1", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "postcss": "^8.5.3", "prettier": "^3.5.3", "tailwindcss": "^4.1.4", "typescript": "^5.3.3", "vite": "^6.2.5"}}
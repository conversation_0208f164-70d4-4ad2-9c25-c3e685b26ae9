{"name": "mcp-server-and-gw", "version": "1.0.1", "description": "An MCP stdio to http sse gateway with example server and MCP client", "type": "module", "bin": {"mcp-server-and-gw": "./build/mcp-server-and-gw.js"}, "files": ["build"], "scripts": {"build": "rm -rf build && tsc", "prepare": "yarn run build", "watch": "tsc --watch", "inspector": "npx @modelcontextprotocol/inspector node build/mcp-server-and-gw.js", "release:patch": "npm version patch && git push && git push --tags", "release:minor": "npm version minor && git push && git push --tags", "release:major": "npm version major && git push && git push --tags"}, "dependencies": {"@modelcontextprotocol/sdk": "1.10.2", "@npmcli/fs": "^4.0.0", "@types/node": "^22.15.3", "are-we-there-yet": "^4.0.2", "eventsource": "^3.0.6", "gauge": "^5.0.2", "glob": "^11.0.2", "npmlog": "^7.0.1", "rimraf": "^6.0.1"}, "devDependencies": {"@types/eventsource": "^3.0.0", "@types/express": "^5.0.1", "@types/json-bigint": "^1.0.4", "duckdb": "^1.2.1", "express": "^5.1.0", "json-bigint": "^1.0.0", "typescript": "^5.8.3"}}
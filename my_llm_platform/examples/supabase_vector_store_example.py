"""
Supabaseベクトルストアの使用例

このスクリプトは、Supabaseベクトルストアを使用してドキュメントを追加し、
検索する方法を示します。

前提条件:
1. Supabaseがローカルで実行されていること
2. pgvector拡張機能が有効化されていること
3. vecsライブラリがインストールされていること (pip install vecs)

使用方法:
python supabase_vector_store_example.py
"""

import os
import sys
import logging
from typing import List, Dict, Any

# ロギングの設定
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# プロジェクトのルートディレクトリをパスに追加
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 必要なモジュールをインポート
from backend.plugins.rag.vector_factory import VectorStoreFactory
from backend.plugins.rag.document_loader import DocumentLoader
from backend.config import settings

def main():
    """
    Supabaseベクトルストアの使用例を実行します
    """
    # 設定を確認
    if not settings.VECTOR_ENABLED_FLAG:
        logger.info("ベクトルデータベース機能が無効になっています")
        logger.info("ベクトルデータベース機能を有効にします")
        settings.VECTOR_ENABLED_FLAG = True

    if settings.VECTOR_DATABASE_TYPE != "supabase":
        logger.info(f"現在のベクトルデータベースタイプは {settings.VECTOR_DATABASE_TYPE} です")
        logger.info("Supabaseベクトルストアを使用するために設定を変更します")
        settings.VECTOR_DATABASE_TYPE = "supabase"

    # Supabase接続文字列を確認
    if not settings.SUPABASE_CONNECTION_STRING:
        logger.error("SUPABASE_CONNECTION_STRINGが設定されていません")
        logger.info("デフォルトの接続文字列を使用します: postgresql://postgres:password@localhost:54322/postgres")
        settings.SUPABASE_CONNECTION_STRING = "postgresql://postgres:password@localhost:54322/postgres"

    # コレクション名を設定
    collection_name = "example_docs"

    try:
        # ベクトルストアを作成
        logger.info(f"Supabaseベクトルストアを作成します (コレクション: {collection_name})")
        vector_store = VectorStoreFactory.create_vector_store(
            vector_store_type="supabase",
            collection_name=collection_name,
            model_name="all-MiniLM-L6-v2"  # 埋め込みモデル名
        )

        # ベクトルストアが利用可能かチェック
        if not vector_store.is_available:
            logger.error("Supabaseベクトルストアが利用できません")
            return

        # コレクションを作成（既存の場合は再利用）
        logger.info("コレクションを作成します")
        vector_store.create_collection()

        # サンプルテキストを追加
        sample_texts = [
            "Supabaseは、PostgreSQLデータベースとRESTful APIを提供するオープンソースのFirebase代替です。",
            "pgvectorは、PostgreSQL用のベクトル類似性検索拡張機能です。",
            "ベクトルデータベースは、機械学習モデルから生成された埋め込みベクトルを保存するために使用されます。",
            "RAG（検索拡張生成）は、大規模言語モデルの回答を外部知識で強化するテクニックです。",
            "Supabaseのベクトルデータベース機能は、pgvector拡張機能を使用して実装されています。"
        ]

        # メタデータを準備
        sample_metadata = [
            {"source": "supabase_docs", "category": "database"},
            {"source": "pgvector_docs", "category": "extension"},
            {"source": "ml_article", "category": "database"},
            {"source": "llm_article", "category": "ai"},
            {"source": "supabase_blog", "category": "database"}
        ]

        # テキストを追加
        logger.info(f"{len(sample_texts)}個のサンプルテキストを追加します")
        vector_store.add_texts(sample_texts, sample_metadata)

        # 検索クエリを実行
        query = "ベクトル検索とは何ですか？"
        logger.info(f"検索クエリ: '{query}'")

        # 検索を実行
        results = vector_store.search(query, top_k=3)

        # 結果を表示
        logger.info(f"{len(results)}個の検索結果が見つかりました")
        for i, result in enumerate(results):
            logger.info(f"結果 {i+1}:")
            logger.info(f"  テキスト: {result['text']}")
            logger.info(f"  スコア: {result['score']}")
            logger.info(f"  メタデータ: {result}")
            logger.info("---")

        # フィルタ付き検索
        logger.info("カテゴリが 'database' のドキュメントのみを検索します")
        filtered_results = vector_store.search(
            query,
            top_k=3,
            filter_params={"category": "database"}
        )

        # フィルタ付き結果を表示
        logger.info(f"{len(filtered_results)}個のフィルタ付き検索結果が見つかりました")
        for i, result in enumerate(filtered_results):
            logger.info(f"結果 {i+1}:")
            logger.info(f"  テキスト: {result['text']}")
            logger.info(f"  スコア: {result['score']}")
            logger.info(f"  メタデータ: {result}")
            logger.info("---")

        # インデックスを作成
        logger.info("ベクトルインデックスを作成します")
        # Supabaseのvecsクライアントを使用してインデックスを作成
        if hasattr(vector_store.collection, 'create_index'):
            vector_store.collection.create_index()
            logger.info("インデックスが正常に作成されました")

        logger.info("Supabaseベクトルストアの使用例が完了しました")

    except Exception as e:
        logger.error(f"エラーが発生しました: {e}")
        import traceback
        logger.error(traceback.format_exc())

if __name__ == "__main__":
    main()

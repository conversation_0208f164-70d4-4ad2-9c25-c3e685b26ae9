"""
Examples of using MCP tools with different frameworks.

This module demonstrates how to use MCP tools with various agent frameworks.
"""

import asyncio
import os
import sys
import importlib.util

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from ..backend.plugins.mcp import register_tool, list_tools, get_tool
from ..backend.plugins.mcp.examples import register_example_tools


# Example 1: Direct usage
async def example_direct_usage():
    """Example of using MCP tools directly."""
    print("\n=== Example 1: Direct Usage ===")

    # Register example tools
    register_example_tools()

    # List available tools
    print(f"Available tools: {list_tools()}")

    # Get a tool
    weather_tool = get_tool("get_weather")

    # Execute the tool
    result = await weather_tool.execute(location="Tokyo")

    # Print the result
    print(f"Weather in Tokyo: {result.result}")


# Example 2: Using with OpenAI
async def example_openai():
    """Example of using MCP tools with OpenAI."""
    print("\n=== Example 2: Using with OpenAI ===")

    try:
        from openai import OpenAI
    except ImportError:
        print("OpenAI package not installed. Install with 'pip install openai'")
        return

    # Register example tools
    register_example_tools()

    # Get OpenAI tool definitions
    from ..backend.plugins.mcp.registry import MCPToolRegistry
    openai_tools = MCPToolRegistry().to_openai_tools()

    print(f"OpenAI tools: {len(openai_tools)}")

    # Create OpenAI client
    # Try to import settings from backend.config
    try:
        from backend.config import settings
        api_key = settings.OPENAI_API_KEY or "your-api-key"
    except ImportError:
        api_key = "your-api-key"

    client = OpenAI(
        api_key=api_key
    )

    # Example of using tools with OpenAI
    print("Example OpenAI request with tools:")
    print("""
    client.chat.completions.create(
        model="gpt-3.5-turbo",
        messages=[
            {"role": "user", "content": "What's the weather in Tokyo?"}
        ],
        tools=openai_tools,
        tool_choice="auto"
    )
    """)

    # Note: We're not actually making the API call here to avoid requiring an API key
    print("Note: This is just an example. No actual API call is made.")


# Example 3: Using with LangChain
async def example_langchain():
    """Example of using MCP tools with LangChain."""
    print("\n=== Example 3: Using with LangChain ===")

    try:
        from langchain.agents import AgentExecutor, create_openai_tools_agent
        from langchain_openai import ChatOpenAI
        from langchain_core.prompts import ChatPromptTemplate
    except ImportError:
        print("LangChain packages not installed. Install with 'pip install langchain langchain-openai'")
        return

    # Register example tools
    register_example_tools()

    # Get LangChain tools
    from ..backend.plugins.mcp.registry import MCPToolRegistry
    langchain_tools = MCPToolRegistry().to_langchain_tools()

    print(f"LangChain tools: {len(langchain_tools)}")

    # Example of using tools with LangChain
    print("Example LangChain agent setup:")
    print("""
    # Create LLM
    llm = ChatOpenAI(model="gpt-3.5-turbo")

    # Create prompt
    prompt = ChatPromptTemplate.from_messages([
        ("system", "You are a helpful assistant that can use tools to answer user questions."),
        ("human", "{input}")
    ])

    # Create agent
    agent = create_openai_tools_agent(llm, langchain_tools, prompt)
    agent_executor = AgentExecutor(agent=agent, tools=langchain_tools)

    # Run agent
    result = agent_executor.invoke({"input": "What's the weather in Tokyo?"})
    """)

    # Note: We're not actually creating the agent here to avoid requiring an API key
    print("Note: This is just an example. No actual agent is created.")


# Example 4: Custom integration
async def example_custom_integration():
    """Example of custom integration with MCP tools."""
    print("\n=== Example 4: Custom Integration ===")

    # Register example tools
    register_example_tools()

    # Create a custom function that uses MCP tools
    async def process_query(query: str):
        """Process a user query using MCP tools."""
        print(f"Processing query: {query}")

        # Simple keyword-based tool selection
        if "weather" in query.lower():
            # Extract location (in a real implementation, use NLP)
            location = "Tokyo"  # Default
            if "in" in query:
                location = query.split("in")[1].strip().split()[0].rstrip("?.,!")

            weather_tool = get_tool("get_weather")
            result = await weather_tool.execute(location=location)
            return f"Weather in {location}: {result.result}"

        elif "calculate" in query.lower() or any(op in query for op in ["+", "-", "*", "/"]):
            # Extract expression (in a real implementation, use NLP)
            expression = query.replace("calculate", "").strip()

            calculate_tool = get_tool("calculate")
            result = await calculate_tool.execute(expression=expression)
            return f"Calculation result: {result.result}"

        elif "search" in query.lower():
            # Extract query (in a real implementation, use NLP)
            search_query = query.replace("search", "").strip()

            search_tool = get_tool("search")
            result = await search_tool.execute(query=search_query)
            return f"Search results: {result.result}"

        else:
            return "I don't know how to process that query."

    # Test the custom integration
    queries = [
        "What's the weather in Paris?",
        "Calculate 2 + 2",
        "Search for machine learning tutorials"
    ]

    for query in queries:
        response = await process_query(query)
        print(f"Query: {query}")
        print(f"Response: {response}")
        print()


# Run all examples
async def run_examples():
    """Run all examples."""
    await example_direct_usage()
    await example_openai()
    await example_langchain()
    await example_custom_integration()


if __name__ == "__main__":
    asyncio.run(run_examples())

#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
ツール呼び出しアシスタントの例

このサンプルは、外部ツールを呼び出すことができるAIアシスタントを作成する方法を示します。
LLMプラットフォームのOpenAI互換APIを使用しています。
"""

import os
import sys
import json
import datetime
import requests
from openai import OpenAI

# API設定
API_BASE_URL = "http://localhost:8000/v1"
API_KEY = "dummy-api-key"  # 認証が必要ない場合は任意の値
DEFAULT_MODEL = "llama3.2"  # デフォルトのモデル

# OpenAIクライアントの初期化
client = OpenAI(
    base_url=API_BASE_URL,
    api_key=API_KEY
)

# ツール関数の定義

def get_weather(city, unit="celsius"):
    """
    指定された都市の天気情報を取得（シミュレーション）
    
    実際のアプリケーションでは、実際の天気APIを呼び出すべきです
    """
    # 天気データのシミュレーション
    weather_data = {
        "東京": {"condition": "晴れ", "temperature": 25, "humidity": 40},
        "大阪": {"condition": "曇り", "temperature": 28, "humidity": 65},
        "名古屋": {"condition": "小雨", "temperature": 30, "humidity": 80},
        "福岡": {"condition": "曇り", "temperature": 29, "humidity": 75},
        "ニューヨーク": {"condition": "晴れ", "temperature": 22, "humidity": 50},
        "ロンドン": {"condition": "霧", "temperature": 18, "humidity": 70},
        "パリ": {"condition": "曇り", "temperature": 26, "humidity": 60},
    }
    
    # 都市の天気データを取得、存在しない場合はデフォルト値を返す
    city_data = weather_data.get(city, {"condition": "不明", "temperature": 20, "humidity": 50})
    
    # 温度単位の変換
    temp = city_data["temperature"]
    if unit == "fahrenheit":
        temp = temp * 9/5 + 32
        temp_unit = "°F"
    else:
        temp_unit = "°C"
    
    return f"{city}の天気状況：{city_data['condition']}、気温：{temp}{temp_unit}、湿度：{city_data['humidity']}%"

def get_current_time(timezone="Asia/Tokyo"):
    """
    現在の時刻を取得
    
    実際のアプリケーションでは、異なるタイムゾーンを処理すべきです
    """
    now = datetime.datetime.now()
    return f"現在の時刻は：{now.strftime('%Y-%m-%d %H:%M:%S')}（{timezone}タイムゾーン）"

def search_web(query):
    """
    ウェブ検索をシミュレート
    
    実際のアプリケーションでは、実際の検索APIを呼び出すべきです
    """
    return f"「{query}」に関する検索結果（シミュレーション）：\n1. {query}のウィキペディアページ\n2. {query}に関する最新ニュース\n3. {query}に関する学術研究"

def calculate_expression(expression):
    """
    数学式を計算
    """
    try:
        # 警告：本番環境でevalを使用することは安全ではありません
        # これはサンプルとしてのみ使用し、実際のアプリケーションではより安全な方法を使用すべきです
        result = eval(expression)
        return f"式 {expression} の計算結果は {result} です"
    except Exception as e:
        return f"式の計算中にエラーが発生しました：{str(e)}"

# 利用可能なツールの定義
available_tools = [
    {
        "type": "function",
        "function": {
            "name": "get_weather",
            "description": "指定された都市の現在の天気情報を取得します",
            "parameters": {
                "type": "object",
                "properties": {
                    "city": {
                        "type": "string",
                        "description": "都市名（例：東京、大阪、ニューヨーク）"
                    },
                    "unit": {
                        "type": "string",
                        "enum": ["celsius", "fahrenheit"],
                        "description": "温度単位、デフォルトは摂氏(celsius)"
                    }
                },
                "required": ["city"]
            }
        }
    },
    {
        "type": "function",
        "function": {
            "name": "get_current_time",
            "description": "現在の時刻を取得します",
            "parameters": {
                "type": "object",
                "properties": {
                    "timezone": {
                        "type": "string",
                        "description": "タイムゾーン、デフォルトはAsia/Tokyo"
                    }
                },
                "required": []
            }
        }
    },
    {
        "type": "function",
        "function": {
            "name": "search_web",
            "description": "ウェブで情報を検索します",
            "parameters": {
                "type": "object",
                "properties": {
                    "query": {
                        "type": "string",
                        "description": "検索クエリ"
                    }
                },
                "required": ["query"]
            }
        }
    },
    {
        "type": "function",
        "function": {
            "name": "calculate_expression",
            "description": "数学式を計算します",
            "parameters": {
                "type": "object",
                "properties": {
                    "expression": {
                        "type": "string",
                        "description": "計算する数学式、例えば '2 + 2 * 3'"
                    }
                },
                "required": ["expression"]
            }
        }
    }
]

# ツール関数のマッピング
tool_functions = {
    "get_weather": get_weather,
    "get_current_time": get_current_time,
    "search_web": search_web,
    "calculate_expression": calculate_expression
}

def clear_screen():
    """ターミナル画面をクリアする"""
    os.system('cls' if os.name == 'nt' else 'clear')

def print_header():
    """アシスタントのヘッダーを表示"""
    print("=" * 60)
    print("       ツール呼び出しアシスタント - LLMプラットフォームAPI使用       ")
    print("=" * 60)
    print(f"使用モデル: {DEFAULT_MODEL}")
    print("利用可能なツール: 天気照会、時刻照会、ウェブ検索、数学計算")
    print("'exit'または'quit'と入力して終了")
    print("'clear'と入力して会話履歴をクリア")
    print("=" * 60)
    print()

def execute_tool_call(tool_call):
    """ツール呼び出しを実行し、結果を返す"""
    function_name = tool_call.function.name
    function_args = json.loads(tool_call.function.arguments)
    
    if function_name in tool_functions:
        try:
            result = tool_functions[function_name](**function_args)
            return result
        except Exception as e:
            return f"ツール {function_name} の実行中にエラーが発生しました: {str(e)}"
    else:
        return f"不明なツール: {function_name}"

def chat_with_tools():
    """ツール呼び出しをサポートするLLMとの対話を行うメイン関数"""
    clear_screen()
    print_header()
    
    # チャット履歴の初期化
    messages = [
        {"role": "system", "content": "あなたは役立つAIアシスタントの「アイ」です。様々なツールを使用してユーザーを支援できます。必要に応じてツールを使用し、回答を明確に説明してください。"}
    ]
    
    while True:
        # ユーザー入力を取得
        user_input = input("あなた: ")
        
        # 特別なコマンドをチェック
        if user_input.lower() in ['exit', 'quit']:
            print("\nさようなら！")
            break
        elif user_input.lower() == 'clear':
            messages = [
                {"role": "system", "content": "あなたは役立つAIアシスタントの「アイ」です。様々なツールを使用してユーザーを支援できます。必要に応じてツールを使用し、回答を明確に説明してください。"}
            ]
            clear_screen()
            print_header()
            continue
        elif not user_input.strip():
            continue
        
        # ユーザーメッセージを履歴に追加
        messages.append({"role": "user", "content": user_input})
        
        try:
            # APIを呼び出して応答を取得
            print("\nアイが考えています...", end="", flush=True)
            
            response = client.chat.completions.create(
                model=DEFAULT_MODEL,
                messages=messages,
                tools=available_tools,
                temperature=0.7,
                max_tokens=500
            )
            
            # "考えています"プロンプトをクリア
            print("\r" + " " * 20 + "\r", end="")
            
            # AI応答を取得
            ai_message = response.choices[0].message
            
            # ツール呼び出しがあるかチェック
            if hasattr(ai_message, 'tool_calls') and ai_message.tool_calls:
                print("アイ: 質問に答えるためにいくつかのツールを使用する必要があります...\n")
                
                # AIメッセージを履歴に追加
                messages.append({
                    "role": "assistant",
                    "content": ai_message.content,
                    "tool_calls": [
                        {
                            "id": tool_call.id,
                            "type": tool_call.type,
                            "function": {
                                "name": tool_call.function.name,
                                "arguments": tool_call.function.arguments
                            }
                        } for tool_call in ai_message.tool_calls
                    ]
                })
                
                # 各ツール呼び出しを実行
                for tool_call in ai_message.tool_calls:
                    print(f"使用ツール: {tool_call.function.name}")
                    tool_result = execute_tool_call(tool_call)
                    print(f"ツール結果: {tool_result}\n")
                    
                    # ツール結果を履歴に追加
                    messages.append({
                        "role": "tool",
                        "tool_call_id": tool_call.id,
                        "content": tool_result
                    })
                
                # 最終応答を取得するために再度APIを呼び出す
                print("アイが情報を統合しています...", end="", flush=True)
                
                final_response = client.chat.completions.create(
                    model=DEFAULT_MODEL,
                    messages=messages,
                    temperature=0.7,
                    max_tokens=500
                )
                
                # "情報を統合しています"プロンプトをクリア
                print("\r" + " " * 30 + "\r", end="")
                
                # 最終応答を表示
                final_content = final_response.choices[0].message.content
                print(f"アイ: {final_content}\n")
                
                # 最終応答を履歴に追加
                messages.append({"role": "assistant", "content": final_content})
                
            else:
                # AI応答を直接表示
                content = ai_message.content or ""
                print(f"アイ: {content}\n")
                
                # AI応答を履歴に追加
                messages.append({"role": "assistant", "content": content})
            
        except Exception as e:
            print(f"\nエラーが発生しました: {e}")
            print("APIサービスが実行されているか、ネットワーク接続が正常かを確認してください。\n")

if __name__ == "__main__":
    try:
        chat_with_tools()
    except KeyboardInterrupt:
        print("\n\nプログラムがユーザーによって中断されました。さようなら！")
        sys.exit(0)

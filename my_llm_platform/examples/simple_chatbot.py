#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
简单聊天机器人示例

这个示例展示了如何创建一个简单的命令行聊天机器人，
使用我们的LLM平台的OpenAI兼容API。
"""

import os
import sys
from openai import OpenAI

# API设置
API_BASE_URL = "http://localhost:8000/v1"
API_KEY = "dummy-api-key"  # 如果不需要认证，可以是任意值
DEFAULT_MODEL = "llama3.2"  # 默认使用的模型

# 初始化OpenAI客户端
client = OpenAI(
    base_url=API_BASE_URL,
    api_key=API_KEY
)

def clear_screen():
    """清除终端屏幕"""
    os.system('cls' if os.name == 'nt' else 'clear')

def print_header():
    """打印聊天机器人标题"""
    print("=" * 50)
    print("       简单聊天机器人 - 使用LLM平台API       ")
    print("=" * 50)
    print(f"使用模型: {DEFAULT_MODEL}")
    print("输入 'exit' 或 'quit' 退出")
    print("输入 'clear' 清除聊天历史")
    print("=" * 50)
    print()

def chat_with_llm():
    """与LLM进行交互的主函数"""
    clear_screen()
    print_header()
    
    # 初始化聊天历史
    messages = [
        {"role": "system", "content": "你是一个有用的AI助手，名叫小智。你会提供友好、准确、有帮助的回答。"}
    ]
    
    while True:
        # 获取用户输入
        user_input = input("你: ")
        
        # 检查特殊命令
        if user_input.lower() in ['exit', 'quit']:
            print("\n再见！")
            break
        elif user_input.lower() == 'clear':
            messages = [
                {"role": "system", "content": "你是一个有用的AI助手，名叫小智。你会提供友好、准确、有帮助的回答。"}
            ]
            clear_screen()
            print_header()
            continue
        elif not user_input.strip():
            continue
        
        # 添加用户消息到历史
        messages.append({"role": "user", "content": user_input})
        
        try:
            # 调用API获取响应
            print("\n小智思考中...", end="", flush=True)
            
            # 使用流式响应以获得更好的用户体验
            stream = client.chat.completions.create(
                model=DEFAULT_MODEL,
                messages=messages,
                temperature=0.7,
                max_tokens=500,
                stream=True
            )
            
            # 清除"思考中"提示
            print("\r" + " " * 20 + "\r", end="")
            
            # 打印AI响应
            print("小智: ", end="")
            ai_response = ""
            for chunk in stream:
                if chunk.choices[0].delta.content:
                    content = chunk.choices[0].delta.content
                    print(content, end="", flush=True)
                    ai_response += content
            print("\n")
            
            # 添加AI响应到历史
            messages.append({"role": "assistant", "content": ai_response})
            
        except Exception as e:
            print(f"\n发生错误: {e}")
            print("请检查API服务是否正在运行，或者网络连接是否正常。\n")

if __name__ == "__main__":
    try:
        chat_with_llm()
    except KeyboardInterrupt:
        print("\n\n程序被用户中断。再见！")
        sys.exit(0)

#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
サンプルコード: LLMプラットフォームの使用方法

このファイルは、OpenAI互換APIを使用してLLMプラットフォームと対話する方法を示します。
様々な使用シナリオを含みます：
- 基本的なチャット補完
- ストリーミングレスポンス
- ツール/関数呼び出し
- テキスト生成
"""

import os
import json
from openai import OpenAI

# APIエンドポイントとキーの設定
# 実際の環境に合わせて変更してください
API_BASE_URL = "http://localhost:8000/v1"
API_KEY = "dummy-api-key"  # 認証が必要ない場合は任意の値

# OpenAIクライアントの初期化
client = OpenAI(
    base_url=API_BASE_URL,
    api_key=API_KEY
)

def basic_chat_completion():
    """
    基本的なチャット補完の例
    """
    print("\n=== 基本的なチャット補完 ===")
    
    response = client.chat.completions.create(
        model="llama3.2",  # 使用するモデル名
        messages=[
            {"role": "system", "content": "あなたは役立つAIアシスタントです。"},
            {"role": "user", "content": "量子コンピューティングの基本原理を説明してください。"}
        ],
        temperature=0.7,
        max_tokens=500
    )
    
    print(f"モデル: {response.model}")
    print(f"回答: {response.choices[0].message.content}")

def streaming_chat_completion():
    """
    ストリーミングレスポンスの例
    """
    print("\n=== ストリーミングチャット補完 ===")
    
    stream = client.chat.completions.create(
        model="llama3.2",
        messages=[
            {"role": "system", "content": "あなたは役立つAIアシスタントです。"},
            {"role": "user", "content": "AIの歴史について簡単に説明してください。"}
        ],
        temperature=0.7,
        max_tokens=300,
        stream=True
    )
    
    print("回答: ", end="", flush=True)
    for chunk in stream:
        if chunk.choices[0].delta.content:
            print(chunk.choices[0].delta.content, end="", flush=True)
    print()

def tool_calling_example():
    """
    ツール/関数呼び出しの例
    """
    print("\n=== ツール/関数呼び出し ===")
    
    # ツール定義
    tools = [
        {
            "type": "function",
            "function": {
                "name": "get_weather",
                "description": "指定された都市の現在の天気情報を取得します",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "city": {
                            "type": "string",
                            "description": "都市名（例：東京、大阪、ニューヨーク）"
                        },
                        "unit": {
                            "type": "string",
                            "enum": ["celsius", "fahrenheit"],
                            "description": "温度の単位"
                        }
                    },
                    "required": ["city"]
                }
            }
        }
    ]
    
    response = client.chat.completions.create(
        model="llama3.2",
        messages=[
            {"role": "system", "content": "あなたは役立つAIアシスタントです。必要に応じてツールを使用してください。"},
            {"role": "user", "content": "東京の今日の天気はどうですか？"}
        ],
        tools=tools,
        temperature=0.7
    )
    
    message = response.choices[0].message
    
    # ツール呼び出しがあるか確認
    if hasattr(message, 'tool_calls') and message.tool_calls:
        print("ツール呼び出しが検出されました:")
        for tool_call in message.tool_calls:
            print(f"  ツールID: {tool_call.id}")
            print(f"  ツールタイプ: {tool_call.type}")
            print(f"  関数名: {tool_call.function.name}")
            print(f"  引数: {tool_call.function.arguments}")
            
            # 実際のツール呼び出し結果をシミュレート
            if tool_call.function.name == "get_weather":
                args = json.loads(tool_call.function.arguments)
                city = args.get("city")
                
                # ツール呼び出し結果をAIに返す
                tool_response = f"{city}の天気は晴れ、気温は25°Cです。"
                
                # ツール結果を含めて再度APIを呼び出す
                second_response = client.chat.completions.create(
                    model="llama3.2",
                    messages=[
                        {"role": "system", "content": "あなたは役立つAIアシスタントです。必要に応じてツールを使用してください。"},
                        {"role": "user", "content": "東京の今日の天気はどうですか？"},
                        {"role": "assistant", "content": None, "tool_calls": [
                            {
                                "id": tool_call.id,
                                "type": "function",
                                "function": {
                                    "name": "get_weather",
                                    "arguments": tool_call.function.arguments
                                }
                            }
                        ]},
                        {"role": "tool", "tool_call_id": tool_call.id, "content": tool_response}
                    ],
                    temperature=0.7
                )
                
                print("\nAIの最終回答:")
                print(second_response.choices[0].message.content)
    else:
        print("回答:")
        print(message.content)

def text_completion_example():
    """
    テキスト生成の例
    """
    print("\n=== テキスト生成 ===")
    
    response = client.completions.create(
        model="llama3.2",
        prompt="日本の四季について説明してください。",
        max_tokens=300,
        temperature=0.7
    )
    
    print(f"生成されたテキスト: {response.choices[0].text}")

def multiple_models_example():
    """
    異なるモデルを使用する例
    """
    print("\n=== 異なるモデルの使用 ===")
    
    models = ["llama3.2", "mistral", "qwen2.5-coder"]
    prompt = "AIの将来について短く説明してください。"
    
    for model in models:
        try:
            print(f"\nモデル: {model}")
            response = client.chat.completions.create(
                model=model,
                messages=[
                    {"role": "user", "content": prompt}
                ],
                max_tokens=100,
                temperature=0.7
            )
            print(f"回答: {response.choices[0].message.content}")
        except Exception as e:
            print(f"エラー: {e}")

def custom_parameters_example():
    """
    カスタムパラメータを使用する例
    """
    print("\n=== カスタムパラメータの使用 ===")
    
    response = client.chat.completions.create(
        model="llama3.2",
        messages=[
            {"role": "system", "content": "あなたは創造的なAIアシスタントです。"},
            {"role": "user", "content": "短い詩を書いてください。"}
        ],
        temperature=1.0,  # より創造的な出力のために高い温度
        max_tokens=200,
        top_p=0.95,
        frequency_penalty=0.5,  # 繰り返しを減らす
        presence_penalty=0.5    # より多様な内容を促進
    )
    
    print(f"回答: {response.choices[0].message.content}")

def main():
    """
    すべての例を実行
    """
    print("=== LLMプラットフォーム使用例 ===")
    print(f"APIエンドポイント: {API_BASE_URL}")
    
    try:
        # 基本的な使用例
        basic_chat_completion()
        
        # ストリーミングレスポンス
        streaming_chat_completion()
        
        # ツール/関数呼び出し
        tool_calling_example()
        
        # テキスト生成
        text_completion_example()
        
        # 異なるモデルの使用
        multiple_models_example()
        
        # カスタムパラメータ
        custom_parameters_example()
        
    except Exception as e:
        print(f"エラーが発生しました: {e}")

if __name__ == "__main__":
    main()

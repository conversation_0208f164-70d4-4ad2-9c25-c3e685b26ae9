#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Web聊天应用示例

这个示例展示了如何创建一个简单的Web聊天应用，
使用我们的LLM平台的OpenAI兼容API。

需要安装的依赖:
pip install flask openai

运行方法:
python web_chat_app.py

然后在浏览器中访问: http://localhost:5000
"""

import json
from flask import Flask, render_template, request, jsonify, Response, stream_with_context
from openai import OpenAI

# API设置
API_BASE_URL = "http://localhost:8000/v1"
API_KEY = "dummy-api-key"  # 如果不需要认证，可以是任意值
DEFAULT_MODEL = "llama3.2"  # 默认使用的模型

# 初始化OpenAI客户端
client = OpenAI(
    base_url=API_BASE_URL,
    api_key=API_KEY
)

# 初始化Flask应用
app = Flask(__name__)

# 创建HTML模板
HTML_TEMPLATE = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LLM平台聊天示例</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
        }
        .chat-container {
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 20px;
            margin-bottom: 20px;
            height: 500px;
            overflow-y: auto;
        }
        .message {
            margin-bottom: 15px;
            padding: 10px 15px;
            border-radius: 18px;
            max-width: 80%;
            position: relative;
        }
        .user-message {
            background-color: #dcf8c6;
            margin-left: auto;
            border-bottom-right-radius: 5px;
        }
        .bot-message {
            background-color: #f1f0f0;
            margin-right: auto;
            border-bottom-left-radius: 5px;
        }
        .input-container {
            display: flex;
            gap: 10px;
        }
        #user-input {
            flex-grow: 1;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
        }
        button {
            padding: 12px 20px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            transition: background-color 0.3s;
        }
        button:hover {
            background-color: #45a049;
        }
        button:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
        }
        .typing-indicator {
            display: none;
            background-color: #f1f0f0;
            padding: 10px 15px;
            border-radius: 18px;
            margin-bottom: 15px;
            width: fit-content;
            border-bottom-left-radius: 5px;
        }
        .typing-indicator span {
            height: 10px;
            width: 10px;
            float: left;
            margin: 0 1px;
            background-color: #9E9EA1;
            display: block;
            border-radius: 50%;
            opacity: 0.4;
        }
        .typing-indicator span:nth-of-type(1) {
            animation: 1s blink infinite 0.3333s;
        }
        .typing-indicator span:nth-of-type(2) {
            animation: 1s blink infinite 0.6666s;
        }
        .typing-indicator span:nth-of-type(3) {
            animation: 1s blink infinite 0.9999s;
        }
        @keyframes blink {
            50% {
                opacity: 1;
            }
        }
        .model-selector {
            margin-bottom: 20px;
            text-align: center;
        }
        select {
            padding: 8px;
            border-radius: 5px;
            border: 1px solid #ddd;
            font-size: 16px;
        }
        .stream-toggle {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 20px;
        }
        .switch {
            position: relative;
            display: inline-block;
            width: 60px;
            height: 34px;
            margin: 0 10px;
        }
        .switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }
        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 34px;
        }
        .slider:before {
            position: absolute;
            content: "";
            height: 26px;
            width: 26px;
            left: 4px;
            bottom: 4px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }
        input:checked + .slider {
            background-color: #4CAF50;
        }
        input:checked + .slider:before {
            transform: translateX(26px);
        }
    </style>
</head>
<body>
    <h1>LLM平台聊天示例</h1>
    
    <div class="model-selector">
        <label for="model-select">选择模型：</label>
        <select id="model-select">
            <option value="llama3.2">Llama 3.2</option>
            <option value="mistral">Mistral</option>
            <option value="qwen2.5-coder">Qwen 2.5 Coder</option>
        </select>
    </div>
    
    <div class="stream-toggle">
        <span>普通响应</span>
        <label class="switch">
            <input type="checkbox" id="stream-toggle" checked>
            <span class="slider"></span>
        </label>
        <span>流式响应</span>
    </div>
    
    <div class="chat-container" id="chat-container">
        <div class="message bot-message">
            你好！我是AI助手。有什么我可以帮助你的吗？
        </div>
        <div class="typing-indicator" id="typing-indicator">
            <span></span>
            <span></span>
            <span></span>
        </div>
    </div>
    
    <div class="input-container">
        <input type="text" id="user-input" placeholder="输入你的问题..." autofocus>
        <button id="send-button">发送</button>
    </div>

    <script>
        const chatContainer = document.getElementById('chat-container');
        const userInput = document.getElementById('user-input');
        const sendButton = document.getElementById('send-button');
        const modelSelect = document.getElementById('model-select');
        const streamToggle = document.getElementById('stream-toggle');
        const typingIndicator = document.getElementById('typing-indicator');
        
        let messages = [
            {"role": "system", "content": "你是一个有用的AI助手。请提供友好、准确、有帮助的回答。"}
        ];
        
        // 添加消息到聊天界面
        function addMessage(content, isUser) {
            const messageDiv = document.createElement('div');
            messageDiv.classList.add('message');
            messageDiv.classList.add(isUser ? 'user-message' : 'bot-message');
            messageDiv.textContent = content;
            
            // 确保打字指示器在最后
            chatContainer.insertBefore(messageDiv, typingIndicator);
            chatContainer.scrollTop = chatContainer.scrollHeight;
        }
        
        // 处理用户输入
        async function handleUserInput() {
            const userMessage = userInput.value.trim();
            if (!userMessage) return;
            
            // 添加用户消息到界面
            addMessage(userMessage, true);
            
            // 添加用户消息到历史
            messages.push({"role": "user", "content": userMessage});
            
            // 清空输入框并禁用按钮
            userInput.value = '';
            sendButton.disabled = true;
            
            // 显示打字指示器
            typingIndicator.style.display = 'block';
            
            try {
                const selectedModel = modelSelect.value;
                const useStream = streamToggle.checked;
                
                if (useStream) {
                    // 使用流式响应
                    const response = await fetch('/api/chat-stream', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            messages: messages,
                            model: selectedModel
                        })
                    });
                    
                    // 创建新的消息div
                    const messageDiv = document.createElement('div');
                    messageDiv.classList.add('message', 'bot-message');
                    
                    // 隐藏打字指示器并添加新消息
                    typingIndicator.style.display = 'none';
                    chatContainer.insertBefore(messageDiv, typingIndicator);
                    
                    let fullContent = '';
                    
                    // 处理流式响应
                    const reader = response.body.getReader();
                    const decoder = new TextDecoder();
                    
                    while (true) {
                        const { done, value } = await reader.read();
                        if (done) break;
                        
                        const chunk = decoder.decode(value);
                        const lines = chunk.split('\\n');
                        
                        for (const line of lines) {
                            if (line.startsWith('data: ') && line !== 'data: [DONE]') {
                                try {
                                    const data = JSON.parse(line.substring(6));
                                    if (data.choices && data.choices[0].delta && data.choices[0].delta.content) {
                                        const content = data.choices[0].delta.content;
                                        fullContent += content;
                                        messageDiv.textContent = fullContent;
                                        chatContainer.scrollTop = chatContainer.scrollHeight;
                                    }
                                } catch (e) {
                                    console.error('Error parsing JSON:', e);
                                }
                            }
                        }
                    }
                    
                    // 添加AI消息到历史
                    messages.push({"role": "assistant", "content": fullContent});
                    
                } else {
                    // 使用普通响应
                    const response = await fetch('/api/chat', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            messages: messages,
                            model: selectedModel
                        })
                    });
                    
                    const data = await response.json();
                    
                    // 隐藏打字指示器
                    typingIndicator.style.display = 'none';
                    
                    // 添加AI消息到界面
                    addMessage(data.content, false);
                    
                    // 添加AI消息到历史
                    messages.push({"role": "assistant", "content": data.content});
                }
                
            } catch (error) {
                console.error('Error:', error);
                typingIndicator.style.display = 'none';
                addMessage('发生错误，请稍后再试。', false);
            }
            
            // 启用按钮
            sendButton.disabled = false;
            
            // 滚动到底部
            chatContainer.scrollTop = chatContainer.scrollHeight;
        }
        
        // 事件监听器
        sendButton.addEventListener('click', handleUserInput);
        
        userInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                handleUserInput();
            }
        });
    </script>
</body>
</html>
"""

@app.route('/')
def index():
    """渲染主页"""
    return HTML_TEMPLATE

@app.route('/api/chat', methods=['POST'])
def chat():
    """处理聊天请求（非流式）"""
    data = request.json
    messages = data.get('messages', [])
    model = data.get('model', DEFAULT_MODEL)
    
    try:
        response = client.chat.completions.create(
            model=model,
            messages=messages,
            temperature=0.7,
            max_tokens=500
        )
        
        content = response.choices[0].message.content
        return jsonify({"content": content})
    
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@app.route('/api/chat-stream', methods=['POST'])
def chat_stream():
    """处理聊天请求（流式）"""
    data = request.json
    messages = data.get('messages', [])
    model = data.get('model', DEFAULT_MODEL)
    
    def generate():
        try:
            stream = client.chat.completions.create(
                model=model,
                messages=messages,
                temperature=0.7,
                max_tokens=500,
                stream=True
            )
            
            for chunk in stream:
                if chunk.choices and len(chunk.choices) > 0:
                    if hasattr(chunk.choices[0], 'delta') and hasattr(chunk.choices[0].delta, 'content'):
                        content = chunk.choices[0].delta.content
                        if content:
                            yield f"data: {json.dumps({'choices': [{'delta': {'content': content}}]})}\n\n"
            
            yield "data: [DONE]\n\n"
            
        except Exception as e:
            yield f"data: {json.dumps({'error': str(e)})}\n\n"
    
    return Response(stream_with_context(generate()), mimetype='text/event-stream')

if __name__ == '__main__':
    print("启动Web聊天应用...")
    print("请在浏览器中访问: http://localhost:5000")
    app.run(debug=True, host='0.0.0.0', port=5000)

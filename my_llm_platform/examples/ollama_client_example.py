#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Ollamaバックエンドを使用したLLMプラットフォームの使用例

このファイルは、OpenAIクライアントライブラリを使用して
Ollamaバックエンドと対話する方法を示します。
"""

import sys
import os
import json

# 親ディレクトリをパスに追加
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# シンプルなクライアントをインポート
from backend.inference import get_client

def basic_chat_example():
    """
    基本的なチャットの例
    """
    print("\n=== 基本的なチャット ===")

    import requests
    import json

    try:
        # Ollama APIを直接呼び出す
        response = requests.post(
            "http://localhost:11434/api/chat",
            json={
                "model": "llama3.2:3b",
                "messages": [
                    {"role": "system", "content": "あなたは役立つAIアシスタントです。"},
                    {"role": "user", "content": "こんにちは、あなたは誰ですか？"}
                ],
                "stream": False
            }
        )

        if response.status_code == 200:
            result = response.json()
            print(f"モデル: {result.get('model')}")
            print(f"回答: {result.get('message', {}).get('content')}")
        else:
            print(f"エラー: {response.status_code} {response.text}")
    except Exception as e:
        print(f"エラー: {e}")
        print("Ollamaサーバーが実行されていることを確認してください。")

def streaming_example():
    """
    ストリーミングレスポンスの例
    """
    print("\n=== ストリーミングレスポンス ===")

    import requests
    import json

    try:
        # Ollama APIを直接呼び出す
        response = requests.post(
            "http://localhost:11434/api/chat",
            json={
                "model": "llama3.2:3b",
                "messages": [
                    {"role": "user", "content": "AIの未来について教えてください。"}
                ],
                "stream": True
            },
            stream=True
        )

        print("回答: ", end="", flush=True)
        for line in response.iter_lines():
            if line:
                # レスポンスをデコードしてJSONとして解析
                data = json.loads(line.decode('utf-8'))
                if not data.get('done'):
                    content = data.get('message', {}).get('content', '')
                    if content:
                        print(content, end="", flush=True)
        print()
    except Exception as e:
        print(f"エラー: {e}")
        print("Ollamaサーバーが実行されていることを確認してください。")

def generate_example():
    """
    生成APIの例
    """
    print("\n=== 生成API ===")

    import requests

    try:
        # Ollamaの/api/generateエンドポイントを直接呼び出す
        response = requests.post(
            "http://localhost:11434/api/generate",
            json={
                "model": "llama3.2:3b",
                "prompt": "量子コンピューティングとは何ですか？",
                "stream": False
            }
        )

        if response.status_code == 200:
            # レスポンスを解析
            result = response.json()
            print(f"モデル: {result.get('model')}")
            print(f"回答: {result.get('response')}")
        else:
            print(f"エラー: {response.status_code} {response.text}")
    except Exception as e:
        print(f"エラー: {e}")
        print("Ollamaサーバーが実行されていることを確認してください。")

def check_ollama_server():
    """
    Ollamaサーバーが実行されているか確認する
    """
    import requests
    print("Ollamaサーバーの接続を確認しています...")
    try:
        response = requests.get("http://localhost:11434/api/version", timeout=2)
        print(f"Ollamaサーバーからのレスポンス: {response.status_code}")
        if response.status_code == 200:
            print(f"Ollamaサーバーが実行中です。バージョン: {response.json().get('version')}")
            return True
        else:
            print(f"Ollamaサーバーから予期しないレスポンスを受け取りました: {response.text}")
            return False
    except Exception as e:
        print(f"Ollamaサーバーに接続できません: {e}")
        print("Ollamaサーバーが実行されていることを確認してください。")
        print("Ollamaをインストールしていない場合は、https://ollama.com からインストールしてください。")
        return False

def main():
    """
    すべての例を実行
    """
    print("=== Ollamaバックエンドを使用したLLMプラットフォーム使用例 ===")

    # Ollamaサーバーが実行されているか確認
    if not check_ollama_server():
        print("\nサンプルコードは以下のように使用します:")
        print("```python")
        print("from backend.inference import get_client")
        print("import requests")
        print("response = requests.post(")
        print("    \"http://localhost:11434/api/chat\",")
        print("    json={")
        print("        \"model\": \"llama3.2:3b\",")
        print("        \"messages\": [{\"role\": \"user\", \"content\": \"Hello!\"}],")
        print("        \"stream\": False")
        print("    }")
        print(")")
        print("print(response.json()[\"message\"][\"content\"])")
        print("```")
        return

    # 基本的なチャット
    basic_chat_example()

    # ストリーミングレスポンス
    streaming_example()

    # 生成API
    generate_example()

if __name__ == "__main__":
    main()

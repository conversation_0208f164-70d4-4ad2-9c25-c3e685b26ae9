"""
ファイルユーティリティの使用例。
"""
import os
import logging
from ..backend.utils import (
    ensure_directory_exists,
    read_text_file,
    write_text_file,
    read_json_file,
    write_json_file,
    read_csv_file,
    write_csv_file,
    file_exists,
    get_file_size,
    get_file_extension,
    copy_file,
    move_file,
    delete_file,
    create_temp_file,
    list_files,
    process_files,
    read_file_chunks
)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def main():
    """
    ファイルユーティリティの使用例。
    """
    # 例用の一時ディレクトリを作成
    example_dir = os.path.join(os.path.dirname(__file__), 'example_files')
    ensure_directory_exists(example_dir)
    logger.info(f"Created example directory: {example_dir}")

    # 例1: テキストファイルの書き込みと読み込み
    text_file_path = os.path.join(example_dir, 'example.txt')
    write_text_file(text_file_path, 'Hello, world!')
    logger.info(f"Wrote text file: {text_file_path}")

    text_content = read_text_file(text_file_path)
    logger.info(f"Read text file content: {text_content}")

    # 例2: JSONファイルの書き込みと読み込み
    json_file_path = os.path.join(example_dir, 'example.json')
    json_data = {
        'name': 'Example',
        'values': [1, 2, 3],
        'nested': {
            'key': 'value'
        }
    }
    write_json_file(json_file_path, json_data)
    logger.info(f"Wrote JSON file: {json_file_path}")

    json_content = read_json_file(json_file_path)
    logger.info(f"Read JSON file content: {json_content}")

    # 例3: CSVファイルの書き込みと読み込み
    csv_file_path = os.path.join(example_dir, 'example.csv')
    csv_data = [
        {'name': 'Alice', 'age': 30, 'city': 'Tokyo'},
        {'name': 'Bob', 'age': 25, 'city': 'Osaka'},
        {'name': 'Charlie', 'age': 35, 'city': 'Kyoto'}
    ]
    write_csv_file(csv_file_path, csv_data)
    logger.info(f"Wrote CSV file: {csv_file_path}")

    csv_content = read_csv_file(csv_file_path)
    logger.info(f"Read CSV file content: {csv_content}")

    # 例4: ファイル操作
    logger.info(f"File exists: {file_exists(text_file_path)}")
    logger.info(f"File size: {get_file_size(text_file_path)} bytes")
    logger.info(f"File extension: {get_file_extension(text_file_path)}")

    # 例5: ファイルのコピー
    copy_file_path = os.path.join(example_dir, 'example_copy.txt')
    copy_file(text_file_path, copy_file_path)
    logger.info(f"Copied file to: {copy_file_path}")

    # 例6: ファイルの移動
    move_file_path = os.path.join(example_dir, 'example_moved.txt')
    move_file(copy_file_path, move_file_path)
    logger.info(f"Moved file to: {move_file_path}")

    # Example 7: Create temporary file
    temp_file_path = create_temp_file(suffix='.txt', content='Temporary file content')
    logger.info(f"Created temporary file: {temp_file_path}")

    # Example 8: List files
    files = list_files(example_dir)
    logger.info(f"Files in directory: {files}")

    # Example 9: Process files
    def get_file_info(file_path):
        return {
            'path': file_path,
            'size': get_file_size(file_path),
            'extension': get_file_extension(file_path)
        }

    file_info = process_files(example_dir, get_file_info)
    logger.info(f"File information: {file_info}")

    # Example 10: Read file in chunks
    logger.info("Reading file in chunks:")
    for i, chunk in enumerate(read_file_chunks(text_file_path, chunk_size=5)):
        logger.info(f"Chunk {i}: {chunk}")

    # Clean up
    delete_file(text_file_path)
    delete_file(json_file_path)
    delete_file(csv_file_path)
    delete_file(move_file_path)
    delete_file(temp_file_path)
    logger.info("Deleted example files")


if __name__ == "__main__":
    main()

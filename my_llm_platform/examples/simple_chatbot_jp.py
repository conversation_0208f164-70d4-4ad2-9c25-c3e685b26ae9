#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
シンプルチャットボットの例

このサンプルは、コマンドラインベースのシンプルなチャットボットを作成する方法を示します。
LLMプラットフォームのOpenAI互換APIを使用しています。
"""

import os
import sys
from openai import OpenAI

# API設定
API_BASE_URL = "http://localhost:8000/v1"
API_KEY = "dummy-api-key"  # 認証が必要ない場合は任意の値
DEFAULT_MODEL = "llama3.2"  # デフォルトのモデル

# OpenAIクライアントの初期化
client = OpenAI(
    base_url=API_BASE_URL,
    api_key=API_KEY
)

def clear_screen():
    """ターミナル画面をクリアする"""
    os.system('cls' if os.name == 'nt' else 'clear')

def print_header():
    """チャットボットのヘッダーを表示"""
    print("=" * 50)
    print("       シンプルチャットボット - LLMプラットフォームAPI使用       ")
    print("=" * 50)
    print(f"使用モデル: {DEFAULT_MODEL}")
    print("'exit'または'quit'と入力して終了")
    print("'clear'と入力して会話履歴をクリア")
    print("=" * 50)
    print()

def chat_with_llm():
    """LLMとの対話を行うメイン関数"""
    clear_screen()
    print_header()
    
    # チャット履歴の初期化
    messages = [
        {"role": "system", "content": "あなたは役立つAIアシスタントの「アイ」です。友好的で正確で役立つ回答を提供してください。"}
    ]
    
    while True:
        # ユーザー入力を取得
        user_input = input("あなた: ")
        
        # 特別なコマンドをチェック
        if user_input.lower() in ['exit', 'quit']:
            print("\nさようなら！")
            break
        elif user_input.lower() == 'clear':
            messages = [
                {"role": "system", "content": "あなたは役立つAIアシスタントの「アイ」です。友好的で正確で役立つ回答を提供してください。"}
            ]
            clear_screen()
            print_header()
            continue
        elif not user_input.strip():
            continue
        
        # ユーザーメッセージを履歴に追加
        messages.append({"role": "user", "content": user_input})
        
        try:
            # APIを呼び出して応答を取得
            print("\nアイが考えています...", end="", flush=True)
            
            # ストリーミングレスポンスを使用してより良いユーザー体験を提供
            stream = client.chat.completions.create(
                model=DEFAULT_MODEL,
                messages=messages,
                temperature=0.7,
                max_tokens=500,
                stream=True
            )
            
            # "考えています"プロンプトをクリア
            print("\r" + " " * 20 + "\r", end="")
            
            # AI応答を表示
            print("アイ: ", end="")
            ai_response = ""
            for chunk in stream:
                if chunk.choices[0].delta.content:
                    content = chunk.choices[0].delta.content
                    print(content, end="", flush=True)
                    ai_response += content
            print("\n")
            
            # AI応答を履歴に追加
            messages.append({"role": "assistant", "content": ai_response})
            
        except Exception as e:
            print(f"\nエラーが発生しました: {e}")
            print("APIサービスが実行されているか、ネットワーク接続が正常かを確認してください。\n")

if __name__ == "__main__":
    try:
        chat_with_llm()
    except KeyboardInterrupt:
        print("\n\nプログラムがユーザーによって中断されました。さようなら！")
        sys.exit(0)

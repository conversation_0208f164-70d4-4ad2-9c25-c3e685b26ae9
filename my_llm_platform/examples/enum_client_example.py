#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
列挙型を使用したLLMクライアントの使用例

このファイルは、BackendType列挙型を使用してLLMクライアントを初期化する方法を示します。
"""

import sys
import os

# 親ディレクトリをパスに追加
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# クライアントと列挙型をインポート
from backend.inference import get_client, BackendType

def main():
    """
    列挙型を使用したLLMクライアントの使用例
    """
    print("=== 列挙型を使用したLLMクライアント使用例 ===")
    
    # 列挙型を使用してクライアントを初期化
    print("\n=== 列挙型を使用したクライアント初期化 ===")
    
    # 1. 列挙型を使用
    print("1. 列挙型を使用:")
    client1 = get_client(BackendType.VLLM)
    print(f"クライアントのベースURL: {client1.base_url}")
    
    # 2. 文字列を使用
    print("\n2. 文字列を使用:")
    client2 = get_client("openai")
    print(f"クライアントのベースURL: {client2.base_url}")
    
    # 3. モデル名に基づいてバックエンドを選択
    print("\n3. モデル名に基づいてバックエンドを選択:")
    client3 = get_client(model="gpt-4")
    print(f"クライアントのベースURL: {client3.base_url}")
    
    # 利用可能なバックエンドタイプを表示
    print("\n=== 利用可能なバックエンドタイプ ===")
    for backend in BackendType:
        print(f"- {backend.name}: {backend.value}")

if __name__ == "__main__":
    main()

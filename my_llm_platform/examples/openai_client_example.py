#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
OpenAIクライアントを使用したLLMプラットフォームの使用例

このファイルは、OpenAIクライアントライブラリを使用して
LLMプラットフォームと対話する最もシンプルな方法を示します。
"""

import sys
import os
import json

# 親ディレクトリをパスに追加
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# シンプルなクライアントをインポート
from backend.inference import get_client

def basic_chat_example():
    """
    基本的なチャットの例
    """
    print("\n=== 基本的なチャット ===")
    
    # デフォルトのバックエンドを使用
    client = get_client()
    
    response = client.chat.completions.create(
        model="llama3.2",
        messages=[
            {"role": "system", "content": "あなたは役立つAIアシスタントです。"},
            {"role": "user", "content": "こんにちは、あなたは誰ですか？"}
        ],
        max_tokens=100
    )
    
    print(f"モデル: {response.model}")
    print(f"回答: {response.choices[0].message.content}")

def streaming_example():
    """
    ストリーミングレスポンスの例
    """
    print("\n=== ストリーミングレスポンス ===")
    
    client = get_client()
    
    stream = client.chat.completions.create(
        model="llama3.2",
        messages=[
            {"role": "user", "content": "AIの未来について教えてください。"}
        ],
        max_tokens=100,
        stream=True
    )
    
    print("回答: ", end="", flush=True)
    for chunk in stream:
        if chunk.choices[0].delta.content:
            print(chunk.choices[0].delta.content, end="", flush=True)
    print()

def different_backends_example():
    """
    異なるバックエンドを使用する例
    """
    print("\n=== 異なるバックエンド ===")
    
    # Ollamaバックエンドを使用
    ollama_client = get_client("ollama")
    
    print("Ollamaバックエンド:")
    response = ollama_client.chat.completions.create(
        model="llama3.2",
        messages=[
            {"role": "user", "content": "量子コンピューティングとは何ですか？"}
        ],
        max_tokens=100
    )
    
    print(f"回答: {response.choices[0].message.content}")
    
    # 他のバックエンドも同様に使用可能
    # vllm_client = get_client("vllm")
    # sglang_client = get_client("sglang")
    # openai_client = get_client("openai", api_key="your-api-key")

def tool_calling_example():
    """
    ツール/関数呼び出しの例
    """
    print("\n=== ツール/関数呼び出し ===")
    
    client = get_client()
    
    # ツール定義
    tools = [
        {
            "type": "function",
            "function": {
                "name": "get_weather",
                "description": "指定された都市の現在の天気情報を取得します",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "city": {
                            "type": "string",
                            "description": "都市名（例：東京、大阪、ニューヨーク）"
                        }
                    },
                    "required": ["city"]
                }
            }
        }
    ]
    
    response = client.chat.completions.create(
        model="llama3.2",
        messages=[
            {"role": "system", "content": "あなたは役立つAIアシスタントです。必要に応じてツールを使用してください。"},
            {"role": "user", "content": "東京の今日の天気はどうですか？"}
        ],
        tools=tools,
        temperature=0.7
    )
    
    message = response.choices[0].message
    
    # ツール呼び出しがあるか確認
    if hasattr(message, 'tool_calls') and message.tool_calls:
        print("ツール呼び出しが検出されました:")
        for tool_call in message.tool_calls:
            print(f"  ツールID: {tool_call.id}")
            print(f"  ツールタイプ: {tool_call.type}")
            print(f"  関数名: {tool_call.function.name}")
            print(f"  引数: {tool_call.function.arguments}")
            
            # 実際のツール呼び出し結果をシミュレート
            if tool_call.function.name == "get_weather":
                args = json.loads(tool_call.function.arguments)
                city = args.get("city")
                
                # ツール呼び出し結果をAIに返す
                tool_response = f"{city}の天気は晴れ、気温は25°Cです。"
                
                # ツール結果を含めて再度APIを呼び出す
                second_response = client.chat.completions.create(
                    model="llama3.2",
                    messages=[
                        {"role": "system", "content": "あなたは役立つAIアシスタントです。必要に応じてツールを使用してください。"},
                        {"role": "user", "content": "東京の今日の天気はどうですか？"},
                        {"role": "assistant", "content": None, "tool_calls": [
                            {
                                "id": tool_call.id,
                                "type": "function",
                                "function": {
                                    "name": "get_weather",
                                    "arguments": tool_call.function.arguments
                                }
                            }
                        ]},
                        {"role": "tool", "tool_call_id": tool_call.id, "content": tool_response}
                    ],
                    temperature=0.7
                )
                
                print("\nAIの最終回答:")
                print(second_response.choices[0].message.content)
    else:
        print("回答:")
        print(message.content)

def main():
    """
    すべての例を実行
    """
    print("=== OpenAIクライアントを使用したLLMプラットフォーム使用例 ===")
    
    try:
        # 基本的なチャット
        basic_chat_example()
        
        # ストリーミングレスポンス
        streaming_example()
        
        # 異なるバックエンド
        different_backends_example()
        
        # ツール/関数呼び出し
        tool_calling_example()
        
    except Exception as e:
        print(f"エラーが発生しました: {e}")

if __name__ == "__main__":
    main()

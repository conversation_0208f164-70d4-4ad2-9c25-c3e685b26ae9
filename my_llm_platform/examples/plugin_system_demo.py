#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
プラグインシステムのデモ

このスクリプトは、LLMプラットフォームのプラグインシステムの使用方法を示します。
RAGプラグインとエージェントプラグインの両方を使用して、LLMの機能を拡張します。

実行方法:
python plugin_system_demo.py
"""

import json
import requests
from openai import OpenAI

# API設定
API_BASE_URL = "http://localhost:8000/v1"
API_KEY = "dummy-api-key"  # 認証が不要な場合は任意の値
DEFAULT_MODEL = "llama3.2"  # デフォルトのモデル

# OpenAIクライアントを初期化
client = OpenAI(
    base_url=API_BASE_URL,
    api_key=API_KEY
)

def print_separator(title):
    """セパレータを表示"""
    print("\n" + "=" * 50)
    print(f" {title} ".center(50, "="))
    print("=" * 50 + "\n")

def list_plugins():
    """利用可能なプラグインを一覧表示"""
    print_separator("利用可能なプラグイン")
    
    try:
        response = requests.get(f"{API_BASE_URL}/plugins")
        if response.status_code == 200:
            plugins = response.json()
            print(f"利用可能なプラグイン数: {len(plugins)}")
            
            for plugin in plugins:
                print(f"\nプラグインID: {plugin['id']}")
                print(f"名前: {plugin['name']}")
                print(f"説明: {plugin['description']}")
                print(f"タイプ: {plugin['type']}")
                print(f"有効: {plugin['config']['enabled']}")
        else:
            print(f"エラー: {response.status_code} - {response.text}")
    except Exception as e:
        print(f"プラグイン一覧の取得中にエラーが発生しました: {e}")

def rag_plugin_demo():
    """RAGプラグインのデモ"""
    print_separator("RAGプラグインのデモ")
    
    try:
        # RAGプラグインを使用してチャット
        response = client.chat.completions.create(
            model=DEFAULT_MODEL,
            messages=[
                {"role": "user", "content": "LLMプラットフォームのプラグインシステムについて教えてください。"}
            ],
            temperature=0.7,
            max_tokens=500,
            plugins=["simple_rag"]  # RAGプラグインを指定
        )
        
        print("ユーザー: LLMプラットフォームのプラグインシステムについて教えてください。")
        print(f"\nアシスタント: {response.choices[0].message.content}")
    except Exception as e:
        print(f"RAGプラグインデモ中にエラーが発生しました: {e}")

def agent_plugin_demo():
    """エージェントプラグインのデモ"""
    print_separator("エージェントプラグインのデモ")
    
    try:
        # エージェントプラグインを使用してチャット
        response = client.chat.completions.create(
            model=DEFAULT_MODEL,
            messages=[
                {"role": "user", "content": "2023年の東京の人口は何人ですか？また、その数字を2倍にするといくつになりますか？"}
            ],
            temperature=0.7,
            max_tokens=500,
            plugins=["simple_agent"]  # エージェントプラグインを指定
        )
        
        print("ユーザー: 2023年の東京の人口は何人ですか？また、その数字を2倍にするといくつになりますか？")
        print(f"\nアシスタント: {response.choices[0].message.content}")
        
        # ツール呼び出しがある場合は表示
        if hasattr(response.choices[0].message, 'tool_calls') and response.choices[0].message.tool_calls:
            print("\nツール呼び出し:")
            for tool_call in response.choices[0].message.tool_calls:
                print(f"ツール: {tool_call.function.name}")
                print(f"引数: {tool_call.function.arguments}")
    except Exception as e:
        print(f"エージェントプラグインデモ中にエラーが発生しました: {e}")

def custom_plugin_demo():
    """カスタムプラグインの作成と使用のデモ"""
    print_separator("カスタムプラグインのデモ")
    
    print("カスタムプラグインを作成するには、以下の手順に従います：")
    print("1. backend/plugins/custom/ ディレクトリにプラグインモジュールを作成")
    print("2. BasePluginクラスを継承したプラグインクラスを実装")
    print("3. プラグインマネージャーにプラグインを登録")
    print("4. チャットAPIでプラグインを使用")
    
    print("\nサンプルコード:")
    print("""
from backend.plugins.base import PreprocessorPlugin
from backend.plugins.types import Message, PluginResult

class MyCustomPlugin(PreprocessorPlugin):
    def __init__(self, config=None):
        super().__init__(config)
        self.id = "my_custom_plugin"
        self.name = "My Custom Plugin"
        self.description = "A custom preprocessor plugin"
        
    async def process(self, messages: list[Message], **kwargs) -> PluginResult:
        # メッセージを処理するロジックを実装
        if messages and messages[-1]["role"] == "user":
            messages[-1]["content"] = f"Enhanced: {messages[-1]['content']}"
        
        return PluginResult(
            messages=messages,
            metadata={"plugin": self.name}
        )

# プラグインマネージャーに登録
from backend.plugins.manager import plugin_manager
plugin_manager.register_plugin("my_custom_plugin", MyCustomPlugin)
    """)

def main():
    """メイン関数"""
    print("=== LLMプラットフォーム プラグインシステムデモ ===")
    
    # 利用可能なプラグインを一覧表示
    list_plugins()
    
    # RAGプラグインのデモ
    rag_plugin_demo()
    
    # エージェントプラグインのデモ
    agent_plugin_demo()
    
    # カスタムプラグインのデモ
    custom_plugin_demo()

if __name__ == "__main__":
    main()

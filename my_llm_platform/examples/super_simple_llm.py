#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
超シンプルなLLMプラットフォームの使用例

このファイルは、OpenAIクライアントライブラリを使用して
LLMプラットフォームと対話する最もシンプルな方法を示します。
"""

from openai import OpenAI
import sys
import os

# 親ディレクトリをパスに追加
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# シンプルなクライアントをインポート
from backend.inference import get_client

def main():
    """
    シンプルなクライアントの使用例
    """
    print("=== 超シンプルなLLMプラットフォーム使用例 ===")

    # 1. デフォルトのバックエンドとモデルを使用
    print("\n=== デフォルトのバックエンドとモデル ===")
    client = get_client()

    response = client.chat.completions.create(
        model="llama3.2",
        messages=[
            {"role": "user", "content": "こんにちは、あなたは誰ですか？"}
        ],
        max_tokens=100
    )

    print(f"モデル: {response.model}")
    print(f"回答: {response.choices[0].message.content}")

    # 2. 特定のバックエンドを使用
    print("\n=== Ollamaバックエンド ===")
    ollama_client = get_client(backend_type="ollama")

    response = ollama_client.chat.completions.create(
        model="llama3.2",
        messages=[
            {"role": "user", "content": "量子コンピューティングとは何ですか？"}
        ],
        max_tokens=100
    )

    print(f"モデル: {response.model}")
    print(f"回答: {response.choices[0].message.content}")

    # 3. ストリーミングレスポンス
    print("\n=== ストリーミングレスポンス ===")
    stream = client.chat.completions.create(
        model="llama3.2",
        messages=[
            {"role": "user", "content": "AIの未来について教えてください。"}
        ],
        max_tokens=100,
        stream=True
    )

    print("回答: ", end="", flush=True)
    for chunk in stream:
        if chunk.choices[0].delta.content:
            print(chunk.choices[0].delta.content, end="", flush=True)
    print()

if __name__ == "__main__":
    main()

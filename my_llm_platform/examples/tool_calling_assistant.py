#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
工具调用助手示例

这个示例展示了如何创建一个能够调用外部工具的AI助手，
使用我们的LLM平台的OpenAI兼容API。
"""

import os
import sys
import json
import datetime
import requests
from openai import OpenAI

# API设置
API_BASE_URL = "http://localhost:8000/v1"
API_KEY = "dummy-api-key"  # 如果不需要认证，可以是任意值
DEFAULT_MODEL = "llama3.2"  # 默认使用的模型

# 初始化OpenAI客户端
client = OpenAI(
    base_url=API_BASE_URL,
    api_key=API_KEY
)

# 定义工具函数

def get_weather(city, unit="celsius"):
    """
    获取指定城市的天气信息（模拟）
    
    在实际应用中，这里应该调用真实的天气API
    """
    # 模拟天气数据
    weather_data = {
        "北京": {"condition": "晴朗", "temperature": 25, "humidity": 40},
        "上海": {"condition": "多云", "temperature": 28, "humidity": 65},
        "广州": {"condition": "小雨", "temperature": 30, "humidity": 80},
        "深圳": {"condition": "阴天", "temperature": 29, "humidity": 75},
        "纽约": {"condition": "晴朗", "temperature": 22, "humidity": 50},
        "伦敦": {"condition": "雾", "temperature": 18, "humidity": 70},
        "东京": {"condition": "多云", "temperature": 26, "humidity": 60},
    }
    
    # 获取城市天气数据，如果不存在则返回默认值
    city_data = weather_data.get(city, {"condition": "未知", "temperature": 20, "humidity": 50})
    
    # 温度单位转换
    temp = city_data["temperature"]
    if unit == "fahrenheit":
        temp = temp * 9/5 + 32
        temp_unit = "°F"
    else:
        temp_unit = "°C"
    
    return f"{city}的天气状况：{city_data['condition']}，温度：{temp}{temp_unit}，湿度：{city_data['humidity']}%"

def get_current_time(timezone="Asia/Shanghai"):
    """
    获取当前时间
    
    在实际应用中，应该处理不同的时区
    """
    now = datetime.datetime.now()
    return f"当前时间是：{now.strftime('%Y-%m-%d %H:%M:%S')}（{timezone}时区）"

def search_web(query):
    """
    模拟网络搜索
    
    在实际应用中，这里应该调用真实的搜索API
    """
    return f"以下是关于\"{query}\"的搜索结果（模拟）：\n1. {query}的维基百科页面\n2. 关于{query}的最新新闻\n3. {query}的相关学术研究"

def calculate_expression(expression):
    """
    计算数学表达式
    """
    try:
        # 警告：在生产环境中使用eval是不安全的
        # 这里仅作为示例，实际应用中应使用更安全的方法
        result = eval(expression)
        return f"表达式 {expression} 的计算结果是 {result}"
    except Exception as e:
        return f"计算表达式时出错：{str(e)}"

# 定义可用工具
available_tools = [
    {
        "type": "function",
        "function": {
            "name": "get_weather",
            "description": "获取指定城市的当前天气信息",
            "parameters": {
                "type": "object",
                "properties": {
                    "city": {
                        "type": "string",
                        "description": "城市名称（例如：北京，上海，纽约）"
                    },
                    "unit": {
                        "type": "string",
                        "enum": ["celsius", "fahrenheit"],
                        "description": "温度单位，默认为摄氏度(celsius)"
                    }
                },
                "required": ["city"]
            }
        }
    },
    {
        "type": "function",
        "function": {
            "name": "get_current_time",
            "description": "获取当前时间",
            "parameters": {
                "type": "object",
                "properties": {
                    "timezone": {
                        "type": "string",
                        "description": "时区，默认为Asia/Shanghai"
                    }
                },
                "required": []
            }
        }
    },
    {
        "type": "function",
        "function": {
            "name": "search_web",
            "description": "在网络上搜索信息",
            "parameters": {
                "type": "object",
                "properties": {
                    "query": {
                        "type": "string",
                        "description": "搜索查询"
                    }
                },
                "required": ["query"]
            }
        }
    },
    {
        "type": "function",
        "function": {
            "name": "calculate_expression",
            "description": "计算数学表达式",
            "parameters": {
                "type": "object",
                "properties": {
                    "expression": {
                        "type": "string",
                        "description": "要计算的数学表达式，例如 '2 + 2 * 3'"
                    }
                },
                "required": ["expression"]
            }
        }
    }
]

# 工具函数映射
tool_functions = {
    "get_weather": get_weather,
    "get_current_time": get_current_time,
    "search_web": search_web,
    "calculate_expression": calculate_expression
}

def clear_screen():
    """清除终端屏幕"""
    os.system('cls' if os.name == 'nt' else 'clear')

def print_header():
    """打印助手标题"""
    print("=" * 60)
    print("       工具调用助手 - 使用LLM平台API       ")
    print("=" * 60)
    print(f"使用模型: {DEFAULT_MODEL}")
    print("可用工具: 天气查询、时间查询、网络搜索、数学计算")
    print("输入 'exit' 或 'quit' 退出")
    print("输入 'clear' 清除聊天历史")
    print("=" * 60)
    print()

def execute_tool_call(tool_call):
    """执行工具调用并返回结果"""
    function_name = tool_call.function.name
    function_args = json.loads(tool_call.function.arguments)
    
    if function_name in tool_functions:
        try:
            result = tool_functions[function_name](**function_args)
            return result
        except Exception as e:
            return f"执行工具 {function_name} 时出错: {str(e)}"
    else:
        return f"未知工具: {function_name}"

def chat_with_tools():
    """与支持工具调用的LLM进行交互的主函数"""
    clear_screen()
    print_header()
    
    # 初始化聊天历史
    messages = [
        {"role": "system", "content": "你是一个有用的AI助手，名叫小智。你可以使用各种工具来帮助用户。请根据需要使用工具，并清晰地解释你的回答。"}
    ]
    
    while True:
        # 获取用户输入
        user_input = input("你: ")
        
        # 检查特殊命令
        if user_input.lower() in ['exit', 'quit']:
            print("\n再见！")
            break
        elif user_input.lower() == 'clear':
            messages = [
                {"role": "system", "content": "你是一个有用的AI助手，名叫小智。你可以使用各种工具来帮助用户。请根据需要使用工具，并清晰地解释你的回答。"}
            ]
            clear_screen()
            print_header()
            continue
        elif not user_input.strip():
            continue
        
        # 添加用户消息到历史
        messages.append({"role": "user", "content": user_input})
        
        try:
            # 调用API获取响应
            print("\n小智思考中...", end="", flush=True)
            
            response = client.chat.completions.create(
                model=DEFAULT_MODEL,
                messages=messages,
                tools=available_tools,
                temperature=0.7,
                max_tokens=500
            )
            
            # 清除"思考中"提示
            print("\r" + " " * 20 + "\r", end="")
            
            # 获取AI响应
            ai_message = response.choices[0].message
            
            # 检查是否有工具调用
            if hasattr(ai_message, 'tool_calls') and ai_message.tool_calls:
                print("小智: 我需要使用一些工具来回答你的问题...\n")
                
                # 将AI消息添加到历史
                messages.append({
                    "role": "assistant",
                    "content": ai_message.content,
                    "tool_calls": [
                        {
                            "id": tool_call.id,
                            "type": tool_call.type,
                            "function": {
                                "name": tool_call.function.name,
                                "arguments": tool_call.function.arguments
                            }
                        } for tool_call in ai_message.tool_calls
                    ]
                })
                
                # 执行每个工具调用
                for tool_call in ai_message.tool_calls:
                    print(f"使用工具: {tool_call.function.name}")
                    tool_result = execute_tool_call(tool_call)
                    print(f"工具结果: {tool_result}\n")
                    
                    # 将工具结果添加到历史
                    messages.append({
                        "role": "tool",
                        "tool_call_id": tool_call.id,
                        "content": tool_result
                    })
                
                # 再次调用API获取最终响应
                print("小智正在整合信息...", end="", flush=True)
                
                final_response = client.chat.completions.create(
                    model=DEFAULT_MODEL,
                    messages=messages,
                    temperature=0.7,
                    max_tokens=500
                )
                
                # 清除"整合信息"提示
                print("\r" + " " * 30 + "\r", end="")
                
                # 打印最终响应
                final_content = final_response.choices[0].message.content
                print(f"小智: {final_content}\n")
                
                # 将最终响应添加到历史
                messages.append({"role": "assistant", "content": final_content})
                
            else:
                # 直接打印AI响应
                content = ai_message.content or ""
                print(f"小智: {content}\n")
                
                # 将AI响应添加到历史
                messages.append({"role": "assistant", "content": content})
            
        except Exception as e:
            print(f"\n发生错误: {e}")
            print("请检查API服务是否正在运行，或者网络连接是否正常。\n")

if __name__ == "__main__":
    try:
        chat_with_tools()
    except KeyboardInterrupt:
        print("\n\n程序被用户中断。再见！")
        sys.exit(0)

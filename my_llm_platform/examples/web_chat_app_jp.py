#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Webチャットアプリケーションの例

このサンプルは、シンプルなWebチャットアプリケーションを作成する方法を示します。
LLMプラットフォームのOpenAI互換APIを使用しています。

必要な依存関係:
pip install flask openai

実行方法:
python web_chat_app_jp.py

その後、ブラウザで次のURLにアクセス: http://localhost:5000
"""

import json
from flask import Flask, render_template, request, jsonify, Response, stream_with_context
from openai import OpenAI

# API設定
API_BASE_URL = "http://localhost:8000/v1"
API_KEY = "dummy-api-key"  # 認証が必要ない場合は任意の値
DEFAULT_MODEL = "llama3.2"  # デフォルトのモデル

# OpenAIクライアントの初期化
client = OpenAI(
    base_url=API_BASE_URL,
    api_key=API_KEY
)

# Flaskアプリケーションの初期化
app = Flask(__name__)

# HTMLテンプレートの作成
HTML_TEMPLATE = """
<!DOCTYPE html>
<html lang="ja">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LLMプラットフォームチャット例</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
        }
        .chat-container {
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 20px;
            margin-bottom: 20px;
            height: 500px;
            overflow-y: auto;
        }
        .message {
            margin-bottom: 15px;
            padding: 10px 15px;
            border-radius: 18px;
            max-width: 80%;
            position: relative;
        }
        .user-message {
            background-color: #dcf8c6;
            margin-left: auto;
            border-bottom-right-radius: 5px;
        }
        .bot-message {
            background-color: #f1f0f0;
            margin-right: auto;
            border-bottom-left-radius: 5px;
        }
        .input-container {
            display: flex;
            gap: 10px;
        }
        #user-input {
            flex-grow: 1;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
        }
        button {
            padding: 12px 20px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            transition: background-color 0.3s;
        }
        button:hover {
            background-color: #45a049;
        }
        button:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
        }
        .typing-indicator {
            display: none;
            background-color: #f1f0f0;
            padding: 10px 15px;
            border-radius: 18px;
            margin-bottom: 15px;
            width: fit-content;
            border-bottom-left-radius: 5px;
        }
        .typing-indicator span {
            height: 10px;
            width: 10px;
            float: left;
            margin: 0 1px;
            background-color: #9E9EA1;
            display: block;
            border-radius: 50%;
            opacity: 0.4;
        }
        .typing-indicator span:nth-of-type(1) {
            animation: 1s blink infinite 0.3333s;
        }
        .typing-indicator span:nth-of-type(2) {
            animation: 1s blink infinite 0.6666s;
        }
        .typing-indicator span:nth-of-type(3) {
            animation: 1s blink infinite 0.9999s;
        }
        @keyframes blink {
            50% {
                opacity: 1;
            }
        }
        .model-selector {
            margin-bottom: 20px;
            text-align: center;
        }
        select {
            padding: 8px;
            border-radius: 5px;
            border: 1px solid #ddd;
            font-size: 16px;
        }
        .stream-toggle {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 20px;
        }
        .switch {
            position: relative;
            display: inline-block;
            width: 60px;
            height: 34px;
            margin: 0 10px;
        }
        .switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }
        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 34px;
        }
        .slider:before {
            position: absolute;
            content: "";
            height: 26px;
            width: 26px;
            left: 4px;
            bottom: 4px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }
        input:checked + .slider {
            background-color: #4CAF50;
        }
        input:checked + .slider:before {
            transform: translateX(26px);
        }
    </style>
</head>
<body>
    <h1>LLMプラットフォームチャット例</h1>
    
    <div class="model-selector">
        <label for="model-select">モデルを選択：</label>
        <select id="model-select">
            <option value="llama3.2">Llama 3.2</option>
            <option value="mistral">Mistral</option>
            <option value="qwen2.5-coder">Qwen 2.5 Coder</option>
        </select>
    </div>
    
    <div class="stream-toggle">
        <span>通常レスポンス</span>
        <label class="switch">
            <input type="checkbox" id="stream-toggle" checked>
            <span class="slider"></span>
        </label>
        <span>ストリーミングレスポンス</span>
    </div>
    
    <div class="chat-container" id="chat-container">
        <div class="message bot-message">
            こんにちは！AIアシスタントです。何かお手伝いできることはありますか？
        </div>
        <div class="typing-indicator" id="typing-indicator">
            <span></span>
            <span></span>
            <span></span>
        </div>
    </div>
    
    <div class="input-container">
        <input type="text" id="user-input" placeholder="質問を入力してください..." autofocus>
        <button id="send-button">送信</button>
    </div>

    <script>
        const chatContainer = document.getElementById('chat-container');
        const userInput = document.getElementById('user-input');
        const sendButton = document.getElementById('send-button');
        const modelSelect = document.getElementById('model-select');
        const streamToggle = document.getElementById('stream-toggle');
        const typingIndicator = document.getElementById('typing-indicator');
        
        let messages = [
            {"role": "system", "content": "あなたは役立つAIアシスタントです。友好的で正確で役立つ回答を提供してください。"}
        ];
        
        // メッセージをチャットインターフェースに追加
        function addMessage(content, isUser) {
            const messageDiv = document.createElement('div');
            messageDiv.classList.add('message');
            messageDiv.classList.add(isUser ? 'user-message' : 'bot-message');
            messageDiv.textContent = content;
            
            // タイピングインジケーターが最後にあることを確認
            chatContainer.insertBefore(messageDiv, typingIndicator);
            chatContainer.scrollTop = chatContainer.scrollHeight;
        }
        
        // ユーザー入力を処理
        async function handleUserInput() {
            const userMessage = userInput.value.trim();
            if (!userMessage) return;
            
            // ユーザーメッセージをインターフェースに追加
            addMessage(userMessage, true);
            
            // ユーザーメッセージを履歴に追加
            messages.push({"role": "user", "content": userMessage});
            
            // 入力ボックスをクリアしてボタンを無効化
            userInput.value = '';
            sendButton.disabled = true;
            
            // タイピングインジケーターを表示
            typingIndicator.style.display = 'block';
            
            try {
                const selectedModel = modelSelect.value;
                const useStream = streamToggle.checked;
                
                if (useStream) {
                    // ストリーミングレスポンスを使用
                    const response = await fetch('/api/chat-stream', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            messages: messages,
                            model: selectedModel
                        })
                    });
                    
                    // 新しいメッセージdivを作成
                    const messageDiv = document.createElement('div');
                    messageDiv.classList.add('message', 'bot-message');
                    
                    // タイピングインジケーターを非表示にして新しいメッセージを追加
                    typingIndicator.style.display = 'none';
                    chatContainer.insertBefore(messageDiv, typingIndicator);
                    
                    let fullContent = '';
                    
                    // ストリーミングレスポンスを処理
                    const reader = response.body.getReader();
                    const decoder = new TextDecoder();
                    
                    while (true) {
                        const { done, value } = await reader.read();
                        if (done) break;
                        
                        const chunk = decoder.decode(value);
                        const lines = chunk.split('\\n');
                        
                        for (const line of lines) {
                            if (line.startsWith('data: ') && line !== 'data: [DONE]') {
                                try {
                                    const data = JSON.parse(line.substring(6));
                                    if (data.choices && data.choices[0].delta && data.choices[0].delta.content) {
                                        const content = data.choices[0].delta.content;
                                        fullContent += content;
                                        messageDiv.textContent = fullContent;
                                        chatContainer.scrollTop = chatContainer.scrollHeight;
                                    }
                                } catch (e) {
                                    console.error('JSONの解析エラー:', e);
                                }
                            }
                        }
                    }
                    
                    // AIメッセージを履歴に追加
                    messages.push({"role": "assistant", "content": fullContent});
                    
                } else {
                    // 通常レスポンスを使用
                    const response = await fetch('/api/chat', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            messages: messages,
                            model: selectedModel
                        })
                    });
                    
                    const data = await response.json();
                    
                    // タイピングインジケーターを非表示
                    typingIndicator.style.display = 'none';
                    
                    // AIメッセージをインターフェースに追加
                    addMessage(data.content, false);
                    
                    // AIメッセージを履歴に追加
                    messages.push({"role": "assistant", "content": data.content});
                }
                
            } catch (error) {
                console.error('エラー:', error);
                typingIndicator.style.display = 'none';
                addMessage('エラーが発生しました。後でもう一度お試しください。', false);
            }
            
            // ボタンを有効化
            sendButton.disabled = false;
            
            // 一番下までスクロール
            chatContainer.scrollTop = chatContainer.scrollHeight;
        }
        
        // イベントリスナー
        sendButton.addEventListener('click', handleUserInput);
        
        userInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                handleUserInput();
            }
        });
    </script>
</body>
</html>
"""

@app.route('/')
def index():
    """メインページをレンダリング"""
    return HTML_TEMPLATE

@app.route('/api/chat', methods=['POST'])
def chat():
    """チャットリクエストを処理（非ストリーミング）"""
    data = request.json
    messages = data.get('messages', [])
    model = data.get('model', DEFAULT_MODEL)
    
    try:
        response = client.chat.completions.create(
            model=model,
            messages=messages,
            temperature=0.7,
            max_tokens=500
        )
        
        content = response.choices[0].message.content
        return jsonify({"content": content})
    
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@app.route('/api/chat-stream', methods=['POST'])
def chat_stream():
    """チャットリクエストを処理（ストリーミング）"""
    data = request.json
    messages = data.get('messages', [])
    model = data.get('model', DEFAULT_MODEL)
    
    def generate():
        try:
            stream = client.chat.completions.create(
                model=model,
                messages=messages,
                temperature=0.7,
                max_tokens=500,
                stream=True
            )
            
            for chunk in stream:
                if chunk.choices and len(chunk.choices) > 0:
                    if hasattr(chunk.choices[0], 'delta') and hasattr(chunk.choices[0].delta, 'content'):
                        content = chunk.choices[0].delta.content
                        if content:
                            yield f"data: {json.dumps({'choices': [{'delta': {'content': content}}]})}\n\n"
            
            yield "data: [DONE]\n\n"
            
        except Exception as e:
            yield f"data: {json.dumps({'error': str(e)})}\n\n"
    
    return Response(stream_with_context(generate()), mimetype='text/event-stream')

if __name__ == '__main__':
    print("Webチャットアプリケーションを起動しています...")
    print("ブラウザで次のURLにアクセスしてください: http://localhost:5000")
    app.run(debug=True, host='0.0.0.0', port=5000)

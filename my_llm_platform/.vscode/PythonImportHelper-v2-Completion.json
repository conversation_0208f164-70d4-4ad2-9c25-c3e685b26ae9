[{"label": "Depends", "importPath": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>", "isExtraImport": true, "detail": "<PERSON><PERSON><PERSON>", "documentation": {}}, {"label": "HTTPException", "importPath": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>", "isExtraImport": true, "detail": "<PERSON><PERSON><PERSON>", "documentation": {}}, {"label": "status", "importPath": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>", "isExtraImport": true, "detail": "<PERSON><PERSON><PERSON>", "documentation": {}}, {"label": "Depends", "importPath": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>", "isExtraImport": true, "detail": "<PERSON><PERSON><PERSON>", "documentation": {}}, {"label": "HTTPException", "importPath": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>", "isExtraImport": true, "detail": "<PERSON><PERSON><PERSON>", "documentation": {}}, {"label": "status", "importPath": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>", "isExtraImport": true, "detail": "<PERSON><PERSON><PERSON>", "documentation": {}}, {"label": "Header", "importPath": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>", "isExtraImport": true, "detail": "<PERSON><PERSON><PERSON>", "documentation": {}}, {"label": "APIRouter", "importPath": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>", "isExtraImport": true, "detail": "<PERSON><PERSON><PERSON>", "documentation": {}}, {"label": "Depends", "importPath": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>", "isExtraImport": true, "detail": "<PERSON><PERSON><PERSON>", "documentation": {}}, {"label": "HTTPException", "importPath": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>", "isExtraImport": true, "detail": "<PERSON><PERSON><PERSON>", "documentation": {}}, {"label": "status", "importPath": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>", "isExtraImport": true, "detail": "<PERSON><PERSON><PERSON>", "documentation": {}}, {"label": "APIRouter", "importPath": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>", "isExtraImport": true, "detail": "<PERSON><PERSON><PERSON>", "documentation": {}}, {"label": "HTTPException", "importPath": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>", "isExtraImport": true, "detail": "<PERSON><PERSON><PERSON>", "documentation": {}}, {"label": "Depends", "importPath": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>", "isExtraImport": true, "detail": "<PERSON><PERSON><PERSON>", "documentation": {}}, {"label": "APIRouter", "importPath": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>", "isExtraImport": true, "detail": "<PERSON><PERSON><PERSON>", "documentation": {}}, {"label": "Depends", "importPath": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>", "isExtraImport": true, "detail": "<PERSON><PERSON><PERSON>", "documentation": {}}, {"label": "HTTPException", "importPath": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>", "isExtraImport": true, "detail": "<PERSON><PERSON><PERSON>", "documentation": {}}, {"label": "status", "importPath": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>", "isExtraImport": true, "detail": "<PERSON><PERSON><PERSON>", "documentation": {}}, {"label": "APIRouter", "importPath": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>", "isExtraImport": true, "detail": "<PERSON><PERSON><PERSON>", "documentation": {}}, {"label": "Depends", "importPath": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>", "isExtraImport": true, "detail": "<PERSON><PERSON><PERSON>", "documentation": {}}, {"label": "HTTPException", "importPath": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>", "isExtraImport": true, "detail": "<PERSON><PERSON><PERSON>", "documentation": {}}, {"label": "Query", "importPath": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>", "isExtraImport": true, "detail": "<PERSON><PERSON><PERSON>", "documentation": {}}, {"label": "APIRouter", "importPath": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>", "isExtraImport": true, "detail": "<PERSON><PERSON><PERSON>", "documentation": {}}, {"label": "Depends", "importPath": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>", "isExtraImport": true, "detail": "<PERSON><PERSON><PERSON>", "documentation": {}}, {"label": "HTTPException", "importPath": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>", "isExtraImport": true, "detail": "<PERSON><PERSON><PERSON>", "documentation": {}}, {"label": "Query", "importPath": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>", "isExtraImport": true, "detail": "<PERSON><PERSON><PERSON>", "documentation": {}}, {"label": "APIRouter", "importPath": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>", "isExtraImport": true, "detail": "<PERSON><PERSON><PERSON>", "documentation": {}}, {"label": "Request", "importPath": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>", "isExtraImport": true, "detail": "<PERSON><PERSON><PERSON>", "documentation": {}}, {"label": "Response", "importPath": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>", "isExtraImport": true, "detail": "<PERSON><PERSON><PERSON>", "documentation": {}}, {"label": "APIRouter", "importPath": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>", "isExtraImport": true, "detail": "<PERSON><PERSON><PERSON>", "documentation": {}}, {"label": "HTTPException", "importPath": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>", "isExtraImport": true, "detail": "<PERSON><PERSON><PERSON>", "documentation": {}}, {"label": "Depends", "importPath": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>", "isExtraImport": true, "detail": "<PERSON><PERSON><PERSON>", "documentation": {}}, {"label": "Request", "importPath": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>", "isExtraImport": true, "detail": "<PERSON><PERSON><PERSON>", "documentation": {}}, {"label": "Response", "importPath": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>", "isExtraImport": true, "detail": "<PERSON><PERSON><PERSON>", "documentation": {}}, {"label": "UploadFile", "importPath": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>", "isExtraImport": true, "detail": "<PERSON><PERSON><PERSON>", "documentation": {}}, {"label": "File", "importPath": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>", "isExtraImport": true, "detail": "<PERSON><PERSON><PERSON>", "documentation": {}}, {"label": "Form", "importPath": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>", "isExtraImport": true, "detail": "<PERSON><PERSON><PERSON>", "documentation": {}}, {"label": "Body", "importPath": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>", "isExtraImport": true, "detail": "<PERSON><PERSON><PERSON>", "documentation": {}}, {"label": "Query", "importPath": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>", "isExtraImport": true, "detail": "<PERSON><PERSON><PERSON>", "documentation": {}}, {"label": "Path", "importPath": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>", "isExtraImport": true, "detail": "<PERSON><PERSON><PERSON>", "documentation": {}}, {"label": "APIRouter", "importPath": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>", "isExtraImport": true, "detail": "<PERSON><PERSON><PERSON>", "documentation": {}}, {"label": "Depends", "importPath": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>", "isExtraImport": true, "detail": "<PERSON><PERSON><PERSON>", "documentation": {}}, {"label": "HTTPException", "importPath": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>", "isExtraImport": true, "detail": "<PERSON><PERSON><PERSON>", "documentation": {}}, {"label": "UploadFile", "importPath": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>", "isExtraImport": true, "detail": "<PERSON><PERSON><PERSON>", "documentation": {}}, {"label": "File", "importPath": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>", "isExtraImport": true, "detail": "<PERSON><PERSON><PERSON>", "documentation": {}}, {"label": "Form", "importPath": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>", "isExtraImport": true, "detail": "<PERSON><PERSON><PERSON>", "documentation": {}}, {"label": "BackgroundTasks", "importPath": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>", "isExtraImport": true, "detail": "<PERSON><PERSON><PERSON>", "documentation": {}}, {"label": "Query", "importPath": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>", "isExtraImport": true, "detail": "<PERSON><PERSON><PERSON>", "documentation": {}}, {"label": "APIRouter", "importPath": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>", "isExtraImport": true, "detail": "<PERSON><PERSON><PERSON>", "documentation": {}}, {"label": "HTTPException", "importPath": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>", "isExtraImport": true, "detail": "<PERSON><PERSON><PERSON>", "documentation": {}}, {"label": "FastAPI", "importPath": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>", "isExtraImport": true, "detail": "<PERSON><PERSON><PERSON>", "documentation": {}}, {"label": "HTTPException", "importPath": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>", "isExtraImport": true, "detail": "<PERSON><PERSON><PERSON>", "documentation": {}}, {"label": "Depends", "importPath": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>", "isExtraImport": true, "detail": "<PERSON><PERSON><PERSON>", "documentation": {}}, {"label": "Request", "importPath": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>", "isExtraImport": true, "detail": "<PERSON><PERSON><PERSON>", "documentation": {}}, {"label": "Response", "importPath": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>", "isExtraImport": true, "detail": "<PERSON><PERSON><PERSON>", "documentation": {}}, {"label": "FastAPIKeycloak", "importPath": "fastapi_keycloak", "description": "fastapi_keycloak", "isExtraImport": true, "detail": "fastapi_keycloak", "documentation": {}}, {"label": "Dict", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Any", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "List", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Optional", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Dict", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Any", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "List", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "List", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Optional", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Dict", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Any", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Dict", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Any", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Optional", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Dict", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Any", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Optional", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "List", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Union", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Callable", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Dict", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Any", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "List", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Optional", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Dict", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Any", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "List", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Optional", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Dict", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Any", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "List", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Optional", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Dict", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Any", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Optional", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "List", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Dict", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Any", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Optional", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Dict", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Any", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "List", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Optional", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Union", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Dict", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Any", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Optional", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "List", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Optional", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Union", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "List", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Dict", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Any", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Optional", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "AsyncGenerator", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Dict", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Any", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "List", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Optional", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Dict", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "List", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Any", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Optional", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Callable", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Union", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "TypedDict", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "TypedDict", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Literal", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Optional", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Dict", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Any", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "List", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Optional", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Dict", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "List", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Any", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Optional", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Dict", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "List", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Any", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Optional", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Dict", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Dict", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Any", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Optional", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "List", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Callable", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Awaitable", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Union", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Dict", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Any", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Optional", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "List", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Callable", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Awaitable", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Union", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "List", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Dict", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Any", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Optional", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "List", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Dict", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Any", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Optional", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "List", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Dict", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Any", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Optional", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Any", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Dict", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "List", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Optional", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Union", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "List", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Dict", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Any", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Optional", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "List", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Dict", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Any", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Optional", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "List", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Dict", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "List", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Dict", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Any", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Optional", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "List", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Dict", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Any", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Optional", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Callable", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "List", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Optional", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Dict", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Any", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "<PERSON><PERSON>", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Dict", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Type", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "List", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "List", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Dict", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Any", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Optional", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Union", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Callable", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Dict", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "List", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Any", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Optional", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Dict", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "List", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Any", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Optional", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Union", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Callable", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Dict", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "List", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Any", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Optional", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Dict", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "List", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Any", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Optional", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Dict", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "List", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Any", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Optional", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Type", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "ClassVar", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "List", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Dict", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Any", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Optional", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Dict", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "List", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Any", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Optional", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Type", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "TypeVar", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "cast", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "List", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Dict", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Any", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Optional", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Dict", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "List", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Any", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Optional", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Union", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "List", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Optional", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Dict", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Any", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Union", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Literal", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Annotated", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "List", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Optional", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Dict", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Any", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Union", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Literal", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Annotated", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "List", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Dict", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Any", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "List", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Optional", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Dict", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Any", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Union", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Literal", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Annotated", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "List", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Dict", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Any", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Optional", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "List", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Optional", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Dict", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Any", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "<PERSON><PERSON>", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Optional", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Dict", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "List", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Any", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Optional", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Union", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "TextIO", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Callable", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Iterator", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Dict", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Any", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "List", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Optional", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Dict", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Any", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "List", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Optional", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "List", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Dict", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Any", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "List", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Dict", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Any", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "logging", "kind": 6, "isExtraImport": true, "importPath": "logging", "description": "logging", "detail": "logging", "documentation": {}}, {"label": "BaseModel", "importPath": "pydantic", "description": "pydantic", "isExtraImport": true, "detail": "pydantic", "documentation": {}}, {"label": "BaseModel", "importPath": "pydantic", "description": "pydantic", "isExtraImport": true, "detail": "pydantic", "documentation": {}}, {"label": "Field", "importPath": "pydantic", "description": "pydantic", "isExtraImport": true, "detail": "pydantic", "documentation": {}}, {"label": "BaseModel", "importPath": "pydantic", "description": "pydantic", "isExtraImport": true, "detail": "pydantic", "documentation": {}}, {"label": "Field", "importPath": "pydantic", "description": "pydantic", "isExtraImport": true, "detail": "pydantic", "documentation": {}}, {"label": "BaseModel", "importPath": "pydantic", "description": "pydantic", "isExtraImport": true, "detail": "pydantic", "documentation": {}}, {"label": "BaseModel", "importPath": "pydantic", "description": "pydantic", "isExtraImport": true, "detail": "pydantic", "documentation": {}}, {"label": "BaseModel", "importPath": "pydantic", "description": "pydantic", "isExtraImport": true, "detail": "pydantic", "documentation": {}}, {"label": "BaseModel", "importPath": "pydantic", "description": "pydantic", "isExtraImport": true, "detail": "pydantic", "documentation": {}}, {"label": "Field", "importPath": "pydantic", "description": "pydantic", "isExtraImport": true, "detail": "pydantic", "documentation": {}}, {"label": "BaseModel", "importPath": "pydantic", "description": "pydantic", "isExtraImport": true, "detail": "pydantic", "documentation": {}}, {"label": "Field", "importPath": "pydantic", "description": "pydantic", "isExtraImport": true, "detail": "pydantic", "documentation": {}}, {"label": "BaseModel", "importPath": "pydantic", "description": "pydantic", "isExtraImport": true, "detail": "pydantic", "documentation": {}}, {"label": "Field", "importPath": "pydantic", "description": "pydantic", "isExtraImport": true, "detail": "pydantic", "documentation": {}}, {"label": "model_validator", "importPath": "pydantic", "description": "pydantic", "isExtraImport": true, "detail": "pydantic", "documentation": {}}, {"label": "BaseModel", "importPath": "pydantic", "description": "pydantic", "isExtraImport": true, "detail": "pydantic", "documentation": {}}, {"label": "Field", "importPath": "pydantic", "description": "pydantic", "isExtraImport": true, "detail": "pydantic", "documentation": {}}, {"label": "BaseModel", "importPath": "pydantic", "description": "pydantic", "isExtraImport": true, "detail": "pydantic", "documentation": {}}, {"label": "Field", "importPath": "pydantic", "description": "pydantic", "isExtraImport": true, "detail": "pydantic", "documentation": {}}, {"label": "Enum", "importPath": "enum", "description": "enum", "isExtraImport": true, "detail": "enum", "documentation": {}}, {"label": "Enum", "importPath": "enum", "description": "enum", "isExtraImport": true, "detail": "enum", "documentation": {}}, {"label": "Enum", "importPath": "enum", "description": "enum", "isExtraImport": true, "detail": "enum", "documentation": {}}, {"label": "Enum", "importPath": "enum", "description": "enum", "isExtraImport": true, "detail": "enum", "documentation": {}}, {"label": "OAuth2PasswordRequestForm", "importPath": "fastapi.security", "description": "fastapi.security", "isExtraImport": true, "detail": "fastapi.security", "documentation": {}}, {"label": "create_engine", "importPath": "sqlalchemy", "description": "sqlalchemy", "isExtraImport": true, "detail": "sqlalchemy", "documentation": {}}, {"label": "Column", "importPath": "sqlalchemy", "description": "sqlalchemy", "isExtraImport": true, "detail": "sqlalchemy", "documentation": {}}, {"label": "Integer", "importPath": "sqlalchemy", "description": "sqlalchemy", "isExtraImport": true, "detail": "sqlalchemy", "documentation": {}}, {"label": "String", "importPath": "sqlalchemy", "description": "sqlalchemy", "isExtraImport": true, "detail": "sqlalchemy", "documentation": {}}, {"label": "DateTime", "importPath": "sqlalchemy", "description": "sqlalchemy", "isExtraImport": true, "detail": "sqlalchemy", "documentation": {}}, {"label": "ForeignKey", "importPath": "sqlalchemy", "description": "sqlalchemy", "isExtraImport": true, "detail": "sqlalchemy", "documentation": {}}, {"label": "Text", "importPath": "sqlalchemy", "description": "sqlalchemy", "isExtraImport": true, "detail": "sqlalchemy", "documentation": {}}, {"label": "create_engine", "importPath": "sqlalchemy", "description": "sqlalchemy", "isExtraImport": true, "detail": "sqlalchemy", "documentation": {}}, {"label": "MetaData", "importPath": "sqlalchemy", "description": "sqlalchemy", "isExtraImport": true, "detail": "sqlalchemy", "documentation": {}}, {"label": "Table", "importPath": "sqlalchemy", "description": "sqlalchemy", "isExtraImport": true, "detail": "sqlalchemy", "documentation": {}}, {"label": "Column", "importPath": "sqlalchemy", "description": "sqlalchemy", "isExtraImport": true, "detail": "sqlalchemy", "documentation": {}}, {"label": "String", "importPath": "sqlalchemy", "description": "sqlalchemy", "isExtraImport": true, "detail": "sqlalchemy", "documentation": {}}, {"label": "Integer", "importPath": "sqlalchemy", "description": "sqlalchemy", "isExtraImport": true, "detail": "sqlalchemy", "documentation": {}}, {"label": "DateTime", "importPath": "sqlalchemy", "description": "sqlalchemy", "isExtraImport": true, "detail": "sqlalchemy", "documentation": {}}, {"label": "ForeignKey", "importPath": "sqlalchemy", "description": "sqlalchemy", "isExtraImport": true, "detail": "sqlalchemy", "documentation": {}}, {"label": "Text", "importPath": "sqlalchemy", "description": "sqlalchemy", "isExtraImport": true, "detail": "sqlalchemy", "documentation": {}}, {"label": "MetaData", "importPath": "sqlalchemy", "description": "sqlalchemy", "isExtraImport": true, "detail": "sqlalchemy", "documentation": {}}, {"label": "create_engine", "importPath": "sqlalchemy", "description": "sqlalchemy", "isExtraImport": true, "detail": "sqlalchemy", "documentation": {}}, {"label": "declarative_base", "importPath": "sqlalchemy.ext.declarative", "description": "sqlalchemy.ext.declarative", "isExtraImport": true, "detail": "sqlalchemy.ext.declarative", "documentation": {}}, {"label": "declarative_base", "importPath": "sqlalchemy.ext.declarative", "description": "sqlalchemy.ext.declarative", "isExtraImport": true, "detail": "sqlalchemy.ext.declarative", "documentation": {}}, {"label": "sessionmaker", "importPath": "sqlalchemy.orm", "description": "sqlalchemy.orm", "isExtraImport": true, "detail": "sqlalchemy.orm", "documentation": {}}, {"label": "relationship", "importPath": "sqlalchemy.orm", "description": "sqlalchemy.orm", "isExtraImport": true, "detail": "sqlalchemy.orm", "documentation": {}}, {"label": "sessionmaker", "importPath": "sqlalchemy.orm", "description": "sqlalchemy.orm", "isExtraImport": true, "detail": "sqlalchemy.orm", "documentation": {}}, {"label": "relationship", "importPath": "sqlalchemy.orm", "description": "sqlalchemy.orm", "isExtraImport": true, "detail": "sqlalchemy.orm", "documentation": {}}, {"label": "sessionmaker", "importPath": "sqlalchemy.orm", "description": "sqlalchemy.orm", "isExtraImport": true, "detail": "sqlalchemy.orm", "documentation": {}}, {"label": "Session", "importPath": "sqlalchemy.orm", "description": "sqlalchemy.orm", "isExtraImport": true, "detail": "sqlalchemy.orm", "documentation": {}}, {"label": "Session", "importPath": "sqlalchemy.orm", "description": "sqlalchemy.orm", "isExtraImport": true, "detail": "sqlalchemy.orm", "documentation": {}}, {"label": "Session", "importPath": "sqlalchemy.orm", "description": "sqlalchemy.orm", "isExtraImport": true, "detail": "sqlalchemy.orm", "documentation": {}}, {"label": "Session", "importPath": "sqlalchemy.orm", "description": "sqlalchemy.orm", "isExtraImport": true, "detail": "sqlalchemy.orm", "documentation": {}}, {"label": "Session", "importPath": "sqlalchemy.orm", "description": "sqlalchemy.orm", "isExtraImport": true, "detail": "sqlalchemy.orm", "documentation": {}}, {"label": "datetime", "kind": 6, "isExtraImport": true, "importPath": "datetime", "description": "datetime", "detail": "datetime", "documentation": {}}, {"label": "datetime", "importPath": "datetime", "description": "datetime", "isExtraImport": true, "detail": "datetime", "documentation": {}}, {"label": "datetime", "importPath": "datetime", "description": "datetime", "isExtraImport": true, "detail": "datetime", "documentation": {}}, {"label": "datetime", "importPath": "datetime", "description": "datetime", "isExtraImport": true, "detail": "datetime", "documentation": {}}, {"label": "datetime", "importPath": "datetime", "description": "datetime", "isExtraImport": true, "detail": "datetime", "documentation": {}}, {"label": "timezone", "importPath": "datetime", "description": "datetime", "isExtraImport": true, "detail": "datetime", "documentation": {}}, {"label": "datetime", "importPath": "datetime", "description": "datetime", "isExtraImport": true, "detail": "datetime", "documentation": {}}, {"label": "timezone", "importPath": "datetime", "description": "datetime", "isExtraImport": true, "detail": "datetime", "documentation": {}}, {"label": "datetime", "importPath": "datetime", "description": "datetime", "isExtraImport": true, "detail": "datetime", "documentation": {}}, {"label": "uuid", "kind": 6, "isExtraImport": true, "importPath": "uuid", "description": "uuid", "detail": "uuid", "documentation": {}}, {"label": "os", "kind": 6, "isExtraImport": true, "importPath": "os", "description": "os", "detail": "os", "documentation": {}}, {"label": "sys", "kind": 6, "isExtraImport": true, "importPath": "sys", "description": "sys", "detail": "sys", "documentation": {}}, {"label": "<PERSON><PERSON><PERSON><PERSON>", "kind": 6, "isExtraImport": true, "importPath": "<PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON>", "detail": "<PERSON><PERSON><PERSON><PERSON>", "documentation": {}}, {"label": "json", "kind": 6, "isExtraImport": true, "importPath": "json", "description": "json", "detail": "json", "documentation": {}}, {"label": "abc", "kind": 6, "isExtraImport": true, "importPath": "abc", "description": "abc", "detail": "abc", "documentation": {}}, {"label": "ABC", "importPath": "abc", "description": "abc", "isExtraImport": true, "detail": "abc", "documentation": {}}, {"label": "abstractmethod", "importPath": "abc", "description": "abc", "isExtraImport": true, "detail": "abc", "documentation": {}}, {"label": "ABC", "importPath": "abc", "description": "abc", "isExtraImport": true, "detail": "abc", "documentation": {}}, {"label": "abstractmethod", "importPath": "abc", "description": "abc", "isExtraImport": true, "detail": "abc", "documentation": {}}, {"label": "requests", "kind": 6, "isExtraImport": true, "importPath": "requests", "description": "requests", "detail": "requests", "documentation": {}}, {"label": "re", "kind": 6, "isExtraImport": true, "importPath": "re", "description": "re", "detail": "re", "documentation": {}}, {"label": "OpenAI", "importPath": "openai", "description": "openai", "isExtraImport": true, "detail": "openai", "documentation": {}}, {"label": "OpenAI", "importPath": "openai", "description": "openai", "isExtraImport": true, "detail": "openai", "documentation": {}}, {"label": "OpenAI", "importPath": "openai", "description": "openai", "isExtraImport": true, "detail": "openai", "documentation": {}}, {"label": "OpenAI", "importPath": "openai", "description": "openai", "isExtraImport": true, "detail": "openai", "documentation": {}}, {"label": "OpenAI", "importPath": "openai", "description": "openai", "isExtraImport": true, "detail": "openai", "documentation": {}}, {"label": "AsyncOpenAI", "importPath": "openai", "description": "openai", "isExtraImport": true, "detail": "openai", "documentation": {}}, {"label": "BadRequestError", "importPath": "openai", "description": "openai", "isExtraImport": true, "detail": "openai", "documentation": {}}, {"label": "AuthenticationError", "importPath": "openai", "description": "openai", "isExtraImport": true, "detail": "openai", "documentation": {}}, {"label": "PermissionDeniedError", "importPath": "openai", "description": "openai", "isExtraImport": true, "detail": "openai", "documentation": {}}, {"label": "NotFoundError", "importPath": "openai", "description": "openai", "isExtraImport": true, "detail": "openai", "documentation": {}}, {"label": "RateLimitError", "importPath": "openai", "description": "openai", "isExtraImport": true, "detail": "openai", "documentation": {}}, {"label": "APIError", "importPath": "openai", "description": "openai", "isExtraImport": true, "detail": "openai", "documentation": {}}, {"label": "OpenAI", "importPath": "openai", "description": "openai", "isExtraImport": true, "detail": "openai", "documentation": {}}, {"label": "OpenAI", "importPath": "openai", "description": "openai", "isExtraImport": true, "detail": "openai", "documentation": {}}, {"label": "OpenAI", "importPath": "openai", "description": "openai", "isExtraImport": true, "detail": "openai", "documentation": {}}, {"label": "OpenAI", "importPath": "openai", "description": "openai", "isExtraImport": true, "detail": "openai", "documentation": {}}, {"label": "OpenAI", "importPath": "openai", "description": "openai", "isExtraImport": true, "detail": "openai", "documentation": {}}, {"label": "OpenAI", "importPath": "openai", "description": "openai", "isExtraImport": true, "detail": "openai", "documentation": {}}, {"label": "OpenAI", "importPath": "openai", "description": "openai", "isExtraImport": true, "detail": "openai", "documentation": {}}, {"label": "OpenAI", "importPath": "openai", "description": "openai", "isExtraImport": true, "detail": "openai", "documentation": {}}, {"label": "OpenAI", "importPath": "openai", "description": "openai", "isExtraImport": true, "detail": "openai", "documentation": {}}, {"label": "OpenAI", "importPath": "openai", "description": "openai", "isExtraImport": true, "detail": "openai", "documentation": {}}, {"label": "OpenAI", "importPath": "openai", "description": "openai", "isExtraImport": true, "detail": "openai", "documentation": {}}, {"label": "OpenAI", "importPath": "openai", "description": "openai", "isExtraImport": true, "detail": "openai", "documentation": {}}, {"label": "httpx", "kind": 6, "isExtraImport": true, "importPath": "httpx", "description": "httpx", "detail": "httpx", "documentation": {}}, {"label": "asyncio", "kind": 6, "isExtraImport": true, "importPath": "asyncio", "description": "asyncio", "detail": "asyncio", "documentation": {}}, {"label": "logging.config", "kind": 6, "isExtraImport": true, "importPath": "logging.config", "description": "logging.config", "detail": "logging.config", "documentation": {}}, {"label": "yaml", "kind": 6, "isExtraImport": true, "importPath": "yaml", "description": "yaml", "detail": "yaml", "documentation": {}}, {"label": "Counter", "importPath": "prometheus_client", "description": "prometheus_client", "isExtraImport": true, "detail": "prometheus_client", "documentation": {}}, {"label": "Histogram", "importPath": "prometheus_client", "description": "prometheus_client", "isExtraImport": true, "detail": "prometheus_client", "documentation": {}}, {"label": "Gauge", "importPath": "prometheus_client", "description": "prometheus_client", "isExtraImport": true, "detail": "prometheus_client", "documentation": {}}, {"label": "BaseChatModel", "importPath": "langchain_core.language_models", "description": "langchain_core.language_models", "isExtraImport": true, "detail": "langchain_core.language_models", "documentation": {}}, {"label": "BaseChatModel", "importPath": "langchain_core.language_models", "description": "langchain_core.language_models", "isExtraImport": true, "detail": "langchain_core.language_models", "documentation": {}}, {"label": "BaseChatModel", "importPath": "langchain_core.language_models", "description": "langchain_core.language_models", "isExtraImport": true, "detail": "langchain_core.language_models", "documentation": {}}, {"label": "BaseTool", "importPath": "langchain.tools", "description": "langchain.tools", "isExtraImport": true, "detail": "langchain.tools", "documentation": {}}, {"label": "BaseTool", "importPath": "langchain.tools", "description": "langchain.tools", "isExtraImport": true, "detail": "langchain.tools", "documentation": {}}, {"label": "Tool", "importPath": "langchain.tools", "description": "langchain.tools", "isExtraImport": true, "detail": "langchain.tools", "documentation": {}}, {"label": "BaseTool", "importPath": "langchain.tools", "description": "langchain.tools", "isExtraImport": true, "detail": "langchain.tools", "documentation": {}}, {"label": "BaseTool", "importPath": "langchain.tools", "description": "langchain.tools", "isExtraImport": true, "detail": "langchain.tools", "documentation": {}}, {"label": "ChatOpenAI", "importPath": "langchain_openai", "description": "langchain_openai", "isExtraImport": true, "detail": "langchain_openai", "documentation": {}}, {"label": "ChatOpenAI", "importPath": "langchain_openai", "description": "langchain_openai", "isExtraImport": true, "detail": "langchain_openai", "documentation": {}}, {"label": "ChatOpenAI", "importPath": "langchain_openai", "description": "langchain_openai", "isExtraImport": true, "detail": "langchain_openai", "documentation": {}}, {"label": "ChatOpenAI", "importPath": "langchain_openai", "description": "langchain_openai", "isExtraImport": true, "detail": "langchain_openai", "documentation": {}}, {"label": "OpenAIEmbeddings", "importPath": "langchain_openai", "description": "langchain_openai", "isExtraImport": true, "detail": "langchain_openai", "documentation": {}}, {"label": "ChatOpenAI", "importPath": "langchain_openai", "description": "langchain_openai", "isExtraImport": true, "detail": "langchain_openai", "documentation": {}}, {"label": "StateGraph", "importPath": "langgraph.graph", "description": "langgraph.graph", "isExtraImport": true, "detail": "langgraph.graph", "documentation": {}}, {"label": "START", "importPath": "langgraph.graph", "description": "langgraph.graph", "isExtraImport": true, "detail": "langgraph.graph", "documentation": {}}, {"label": "END", "importPath": "langgraph.graph", "description": "langgraph.graph", "isExtraImport": true, "detail": "langgraph.graph", "documentation": {}}, {"label": "StateGraph", "importPath": "langgraph.graph", "description": "langgraph.graph", "isExtraImport": true, "detail": "langgraph.graph", "documentation": {}}, {"label": "MessagesState", "importPath": "langgraph.graph", "description": "langgraph.graph", "isExtraImport": true, "detail": "langgraph.graph", "documentation": {}}, {"label": "START", "importPath": "langgraph.graph", "description": "langgraph.graph", "isExtraImport": true, "detail": "langgraph.graph", "documentation": {}}, {"label": "END", "importPath": "langgraph.graph", "description": "langgraph.graph", "isExtraImport": true, "detail": "langgraph.graph", "documentation": {}}, {"label": "StateGraph", "importPath": "langgraph.graph", "description": "langgraph.graph", "isExtraImport": true, "detail": "langgraph.graph", "documentation": {}}, {"label": "START", "importPath": "langgraph.graph", "description": "langgraph.graph", "isExtraImport": true, "detail": "langgraph.graph", "documentation": {}}, {"label": "END", "importPath": "langgraph.graph", "description": "langgraph.graph", "isExtraImport": true, "detail": "langgraph.graph", "documentation": {}}, {"label": "autopep8", "kind": 6, "isExtraImport": true, "importPath": "autopep8", "description": "autopep8", "detail": "autopep8", "documentation": {}}, {"label": "tool", "importPath": "langchain_core.tools", "description": "langchain_core.tools", "isExtraImport": true, "detail": "langchain_core.tools", "documentation": {}}, {"label": "ToolNode", "importPath": "langgraph.prebuilt", "description": "langgraph.prebuilt", "isExtraImport": true, "detail": "langgraph.prebuilt", "documentation": {}}, {"label": "create_react_agent", "importPath": "langgraph.prebuilt", "description": "langgraph.prebuilt", "isExtraImport": true, "detail": "langgraph.prebuilt", "documentation": {}}, {"label": "ChatPromptTemplate", "importPath": "langchain_core.prompts", "description": "langchain_core.prompts", "isExtraImport": true, "detail": "langchain_core.prompts", "documentation": {}}, {"label": "MessagesPlaceholder", "importPath": "langchain_core.prompts", "description": "langchain_core.prompts", "isExtraImport": true, "detail": "langchain_core.prompts", "documentation": {}}, {"label": "ChatPromptTemplate", "importPath": "langchain_core.prompts", "description": "langchain_core.prompts", "isExtraImport": true, "detail": "langchain_core.prompts", "documentation": {}}, {"label": "HumanMessage", "importPath": "langchain_core.messages", "description": "langchain_core.messages", "isExtraImport": true, "detail": "langchain_core.messages", "documentation": {}}, {"label": "SystemMessage", "importPath": "langchain_core.messages", "description": "langchain_core.messages", "isExtraImport": true, "detail": "langchain_core.messages", "documentation": {}}, {"label": "FastMCP", "importPath": "mcp.server.fastmcp", "description": "mcp.server.fastmcp", "isExtraImport": true, "detail": "mcp.server.fastmcp", "documentation": {}}, {"label": "ClientSession", "importPath": "mcp", "description": "mcp", "isExtraImport": true, "detail": "mcp", "documentation": {}}, {"label": "StdioServerParameters", "importPath": "mcp", "description": "mcp", "isExtraImport": true, "detail": "mcp", "documentation": {}}, {"label": "stdio_client", "importPath": "mcp.client.stdio", "description": "mcp.client.stdio", "isExtraImport": true, "detail": "mcp.client.stdio", "documentation": {}}, {"label": "sse_client", "importPath": "mcp.client.sse", "description": "mcp.client.sse", "isExtraImport": true, "detail": "mcp.client.sse", "documentation": {}}, {"label": "load_mcp_tools", "importPath": "langchain_mcp_adapters.tools", "description": "langchain_mcp_adapters.tools", "isExtraImport": true, "detail": "langchain_mcp_adapters.tools", "documentation": {}}, {"label": "load_dotenv", "importPath": "dotenv", "description": "dotenv", "isExtraImport": true, "detail": "dotenv", "documentation": {}}, {"label": "load_dotenv", "importPath": "dotenv", "description": "dotenv", "isExtraImport": true, "detail": "dotenv", "documentation": {}}, {"label": "pprint", "importPath": "pprint", "description": "pprint", "isExtraImport": true, "detail": "pprint", "documentation": {}}, {"label": "Document", "importPath": "langchain_core.documents", "description": "langchain_core.documents", "isExtraImport": true, "detail": "langchain_core.documents", "documentation": {}}, {"label": "Document", "importPath": "langchain_core.documents", "description": "langchain_core.documents", "isExtraImport": true, "detail": "langchain_core.documents", "documentation": {}}, {"label": "Document", "importPath": "langchain_core.documents", "description": "langchain_core.documents", "isExtraImport": true, "detail": "langchain_core.documents", "documentation": {}}, {"label": "Document", "importPath": "langchain_core.documents", "description": "langchain_core.documents", "isExtraImport": true, "detail": "langchain_core.documents", "documentation": {}}, {"label": "Document", "importPath": "langchain_core.documents", "description": "langchain_core.documents", "isExtraImport": true, "detail": "langchain_core.documents", "documentation": {}}, {"label": "Document", "importPath": "langchain_core.documents", "description": "langchain_core.documents", "isExtraImport": true, "detail": "langchain_core.documents", "documentation": {}}, {"label": "numpy", "kind": 6, "isExtraImport": true, "importPath": "numpy", "description": "numpy", "detail": "numpy", "documentation": {}}, {"label": "FAISS", "importPath": "langchain_community.vectorstores", "description": "langchain_community.vectorstores", "isExtraImport": true, "detail": "langchain_community.vectorstores", "documentation": {}}, {"label": "PGVector", "importPath": "langchain_community.vectorstores", "description": "langchain_community.vectorstores", "isExtraImport": true, "detail": "langchain_community.vectorstores", "documentation": {}}, {"label": "PGVector", "importPath": "langchain_community.vectorstores", "description": "langchain_community.vectorstores", "isExtraImport": true, "detail": "langchain_community.vectorstores", "documentation": {}}, {"label": "PGVector", "importPath": "langchain_community.vectorstores", "description": "langchain_community.vectorstores", "isExtraImport": true, "detail": "langchain_community.vectorstores", "documentation": {}}, {"label": "PGVector", "importPath": "langchain_community.vectorstores", "description": "langchain_community.vectorstores", "isExtraImport": true, "detail": "langchain_community.vectorstores", "documentation": {}}, {"label": "QdrantClient", "importPath": "qdrant_client", "description": "qdrant_client", "isExtraImport": true, "detail": "qdrant_client", "documentation": {}}, {"label": "models", "importPath": "qdrant_client.http", "description": "qdrant_client.http", "isExtraImport": true, "detail": "qdrant_client.http", "documentation": {}}, {"label": "Filter", "importPath": "qdrant_client.http.models", "description": "qdrant_client.http.models", "isExtraImport": true, "detail": "qdrant_client.http.models", "documentation": {}}, {"label": "FieldCondition", "importPath": "qdrant_client.http.models", "description": "qdrant_client.http.models", "isExtraImport": true, "detail": "qdrant_client.http.models", "documentation": {}}, {"label": "MatchValue", "importPath": "qdrant_client.http.models", "description": "qdrant_client.http.models", "isExtraImport": true, "detail": "qdrant_client.http.models", "documentation": {}}, {"label": "Qdrant", "importPath": "langchain_qdrant", "description": "langchain_qdrant", "isExtraImport": true, "detail": "langchain_qdrant", "documentation": {}}, {"label": "DistanceStrategy", "importPath": "langchain_community.vectorstores.pgvector", "description": "langchain_community.vectorstores.pgvector", "isExtraImport": true, "detail": "langchain_community.vectorstores.pgvector", "documentation": {}}, {"label": "DistanceStrategy", "importPath": "langchain_community.vectorstores.pgvector", "description": "langchain_community.vectorstores.pgvector", "isExtraImport": true, "detail": "langchain_community.vectorstores.pgvector", "documentation": {}}, {"label": "DistanceStrategy", "importPath": "langchain_community.vectorstores.pgvector", "description": "langchain_community.vectorstores.pgvector", "isExtraImport": true, "detail": "langchain_community.vectorstores.pgvector", "documentation": {}}, {"label": "DistanceStrategy", "importPath": "langchain_community.vectorstores.pgvector", "description": "langchain_community.vectorstores.pgvector", "isExtraImport": true, "detail": "langchain_community.vectorstores.pgvector", "documentation": {}}, {"label": "weaviate", "kind": 6, "isExtraImport": true, "importPath": "weaviate", "description": "weaviate", "detail": "weaviate", "documentation": {}}, {"label": "EmbeddedOptions", "importPath": "weaviate.embedded", "description": "weaviate.embedded", "isExtraImport": true, "detail": "weaviate.embedded", "documentation": {}}, {"label": "WeaviateVectorStore", "importPath": "langchain_weaviate", "description": "langchain_weaviate", "isExtraImport": true, "detail": "langchain_weaviate", "documentation": {}}, {"label": "UnstructuredFileLoader", "importPath": "langchain_community.document_loaders", "description": "langchain_community.document_loaders", "isExtraImport": true, "detail": "langchain_community.document_loaders", "documentation": {}}, {"label": "PyPD<PERSON><PERSON>der", "importPath": "langchain_community.document_loaders", "description": "langchain_community.document_loaders", "isExtraImport": true, "detail": "langchain_community.document_loaders", "documentation": {}}, {"label": "TextLoader", "importPath": "langchain_community.document_loaders", "description": "langchain_community.document_loaders", "isExtraImport": true, "detail": "langchain_community.document_loaders", "documentation": {}}, {"label": "CSVLoader", "importPath": "langchain_community.document_loaders", "description": "langchain_community.document_loaders", "isExtraImport": true, "detail": "langchain_community.document_loaders", "documentation": {}}, {"label": "JSONLoader", "importPath": "langchain_community.document_loaders", "description": "langchain_community.document_loaders", "isExtraImport": true, "detail": "langchain_community.document_loaders", "documentation": {}}, {"label": "UnstructuredExcelLoader", "importPath": "langchain_community.document_loaders", "description": "langchain_community.document_loaders", "isExtraImport": true, "detail": "langchain_community.document_loaders", "documentation": {}}, {"label": "UnstructuredPowerPointLoader", "importPath": "langchain_community.document_loaders", "description": "langchain_community.document_loaders", "isExtraImport": true, "detail": "langchain_community.document_loaders", "documentation": {}}, {"label": "UnstructuredWordDocumentLoader", "importPath": "langchain_community.document_loaders", "description": "langchain_community.document_loaders", "isExtraImport": true, "detail": "langchain_community.document_loaders", "documentation": {}}, {"label": "UnstructuredHTMLLoader", "importPath": "langchain_community.document_loaders", "description": "langchain_community.document_loaders", "isExtraImport": true, "detail": "langchain_community.document_loaders", "documentation": {}}, {"label": "UnstructuredMarkdownLoader", "importPath": "langchain_community.document_loaders", "description": "langchain_community.document_loaders", "isExtraImport": true, "detail": "langchain_community.document_loaders", "documentation": {}}, {"label": "RecursiveCharacterTextSplitter", "importPath": "langchain_text_splitters", "description": "langchain_text_splitters", "isExtraImport": true, "detail": "langchain_text_splitters", "documentation": {}}, {"label": "Hugging<PERSON><PERSON><PERSON>mbe<PERSON><PERSON>", "importPath": "langchain_huggingface", "description": "langchain_huggingface", "isExtraImport": true, "detail": "langchain_huggingface", "documentation": {}}, {"label": "Embeddings", "importPath": "langchain_core.embeddings", "description": "langchain_core.embeddings", "isExtraImport": true, "detail": "langchain_core.embeddings", "documentation": {}}, {"label": "Embeddings", "importPath": "langchain_core.embeddings", "description": "langchain_core.embeddings", "isExtraImport": true, "detail": "langchain_core.embeddings", "documentation": {}}, {"label": "Embeddings", "importPath": "langchain_core.embeddings", "description": "langchain_core.embeddings", "isExtraImport": true, "detail": "langchain_core.embeddings", "documentation": {}}, {"label": "RAGService", "importPath": "backend.plugins.rag.rag_service", "description": "backend.plugins.rag.rag_service", "isExtraImport": true, "detail": "backend.plugins.rag.rag_service", "documentation": {}}, {"label": "RankingStrategy", "importPath": "backend.plugins.rag.reranker", "description": "backend.plugins.rag.reranker", "isExtraImport": true, "detail": "backend.plugins.rag.reranker", "documentation": {}}, {"label": "tempfile", "kind": 6, "isExtraImport": true, "importPath": "tempfile", "description": "tempfile", "detail": "tempfile", "documentation": {}}, {"label": "CrossEncoderReranker", "importPath": "langchain.retrievers.document_compressors", "description": "langchain.retrievers.document_compressors", "isExtraImport": true, "detail": "langchain.retrievers.document_compressors", "documentation": {}}, {"label": "HuggingFaceCrossEncoder", "importPath": "langchain_community.cross_encoders", "description": "langchain_community.cross_encoders", "isExtraImport": true, "detail": "langchain_community.cross_encoders", "documentation": {}}, {"label": "unicodedata", "kind": 6, "isExtraImport": true, "importPath": "unicodedata", "description": "unicodedata", "detail": "unicodedata", "documentation": {}}, {"label": "string", "kind": 6, "isExtraImport": true, "importPath": "string", "description": "string", "detail": "string", "documentation": {}}, {"label": "functools", "kind": 6, "isExtraImport": true, "importPath": "functools", "description": "functools", "detail": "functools", "documentation": {}}, {"label": "importlib", "kind": 6, "isExtraImport": true, "importPath": "importlib", "description": "importlib", "detail": "importlib", "documentation": {}}, {"label": "pkgu<PERSON>", "kind": 6, "isExtraImport": true, "importPath": "pkgu<PERSON>", "description": "pkgu<PERSON>", "detail": "pkgu<PERSON>", "documentation": {}}, {"label": "inspect", "kind": 6, "isExtraImport": true, "importPath": "inspect", "description": "inspect", "detail": "inspect", "documentation": {}}, {"label": "markdown", "kind": 6, "isExtraImport": true, "importPath": "markdown", "description": "markdown", "detail": "markdown", "documentation": {}}, {"label": "Path", "importPath": "pathlib", "description": "pathlib", "isExtraImport": true, "detail": "pathlib", "documentation": {}}, {"label": "Path", "importPath": "pathlib", "description": "pathlib", "isExtraImport": true, "detail": "pathlib", "documentation": {}}, {"label": "HTMLResponse", "importPath": "fastapi.responses", "description": "fastapi.responses", "isExtraImport": true, "detail": "fastapi.responses", "documentation": {}}, {"label": "StreamingResponse", "importPath": "fastapi.responses", "description": "fastapi.responses", "isExtraImport": true, "detail": "fastapi.responses", "documentation": {}}, {"label": "StreamingResponse", "importPath": "fastapi.responses", "description": "fastapi.responses", "isExtraImport": true, "detail": "fastapi.responses", "documentation": {}}, {"label": "StreamingResponse", "importPath": "fastapi.responses", "description": "fastapi.responses", "isExtraImport": true, "detail": "fastapi.responses", "documentation": {}}, {"label": "JSONResponse", "importPath": "fastapi.responses", "description": "fastapi.responses", "isExtraImport": true, "detail": "fastapi.responses", "documentation": {}}, {"label": "JSONResponse", "importPath": "fastapi.responses", "description": "fastapi.responses", "isExtraImport": true, "detail": "fastapi.responses", "documentation": {}}, {"label": "TypedDict", "importPath": "typing_extensions", "description": "typing_extensions", "isExtraImport": true, "detail": "typing_extensions", "documentation": {}}, {"label": "psutil", "kind": 6, "isExtraImport": true, "importPath": "psutil", "description": "psutil", "detail": "psutil", "documentation": {}}, {"label": "platform", "kind": 6, "isExtraImport": true, "importPath": "platform", "description": "platform", "detail": "platform", "documentation": {}}, {"label": "time", "kind": 6, "isExtraImport": true, "importPath": "time", "description": "time", "detail": "time", "documentation": {}}, {"label": "subprocess", "kind": 6, "isExtraImport": true, "importPath": "subprocess", "description": "subprocess", "detail": "subprocess", "documentation": {}}, {"label": "traceback", "kind": 6, "isExtraImport": true, "importPath": "traceback", "description": "traceback", "detail": "traceback", "documentation": {}}, {"label": "NOT_GIVEN", "importPath": "openai._types", "description": "openai._types", "isExtraImport": true, "detail": "openai._types", "documentation": {}}, {"label": "CompletionChoice", "importPath": "openai.types", "description": "openai.types", "isExtraImport": true, "detail": "openai.types", "documentation": {}}, {"label": "CompletionUsage", "importPath": "openai.types", "description": "openai.types", "isExtraImport": true, "detail": "openai.types", "documentation": {}}, {"label": "shutil", "kind": 6, "isExtraImport": true, "importPath": "shutil", "description": "shutil", "detail": "shutil", "documentation": {}}, {"label": "redis", "kind": 6, "isExtraImport": true, "importPath": "redis", "description": "redis", "detail": "redis", "documentation": {}}, {"label": "socket", "kind": 6, "isExtraImport": true, "importPath": "socket", "description": "socket", "detail": "socket", "documentation": {}}, {"label": "Celery", "importPath": "celery", "description": "celery", "isExtraImport": true, "detail": "celery", "documentation": {}}, {"label": "vecs", "kind": 6, "isExtraImport": true, "importPath": "vecs", "description": "vecs", "detail": "vecs", "documentation": {}}, {"label": "psycopg2", "kind": 6, "isExtraImport": true, "importPath": "psycopg2", "description": "psycopg2", "detail": "psycopg2", "documentation": {}}, {"label": "pytest", "kind": 6, "isExtraImport": true, "importPath": "pytest", "description": "pytest", "detail": "pytest", "documentation": {}}, {"label": "TestClient", "importPath": "fastapi.testclient", "description": "fastapi.testclient", "isExtraImport": true, "detail": "fastapi.testclient", "documentation": {}}, {"label": "TestClient", "importPath": "fastapi.testclient", "description": "fastapi.testclient", "isExtraImport": true, "detail": "fastapi.testclient", "documentation": {}}, {"label": "app", "importPath": "backend.main", "description": "backend.main", "isExtraImport": true, "detail": "backend.main", "documentation": {}}, {"label": "app", "importPath": "backend.main", "description": "backend.main", "isExtraImport": true, "detail": "backend.main", "documentation": {}}, {"label": "settings", "importPath": "backend.config", "description": "backend.config", "isExtraImport": true, "detail": "backend.config", "documentation": {}}, {"label": "settings", "importPath": "backend.config", "description": "backend.config", "isExtraImport": true, "detail": "backend.config", "documentation": {}}, {"label": "settings", "importPath": "backend.config", "description": "backend.config", "isExtraImport": true, "detail": "backend.config", "documentation": {}}, {"label": "EmbeddingModel", "importPath": "backend.plugins.rag.embeddings", "description": "backend.plugins.rag.embeddings", "isExtraImport": true, "detail": "backend.plugins.rag.embeddings", "documentation": {}}, {"label": "csv", "kind": 6, "isExtraImport": true, "importPath": "csv", "description": "csv", "detail": "csv", "documentation": {}}, {"label": "BaseSettings", "importPath": "pydantic_settings", "description": "pydantic_settings", "isExtraImport": true, "detail": "pydantic_settings", "documentation": {}}, {"label": "CORSMiddleware", "importPath": "fastapi.middleware.cors", "description": "fastapi.middleware.cors", "isExtraImport": true, "detail": "fastapi.middleware.cors", "documentation": {}}, {"label": "RequestValidationError", "importPath": "fastapi.exceptions", "description": "fastapi.exceptions", "isExtraImport": true, "detail": "fastapi.exceptions", "documentation": {}}, {"label": "asynccontextmanager", "importPath": "contextlib", "description": "contextlib", "isExtraImport": true, "detail": "contextlib", "documentation": {}}, {"label": "signal", "kind": 6, "isExtraImport": true, "importPath": "signal", "description": "signal", "detail": "signal", "documentation": {}}, {"label": "atexit", "kind": 6, "isExtraImport": true, "importPath": "atexit", "description": "atexit", "detail": "atexit", "documentation": {}}, {"label": "u<PERSON><PERSON>", "kind": 6, "isExtraImport": true, "importPath": "u<PERSON><PERSON>", "description": "u<PERSON><PERSON>", "detail": "u<PERSON><PERSON>", "documentation": {}}, {"label": "get_client", "importPath": "backend.inference", "description": "backend.inference", "isExtraImport": true, "detail": "backend.inference", "documentation": {}}, {"label": "BackendType", "importPath": "backend.inference", "description": "backend.inference", "isExtraImport": true, "detail": "backend.inference", "documentation": {}}, {"label": "get_client", "importPath": "backend.inference", "description": "backend.inference", "isExtraImport": true, "detail": "backend.inference", "documentation": {}}, {"label": "get_client", "importPath": "backend.inference", "description": "backend.inference", "isExtraImport": true, "detail": "backend.inference", "documentation": {}}, {"label": "get_client", "importPath": "backend.inference", "description": "backend.inference", "isExtraImport": true, "detail": "backend.inference", "documentation": {}}, {"label": "get_client", "importPath": "backend.inference", "description": "backend.inference", "isExtraImport": true, "detail": "backend.inference", "documentation": {}}, {"label": "importlib.util", "kind": 6, "isExtraImport": true, "importPath": "importlib.util", "description": "importlib.util", "detail": "importlib.util", "documentation": {}}, {"label": "PreprocessorPlugin", "importPath": "backend.plugins.base", "description": "backend.plugins.base", "isExtraImport": true, "detail": "backend.plugins.base", "documentation": {}}, {"label": "Message", "importPath": "backend.plugins.types", "description": "backend.plugins.types", "isExtraImport": true, "detail": "backend.plugins.types", "documentation": {}}, {"label": "PluginResult", "importPath": "backend.plugins.types", "description": "backend.plugins.types", "isExtraImport": true, "detail": "backend.plugins.types", "documentation": {}}, {"label": "plugin_manager", "importPath": "backend.plugins.manager", "description": "backend.plugins.manager", "isExtraImport": true, "detail": "backend.plugins.manager", "documentation": {}}, {"label": "VectorStoreFactory", "importPath": "backend.plugins.rag.vector_factory", "description": "backend.plugins.rag.vector_factory", "isExtraImport": true, "detail": "backend.plugins.rag.vector_factory", "documentation": {}}, {"label": "DocumentLoader", "importPath": "backend.plugins.rag.document_loader", "description": "backend.plugins.rag.document_loader", "isExtraImport": true, "detail": "backend.plugins.rag.document_loader", "documentation": {}}, {"label": "Flask", "importPath": "flask", "description": "flask", "isExtraImport": true, "detail": "flask", "documentation": {}}, {"label": "render_template", "importPath": "flask", "description": "flask", "isExtraImport": true, "detail": "flask", "documentation": {}}, {"label": "request", "importPath": "flask", "description": "flask", "isExtraImport": true, "detail": "flask", "documentation": {}}, {"label": "jsonify", "importPath": "flask", "description": "flask", "isExtraImport": true, "detail": "flask", "documentation": {}}, {"label": "Response", "importPath": "flask", "description": "flask", "isExtraImport": true, "detail": "flask", "documentation": {}}, {"label": "stream_with_context", "importPath": "flask", "description": "flask", "isExtraImport": true, "detail": "flask", "documentation": {}}, {"label": "Flask", "importPath": "flask", "description": "flask", "isExtraImport": true, "detail": "flask", "documentation": {}}, {"label": "render_template", "importPath": "flask", "description": "flask", "isExtraImport": true, "detail": "flask", "documentation": {}}, {"label": "request", "importPath": "flask", "description": "flask", "isExtraImport": true, "detail": "flask", "documentation": {}}, {"label": "jsonify", "importPath": "flask", "description": "flask", "isExtraImport": true, "detail": "flask", "documentation": {}}, {"label": "Response", "importPath": "flask", "description": "flask", "isExtraImport": true, "detail": "flask", "documentation": {}}, {"label": "stream_with_context", "importPath": "flask", "description": "flask", "isExtraImport": true, "detail": "flask", "documentation": {}}, {"label": "main", "importPath": "backend.db.db_cli", "description": "backend.db.db_cli", "isExtraImport": true, "detail": "backend.db.db_cli", "documentation": {}}, {"label": "get_current_user", "kind": 2, "importPath": "backend.auth.auth_manager", "description": "backend.auth.auth_manager", "peekOfCode": "def get_current_user(user: Dict[str, Any] = Depends(auth_manager.get_current_user())):\n    \"\"\"\n    現在のユーザー情報を取得します。\n    認証モードに応じて、自前アカウントまたは企業SSOからユーザー情報を取得します。\n    \"\"\"\n    try:\n        # 認証モードに基づいて処理を分岐\n        if settings.is_enterprise_mode:\n            # 企業SSOの場合、ロールマッピングを適用\n            enterprise_roles = user.get(\"realm_access\", {}).get(\"roles\", [])", "detail": "backend.auth.auth_manager", "documentation": {}}, {"label": "get_current_admin", "kind": 2, "importPath": "backend.auth.auth_manager", "description": "backend.auth.auth_manager", "peekOfCode": "def get_current_admin(user: UserInfo = Depends(get_current_user)):\n    \"\"\"\n    管理者権限を持つユーザーを取得します。\n    管理者権限がない場合は403エラーを返します。\n    \"\"\"\n    if not user.is_admin:\n        raise HTTPException(\n            status_code=status.HTTP_403_FORBIDDEN,\n            detail=\"この操作には管理者権限が必要です\"\n        )", "detail": "backend.auth.auth_manager", "documentation": {}}, {"label": "get_current_sales", "kind": 2, "importPath": "backend.auth.auth_manager", "description": "backend.auth.auth_manager", "peekOfCode": "def get_current_sales(user: UserInfo = Depends(get_current_user)):\n    \"\"\"\n    営業権限を持つユーザーを取得します。\n    営業権限がない場合は403エラーを返します。\n    管理者は営業権限も持つとみなされます。\n    \"\"\"\n    if not (user.is_sales or user.is_admin):\n        raise HTTPException(\n            status_code=status.HTTP_403_FORBIDDEN,\n            detail=\"この操作には営業権限が必要です\"", "detail": "backend.auth.auth_manager", "documentation": {}}, {"label": "get_current_regular_user", "kind": 2, "importPath": "backend.auth.auth_manager", "description": "backend.auth.auth_manager", "peekOfCode": "def get_current_regular_user(user: UserInfo = Depends(get_current_user)):\n    \"\"\"\n    一般ユーザー権限を持つユーザーを取得します。\n    一般ユーザー権限がない場合は403エラーを返します。\n    管理者と営業担当者は一般ユーザー権限も持つとみなされます。\n    \"\"\"\n    if not (user.is_regular_user or user.is_admin or user.is_sales):\n        raise HTTPException(\n            status_code=status.HTTP_403_FORBIDDEN,\n            detail=\"この操作には一般ユーザー権限が必要です\"", "detail": "backend.auth.auth_manager", "documentation": {}}, {"label": "logger", "kind": 5, "importPath": "backend.auth.auth_manager", "description": "backend.auth.auth_manager", "peekOfCode": "logger = logging.getLogger(__name__)\n# Keycloakインスタンスを初期化\nauth_manager = FastAPIKeycloak(\n    server_url=settings.KEYCLOAK_SERVER_URL,\n    client_id=settings.KEYCLOAK_CLIENT_ID,\n    client_secret=settings.KEYCLOAK_CLIENT_SECRET,\n    realm=settings.KEYCLOAK_REALM,\n    callback_uri=settings.KEYCLOAK_CALLBACK_URI,\n    admin_client_secret=settings.KEYCLOAK_CLIENT_SECRET  # 管理者クライアントシークレット\n)", "detail": "backend.auth.auth_manager", "documentation": {}}, {"label": "auth_manager", "kind": 5, "importPath": "backend.auth.auth_manager", "description": "backend.auth.auth_manager", "peekOfCode": "auth_manager = FastAPIKeycloak(\n    server_url=settings.KEY<PERSON>OAK_SERVER_URL,\n    client_id=settings.KEYCLOAK_CLIENT_ID,\n    client_secret=settings.KEYCLOAK_CLIENT_SECRET,\n    realm=settings.KEYCLOAK_REALM,\n    callback_uri=settings.KEYCLOAK_CALLBACK_URI,\n    admin_client_secret=settings.KEYCLOAK_CLIENT_SECRET  # 管理者クライアントシークレット\n)\n# 現在のユーザーを取得する依存関数\ndef get_current_user(user: Dict[str, Any] = Depends(auth_manager.get_current_user())):", "detail": "backend.auth.auth_manager", "documentation": {}}, {"label": "LocalAuthManager", "kind": 6, "importPath": "backend.auth.local_auth", "description": "backend.auth.local_auth", "peekOfCode": "class LocalAuthManager:\n    \"\"\"\n    ローカルテストモード用の認証マネージャー\n    実際の認証サーバーを使わずに、設定ファイルに基づいて認証を行います。\n    \"\"\"\n    def __init__(self):\n        \"\"\"\n        ローカルテストモード用の認証マネージャーを初期化します。\n        \"\"\"\n        self.test_user = settings.LOCAL_TEST_USER", "detail": "backend.auth.local_auth", "documentation": {}}, {"label": "get_current_admin", "kind": 2, "importPath": "backend.auth.local_auth", "description": "backend.auth.local_auth", "peekOfCode": "def get_current_admin(user: UserInfo = Depends(get_current_user)):\n    \"\"\"\n    管理者権限を持つユーザーを取得します。\n    管理者権限がない場合は403エラーを返します。\n    \"\"\"\n    if not user.is_admin:\n        raise HTTPException(\n            status_code=status.HTTP_403_FORBIDDEN,\n            detail=\"この操作には管理者権限が必要です\"\n        )", "detail": "backend.auth.local_auth", "documentation": {}}, {"label": "get_current_sales", "kind": 2, "importPath": "backend.auth.local_auth", "description": "backend.auth.local_auth", "peekOfCode": "def get_current_sales(user: UserInfo = Depends(get_current_user)):\n    \"\"\"\n    営業権限を持つユーザーを取得します。\n    営業権限がない場合は403エラーを返します。\n    管理者は営業権限も持つとみなされます。\n    \"\"\"\n    if not (user.is_sales or user.is_admin):\n        raise HTTPException(\n            status_code=status.HTTP_403_FORBIDDEN,\n            detail=\"この操作には営業権限が必要です\"", "detail": "backend.auth.local_auth", "documentation": {}}, {"label": "get_current_regular_user", "kind": 2, "importPath": "backend.auth.local_auth", "description": "backend.auth.local_auth", "peekOfCode": "def get_current_regular_user(user: UserInfo = Depends(get_current_user)):\n    \"\"\"\n    一般ユーザー権限を持つユーザーを取得します。\n    一般ユーザー権限がない場合は403エラーを返します。\n    管理者と営業担当者は一般ユーザー権限も持つとみなされます。\n    \"\"\"\n    if not (user.is_regular_user or user.is_admin or user.is_sales):\n        raise HTTPException(\n            status_code=status.HTTP_403_FORBIDDEN,\n            detail=\"この操作には一般ユーザー権限が必要です\"", "detail": "backend.auth.local_auth", "documentation": {}}, {"label": "logger", "kind": 5, "importPath": "backend.auth.local_auth", "description": "backend.auth.local_auth", "peekOfCode": "logger = logging.getLogger(__name__)\nclass LocalAuthManager:\n    \"\"\"\n    ローカルテストモード用の認証マネージャー\n    実際の認証サーバーを使わずに、設定ファイルに基づいて認証を行います。\n    \"\"\"\n    def __init__(self):\n        \"\"\"\n        ローカルテストモード用の認証マネージャーを初期化します。\n        \"\"\"", "detail": "backend.auth.local_auth", "documentation": {}}, {"label": "local_auth_manager", "kind": 5, "importPath": "backend.auth.local_auth", "description": "backend.auth.local_auth", "peekOfCode": "local_auth_manager = LocalAuthManager()\n# 現在のユーザーを取得する依存関数\nget_current_user = local_auth_manager.get_current_user()\n# 管理者権限を持つユーザーを取得する依存関数\ndef get_current_admin(user: UserInfo = Depends(get_current_user)):\n    \"\"\"\n    管理者権限を持つユーザーを取得します。\n    管理者権限がない場合は403エラーを返します。\n    \"\"\"\n    if not user.is_admin:", "detail": "backend.auth.local_auth", "documentation": {}}, {"label": "get_current_user", "kind": 5, "importPath": "backend.auth.local_auth", "description": "backend.auth.local_auth", "peekOfCode": "get_current_user = local_auth_manager.get_current_user()\n# 管理者権限を持つユーザーを取得する依存関数\ndef get_current_admin(user: UserInfo = Depends(get_current_user)):\n    \"\"\"\n    管理者権限を持つユーザーを取得します。\n    管理者権限がない場合は403エラーを返します。\n    \"\"\"\n    if not user.is_admin:\n        raise HTTPException(\n            status_code=status.HTTP_403_FORBIDDEN,", "detail": "backend.auth.local_auth", "documentation": {}}, {"label": "UserRole", "kind": 6, "importPath": "backend.auth.models", "description": "backend.auth.models", "peekOfCode": "class UserRole(str, Enum):\n    \"\"\"\n    ユーザーロールを表す列挙型\n    \"\"\"\n    ADMIN = \"admin\"          # 管理者権限\n    USER = \"user\"           # 一般ユーザー権限\n    SALES = \"sales\"         # 営業権限\nclass UserInfo(BaseModel):\n    \"\"\"\n    ユーザー情報を表すモデル", "detail": "backend.auth.models", "documentation": {}}, {"label": "UserInfo", "kind": 6, "importPath": "backend.auth.models", "description": "backend.auth.models", "peekOfCode": "class UserInfo(BaseModel):\n    \"\"\"\n    ユーザー情報を表すモデル\n    \"\"\"\n    id: str\n    username: str\n    email: Optional[str] = None\n    roles: List[str] = []\n    @property\n    def is_admin(self) -> bool:", "detail": "backend.auth.models", "documentation": {}}, {"label": "logger", "kind": 5, "importPath": "backend.auth.router", "description": "backend.auth.router", "peekOfCode": "logger = logging.getLogger(__name__)\nrouter = APIRouter(prefix=\"/auth\", tags=[\"Auth APIs\"])\*************(\"/login\")\nasync def login(form_data: OAuth2PasswordRequestForm = Depends()):\n    \"\"\"\n    ユーザー認証を行い、アクセストークンを返します。\n    \"\"\"\n    # 自前アカウントテストモードの場合\n    if settings.is_self_hosted_test_mode:\n        logger.info(f\"自前アカウントテストモードでログイン: {form_data.username}\")", "detail": "backend.auth.router", "documentation": {}}, {"label": "router", "kind": 5, "importPath": "backend.auth.router", "description": "backend.auth.router", "peekOfCode": "router = APIRouter(prefix=\"/auth\", tags=[\"Auth APIs\"])\*************(\"/login\")\nasync def login(form_data: OAuth2PasswordRequestForm = Depends()):\n    \"\"\"\n    ユーザー認証を行い、アクセストークンを返します。\n    \"\"\"\n    # 自前アカウントテストモードの場合\n    if settings.is_self_hosted_test_mode:\n        logger.info(f\"自前アカウントテストモードでログイン: {form_data.username}\")\n        # 設定ファイルのユーザー情報を返す", "detail": "backend.auth.router", "documentation": {}}, {"label": "User", "kind": 6, "importPath": "backend.db.database", "description": "backend.db.database", "peekOfCode": "class User(Base):\n    \"\"\"ユーザーモデル\"\"\"\n    __tablename__ = \"users\"\n    id = Column(Integer, primary_key=True)\n    username = Column(String, unique=True)\n    hashed_password = Column(String)\n    role = Column(String)\n    chat_sessions = relationship(\"ChatSession\", back_populates=\"user\")\nclass ChatSession(Base):\n    \"\"\"チャットセッションモデル\"\"\"", "detail": "backend.db.database", "documentation": {}}, {"label": "ChatSession", "kind": 6, "importPath": "backend.db.database", "description": "backend.db.database", "peekOfCode": "class ChatSession(Base):\n    \"\"\"チャットセッションモデル\"\"\"\n    __tablename__ = \"chat_sessions\"\n    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))\n    user_id = Column(Integer, ForeignKey(\"users.id\"))\n    title = Column(String)\n    created_at = Column(DateTime, default=lambda: datetime.now())\n    user = relationship(\"User\", back_populates=\"chat_sessions\")\n    messages = relationship(\"ChatMessage\", back_populates=\"session\")\nclass ChatMessage(Base):", "detail": "backend.db.database", "documentation": {}}, {"label": "ChatMessage", "kind": 6, "importPath": "backend.db.database", "description": "backend.db.database", "peekOfCode": "class ChatMessage(Base):\n    \"\"\"チャットメッセージモデル\"\"\"\n    __tablename__ = \"chat_messages\"\n    id = Column(String(36), primary_key=True, default=lambda: str(uuid.uuid4()))\n    session_id = Column(String(36), ForeignKey(\"chat_sessions.id\"), nullable=False)\n    role = Column(String(50), nullable=False)\n    content = Column(Text, nullable=False)\n    created_at = Column(DateTime, default=lambda: datetime.now())\n    session = relationship(\"ChatSession\", back_populates=\"messages\")\n    def __repr__(self):", "detail": "backend.db.database", "documentation": {}}, {"label": "get_database_url", "kind": 2, "importPath": "backend.db.database", "description": "backend.db.database", "peekOfCode": "def get_database_url():\n    \"\"\"\n    設定に基づいてデータベースURLを生成\n    \"\"\"\n    # 直接DATABASE_URLが提供されている場合、優先的に使用\n    if settings.DATABASE_URL:\n        return settings.DATABASE_URL\n    # データベースタイプに基づいてURLを生成\n    db_type = settings.DATABASE_TYPE.lower()\n    if db_type == \"sqlite\":", "detail": "backend.db.database", "documentation": {}}, {"label": "get_database_engine", "kind": 2, "importPath": "backend.db.database", "description": "backend.db.database", "peekOfCode": "def get_database_engine(url):\n    \"\"\"\n    データベースURLに基づいてエンジンを作成\n    \"\"\"\n    if url.startswith(\"sqlite\"):\n        return create_engine(url, connect_args={\"check_same_thread\": False})\n    else:\n        return create_engine(url)\n# データベースURLとエンジンを取得\nSQLALCHEMY_DATABASE_URL = get_database_url()", "detail": "backend.db.database", "documentation": {}}, {"label": "get_db", "kind": 2, "importPath": "backend.db.database", "description": "backend.db.database", "peekOfCode": "def get_db():\n    \"\"\"\n    データベースセッションを取得するためのジェネレータ関数\n    FastAPIのDependsで使用されます\n    \"\"\"\n    db = SessionLocal()\n    try:\n        yield db\n    finally:\n        db.close()", "detail": "backend.db.database", "documentation": {}}, {"label": "logger", "kind": 5, "importPath": "backend.db.database", "description": "backend.db.database", "peekOfCode": "logger = logging.getLogger(__name__)\ndef get_database_url():\n    \"\"\"\n    設定に基づいてデータベースURLを生成\n    \"\"\"\n    # 直接DATABASE_URLが提供されている場合、優先的に使用\n    if settings.DATABASE_URL:\n        return settings.DATABASE_URL\n    # データベースタイプに基づいてURLを生成\n    db_type = settings.DATABASE_TYPE.lower()", "detail": "backend.db.database", "documentation": {}}, {"label": "SQLALCHEMY_DATABASE_URL", "kind": 5, "importPath": "backend.db.database", "description": "backend.db.database", "peekOfCode": "SQLALCHEMY_DATABASE_URL = get_database_url()\nengine = get_database_engine(SQLALCHEMY_DATABASE_URL)\n# セッションファクトリを作成\nSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)\nBase = declarative_base()\n# 使用するデータベースタイプを記録\nlogger.info(f\"使用するデータベース: {SQLALCHEMY_DATABASE_URL}\")\n# データベースモデル定義\nclass User(Base):\n    \"\"\"ユーザーモデル\"\"\"", "detail": "backend.db.database", "documentation": {}}, {"label": "engine", "kind": 5, "importPath": "backend.db.database", "description": "backend.db.database", "peekOfCode": "engine = get_database_engine(SQLALCHEMY_DATABASE_URL)\n# セッションファクトリを作成\nSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)\nBase = declarative_base()\n# 使用するデータベースタイプを記録\nlogger.info(f\"使用するデータベース: {SQLALCHEMY_DATABASE_URL}\")\n# データベースモデル定義\nclass User(Base):\n    \"\"\"ユーザーモデル\"\"\"\n    __tablename__ = \"users\"", "detail": "backend.db.database", "documentation": {}}, {"label": "SessionLocal", "kind": 5, "importPath": "backend.db.database", "description": "backend.db.database", "peekOfCode": "SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)\nBase = declarative_base()\n# 使用するデータベースタイプを記録\nlogger.info(f\"使用するデータベース: {SQLALCHEMY_DATABASE_URL}\")\n# データベースモデル定義\nclass User(Base):\n    \"\"\"ユーザーモデル\"\"\"\n    __tablename__ = \"users\"\n    id = Column(Integer, primary_key=True)\n    username = Column(String, unique=True)", "detail": "backend.db.database", "documentation": {}}, {"label": "Base", "kind": 5, "importPath": "backend.db.database", "description": "backend.db.database", "peekOfCode": "Base = declarative_base()\n# 使用するデータベースタイプを記録\nlogger.info(f\"使用するデータベース: {SQLALCHEMY_DATABASE_URL}\")\n# データベースモデル定義\nclass User(Base):\n    \"\"\"ユーザーモデル\"\"\"\n    __tablename__ = \"users\"\n    id = Column(Integer, primary_key=True)\n    username = Column(String, unique=True)\n    hashed_password = Column(String)", "detail": "backend.db.database", "documentation": {}}, {"label": "main", "kind": 2, "importPath": "backend.db.db_cli", "description": "backend.db.db_cli", "peekOfCode": "def main():\n    \"\"\"\n    メイン関数\n    \"\"\"\n    parser = argparse.ArgumentParser(description=\"データベース管理ツール\")\n    parser.add_argument(\"action\", choices=[\"init\", \"export\", \"import\"], help=\"操作タイプ: init (初期化), export (エクスポート), import (インポート)\")\n    parser.add_argument(\"--file\", default=\"database_export.json\", help=\"インポート/エクスポートファイルのパス\")\n    args = parser.parse_args()\n    if args.action == \"init\":\n        # データベースを初期化", "detail": "backend.db.db_cli", "documentation": {}}, {"label": "logger", "kind": 5, "importPath": "backend.db.db_cli", "description": "backend.db.db_cli", "peekOfCode": "logger = logging.getLogger(__name__)\nfrom .db_manager import init_database, export_data, import_data\ndef main():\n    \"\"\"\n    メイン関数\n    \"\"\"\n    parser = argparse.ArgumentParser(description=\"データベース管理ツール\")\n    parser.add_argument(\"action\", choices=[\"init\", \"export\", \"import\"], help=\"操作タイプ: init (初期化), export (エクスポート), import (インポート)\")\n    parser.add_argument(\"--file\", default=\"database_export.json\", help=\"インポート/エクスポートファイルのパス\")\n    args = parser.parse_args()", "detail": "backend.db.db_cli", "documentation": {}}, {"label": "is_database_initialized", "kind": 2, "importPath": "backend.db.db_manager", "description": "backend.db.db_manager", "peekOfCode": "def is_database_initialized():\n    \"\"\"\n    データベースが既に初期化されているかどうかを確認します\n    ユーザーテーブルの存在を確認します\n    \"\"\"\n    try:\n        # データベースに接続してユーザーテーブルの存在を確認\n        inspector = MetaData()\n        inspector.reflect(bind=engine, only=['users'])\n        return 'users' in inspector.tables", "detail": "backend.db.db_manager", "documentation": {}}, {"label": "init_database", "kind": 2, "importPath": "backend.db.db_manager", "description": "backend.db.db_manager", "peekOfCode": "def init_database():\n    \"\"\"\n    データベーステーブルを初期化し、必要に応じてデモユーザーを作成します\n    \"\"\"\n    # 強制初期化フラグとテストモードを確認\n    force_init = settings.FORCE_DB_INIT\n    is_test_mode = settings.AUTH_MODE == \"self_hosted_test\"\n    # データベースが既に初期化されているか確認\n    if is_database_initialized() and not force_init:\n        # テストモードの場合は、強制初期化が有効でなくてもデータベースを再作成するか確認", "detail": "backend.db.db_manager", "documentation": {}}, {"label": "export_data", "kind": 2, "importPath": "backend.db.db_manager", "description": "backend.db.db_manager", "peekOfCode": "def export_data(source_url, output_file):\n    \"\"\"\n    ソースデータベースからデータをJSONファイルにエクスポート\n    \"\"\"\n    # ソースデータベースエンジンとセッションを作成\n    source_engine = get_database_engine(source_url)\n    SourceSession = sessionmaker(bind=source_engine)\n    source_session = SourceSession()\n    # メタデータを定義\n    metadata = MetaData()", "detail": "backend.db.db_manager", "documentation": {}}, {"label": "import_data", "kind": 2, "importPath": "backend.db.db_manager", "description": "backend.db.db_manager", "peekOfCode": "def import_data(target_url, input_file):\n    \"\"\"\n    JSONファイルからターゲットデータベースにデータをインポート\n    \"\"\"\n    # ターゲットデータベースエンジンとセッションを作成\n    target_engine = get_database_engine(target_url)\n    TargetSession = sessionmaker(bind=target_engine)\n    target_session = TargetSession()\n    # メタデータを定義\n    metadata = MetaData()", "detail": "backend.db.db_manager", "documentation": {}}, {"label": "parse_args", "kind": 2, "importPath": "backend.db.db_manager", "description": "backend.db.db_manager", "peekOfCode": "def parse_args():\n    \"\"\"\n    コマンドライン引数を解析\n    \"\"\"\n    parser = argparse.ArgumentParser(description=\"データベース管理ツール\")\n    parser.add_argument(\"action\", choices=[\"init\", \"export\", \"import\"], help=\"操作タイプ: init (初期化), export (エクスポート), import (インポート)\")\n    # ソースデータベースパラメータ\n    parser.add_argument(\"--source-type\", choices=[\"sqlite\", \"postgresql\", \"mysql\"], default=settings.DATABASE_TYPE, help=\"ソースデータベースの種類\")\n    parser.add_argument(\"--source-host\", default=settings.DATABASE_HOST, help=\"ソースデータベースのホスト\")\n    parser.add_argument(\"--source-port\", type=int, default=settings.DATABASE_PORT, help=\"ソースデータベースのポート\")", "detail": "backend.db.db_manager", "documentation": {}}, {"label": "main", "kind": 2, "importPath": "backend.db.db_manager", "description": "backend.db.db_manager", "peekOfCode": "def main():\n    \"\"\"\n    メイン関数\n    \"\"\"\n    args = parse_args()\n    if args.action == \"init\":\n        # データベースを初期化\n        init_database()\n        print(\"データベースの初期化が完了しました。\")\n    elif args.action == \"export\":", "detail": "backend.db.db_manager", "documentation": {}}, {"label": "logger", "kind": 5, "importPath": "backend.db.db_manager", "description": "backend.db.db_manager", "peekOfCode": "logger = logging.getLogger(__name__)\ndef is_database_initialized():\n    \"\"\"\n    データベースが既に初期化されているかどうかを確認します\n    ユーザーテーブルの存在を確認します\n    \"\"\"\n    try:\n        # データベースに接続してユーザーテーブルの存在を確認\n        inspector = MetaData()\n        inspector.reflect(bind=engine, only=['users'])", "detail": "backend.db.db_manager", "documentation": {}}, {"label": "get_database_url", "kind": 2, "importPath": "backend.db.migrate", "description": "backend.db.migrate", "peekOfCode": "def get_database_url(db_type, db_host, db_port, db_user, db_password, db_name):\n    \"\"\"\n    パラメータに基づいてデータベースURLを生成\n    \"\"\"\n    if db_type == \"sqlite\":\n        return f\"sqlite:///./{db_name}.db\"\n    elif db_type == \"postgresql\":\n        return f\"postgresql://{db_user}:{db_password}@{db_host}:{db_port}/{db_name}\"\n    elif db_type == \"mysql\":\n        return f\"mysql+pymysql://{db_user}:{db_password}@{db_host}:{db_port}/{db_name}\"", "detail": "backend.db.migrate", "documentation": {}}, {"label": "get_engine", "kind": 2, "importPath": "backend.db.migrate", "description": "backend.db.migrate", "peekOfCode": "def get_engine(url):\n    \"\"\"\n    データベースエンジンを作成\n    \"\"\"\n    # データベースエンジンを作成するためにインポートした create_engine を使用\n    if url.startswith(\"sqlite\"):\n        return create_engine(url, connect_args={\"check_same_thread\": False})\n    else:\n        return create_engine(url)\ndef export_data(source_url, output_file):", "detail": "backend.db.migrate", "documentation": {}}, {"label": "export_data", "kind": 2, "importPath": "backend.db.migrate", "description": "backend.db.migrate", "peekOfCode": "def export_data(source_url, output_file):\n    \"\"\"\n    ソースデータベースからデータをJSONファイルにエクスポート\n    \"\"\"\n    # ソースデータベースエンジンとセッションを作成\n    source_engine = get_engine(source_url)\n    SourceSession = sessionmaker(bind=source_engine)\n    source_session = SourceSession()\n    # メタデータを定義\n    metadata = MetaData()", "detail": "backend.db.migrate", "documentation": {}}, {"label": "import_data", "kind": 2, "importPath": "backend.db.migrate", "description": "backend.db.migrate", "peekOfCode": "def import_data(target_url, input_file):\n    \"\"\"\n    JSONファイルからターゲットデータベースにデータをインポート\n    \"\"\"\n    # ターゲットデータベースエンジンとセッションを作成\n    target_engine = get_engine(target_url)\n    TargetSession = sessionmaker(bind=target_engine)\n    target_session = TargetSession()\n    # メタデータを定義\n    metadata = MetaData()", "detail": "backend.db.migrate", "documentation": {}}, {"label": "parse_args", "kind": 2, "importPath": "backend.db.migrate", "description": "backend.db.migrate", "peekOfCode": "def parse_args():\n    \"\"\"\n    コマンドライン引数を解析\n    \"\"\"\n    parser = argparse.ArgumentParser(description=\"データベース移行ツール\")\n    parser.add_argument(\"action\", choices=[\"export\", \"import\"], help=\"操作タイプ: export (エクスポート) または import (インポート)\")\n    # ソースデータベースパラメータ\n    parser.add_argument(\"--source-type\", choices=[\"sqlite\", \"postgresql\", \"mysql\"], default=settings.DATABASE_TYPE, help=\"ソースデータベースの種類\")\n    parser.add_argument(\"--source-host\", default=settings.DATABASE_HOST, help=\"ソースデータベースのホスト\")\n    parser.add_argument(\"--source-port\", type=int, default=settings.DATABASE_PORT, help=\"ソースデータベースのポート\")", "detail": "backend.db.migrate", "documentation": {}}, {"label": "main", "kind": 2, "importPath": "backend.db.migrate", "description": "backend.db.migrate", "peekOfCode": "def main():\n    \"\"\"\n    メイン関数\n    \"\"\"\n    args = parse_args()\n    if args.action == \"export\":\n        # ソースデータベースURLを決定\n        if args.source_url:\n            source_url = args.source_url\n        else:", "detail": "backend.db.migrate", "documentation": {}}, {"label": "logger", "kind": 5, "importPath": "backend.db.migrate", "description": "backend.db.migrate", "peekOfCode": "logger = logging.getLogger(__name__)\ndef get_database_url(db_type, db_host, db_port, db_user, db_password, db_name):\n    \"\"\"\n    パラメータに基づいてデータベースURLを生成\n    \"\"\"\n    if db_type == \"sqlite\":\n        return f\"sqlite:///./{db_name}.db\"\n    elif db_type == \"postgresql\":\n        return f\"postgresql://{db_user}:{db_password}@{db_host}:{db_port}/{db_name}\"\n    elif db_type == \"mysql\":", "detail": "backend.db.migrate", "documentation": {}}, {"label": "get_backend", "kind": 2, "importPath": "backend.inference.backup.backend_selector", "description": "backend.inference.backup.backend_selector", "peekOfCode": "def get_backend(name: str, **kwargs):\n    \"\"\"\n    モデル推論に適切なバックエンドを取得します。\n    引数:\n        name: 使用するバックエンドの名前\n        **kwargs: バックエンドコンストラクタに渡す追加パラメータ\n    戻り値:\n        要求されたバックエンドのインスタンス\n    \"\"\"\n    name = name.lower()", "detail": "backend.inference.backup.backend_selector", "documentation": {}}, {"label": "get_backend_for_model", "kind": 2, "importPath": "backend.inference.backup.backend_utils", "description": "backend.inference.backup.backend_utils", "peekOfCode": "def get_backend_for_model(model: str, backend: Optional[str] = None, **backend_options) -> Any:\n    \"\"\"\n    モデルに適したバックエンドを取得します。\n    この関数は、以下に基づいて使用するバックエンドを決定します:\n    1. 明示的に提供されたバックエンドパラメータ\n    2. 設定内のモデルとバックエンドのマッピング\n    3. 設定からのデフォルトバックエンド\n    引数:\n        model: 使用するモデルの名前\n        backend: オプションで明示的に使用するバックエンド", "detail": "backend.inference.backup.backend_utils", "documentation": {}}, {"label": "BaseLLMBackend", "kind": 6, "importPath": "backend.inference.backup.base_backend", "description": "backend.inference.backup.base_backend", "peekOfCode": "class BaseLLMBackend(ABC):\n    \"\"\"\n    すべてのLLM推論バックエンドの基底クラス。\n    すべてのバックエンド実装はこのクラスを継承する必要があります。\n    \"\"\"\n    @abstractmethod\n    def generate(self, prompt: str, max_tokens: int = 128, **kwargs) -> str:\n        \"\"\"\n        プロンプトに基づいてテキストを生成します。\n        引数:", "detail": "backend.inference.backup.base_backend", "documentation": {}}, {"label": "OllamaBackend", "kind": 6, "importPath": "backend.inference.backup.ollama_backend", "description": "backend.inference.backup.ollama_backend", "peekOfCode": "class OllamaBackend(OpenAICompatibleBackend):\n    \"\"\"\n    Backend for Ollama inference.\n    Supports both text generation and chat completion.\n    \"\"\"\n    def __init__(self,\n                 endpoint: str = \"http://localhost:11434\",\n                 model_name: str = \"llama2\",\n                 **kwargs):\n        \"\"\"", "detail": "backend.inference.backup.ollama_backend", "documentation": {}}, {"label": "OpenAIBackend", "kind": 6, "importPath": "backend.inference.backup.openai_backend", "description": "backend.inference.backup.openai_backend", "peekOfCode": "class OpenAIBackend(BaseLLMBackend):\n    \"\"\"\n    Backend for OpenAI API.\n    Supports both direct OpenAI client and API calls.\n    \"\"\"\n    def __init__(self,\n                 api_key: Optional[str] = None,\n                 model_name: str = \"gpt-3.5-turbo\",\n                 organization: Optional[str] = None,\n                 **kwargs):", "detail": "backend.inference.backup.openai_backend", "documentation": {}}, {"label": "OpenAICompatibleBackend", "kind": 6, "importPath": "backend.inference.backup.openai_compatible_backend", "description": "backend.inference.backup.openai_compatible_backend", "peekOfCode": "class OpenAICompatibleBackend(BaseLLMBackend):\n    \"\"\"\n    OpenAI互換APIを持つバックエンド用の基底クラス。\n    OpenAIクライアントライブラリを直接使用します。\n    \"\"\"\n    def __init__(self,\n                 endpoint: str,\n                 model_name: str,\n                 api_key: str = \"\",\n                 **kwargs):", "detail": "backend.inference.backup.openai_compatible_backend", "documentation": {}}, {"label": "SGLangBackend", "kind": 6, "importPath": "backend.inference.backup.sglang_backend", "description": "backend.inference.backup.sglang_backend", "peekOfCode": "class SGLangBackend(OpenAICompatibleBackend):\n    \"\"\"\n    SGLang推論用のバックエンド。\n    直接SGLang統合とAPIエンドポイントの両方をサポートします。\n    \"\"\"\n    def __init__(self,\n                 endpoint: str = \"http://localhost:8000\",\n                 model_name: str = \"llama3\",\n                 use_direct: bool = False,\n                 model_kwargs: Optional[Dict[str, Any]] = None,", "detail": "backend.inference.backup.sglang_backend", "documentation": {}}, {"label": "get_client", "kind": 2, "importPath": "backend.inference.backup.simple_client", "description": "backend.inference.backup.simple_client", "peekOfCode": "def get_client(backend_type=None, model=None, api_key=None):\n    \"\"\"\n    指定されたバックエンドタイプとモデルに基づいてOpenAIクライアントを取得します。\n    引数:\n        backend_type: バックエンドタイプ（\"ollama\", \"vllm\", \"sglang\", \"x-inference\", \"openai\"）\n                     Noneの場合、settings.DEFAULT_LLM_BACKENDを使用\n        model: モデル名（Noneの場合、モデルに基づいてバックエンドを選択）\n        api_key: APIキー（OpenAIの場合は必須、他のバックエンドではダミー値でOK）\n    戻り値:\n        OpenAIクライアントのインスタンス", "detail": "backend.inference.backup.simple_client", "documentation": {}}, {"label": "get_backend_for_model", "kind": 2, "importPath": "backend.inference.backup.simplified_selector", "description": "backend.inference.backup.simplified_selector", "peekOfCode": "def get_backend_for_model(model: str, **kwargs) -> UnifiedLLMBackend:\n    \"\"\"\n    指定されたモデルに適したバックエンドを取得します。\n    引数:\n        model: モデル名\n        **kwargs: バックエンドに渡す追加パラメータ\n    戻り値:\n        UnifiedLLMBackendのインスタンス\n    \"\"\"\n    # モデルバックエンドマッピングをチェック", "detail": "backend.inference.backup.simplified_selector", "documentation": {}}, {"label": "get_backend", "kind": 2, "importPath": "backend.inference.backup.simplified_selector", "description": "backend.inference.backup.simplified_selector", "peekOfCode": "def get_backend(backend_type: str, **kwargs) -> UnifiedLLMBackend:\n    \"\"\"\n    指定されたバックエンドタイプのバックエンドを取得します。\n    引数:\n        backend_type: バックエンドタイプ\n        **kwargs: バックエンドに渡す追加パラメータ\n    戻り値:\n        UnifiedLLMBackendのインスタンス\n    \"\"\"\n    logging.info(f\"バックエンド '{backend_type}' を初期化します\")", "detail": "backend.inference.backup.simplified_selector", "documentation": {}}, {"label": "UnifiedLLMBackend", "kind": 6, "importPath": "backend.inference.backup.unified_backend", "description": "backend.inference.backup.unified_backend", "peekOfCode": "class UnifiedLLMBackend:\n    \"\"\"\n    OpenAIクライアントを使用した統一LLMバックエンド。\n    すべてのバックエンドに対して一貫したインターフェースを提供します。\n    \"\"\"\n    def __init__(self,\n                 backend_type: str = \"ollama\",\n                 model_name: str = None,\n                 api_key: str = None,\n                 endpoint: str = None,", "detail": "backend.inference.backup.unified_backend", "documentation": {}}, {"label": "VLLMBackend", "kind": 6, "importPath": "backend.inference.backup.vllm_backend", "description": "backend.inference.backup.vllm_backend", "peekOfCode": "class VLLMBackend(OpenAICompatibleBackend):\n    \"\"\"\n    vLLM推論用のバックエンド。\n    直接vLLM統合とOpenAI互換APIエンドポイントの両方をサポートします。\n    \"\"\"\n    def __init__(self,\n                 endpoint: str = \"http://localhost:8001/v1\",\n                 model_name: str = \"my-vllm-model\",\n                 use_direct: bool = False,\n                 model_kwargs: Optional[Dict[str, Any]] = None,", "detail": "backend.inference.backup.vllm_backend", "documentation": {}}, {"label": "XInferenceBackend", "kind": 6, "importPath": "backend.inference.backup.x_inference_backend", "description": "backend.inference.backup.x_inference_backend", "peekOfCode": "class XInferenceBackend(OpenAICompatibleBackend):\n    \"\"\"\n    Backend for X-Inference API.\n    Supports OpenAI-compatible API endpoints.\n    \"\"\"\n    def __init__(self,\n                 endpoint: str = \"http://localhost:9999/v1\",\n                 model_name: str = \"x-inference-model\",\n                 **kwargs):\n        \"\"\"", "detail": "backend.inference.backup.x_inference_backend", "documentation": {}}, {"label": "BackendType", "kind": 6, "importPath": "backend.inference.llm_client", "description": "backend.inference.llm_client", "peekOfCode": "class BackendType(Enum):\n    \"\"\"\n    LLMバックエンドタイプの列挙型\n    \"\"\"\n    OLLAMA = \"ollama\"\n    VLLM = \"vllm\"\n    SGLANG = \"sglang\"\n    X_INFERENCE = \"x-inference\"\n    OPENAI = \"openai\"\n    @classmethod", "detail": "backend.inference.llm_client", "documentation": {}}, {"label": "LLMClient", "kind": 6, "importPath": "backend.inference.llm_client", "description": "backend.inference.llm_client", "peekOfCode": "class LLMClient:\n    \"\"\"\n    LLMクライアント - OpenAIクライアントを使用して異なるバックエンドに接続します\n    \"\"\"\n    @staticmethod\n    def get_client(backend_type: Union[BackendType, str, None] = None, model: Optional[str] = None, api_key: Optional[str] = None) -> OpenAI:\n        \"\"\"\n        指定されたバックエンドタイプとモデルに基づいてOpenAIクライアントを取得します。\n        引数:\n            backend_type: バックエンドタイプ（BackendType列挙型または文字列）", "detail": "backend.inference.llm_client", "documentation": {}}, {"label": "get_client", "kind": 2, "importPath": "backend.inference.llm_client", "description": "backend.inference.llm_client", "peekOfCode": "def get_client(backend_type: Union[BackendType, str, None] = None, model: Optional[str] = None, api_key: Optional[str] = None) -> OpenAI:\n    \"\"\"\n    指定されたバックエンドタイプとモデルに基づいてOpenAIクライアントを取得します。\n    LLMClient.get_clientのショートカット。\n    引数:\n        backend_type: バックエンドタイプ（BackendType列挙型または文字列）\n                     例: BackendType.OLLAMA または \"ollama\"\n        model: モデル名\n        api_key: APIキー\n    \"\"\"", "detail": "backend.inference.llm_client", "documentation": {}}, {"label": "get_client_for_model", "kind": 2, "importPath": "backend.inference.llm_client", "description": "backend.inference.llm_client", "peekOfCode": "def get_client_for_model(model: str, api_key: Optional[str] = None) -> OpenAI:\n    \"\"\"\n    指定されたモデルに基づいてOpenAIクライアントを取得します。\n    引数:\n        model: モデル名\n        api_key: APIキー\n    \"\"\"\n    return LLMClient.get_client(model=model, api_key=api_key)", "detail": "backend.inference.llm_client", "documentation": {}}, {"label": "logger", "kind": 5, "importPath": "backend.inference.vllm_direct", "description": "backend.inference.vllm_direct", "peekOfCode": "logger = logging.getLogger(__name__)\nasync def vllm_chat_completion(\n    model: str,\n    messages: List[Dict[str, str]],\n    temperature: float = 0.7,\n    max_tokens: int = 1000,\n    top_p: float = 1.0,\n    stream: bool = False,\n    tools: Optional[List[Dict[str, Any]]] = None,\n    tool_choice: Optional[Any] = None,", "detail": "backend.inference.vllm_direct", "documentation": {}}, {"label": "setup_logger", "kind": 2, "importPath": "backend.monitoring.logger", "description": "backend.monitoring.logger", "peekOfCode": "def setup_logger():\n    config_file = os.path.join(os.path.dirname(__file__), \"log_config.yaml\")\n    if os.path.exists(config_file):\n        with open(config_file, \"r\") as f:\n            config = yaml.safe_load(f.read())\n        logging.config.dictConfig(config)\n    else:\n        logging.basicConfig(level=logging.INFO)\nlogger = logging.getLogger(\"myllm\")\ndef get_request_logger(request_id):", "detail": "backend.monitoring.logger", "documentation": {}}, {"label": "get_request_logger", "kind": 2, "importPath": "backend.monitoring.logger", "description": "backend.monitoring.logger", "peekOfCode": "def get_request_logger(request_id):\n    \"\"\"特定のリクエストのロガー\"\"\"\n    return logging.getLogger(f\"myllm.request.{request_id}\")\ndef log_request(request, response, processing_time):\n    \"\"\"APIリクエストとレスポンスを記録\"\"\"\n    logger.info(f\"リクエスト: {request.method} {request.url.path} - レスポンス: {response.status_code} - 処理時間: {processing_time}ms\")", "detail": "backend.monitoring.logger", "documentation": {}}, {"label": "log_request", "kind": 2, "importPath": "backend.monitoring.logger", "description": "backend.monitoring.logger", "peekOfCode": "def log_request(request, response, processing_time):\n    \"\"\"APIリクエストとレスポンスを記録\"\"\"\n    logger.info(f\"リクエスト: {request.method} {request.url.path} - レスポンス: {response.status_code} - 処理時間: {processing_time}ms\")", "detail": "backend.monitoring.logger", "documentation": {}}, {"label": "logger", "kind": 5, "importPath": "backend.monitoring.logger", "description": "backend.monitoring.logger", "peekOfCode": "logger = logging.getLogger(\"myllm\")\ndef get_request_logger(request_id):\n    \"\"\"特定のリクエストのロガー\"\"\"\n    return logging.getLogger(f\"myllm.request.{request_id}\")\ndef log_request(request, response, processing_time):\n    \"\"\"APIリクエストとレスポンスを記録\"\"\"\n    logger.info(f\"リクエスト: {request.method} {request.url.path} - レスポンス: {response.status_code} - 処理時間: {processing_time}ms\")", "detail": "backend.monitoring.logger", "documentation": {}}, {"label": "request_count", "kind": 5, "importPath": "backend.monitoring.metrics", "description": "backend.monitoring.metrics", "peekOfCode": "request_count = Counter(\n    'llm_api_requests_total', \n    'Total number of requests to the LLM API',\n    ['model', 'endpoint']\n)\n# レスポンス時間ヒストグラム\nresponse_time = Histogram(\n    'llm_api_response_time_seconds', \n    'Response time in seconds',\n    ['model', 'endpoint']", "detail": "backend.monitoring.metrics", "documentation": {}}, {"label": "response_time", "kind": 5, "importPath": "backend.monitoring.metrics", "description": "backend.monitoring.metrics", "peekOfCode": "response_time = Histogram(\n    'llm_api_response_time_seconds', \n    'Response time in seconds',\n    ['model', 'endpoint']\n)\n# 現在のリクエスト数ゲージ\nactive_requests = Gauge(\n    'llm_api_active_requests', \n    'Number of currently active requests',\n    ['model', 'endpoint']", "detail": "backend.monitoring.metrics", "documentation": {}}, {"label": "active_requests", "kind": 5, "importPath": "backend.monitoring.metrics", "description": "backend.monitoring.metrics", "peekOfCode": "active_requests = Gauge(\n    'llm_api_active_requests', \n    'Number of currently active requests',\n    ['model', 'endpoint']\n)\n# トークン使用量カウンター\ntoken_usage = Counter(\n    'llm_api_token_usage_total', \n    'Total number of tokens used',\n    ['model', 'type']  # type: prompt, completion", "detail": "backend.monitoring.metrics", "documentation": {}}, {"label": "token_usage", "kind": 5, "importPath": "backend.monitoring.metrics", "description": "backend.monitoring.metrics", "peekOfCode": "token_usage = Counter(\n    'llm_api_token_usage_total', \n    'Total number of tokens used',\n    ['model', 'type']  # type: prompt, completion\n)\n# エラー数カウンター\nerror_count = Counter(\n    'llm_api_errors_total', \n    'Total number of errors',\n    ['model', 'error_type']", "detail": "backend.monitoring.metrics", "documentation": {}}, {"label": "error_count", "kind": 5, "importPath": "backend.monitoring.metrics", "description": "backend.monitoring.metrics", "peekOfCode": "error_count = Counter(\n    'llm_api_errors_total', \n    'Total number of errors',\n    ['model', 'error_type']\n)", "detail": "backend.monitoring.metrics", "documentation": {}}, {"label": "analyze_question", "kind": 2, "importPath": "backend.plugins.agents.agent_functions", "description": "backend.plugins.agents.agent_functions", "peekOfCode": "def analyze_question(state: SupportState, llm: BaseChatModel, tools: Optional[List[BaseTool]] = None) -> dict:\n    \"\"\"\n    サブAgent1: ユーザーの質問を分析し、コード診断が必要かどうかを判断\n    Args:\n        state: 現在の状態\n        llm: 使用する言語モデル\n        tools: 使用可能なツールのリスト\n    Returns:\n        更新された状態の一部\n    \"\"\"", "detail": "backend.plugins.agents.agent_functions", "documentation": {}}, {"label": "diagnose_code", "kind": 2, "importPath": "backend.plugins.agents.agent_functions", "description": "backend.plugins.agents.agent_functions", "peekOfCode": "def diagnose_code(state: SupportState, llm: BaseChatModel, tools: Optional[List[BaseTool]] = None) -> dict:\n    \"\"\"\n    サブAgent2: ユーザーが提供したコードのエラーを診断\n    Args:\n        state: 現在の状態\n        llm: 使用する言語モデル\n        tools: 使用可能なツールのリスト\n    Returns:\n        更新された状態の一部\n    \"\"\"", "detail": "backend.plugins.agents.agent_functions", "documentation": {}}, {"label": "compose_answer", "kind": 2, "importPath": "backend.plugins.agents.agent_functions", "description": "backend.plugins.agents.agent_functions", "peekOfCode": "def compose_answer(state: SupportState, llm: BaseChatModel, tools: Optional[List[BaseTool]] = None) -> dict:\n    \"\"\"\n    サブAgent3: 分析と診断結果に基づいて最終回答を生成\n    Args:\n        state: 現在の状態\n        llm: 使用する言語モデル\n        tools: 使用可能なツールのリスト\n    Returns:\n        更新された状態の一部\n    \"\"\"", "detail": "backend.plugins.agents.agent_functions", "documentation": {}}, {"label": "create_agent_functions", "kind": 2, "importPath": "backend.plugins.agents.agent_functions", "description": "backend.plugins.agents.agent_functions", "peekOfCode": "def create_agent_functions(llm: BaseChatModel, tools: Optional[Dict[str, List[BaseTool]]] = None) -> Dict[str, Any]:\n    \"\"\"\n    LLMとツールを使用してエージェント関数を作成します。\n    Args:\n        llm: 使用する言語モデル\n        tools: エージェント名をキー、ツールのリストを値とする辞書\n    Returns:\n        エージェント関数の辞書\n    \"\"\"\n    if tools is None:", "detail": "backend.plugins.agents.agent_functions", "documentation": {}}, {"label": "SearchInput", "kind": 6, "importPath": "backend.plugins.agents.agent_tools", "description": "backend.plugins.agents.agent_tools", "peekOfCode": "class SearchInput(BaseModel):\n    \"\"\"検索ツールの入力モデル。\"\"\"\n    query: str = Field(description=\"検索クエリ\")\nclass CodeExecutionInput(BaseModel):\n    \"\"\"コード実行ツールの入力モデル。\"\"\"\n    code: str = Field(description=\"実行するPythonコード\")\n# class DocumentLookupInput(BaseModel):\n#     \"\"\"ドキュメント検索ツールの入力モデル。\"\"\"\n#     keyword: str = Field(description=\"検索キーワード\")\n#     max_results: int = Field(default=3, description=\"返す結果の最大数\")", "detail": "backend.plugins.agents.agent_tools", "documentation": {}}, {"label": "CodeExecutionInput", "kind": 6, "importPath": "backend.plugins.agents.agent_tools", "description": "backend.plugins.agents.agent_tools", "peekOfCode": "class CodeExecutionInput(BaseModel):\n    \"\"\"コード実行ツールの入力モデル。\"\"\"\n    code: str = Field(description=\"実行するPythonコード\")\n# class DocumentLookupInput(BaseModel):\n#     \"\"\"ドキュメント検索ツールの入力モデル。\"\"\"\n#     keyword: str = Field(description=\"検索キーワード\")\n#     max_results: int = Field(default=3, description=\"返す結果の最大数\")\ndef search_web(query: str) -> str:\n    \"\"\"\n    ウェブ検索を実行するツール関数。", "detail": "backend.plugins.agents.agent_tools", "documentation": {}}, {"label": "search_web", "kind": 2, "importPath": "backend.plugins.agents.agent_tools", "description": "backend.plugins.agents.agent_tools", "peekOfCode": "def search_web(query: str) -> str:\n    \"\"\"\n    ウェブ検索を実行するツール関数。\n    Args:\n        query: 検索クエリ\n    Returns:\n        検索結果の要約\n    \"\"\"\n    try:\n        # 実際の実装では外部APIを呼び出す", "detail": "backend.plugins.agents.agent_tools", "documentation": {}}, {"label": "execute_python_code", "kind": 2, "importPath": "backend.plugins.agents.agent_tools", "description": "backend.plugins.agents.agent_tools", "peekOfCode": "def execute_python_code(code: str) -> str:\n    \"\"\"\n    Pythonコードを安全な環境で実行するツール関数。\n    Args:\n        code: 実行するPythonコード\n    Returns:\n        実行結果\n    \"\"\"\n    try:\n        # 実際の実装では安全なサンドボックスでコードを実行", "detail": "backend.plugins.agents.agent_tools", "documentation": {}}, {"label": "lookup_documentation", "kind": 2, "importPath": "backend.plugins.agents.agent_tools", "description": "backend.plugins.agents.agent_tools", "peekOfCode": "def lookup_documentation(keyword: str) -> str:\n    \"\"\"\n    ドキュメントを検索するツール関数。\n    Args:\n        keyword: 検索キーワード\n    Returns:\n        ドキュメント検索結果\n    \"\"\"\n    try:\n        # 実際の実装ではドキュメントデータベースを検索", "detail": "backend.plugins.agents.agent_tools", "documentation": {}}, {"label": "create_analysis_tools", "kind": 2, "importPath": "backend.plugins.agents.agent_tools", "description": "backend.plugins.agents.agent_tools", "peekOfCode": "def create_analysis_tools() -> List[BaseTool]:\n    \"\"\"\n    分析エージェント用のツールを作成します。\n    Returns:\n        ツールのリスト\n    \"\"\"\n    return [\n        Tool(\n            name=\"web_search\",\n            func=search_web,", "detail": "backend.plugins.agents.agent_tools", "documentation": {}}, {"label": "create_diagnosis_tools", "kind": 2, "importPath": "backend.plugins.agents.agent_tools", "description": "backend.plugins.agents.agent_tools", "peekOfCode": "def create_diagnosis_tools() -> List[BaseTool]:\n    \"\"\"\n    診断エージェント用のツールを作成します。\n    Returns:\n        ツールのリスト\n    \"\"\"\n    return [\n        Tool(\n            name=\"execute_code\",\n            func=execute_python_code,", "detail": "backend.plugins.agents.agent_tools", "documentation": {}}, {"label": "create_answer_tools", "kind": 2, "importPath": "backend.plugins.agents.agent_tools", "description": "backend.plugins.agents.agent_tools", "peekOfCode": "def create_answer_tools() -> List[BaseTool]:\n    \"\"\"\n    回答生成エージェント用のツールを作成します。\n    Returns:\n        ツールのリスト\n    \"\"\"\n    return [\n        Tool(\n            name=\"documentation_lookup\",\n            func=lookup_documentation,", "detail": "backend.plugins.agents.agent_tools", "documentation": {}}, {"label": "create_agent_tools", "kind": 2, "importPath": "backend.plugins.agents.agent_tools", "description": "backend.plugins.agents.agent_tools", "peekOfCode": "def create_agent_tools() -> Dict[str, List[BaseTool]]:\n    \"\"\"\n    各エージェントに必要なツールを作成します。\n    Returns:\n        エージェント名をキー、ツールのリストを値とする辞書\n    \"\"\"\n    return {\n        \"analyze_question\": create_analysis_tools(),\n        \"diagnose_code\": create_diagnosis_tools(),\n        \"compose_answer\": create_answer_tools()", "detail": "backend.plugins.agents.agent_tools", "documentation": {}}, {"label": "logger", "kind": 5, "importPath": "backend.plugins.agents.agent_tools", "description": "backend.plugins.agents.agent_tools", "peekOfCode": "logger = logging.getLogger(__name__)\nclass SearchInput(BaseModel):\n    \"\"\"検索ツールの入力モデル。\"\"\"\n    query: str = Field(description=\"検索クエリ\")\nclass CodeExecutionInput(BaseModel):\n    \"\"\"コード実行ツールの入力モデル。\"\"\"\n    code: str = Field(description=\"実行するPythonコード\")\n# class DocumentLookupInput(BaseModel):\n#     \"\"\"ドキュメント検索ツールの入力モデル。\"\"\"\n#     keyword: str = Field(description=\"検索キーワード\")", "detail": "backend.plugins.agents.agent_tools", "documentation": {}}, {"label": "CodeMigrationState", "kind": 6, "importPath": "backend.plugins.agents.legacy_code_migration_and_unittest_generation", "description": "backend.plugins.agents.legacy_code_migration_and_unittest_generation", "peekOfCode": "class CodeMigrationState(TypedDict):\n    original_code: str   # ユーザーが提供する旧コード\n    new_code: str        # LLMがリファクタリングした新コード\n    tests: str           # LLMが生成した新コード用のテストコード\n    test_results: str    # テスト実行結果の出力\n# LLMを初期化（OpenAI APIキーは環境変数で設定済みと仮定）\nopenai_client = get_client(settings.DEFAULT_LLM_BACKEND)\nllm = ChatOpenAI(\n                    model_name=settings.DEFAULT_LLM_MODEL or \"llama3.2:3b\",\n                    openai_api_key=openai_client.api_key,", "detail": "backend.plugins.agents.legacy_code_migration_and_unittest_generation", "documentation": {}}, {"label": "rewrite_code", "kind": 2, "importPath": "backend.plugins.agents.legacy_code_migration_and_unittest_generation", "description": "backend.plugins.agents.legacy_code_migration_and_unittest_generation", "peekOfCode": "def rewrite_code(state: CodeMigrationState) -> dict:\n    \"\"\"LLMを使用して旧コードをリファクタリングし、新しいコードに変換\"\"\"\n    old_code = state[\"original_code\"]\n    prompt = (\n        \"あなたは上級Python開発者です。以下の旧コードをリファクタリングし、最新のベストプラクティスとコードアーキテクチャに従ってください。\\n\"\n        \"機能はそのままに、コードをより明確で効率的にし、PEP8規約に準拠させてください：\\n\\n\"\n        f\"{old_code}\\n\\nリファクタリング後のコード：\"\n    )\n    try:\n        response = llm.invoke(prompt).content", "detail": "backend.plugins.agents.legacy_code_migration_and_unittest_generation", "documentation": {}}, {"label": "generate_tests", "kind": 2, "importPath": "backend.plugins.agents.legacy_code_migration_and_unittest_generation", "description": "backend.plugins.agents.legacy_code_migration_and_unittest_generation", "peekOfCode": "def generate_tests(state: CodeMigrationState) -> dict:\n    \"\"\"LLMを使用して新コードの単体テストを生成（pytest形式）\"\"\"\n    new_code = state[\"new_code\"]\n    prompt = (\n        \"以下のコードに対してPython単体テストを作成してください。pytest構文を使用し、主要な機能と境界条件をカバーしてください：\\n\\n\"\n        f\"{new_code}\\n\\n# テストコードのみを記載し、説明は不要です。\"\n    )\n    try:\n        tests_code = llm.invoke(prompt).content\n        return {\"tests\": tests_code.strip()}", "detail": "backend.plugins.agents.legacy_code_migration_and_unittest_generation", "documentation": {}}, {"label": "run_tests", "kind": 2, "importPath": "backend.plugins.agents.legacy_code_migration_and_unittest_generation", "description": "backend.plugins.agents.legacy_code_migration_and_unittest_generation", "peekOfCode": "def run_tests(state: CodeMigrationState) -> dict:\n    \"\"\"生成されたテストコードを実行し、テストの合格/不合格の結果を返す\"\"\"\n    import io, contextlib\n    new_code = state[\"new_code\"]\n    tests_code = state[\"tests\"]\n    # コードを実行するための独立したグローバル名前空間を準備し、汚染を防ぐ\n    namespace = {}\n    output = io.StringIO()\n    result_summary = \"\"\n    try:", "detail": "backend.plugins.agents.legacy_code_migration_and_unittest_generation", "documentation": {}}, {"label": "build_workflow", "kind": 2, "importPath": "backend.plugins.agents.legacy_code_migration_and_unittest_generation", "description": "backend.plugins.agents.legacy_code_migration_and_unittest_generation", "peekOfCode": "def build_workflow():\n    \"\"\"ワークフローグラフを構築して返す\"\"\"\n    workflow = StateGraph(CodeMigrationState)\n    workflow.add_node(\"rewrite_code\", rewrite_code)\n    workflow.add_node(\"generate_tests\", generate_tests)\n    workflow.add_node(\"run_tests\", run_tests)\n    # 順次エッジを追加: Start -> コードリファクタリング -> テスト生成 -> テスト実行 -> End\n    workflow.add_edge(START, \"rewrite_code\")\n    workflow.add_edge(\"rewrite_code\", \"generate_tests\")\n    workflow.add_edge(\"generate_tests\", \"run_tests\")", "detail": "backend.plugins.agents.legacy_code_migration_and_unittest_generation", "documentation": {}}, {"label": "human_check_function", "kind": 2, "importPath": "backend.plugins.agents.legacy_code_migration_and_unittest_generation", "description": "backend.plugins.agents.legacy_code_migration_and_unittest_generation", "peekOfCode": "def human_check_function(state: CodeMigrationState) -> dict:\n    \"\"\"\n    リファクタリングされたコードを人間が確認し、承認、拒否、または修正するためのノード。\n    LangGraphのinterruptを使用して一時停止を実現します。\n    引数:\n        state: 現在のグラフの状態\n    戻り値:\n        更新された状態の辞書\n    処理の流れ:\n    1. 承認の場合 (True): 変更なし（リファクタリングされたコードをそのまま使用）", "detail": "backend.plugins.agents.legacy_code_migration_and_unittest_generation", "documentation": {}}, {"label": "build_workflow_human_check", "kind": 2, "importPath": "backend.plugins.agents.legacy_code_migration_and_unittest_generation", "description": "backend.plugins.agents.legacy_code_migration_and_unittest_generation", "peekOfCode": "def build_workflow_human_check():\n    \"\"\"ワークフローグラフを構築して返す\"\"\"\n    workflow = StateGraph(CodeMigrationState)\n    workflow.add_node(\"rewrite_code\", rewrite_code)\n    workflow.add_node(\"generate_tests\", generate_tests)\n    workflow.add_node(\"human_check\", human_check_function)\n    workflow.add_node(\"run_tests\", run_tests)\n    # 順次エッジを追加: Start -> コードリファクタリング -> テスト生成 -> テスト実行 -> End\n    workflow.add_edge(START, \"rewrite_code\")\n    workflow.add_edge(\"rewrite_code\", \"human_check\")", "detail": "backend.plugins.agents.legacy_code_migration_and_unittest_generation", "documentation": {}}, {"label": "run_example", "kind": 2, "importPath": "backend.plugins.agents.legacy_code_migration_and_unittest_generation", "description": "backend.plugins.agents.legacy_code_migration_and_unittest_generation", "peekOfCode": "def run_example(agent):\n    \"\"\"サンプルコードを実行する関数\"\"\"\n    original_code = '''\n    # 旧コードの例：階乗を計算するが、記述が不適切\n    def fact(n):\n        # 再帰を使用して階乗を計算\n        if n == 0 or n == 1:\n            return 1\n        return n*fact(n-1)\n    print(fact(5))", "detail": "backend.plugins.agents.legacy_code_migration_and_unittest_generation", "documentation": {}}, {"label": "run_example_with_human_check", "kind": 2, "importPath": "backend.plugins.agents.legacy_code_migration_and_unittest_generation", "description": "backend.plugins.agents.legacy_code_migration_and_unittest_generation", "peekOfCode": "def run_example_with_human_check(agent_with_human_check):\n    \"\"\"人間の確認を含むサンプルコードを実行する関数\"\"\"\n    import uuid\n    from langgraph.types import Command\n    original_code = '''\n    # 旧コードの例：階乗を計算するが、記述が不適切\n    def fact(n):\n        # 再帰を使用して階乗を計算\n        if n == 0 or n == 1:\n            return 1", "detail": "backend.plugins.agents.legacy_code_migration_and_unittest_generation", "documentation": {}}, {"label": "openai_client", "kind": 5, "importPath": "backend.plugins.agents.legacy_code_migration_and_unittest_generation", "description": "backend.plugins.agents.legacy_code_migration_and_unittest_generation", "peekOfCode": "openai_client = get_client(settings.DEFAULT_LLM_BACKEND)\nllm = ChatOpenAI(\n                    model_name=settings.DEFAULT_LLM_MODEL or \"llama3.2:3b\",\n                    openai_api_key=openai_client.api_key,\n                    openai_api_base=str(openai_client.base_url)\n                )\n# 2. 各ノードの処理関数を定義\ndef rewrite_code(state: CodeMigrationState) -> dict:\n    \"\"\"LLMを使用して旧コードをリファクタリングし、新しいコードに変換\"\"\"\n    old_code = state[\"original_code\"]", "detail": "backend.plugins.agents.legacy_code_migration_and_unittest_generation", "documentation": {}}, {"label": "llm", "kind": 5, "importPath": "backend.plugins.agents.legacy_code_migration_and_unittest_generation", "description": "backend.plugins.agents.legacy_code_migration_and_unittest_generation", "peekOfCode": "llm = ChatOpenAI(\n                    model_name=settings.DEFAULT_LLM_MODEL or \"llama3.2:3b\",\n                    openai_api_key=openai_client.api_key,\n                    openai_api_base=str(openai_client.base_url)\n                )\n# 2. 各ノードの処理関数を定義\ndef rewrite_code(state: CodeMigrationState) -> dict:\n    \"\"\"LLMを使用して旧コードをリファクタリングし、新しいコードに変換\"\"\"\n    old_code = state[\"original_code\"]\n    prompt = (", "detail": "backend.plugins.agents.legacy_code_migration_and_unittest_generation", "documentation": {}}, {"label": "SupportState", "kind": 6, "importPath": "backend.plugins.agents.models", "description": "backend.plugins.agents.models", "peekOfCode": "class SupportState(TypedDict):\n    \"\"\"カスタマーサポートワークフローでエージェント間で共有される状態。\"\"\"\n    question: str        # ユーザーの質問\n    code: str            # ユーザーが提供したコード（存在しない場合は空文字列）\n    analysis: str        # 問題の分析/理解\n    diagnosis: str       # コード問題の診断結果\n    answer: str          # ユーザーへの最終回答\n    next_step: Optional[str]  # 次のステップを示す\n    tool_results: Dict[str, Any]  # ツールの実行結果を保存", "detail": "backend.plugins.agents.models", "documentation": {}}, {"label": "MultiAgentCustomerAssistant", "kind": 6, "importPath": "backend.plugins.agents.multi_agent_customer_assistant", "description": "backend.plugins.agents.multi_agent_customer_assistant", "peekOfCode": "class MultiAgentCustomerAssistant:\n    \"\"\"\n    質問を分析し、コードの問題を診断し、回答を作成するマルチエージェントシステム。\n    \"\"\"\n    def __init__(\n        self,\n        llm: Optional[BaseChatModel] = None,\n        model_name: str = \"qwen3:latest\",\n        temperature: float = 0,\n        custom_tools: Optional[Dict[str, List[BaseTool]]] = None", "detail": "backend.plugins.agents.multi_agent_customer_assistant", "documentation": {}}, {"label": "main", "kind": 2, "importPath": "backend.plugins.agents.multi_agent_example", "description": "backend.plugins.agents.multi_agent_example", "peekOfCode": "def main():\n    \"\"\"\n    マルチエージェントカスタマーアシスタントの使用例。\n    \"\"\"\n    logger.info(\"マルチエージェントカスタマーアシスタントの初期化...\")\n    # 1. デフォルトのアシスタント（ツールなし）\n    assistant = MultiAgentCustomerAssistant()\n    # 例1: コードなしの一般的な質問\n    question1 = \"PythonでCSVファイルを読み取る方法は？\"\n    logger.info(f\"質問1: {question1}\")", "detail": "backend.plugins.agents.multi_agent_example", "documentation": {}}, {"label": "logger", "kind": 5, "importPath": "backend.plugins.agents.multi_agent_example", "description": "backend.plugins.agents.multi_agent_example", "peekOfCode": "logger = logging.getLogger(__name__)\ndef main():\n    \"\"\"\n    マルチエージェントカスタマーアシスタントの使用例。\n    \"\"\"\n    logger.info(\"マルチエージェントカスタマーアシスタントの初期化...\")\n    # 1. デフォルトのアシスタント（ツールなし）\n    assistant = MultiAgentCustomerAssistant()\n    # 例1: コードなしの一般的な質問\n    question1 = \"PythonでCSVファイルを読み取る方法は？\"", "detail": "backend.plugins.agents.multi_agent_example", "documentation": {}}, {"label": "divide", "kind": 2, "importPath": "backend.plugins.agents.multi_agent_stream_example", "description": "backend.plugins.agents.multi_agent_stream_example", "peekOfCode": "def divide(a, b):\n    return a/b\nprint(divide(10, 0))\n'''\nquestion2 = \"上記のコードを実行するとエラーが発生しました。原因は何ですか？修正方法を教えてください。\"\n# ロギングの設定\nlogging.basicConfig(\n    level=logging.WARNING, # DEBUG,  # DEBUGレベルに変更して詳細なログを表示\n    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'\n)", "detail": "backend.plugins.agents.multi_agent_stream_example", "documentation": {}}, {"label": "main", "kind": 2, "importPath": "backend.plugins.agents.multi_agent_stream_example", "description": "backend.plugins.agents.multi_agent_stream_example", "peekOfCode": "def main():\n    \"\"\"\n    マルチエージェントカスタマーアシスタントの使用例。\n    \"\"\"\n    # 4. ストリーミング形式でのカスタムアシスタントの使用例\n    logger.info(\"ストリーミング形式でのカスタムアシスタントの使用1...\\n\")\n    # 特定のツールを使用するアシスタントを初期化\n    # 例: 検索ツールのみを使用\n    search_tools = {\n        \"analyze_question\": [tool for tool in custom_tools[\"analyze_question\"] if tool.name == \"web_search\"]", "detail": "backend.plugins.agents.multi_agent_stream_example", "documentation": {}}, {"label": "custom_tools", "kind": 5, "importPath": "backend.plugins.agents.multi_agent_stream_example", "description": "backend.plugins.agents.multi_agent_stream_example", "peekOfCode": "custom_tools = create_agent_tools()\ndiagnosis_tools = {\n    \"diagnose_code\": custom_tools[\"diagnose_code\"]\n}\n# サンプルコードと質問\nuser_code = '''\ndef divide(a, b):\n    return a/b\nprint(divide(10, 0))\n'''", "detail": "backend.plugins.agents.multi_agent_stream_example", "documentation": {}}, {"label": "diagnosis_tools", "kind": 5, "importPath": "backend.plugins.agents.multi_agent_stream_example", "description": "backend.plugins.agents.multi_agent_stream_example", "peekOfCode": "diagnosis_tools = {\n    \"diagnose_code\": custom_tools[\"diagnose_code\"]\n}\n# サンプルコードと質問\nuser_code = '''\ndef divide(a, b):\n    return a/b\nprint(divide(10, 0))\n'''\nquestion2 = \"上記のコードを実行するとエラーが発生しました。原因は何ですか？修正方法を教えてください。\"", "detail": "backend.plugins.agents.multi_agent_stream_example", "documentation": {}}, {"label": "user_code", "kind": 5, "importPath": "backend.plugins.agents.multi_agent_stream_example", "description": "backend.plugins.agents.multi_agent_stream_example", "peekOfCode": "user_code = '''\ndef divide(a, b):\n    return a/b\nprint(divide(10, 0))\n'''\nquestion2 = \"上記のコードを実行するとエラーが発生しました。原因は何ですか？修正方法を教えてください。\"\n# ロギングの設定\nlogging.basicConfig(\n    level=logging.WARNING, # DEBUG,  # DEBUGレベルに変更して詳細なログを表示\n    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'", "detail": "backend.plugins.agents.multi_agent_stream_example", "documentation": {}}, {"label": "question2", "kind": 5, "importPath": "backend.plugins.agents.multi_agent_stream_example", "description": "backend.plugins.agents.multi_agent_stream_example", "peekOfCode": "question2 = \"上記のコードを実行するとエラーが発生しました。原因は何ですか？修正方法を教えてください。\"\n# ロギングの設定\nlogging.basicConfig(\n    level=logging.WARNING, # DEBUG,  # DEBUGレベルに変更して詳細なログを表示\n    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'\n)\nlogger = logging.getLogger(__name__)\ndef main():\n    \"\"\"\n    マルチエージェントカスタマーアシスタントの使用例。", "detail": "backend.plugins.agents.multi_agent_stream_example", "documentation": {}}, {"label": "logger", "kind": 5, "importPath": "backend.plugins.agents.multi_agent_stream_example", "description": "backend.plugins.agents.multi_agent_stream_example", "peekOfCode": "logger = logging.getLogger(__name__)\ndef main():\n    \"\"\"\n    マルチエージェントカスタマーアシスタントの使用例。\n    \"\"\"\n    # 4. ストリーミング形式でのカスタムアシスタントの使用例\n    logger.info(\"ストリーミング形式でのカスタムアシスタントの使用1...\\n\")\n    # 特定のツールを使用するアシスタントを初期化\n    # 例: 検索ツールのみを使用\n    search_tools = {", "detail": "backend.plugins.agents.multi_agent_stream_example", "documentation": {}}, {"label": "format_code", "kind": 2, "importPath": "backend.plugins.agents.oldcode_style_converted_pep8", "description": "backend.plugins.agents.oldcode_style_converted_pep8", "peekOfCode": "def format_code(code: str) -> str:\n    \"\"\"autopep8 を使用してコードをPEP8スタイルにフォーマットする\"\"\"\n    try:\n        return autopep8.fix_code(code)\n    except Exception as e:\n        raise ValueError(f\"フォーマットに失敗しました: {e}\")\n# LLM初期化\ndef initialize_llm():\n    \"\"\"LLMとツールの初期化\"\"\"\n    tools = [format_code]", "detail": "backend.plugins.agents.oldcode_style_converted_pep8", "documentation": {}}, {"label": "initialize_llm", "kind": 2, "importPath": "backend.plugins.agents.oldcode_style_converted_pep8", "description": "backend.plugins.agents.oldcode_style_converted_pep8", "peekOfCode": "def initialize_llm():\n    \"\"\"LLMとツールの初期化\"\"\"\n    tools = [format_code]\n    openai_client = get_client(settings.DEFAULT_LLM_BACKEND)\n    llm = ChatOpenAI(\n                    model_name=settings.DEFAULT_LLM_MODEL or \"llama3.2:3b\",\n                    openai_api_key=openai_client.api_key,\n                    openai_api_base=str(openai_client.base_url)\n                )\n    # llm = ChatOpenAI(model=\"gpt-3.5-turbo-0613\", temperature=0)", "detail": "backend.plugins.agents.oldcode_style_converted_pep8", "documentation": {}}, {"label": "create_prompt_template", "kind": 2, "importPath": "backend.plugins.agents.oldcode_style_converted_pep8", "description": "backend.plugins.agents.oldcode_style_converted_pep8", "peekOfCode": "def create_prompt_template():\n    \"\"\"構造化された出力を促すプロンプトテンプレートを作成\"\"\"\n    system_template = \"\"\"あなたはPythonコードのフォーマット専門家です。\nユーザーから提供されたコードをPEP8スタイルに変換してください。\n出力形式:\n1. まず、<think>タグ内で思考プロセスを詳細に記述してください。\n2. 次に、フォーマット後のコードを```python```コードブロック内に記述してください。\n3. 最後に、「変更点：」という見出しの後に、番号付きリストで変更内容を説明してください。\n例:\n<think>", "detail": "backend.plugins.agents.oldcode_style_converted_pep8", "documentation": {}}, {"label": "call_model", "kind": 2, "importPath": "backend.plugins.agents.oldcode_style_converted_pep8", "description": "backend.plugins.agents.oldcode_style_converted_pep8", "peekOfCode": "def call_model(state: MessagesState, llm_with_tools, prompt_template):\n    \"\"\"LLM意思決定ノード：対話メッセージを読み取り、ツールがバインドされたLLMを呼び出す\"\"\"\n    try:\n        messages = state[\"messages\"]\n        # 最後のユーザーメッセージを取得\n        last_user_message = None\n        for msg in reversed(messages):\n            if msg.type == \"human\":\n                last_user_message = msg.content\n                break", "detail": "backend.plugins.agents.oldcode_style_converted_pep8", "documentation": {}}, {"label": "call_tools", "kind": 2, "importPath": "backend.plugins.agents.oldcode_style_converted_pep8", "description": "backend.plugins.agents.oldcode_style_converted_pep8", "peekOfCode": "def call_tools(state: MessagesState):\n    \"\"\"前のステップでLLMがツール呼び出しを要求したかどうかを判断\"\"\"\n    try:\n        last_message = state[\"messages\"][-1]\n        if last_message.tool_calls:\n            return \"tools\"\n        return END\n    except Exception as e:\n        raise RuntimeError(f\"ツール呼び出しの判断中にエラーが発生しました: {e}\")\n# ワークフロー構築", "detail": "backend.plugins.agents.oldcode_style_converted_pep8", "documentation": {}}, {"label": "build_workflow", "kind": 2, "importPath": "backend.plugins.agents.oldcode_style_converted_pep8", "description": "backend.plugins.agents.oldcode_style_converted_pep8", "peekOfCode": "def build_workflow():\n    \"\"\"LangGraph ワークフローを構築\"\"\"\n    llm_with_tools, tool_node = initialize_llm()\n    prompt_template = create_prompt_template()\n    workflow = StateGraph(MessagesState)\n    workflow.add_node(\"LLM\", lambda state: call_model(state, llm_with_tools, prompt_template))\n    workflow.add_node(\"tools\", tool_node)\n    workflow.add_edge(START, \"LLM\")\n    workflow.add_conditional_edges(\"LLM\", call_tools)\n    workflow.add_edge(\"tools\", \"LLM\")", "detail": "backend.plugins.agents.oldcode_style_converted_pep8", "documentation": {}}, {"label": "create_workflow", "kind": 2, "importPath": "backend.plugins.agents.workflow", "description": "backend.plugins.agents.workflow", "peekOfCode": "def create_workflow(\n    llm: Optional[BaseChatModel] = None,\n    model_name: str = \"qwen3:latest\",\n    temperature: float = 0,\n    custom_tools: Optional[Dict[str, List[BaseTool]]] = None\n) -> StateGraph:\n    \"\"\"\n    カスタマーアシスタンス用のマルチエージェントワークフローを作成します。\n    Args:\n        llm: 使用する言語モデル（Noneの場合、ChatOpenAIインスタンスを作成）", "detail": "backend.plugins.agents.workflow", "documentation": {}}, {"label": "add", "kind": 2, "importPath": "backend.plugins.mcp.server.mcp_server", "description": "backend.plugins.mcp.server.mcp_server", "peekOfCode": "def add(a: int, b: int) -> int:\n    \"\"\"\n    2つの数値を加算する\n    Args:\n        a (int): 1番目の数値\n        b (int): 2番目の数値\n    Returns:\n        int: 加算結果\n    \"\"\"\n    return a + b", "detail": "backend.plugins.mcp.server.mcp_server", "documentation": {}}, {"label": "multiply", "kind": 2, "importPath": "backend.plugins.mcp.server.mcp_server", "description": "backend.plugins.mcp.server.mcp_server", "peekOfCode": "def multiply(a: int, b: int) -> int:\n    \"\"\"\n    2つの数値を乗算する\n    Args:\n        a (int): 1番目の数値\n        b (int): 2番目の数値\n    Returns:\n        int: 乗算結果\n    \"\"\"\n    return a * b", "detail": "backend.plugins.mcp.server.mcp_server", "documentation": {}}, {"label": "mcp", "kind": 5, "importPath": "backend.plugins.mcp.server.mcp_server", "description": "backend.plugins.mcp.server.mcp_server", "peekOfCode": "mcp = FastMCP(\"数学工具\",\n    host=\"0.0.0.0\",\n    port=8005,  \n    sse_path=\"/sse\",\n    message_path=\"/message/\"\n          )\**********()\ndef add(a: int, b: int) -> int:\n    \"\"\"\n    2つの数値を加算する", "detail": "backend.plugins.mcp.server.mcp_server", "documentation": {}}, {"label": "get_client_by_param_type", "kind": 2, "importPath": "backend.plugins.mcp.client", "description": "backend.plugins.mcp.client", "peekOfCode": "def get_client_by_param_type(server_params):\n    if isinstance(server_params, StdioServerParameters):\n        print(\"\\n====== 👀 transport（デバッグ用） :stdio======\\n\")\n        return stdio_client(server_params)\n    elif isinstance(server_params, SSEServerParameters):\n        print(\"\\n====== 👀 transport（デバッグ用） :sse======\\n\")\n        return sse_client(**server_params.to_kwargs())\n    else:\n        raise ValueError(f\"未対応のserver_params型: {type(server_params)}\")\n# 💡 推理ログ出力用", "detail": "backend.plugins.mcp.client", "documentation": {}}, {"label": "extract_think_content", "kind": 2, "importPath": "backend.plugins.mcp.client", "description": "backend.plugins.mcp.client", "peekOfCode": "def extract_think_content(text):\n    match = re.search(r\"<think>(.*?)</think>\", text, re.DOTALL)\n    return match.group(1).strip() if match else None\n# 💡 最終回答から<think>を除去\ndef clean_final_answer(text):\n    # <think>...</think> を全部消す\n    return re.sub(r\"<think>.*?</think>\", \"\", text, flags=re.DOTALL).strip()\nasync def run_agent(server_params, model):\n    try:\n        async with get_client_by_param_type(server_params) as (read, write):", "detail": "backend.plugins.mcp.client", "documentation": {}}, {"label": "clean_final_answer", "kind": 2, "importPath": "backend.plugins.mcp.client", "description": "backend.plugins.mcp.client", "peekOfCode": "def clean_final_answer(text):\n    # <think>...</think> を全部消す\n    return re.sub(r\"<think>.*?</think>\", \"\", text, flags=re.DOTALL).strip()\nasync def run_agent(server_params, model):\n    try:\n        async with get_client_by_param_type(server_params) as (read, write):\n            async with ClientSession(read, write) as session:\n                print(\"セッション初期化完了\")  # デバッグログ\n                await session.initialize()\n                tools = await load_mcp_tools(session)", "detail": "backend.plugins.mcp.client", "documentation": {}}, {"label": "basic_url", "kind": 5, "importPath": "backend.plugins.mcp.client", "description": "backend.plugins.mcp.client", "peekOfCode": "basic_url = Config.OPENAI_BASE_URL\napi_key = os.getenv(\"OPENAI_API_KEY\")\nmcp_srv_name = \"server/mcp_server.py\"\n# 🧠 サーバーパラメータの型によりクライアントを自動選択\ndef get_client_by_param_type(server_params):\n    if isinstance(server_params, StdioServerParameters):\n        print(\"\\n====== 👀 transport（デバッグ用） :stdio======\\n\")\n        return stdio_client(server_params)\n    elif isinstance(server_params, SSEServerParameters):\n        print(\"\\n====== 👀 transport（デバッグ用） :sse======\\n\")", "detail": "backend.plugins.mcp.client", "documentation": {}}, {"label": "api_key", "kind": 5, "importPath": "backend.plugins.mcp.client", "description": "backend.plugins.mcp.client", "peekOfCode": "api_key = os.getenv(\"OPENAI_API_KEY\")\nmcp_srv_name = \"server/mcp_server.py\"\n# 🧠 サーバーパラメータの型によりクライアントを自動選択\ndef get_client_by_param_type(server_params):\n    if isinstance(server_params, StdioServerParameters):\n        print(\"\\n====== 👀 transport（デバッグ用） :stdio======\\n\")\n        return stdio_client(server_params)\n    elif isinstance(server_params, SSEServerParameters):\n        print(\"\\n====== 👀 transport（デバッグ用） :sse======\\n\")\n        return sse_client(**server_params.to_kwargs())", "detail": "backend.plugins.mcp.client", "documentation": {}}, {"label": "mcp_srv_name", "kind": 5, "importPath": "backend.plugins.mcp.client", "description": "backend.plugins.mcp.client", "peekOfCode": "mcp_srv_name = \"server/mcp_server.py\"\n# 🧠 サーバーパラメータの型によりクライアントを自動選択\ndef get_client_by_param_type(server_params):\n    if isinstance(server_params, StdioServerParameters):\n        print(\"\\n====== 👀 transport（デバッグ用） :stdio======\\n\")\n        return stdio_client(server_params)\n    elif isinstance(server_params, SSEServerParameters):\n        print(\"\\n====== 👀 transport（デバッグ用） :sse======\\n\")\n        return sse_client(**server_params.to_kwargs())\n    else:", "detail": "backend.plugins.mcp.client", "documentation": {}}, {"label": "SSEServerParameters", "kind": 6, "importPath": "backend.plugins.mcp.config", "description": "backend.plugins.mcp.config", "peekOfCode": "class SSEServerParameters:\n    def __init__(\n        self,\n        url: str,\n        headers: Optional[Dict[str, str]] = None,\n        timeout: Optional[int] = None,\n        sse_read_timeout: Optional[int] = None\n    ):\n        self.url = url  # 接続するSSEサーバーのURL\n        self.headers = headers  # HTTPヘッダー（省略可能）", "detail": "backend.plugins.mcp.config", "documentation": {}}, {"label": "Config", "kind": 6, "importPath": "backend.plugins.mcp.config", "description": "backend.plugins.mcp.config", "peekOfCode": "class Config:\n    OPENAI_API_KEY = os.getenv(\"OPENAI_API_KEY\", \"your_openai_api_key\")\n    POSTGRES_DB_URL = os.getenv(\"POSTGRES_DB_URL\", \"postgresql://mcp_user:mcp_password@localhost/mcp_db\")\n    CHROMA_COLLECTION_NAME = os.getenv(\"CHROMA_COLLECTION_NAME\", \"code_vectors_db\")\n    OPENAI_BASE_URL = os.getenv(\"OPENAI_BASE_URL\", \"https://api.openai.com/v1\")", "detail": "backend.plugins.mcp.config", "documentation": {}}, {"label": "extract_think", "kind": 2, "importPath": "backend.plugins.mcp.output_parser", "description": "backend.plugins.mcp.output_parser", "peekOfCode": "def extract_think(text: str) -> str:\n    \"\"\"<think> タグの中身だけを返す\"\"\"\n    match = re.search(r\"<think>(.*?)</think>\", text, re.DOTALL)\n    return match.group(1).strip() if match else \"\"\ndef strip_think(text: str) -> str:\n    \"\"\"<think> タグごと削除して、最終出力用に整形\"\"\"\n    return re.sub(r\"<think>.*?</think>\", \"\", text, flags=re.DOTALL).strip()\ndef parse_agent_result(result) -> dict:\n    \"\"\"\n    agent の出力を解析して、最終的な値・理由・thinking等を抽出。", "detail": "backend.plugins.mcp.output_parser", "documentation": {}}, {"label": "strip_think", "kind": 2, "importPath": "backend.plugins.mcp.output_parser", "description": "backend.plugins.mcp.output_parser", "peekOfCode": "def strip_think(text: str) -> str:\n    \"\"\"<think> タグごと削除して、最終出力用に整形\"\"\"\n    return re.sub(r\"<think>.*?</think>\", \"\", text, flags=re.DOTALL).strip()\ndef parse_agent_result(result) -> dict:\n    \"\"\"\n    agent の出力を解析して、最終的な値・理由・thinking等を抽出。\n    result が dict(messagesあり) でない場合、直接使う。\n    \"\"\"\n    # 📌 LangGraphのmessages構造なら処理する\n    if isinstance(result, dict) and \"messages\" in result:", "detail": "backend.plugins.mcp.output_parser", "documentation": {}}, {"label": "parse_agent_result", "kind": 2, "importPath": "backend.plugins.mcp.output_parser", "description": "backend.plugins.mcp.output_parser", "peekOfCode": "def parse_agent_result(result) -> dict:\n    \"\"\"\n    agent の出力を解析して、最終的な値・理由・thinking等を抽出。\n    result が dict(messagesあり) でない場合、直接使う。\n    \"\"\"\n    # 📌 LangGraphのmessages構造なら処理する\n    if isinstance(result, dict) and \"messages\" in result:\n        messages = result[\"messages\"]\n        user_question = None\n        reasoning_steps = []", "detail": "backend.plugins.mcp.output_parser", "documentation": {}}, {"label": "MCPToolRegistry", "kind": 6, "importPath": "backend.plugins.mcp.registry", "description": "backend.plugins.mcp.registry", "peekOfCode": "class MCPToolRegistry:\n    \"\"\"Registry for MCP tools.\"\"\"\n    _instance = None\n    def __new__(cls):\n        if cls._instance is None:\n            cls._instance = super(MCPToolRegistry, cls).__new__(cls)\n            cls._instance.tools = {}\n        return cls._instance\n    def register(self, tool: MCPTool) -> None:\n        \"\"\"", "detail": "backend.plugins.mcp.registry", "documentation": {}}, {"label": "register_tool", "kind": 2, "importPath": "backend.plugins.mcp.registry", "description": "backend.plugins.mcp.registry", "peekOfCode": "def register_tool(\n    name: str,\n    description: str,\n    func: Callable[[Dict[str, Any]], Union[Any, Awaitable[Any]]],\n    parameters: Optional[Dict[str, Any]] = None\n) -> MCPTool:\n    \"\"\"\n    Register a new tool.\n    Args:\n        name: The name of the tool", "detail": "backend.plugins.mcp.registry", "documentation": {}}, {"label": "get_tool", "kind": 2, "importPath": "backend.plugins.mcp.registry", "description": "backend.plugins.mcp.registry", "peekOfCode": "def get_tool(name: str) -> Optional[MCPTool]:\n    \"\"\"\n    Get a tool by name.\n    Args:\n        name: The name of the tool\n    Returns:\n        The tool, or None if not found\n    \"\"\"\n    return MCPToolRegistry().get(name)\ndef list_tools() -> List[str]:", "detail": "backend.plugins.mcp.registry", "documentation": {}}, {"label": "list_tools", "kind": 2, "importPath": "backend.plugins.mcp.registry", "description": "backend.plugins.mcp.registry", "peekOfCode": "def list_tools() -> List[str]:\n    \"\"\"\n    List all registered tools.\n    Returns:\n        A list of tool names\n    \"\"\"\n    return MCPToolRegistry().list()", "detail": "backend.plugins.mcp.registry", "documentation": {}}, {"label": "logger", "kind": 5, "importPath": "backend.plugins.mcp.registry", "description": "backend.plugins.mcp.registry", "peekOfCode": "logger = logging.getLogger(__name__)\nclass MCPToolRegistry:\n    \"\"\"Registry for MCP tools.\"\"\"\n    _instance = None\n    def __new__(cls):\n        if cls._instance is None:\n            cls._instance = super(MCPToolRegistry, cls).__new__(cls)\n            cls._instance.tools = {}\n        return cls._instance\n    def register(self, tool: MCPTool) -> None:", "detail": "backend.plugins.mcp.registry", "documentation": {}}, {"label": "MCPToolResult", "kind": 6, "importPath": "backend.plugins.mcp.tool", "description": "backend.plugins.mcp.tool", "peekOfCode": "class MCPToolResult(BaseModel):\n    \"\"\"Result of an MCP tool execution.\"\"\"\n    success: bool = True\n    result: Any = None\n    error: Optional[str] = None\nclass MCPTool:\n    \"\"\"Base class for MCP tools.\"\"\"\n    def __init__(\n        self,\n        name: str,", "detail": "backend.plugins.mcp.tool", "documentation": {}}, {"label": "MCPTool", "kind": 6, "importPath": "backend.plugins.mcp.tool", "description": "backend.plugins.mcp.tool", "peekOfCode": "class MCPTool:\n    \"\"\"Base class for MCP tools.\"\"\"\n    def __init__(\n        self,\n        name: str,\n        description: str,\n        func: Callable[[Dict[str, Any]], Union[Any, Awaitable[Any]]],\n        parameters: Optional[Dict[str, Any]] = None\n    ):\n        \"\"\"", "detail": "backend.plugins.mcp.tool", "documentation": {}}, {"label": "BaseVectorStore", "kind": 6, "importPath": "backend.plugins.rag.vector_stores.base", "description": "backend.plugins.rag.vector_stores.base", "peekOfCode": "class BaseVectorStore(abc.ABC):\n    \"\"\"\n    ベクトルストアの抽象基底クラス\n    すべてのベクトルストア実装はこのクラスを継承する必要があります\n    \"\"\"\n    def __init__(self, collection_name: str, embedding_model: EmbeddingModel):\n        \"\"\"\n        ベクトルストアの基底クラスを初期化します\n        Args:\n            collection_name: コレクション名", "detail": "backend.plugins.rag.vector_stores.base", "documentation": {}}, {"label": "logger", "kind": 5, "importPath": "backend.plugins.rag.vector_stores.base", "description": "backend.plugins.rag.vector_stores.base", "peekOfCode": "logger = logging.getLogger(__name__)\n# デフォルトのコレクション名\nDEFAULT_COLLECTION_NAME = \"documents\"\n# デフォルトのベクトルサイズ\nDEFAULT_VECTOR_SIZE = 768\nclass BaseVectorStore(abc.ABC):\n    \"\"\"\n    ベクトルストアの抽象基底クラス\n    すべてのベクトルストア実装はこのクラスを継承する必要があります\n    \"\"\"", "detail": "backend.plugins.rag.vector_stores.base", "documentation": {}}, {"label": "DEFAULT_COLLECTION_NAME", "kind": 5, "importPath": "backend.plugins.rag.vector_stores.base", "description": "backend.plugins.rag.vector_stores.base", "peekOfCode": "DEFAULT_COLLECTION_NAME = \"documents\"\n# デフォルトのベクトルサイズ\nDEFAULT_VECTOR_SIZE = 768\nclass BaseVectorStore(abc.ABC):\n    \"\"\"\n    ベクトルストアの抽象基底クラス\n    すべてのベクトルストア実装はこのクラスを継承する必要があります\n    \"\"\"\n    def __init__(self, collection_name: str, embedding_model: EmbeddingModel):\n        \"\"\"", "detail": "backend.plugins.rag.vector_stores.base", "documentation": {}}, {"label": "DEFAULT_VECTOR_SIZE", "kind": 5, "importPath": "backend.plugins.rag.vector_stores.base", "description": "backend.plugins.rag.vector_stores.base", "peekOfCode": "DEFAULT_VECTOR_SIZE = 768\nclass BaseVectorStore(abc.ABC):\n    \"\"\"\n    ベクトルストアの抽象基底クラス\n    すべてのベクトルストア実装はこのクラスを継承する必要があります\n    \"\"\"\n    def __init__(self, collection_name: str, embedding_model: EmbeddingModel):\n        \"\"\"\n        ベクトルストアの基底クラスを初期化します\n        Args:", "detail": "backend.plugins.rag.vector_stores.base", "documentation": {}}, {"label": "FAISSVectorStore", "kind": 6, "importPath": "backend.plugins.rag.vector_stores.faiss_store", "description": "backend.plugins.rag.vector_stores.faiss_store", "peekOfCode": "class FAISSVectorStore(BaseVectorStore):\n    \"\"\"\n    FAISSを使用したベクトルストア実装\n    \"\"\"\n    def __init__(self, collection_name: str, embedding_model: EmbeddingModel):\n        \"\"\"\n        FAISSベクトルストアを初期化します\n        Args:\n            collection_name: コレクション名\n            embedding_model: 埋め込みモデルのインスタンス", "detail": "backend.plugins.rag.vector_stores.faiss_store", "documentation": {}}, {"label": "logger", "kind": 5, "importPath": "backend.plugins.rag.vector_stores.faiss_store", "description": "backend.plugins.rag.vector_stores.faiss_store", "peekOfCode": "logger = logging.getLogger(__name__)\nclass FAISSVectorStore(BaseVectorStore):\n    \"\"\"\n    FAISSを使用したベクトルストア実装\n    \"\"\"\n    def __init__(self, collection_name: str, embedding_model: EmbeddingModel):\n        \"\"\"\n        FAISSベクトルストアを初期化します\n        Args:\n            collection_name: コレクション名", "detail": "backend.plugins.rag.vector_stores.faiss_store", "documentation": {}}, {"label": "QdrantVectorStore", "kind": 6, "importPath": "backend.plugins.rag.vector_stores.qdrant_store", "description": "backend.plugins.rag.vector_stores.qdrant_store", "peekOfCode": "class QdrantVectorStore(BaseVectorStore):\n    \"\"\"\n    Qdrantを使用したベクトルストア実装\n    \"\"\"\n    def __init__(self, collection_name: str, embedding_model: EmbeddingModel):\n        \"\"\"\n        Qdrantベクトルストアを初期化します\n        Args:\n            collection_name: コレクション名\n            embedding_model: 埋め込みモデルのインスタンス", "detail": "backend.plugins.rag.vector_stores.qdrant_store", "documentation": {}}, {"label": "logger", "kind": 5, "importPath": "backend.plugins.rag.vector_stores.qdrant_store", "description": "backend.plugins.rag.vector_stores.qdrant_store", "peekOfCode": "logger = logging.getLogger(__name__)\nclass QdrantVectorStore(BaseVectorStore):\n    \"\"\"\n    Qdrantを使用したベクトルストア実装\n    \"\"\"\n    def __init__(self, collection_name: str, embedding_model: EmbeddingModel):\n        \"\"\"\n        Qdrantベクトルストアを初期化します\n        Args:\n            collection_name: コレクション名", "detail": "backend.plugins.rag.vector_stores.qdrant_store", "documentation": {}}, {"label": "SupabaseVectorStore", "kind": 6, "importPath": "backend.plugins.rag.vector_stores.supabase_store", "description": "backend.plugins.rag.vector_stores.supabase_store", "peekOfCode": "class SupabaseVectorStore(BaseVectorStore):\n    \"\"\"\n    Supabase (pgvector) を使用したベクトルストア実装\n    \"\"\"\n    def __init__(self, collection_name: str, embedding_model: EmbeddingModel):\n        \"\"\"\n        Supabaseベクトルストアを初期化します\n        Args:\n            collection_name: コレクション名\n            embedding_model: 埋め込みモデルのインスタンス", "detail": "backend.plugins.rag.vector_stores.supabase_store", "documentation": {}}, {"label": "logger", "kind": 5, "importPath": "backend.plugins.rag.vector_stores.supabase_store", "description": "backend.plugins.rag.vector_stores.supabase_store", "peekOfCode": "logger = logging.getLogger(__name__)\ntry:\n    import vecs\n    VECS_AVAILABLE = True\nexcept ImportError:\n    logger.warning(\"vecsライブラリがインストールされていません。Supabaseベクトルストアは利用できません。\")\n    logger.warning(\"pip install vecs でインストールしてください。\")\n    VECS_AVAILABLE = False\nclass SupabaseVectorStore(BaseVectorStore):\n    \"\"\"", "detail": "backend.plugins.rag.vector_stores.supabase_store", "documentation": {}}, {"label": "WeaviateVectorStore", "kind": 6, "importPath": "backend.plugins.rag.vector_stores.weaviate_store", "description": "backend.plugins.rag.vector_stores.weaviate_store", "peekOfCode": "class WeaviateVectorStore(BaseVectorStore):\n    \"\"\"\n    Weaviateを使用したベクトルストア実装\n    \"\"\"\n    def __init__(self, collection_name: str, embedding_model: EmbeddingModel):\n        \"\"\"\n        Weaviateベクトルストアを初期化します\n        Args:\n            collection_name: コレクション名（Weaviateではクラス名として使用）\n            embedding_model: 埋め込みモデルのインスタンス", "detail": "backend.plugins.rag.vector_stores.weaviate_store", "documentation": {}}, {"label": "logger", "kind": 5, "importPath": "backend.plugins.rag.vector_stores.weaviate_store", "description": "backend.plugins.rag.vector_stores.weaviate_store", "peekOfCode": "logger = logging.getLogger(__name__)\nclass WeaviateVectorStore(BaseVectorStore):\n    \"\"\"\n    Weaviateを使用したベクトルストア実装\n    \"\"\"\n    def __init__(self, collection_name: str, embedding_model: EmbeddingModel):\n        \"\"\"\n        Weaviateベクトルストアを初期化します\n        Args:\n            collection_name: コレクション名（Weaviateではクラス名として使用）", "detail": "backend.plugins.rag.vector_stores.weaviate_store", "documentation": {}}, {"label": "DocumentLoader", "kind": 6, "importPath": "backend.plugins.rag.document_loader", "description": "backend.plugins.rag.document_loader", "peekOfCode": "class DocumentLoader:\n    \"\"\"\n    複数のファイル形式をサポートするドキュメントローダー\n    \"\"\"\n    # サポートされているファイル拡張子とローダーのマッピングをクラス変数として公開\n    FILE_LOADER_MAPPING = FILE_LOADER_MAPPING\n    def __init__(self, chunk_size: int = DEFAULT_CHUNK_SIZE, chunk_overlap: int = DEFAULT_CHUNK_OVERLAP,\n                 enable_text_processing: bool = True):\n        self.chunk_size = chunk_size\n        self.chunk_overlap = chunk_overlap", "detail": "backend.plugins.rag.document_loader", "documentation": {}}, {"label": "load_documents_from_file", "kind": 2, "importPath": "backend.plugins.rag.document_loader", "description": "backend.plugins.rag.document_loader", "peekOfCode": "def load_documents_from_file(file_path: str):\n    \"\"\"\n    単一のファイルを読み込む（後方互換性のため）\n    \"\"\"\n    loader = DocumentLoader()\n    return loader.load_document(file_path)", "detail": "backend.plugins.rag.document_loader", "documentation": {}}, {"label": "logger", "kind": 5, "importPath": "backend.plugins.rag.document_loader", "description": "backend.plugins.rag.document_loader", "peekOfCode": "logger = logging.getLogger(__name__)\n# サポートされているファイル拡張子とローダーのマッピング\nFILE_LOADER_MAPPING = {\n    \".txt\": TextLoader,\n    \".pdf\": PyPDFLoader,\n    \".csv\": CSVLoader,\n    \".json\": JSONLoader,\n    \".xlsx\": UnstructuredExcelLoader,\n    \".xls\": UnstructuredExcelLoader,\n    \".pptx\": UnstructuredPowerPointLoader,", "detail": "backend.plugins.rag.document_loader", "documentation": {}}, {"label": "FILE_LOADER_MAPPING", "kind": 5, "importPath": "backend.plugins.rag.document_loader", "description": "backend.plugins.rag.document_loader", "peekOfCode": "FILE_LOADER_MAPPING = {\n    \".txt\": TextLoader,\n    \".pdf\": PyPDFLoader,\n    \".csv\": CSVLoader,\n    \".json\": JSONLoader,\n    \".xlsx\": UnstructuredExcelLoader,\n    \".xls\": UnstructuredExcelLoader,\n    \".pptx\": UnstructuredPowerPointLoader,\n    \".ppt\": UnstructuredPowerPointLoader,\n    \".docx\": UnstructuredWordDocumentLoader,", "detail": "backend.plugins.rag.document_loader", "documentation": {}}, {"label": "DEFAULT_CHUNK_SIZE", "kind": 5, "importPath": "backend.plugins.rag.document_loader", "description": "backend.plugins.rag.document_loader", "peekOfCode": "DEFAULT_CHUNK_SIZE = 1000\nDEFAULT_CHUNK_OVERLAP = 200\nclass DocumentLoader:\n    \"\"\"\n    複数のファイル形式をサポートするドキュメントローダー\n    \"\"\"\n    # サポートされているファイル拡張子とローダーのマッピングをクラス変数として公開\n    FILE_LOADER_MAPPING = FILE_LOADER_MAPPING\n    def __init__(self, chunk_size: int = DEFAULT_CHUNK_SIZE, chunk_overlap: int = DEFAULT_CHUNK_OVERLAP,\n                 enable_text_processing: bool = True):", "detail": "backend.plugins.rag.document_loader", "documentation": {}}, {"label": "DEFAULT_CHUNK_OVERLAP", "kind": 5, "importPath": "backend.plugins.rag.document_loader", "description": "backend.plugins.rag.document_loader", "peekOfCode": "DEFAULT_CHUNK_OVERLAP = 200\nclass DocumentLoader:\n    \"\"\"\n    複数のファイル形式をサポートするドキュメントローダー\n    \"\"\"\n    # サポートされているファイル拡張子とローダーのマッピングをクラス変数として公開\n    FILE_LOADER_MAPPING = FILE_LOADER_MAPPING\n    def __init__(self, chunk_size: int = DEFAULT_CHUNK_SIZE, chunk_overlap: int = DEFAULT_CHUNK_OVERLAP,\n                 enable_text_processing: bool = True):\n        self.chunk_size = chunk_size", "detail": "backend.plugins.rag.document_loader", "documentation": {}}, {"label": "EmbeddingModel", "kind": 6, "importPath": "backend.plugins.rag.embeddings", "description": "backend.plugins.rag.embeddings", "peekOfCode": "class EmbeddingModel:\n    \"\"\"\n    テキストをベクトルに変換するための埋め込みモデルクラス\n    \"\"\"\n    def __init__(self, model_name: str = None):\n        \"\"\"\n        埋め込みモデルを初期化します\n        Args:\n            model_name: 使用する埋め込みモデルの名前。Noneの場合はデフォルトモデルを使用。\n        \"\"\"", "detail": "backend.plugins.rag.embeddings", "documentation": {}}, {"label": "logger", "kind": 5, "importPath": "backend.plugins.rag.embeddings", "description": "backend.plugins.rag.embeddings", "peekOfCode": "logger = logging.getLogger(__name__)\n# サポートされている埋め込みモデル\nSUPPORTED_EMBEDDING_MODELS = {\n    \"hotchpotch-japanese\":\"hotchpotch/static-embedding-japanese\",\n    \"instructor\": \"hkunlp/instructor-large\",\n    \"instructor-xl\": \"hkunlp/instructor-xl\",\n    \"instructor-base\": \"hkunlp/instructor-base\",\n    \"mpnet\": \"sentence-transformers/all-mpnet-base-v2\",\n    \"minilm\": \"sentence-transformers/all-MiniLM-L6-v2\",\n    \"openai\": \"text-embedding-3-small\",", "detail": "backend.plugins.rag.embeddings", "documentation": {}}, {"label": "SUPPORTED_EMBEDDING_MODELS", "kind": 5, "importPath": "backend.plugins.rag.embeddings", "description": "backend.plugins.rag.embeddings", "peekOfCode": "SUPPORTED_EMBEDDING_MODELS = {\n    \"hotchpotch-japanese\":\"hotchpotch/static-embedding-japanese\",\n    \"instructor\": \"hkunlp/instructor-large\",\n    \"instructor-xl\": \"hkunlp/instructor-xl\",\n    \"instructor-base\": \"hkunlp/instructor-base\",\n    \"mpnet\": \"sentence-transformers/all-mpnet-base-v2\",\n    \"minilm\": \"sentence-transformers/all-MiniLM-L6-v2\",\n    \"openai\": \"text-embedding-3-small\",\n    \"openai-ada\": \"text-embedding-ada-002\",\n}", "detail": "backend.plugins.rag.embeddings", "documentation": {}}, {"label": "DEFAULT_EMBEDDING_MODEL", "kind": 5, "importPath": "backend.plugins.rag.embeddings", "description": "backend.plugins.rag.embeddings", "peekOfCode": "DEFAULT_EMBEDDING_MODEL = \"hotchpotch-japanese\"\nclass EmbeddingModel:\n    \"\"\"\n    テキストをベクトルに変換するための埋め込みモデルクラス\n    \"\"\"\n    def __init__(self, model_name: str = None):\n        \"\"\"\n        埋め込みモデルを初期化します\n        Args:\n            model_name: 使用する埋め込みモデルの名前。Noneの場合はデフォルトモデルを使用。", "detail": "backend.plugins.rag.embeddings", "documentation": {}}, {"label": "project_root", "kind": 5, "importPath": "backend.plugins.rag.rag_sample_0_store", "description": "backend.plugins.rag.rag_sample_0_store", "peekOfCode": "project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), \"../../../\"))\nsys.path.insert(0, project_root)\nimport logging\nlogging.basicConfig(\n    level=logging.DEBUG,\n    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',\n    handlers=[logging.StreamHandler(sys.stdout)]\n)\ntry:\n    from .rag_service import RAGService", "detail": "backend.plugins.rag.rag_sample_0_store", "documentation": {}}, {"label": "main", "kind": 2, "importPath": "backend.plugins.rag.rag_sample_1_query", "description": "backend.plugins.rag.rag_sample_1_query", "peekOfCode": "def main():\n    print(\"RAG検索テスト開始\")\n    # RAGサービスを初期化\n    rag = RAGService(collection_name=\"my_collection_sample\")\n    # クエリを実行\n    query = \"会社の情報について教えてください\"\n    print(f\"クエリ: {query}\")\n    # 検索結果を取得\n    results = rag.query(query, top_k=3)\n    # 結果を表示", "detail": "backend.plugins.rag.rag_sample_1_query", "documentation": {}}, {"label": "project_root", "kind": 5, "importPath": "backend.plugins.rag.rag_sample_1_search1", "description": "backend.plugins.rag.rag_sample_1_search1", "peekOfCode": "project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), \"../../../\"))\nsys.path.insert(0, project_root)\nimport logging\nlogging.basicConfig(\n    level=logging.DEBUG,\n    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',\n    handlers=[logging.StreamHandler(sys.stdout)]\n)\ntry:\n    from .rag_service import RAGService", "detail": "backend.plugins.rag.rag_sample_1_search1", "documentation": {}}, {"label": "project_root", "kind": 5, "importPath": "backend.plugins.rag.rag_sample_1_search2", "description": "backend.plugins.rag.rag_sample_1_search2", "peekOfCode": "project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), \"../../../\"))\nsys.path.insert(0, project_root)\nimport logging\nlogging.basicConfig(\n    level=logging.DEBUG,\n    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',\n    handlers=[logging.StreamHandler(sys.stdout)]\n)\ntry:\n    from .rag_service import RAGService", "detail": "backend.plugins.rag.rag_sample_1_search2", "documentation": {}}, {"label": "rag", "kind": 5, "importPath": "backend.plugins.rag.rag_sample_feedback", "description": "backend.plugins.rag.rag_sample_feedback", "peekOfCode": "rag = RAGService(\n    collection_name=\"my_collection_sample\",\n    ranking_strategy=RankingStrategy.FEEDBACK_ENHANCED,\n    enable_reranking=True,\n    enable_feedback=True\n)\n# 初回のクエリ実行\nprint(\"初回クエリ実行:\")\nresult1 = rag.run_rag_with_feedback(\"SR会社の社長は誰ですか\")\nprint(f\"回答: {result1['result']}\")", "detail": "backend.plugins.rag.rag_sample_feedback", "documentation": {}}, {"label": "result1", "kind": 5, "importPath": "backend.plugins.rag.rag_sample_feedback", "description": "backend.plugins.rag.rag_sample_feedback", "peekOfCode": "result1 = rag.run_rag_with_feedback(\"SR会社の社長は誰ですか\")\nprint(f\"回答: {result1['result']}\")\n# ユーザーからのフィードバック記録（最初のドキュメントは役立った）\nif \"feedback_info\" in result1 and len(result1[\"feedback_info\"]) > 0:\n    doc_info = result1[\"feedback_info\"][0]\n    rag.record_feedback(doc_info[\"query\"], doc_info[\"doc_id\"], True)  # 役立った\n    print(f\"フィードバック記録: ドキュメント '{doc_info['doc_id']}' は役立った\")\n# 類似クエリの実行（フィードバックが反映されるはず）\nprint(\"\\n類似クエリ実行:\")\nresult2 = rag.run_rag_with_feedback(\"SR会社の代表者について教えてください\")", "detail": "backend.plugins.rag.rag_sample_feedback", "documentation": {}}, {"label": "result2", "kind": 5, "importPath": "backend.plugins.rag.rag_sample_feedback", "description": "backend.plugins.rag.rag_sample_feedback", "peekOfCode": "result2 = rag.run_rag_with_feedback(\"SR会社の代表者について教えてください\")\nprint(f\"回答: {result2['result']}\")\n# 別のクエリでも試してみる\nprint(\"\\n別のクエリ実行:\")\nresult3 = rag.run_rag_with_feedback(\"SR会社のCTOは誰ですか\")\nprint(f\"回答: {result3['result']}\")", "detail": "backend.plugins.rag.rag_sample_feedback", "documentation": {}}, {"label": "result3", "kind": 5, "importPath": "backend.plugins.rag.rag_sample_feedback", "description": "backend.plugins.rag.rag_sample_feedback", "peekOfCode": "result3 = rag.run_rag_with_feedback(\"SR会社のCTOは誰ですか\")\nprint(f\"回答: {result3['result']}\")", "detail": "backend.plugins.rag.rag_sample_feedback", "documentation": {}}, {"label": "RAGService", "kind": 6, "importPath": "backend.plugins.rag.rag_service", "description": "backend.plugins.rag.rag_service", "peekOfCode": "class RAGService:\n    \"\"\"\n    検索拡張生成（RAG）サービスクラス\n    \"\"\"\n    def __init__(self, collection_name: str = None, embedding_model_name: str = None,\n                 backend: str = None, recreate_collection: bool = False, directory_path: str = None,\n                 force_reimport: bool = False, ranking_strategy: RankingStrategy = DEFAULT_RANKING_STRATEGY,\n                 enable_reranking: bool = True, reranker_model: str = \"hotchpotch/japanese-reranker-cross-encoder-base-v1\"):\n        \"\"\"\n        RAGサービスを初期化します", "detail": "backend.plugins.rag.rag_service", "documentation": {}}, {"label": "logger", "kind": 5, "importPath": "backend.plugins.rag.rag_service", "description": "backend.plugins.rag.rag_service", "peekOfCode": "logger = logging.getLogger(__name__)\n# デフォルト設定\nDEFAULT_COLLECTION_NAME = \"documents\"\nDEFAULT_EMBEDDING_MODEL = \"hotchpotch-japanese\"\nDEFAULT_BACKEND = settings.DEFAULT_LLM_BACKEND or \"ollama\"\n# デフォルトのデータディレクトリは、このファイルと同じディレクトリの \"data\" フォルダ\nDEFAULT_DIRECTORY_PATH = os.path.join(os.path.dirname(__file__), \"data\")\n# デフォルトの再ランキング戦略\nDEFAULT_RANKING_STRATEGY = RankingStrategy.CROSS_ENCODER\nclass RAGService:", "detail": "backend.plugins.rag.rag_service", "documentation": {}}, {"label": "DEFAULT_COLLECTION_NAME", "kind": 5, "importPath": "backend.plugins.rag.rag_service", "description": "backend.plugins.rag.rag_service", "peekOfCode": "DEFAULT_COLLECTION_NAME = \"documents\"\nDEFAULT_EMBEDDING_MODEL = \"hotchpotch-japanese\"\nDEFAULT_BACKEND = settings.DEFAULT_LLM_BACKEND or \"ollama\"\n# デフォルトのデータディレクトリは、このファイルと同じディレクトリの \"data\" フォルダ\nDEFAULT_DIRECTORY_PATH = os.path.join(os.path.dirname(__file__), \"data\")\n# デフォルトの再ランキング戦略\nDEFAULT_RANKING_STRATEGY = RankingStrategy.CROSS_ENCODER\nclass RAGService:\n    \"\"\"\n    検索拡張生成（RAG）サービスクラス", "detail": "backend.plugins.rag.rag_service", "documentation": {}}, {"label": "DEFAULT_EMBEDDING_MODEL", "kind": 5, "importPath": "backend.plugins.rag.rag_service", "description": "backend.plugins.rag.rag_service", "peekOfCode": "DEFAULT_EMBEDDING_MODEL = \"hotchpotch-japanese\"\nDEFAULT_BACKEND = settings.DEFAULT_LLM_BACKEND or \"ollama\"\n# デフォルトのデータディレクトリは、このファイルと同じディレクトリの \"data\" フォルダ\nDEFAULT_DIRECTORY_PATH = os.path.join(os.path.dirname(__file__), \"data\")\n# デフォルトの再ランキング戦略\nDEFAULT_RANKING_STRATEGY = RankingStrategy.CROSS_ENCODER\nclass RAGService:\n    \"\"\"\n    検索拡張生成（RAG）サービスクラス\n    \"\"\"", "detail": "backend.plugins.rag.rag_service", "documentation": {}}, {"label": "DEFAULT_BACKEND", "kind": 5, "importPath": "backend.plugins.rag.rag_service", "description": "backend.plugins.rag.rag_service", "peekOfCode": "DEFAULT_BACKEND = settings.DEFAULT_LLM_BACKEND or \"ollama\"\n# デフォルトのデータディレクトリは、このファイルと同じディレクトリの \"data\" フォルダ\nDEFAULT_DIRECTORY_PATH = os.path.join(os.path.dirname(__file__), \"data\")\n# デフォルトの再ランキング戦略\nDEFAULT_RANKING_STRATEGY = RankingStrategy.CROSS_ENCODER\nclass RAGService:\n    \"\"\"\n    検索拡張生成（RAG）サービスクラス\n    \"\"\"\n    def __init__(self, collection_name: str = None, embedding_model_name: str = None,", "detail": "backend.plugins.rag.rag_service", "documentation": {}}, {"label": "DEFAULT_DIRECTORY_PATH", "kind": 5, "importPath": "backend.plugins.rag.rag_service", "description": "backend.plugins.rag.rag_service", "peekOfCode": "DEFAULT_DIRECTORY_PATH = os.path.join(os.path.dirname(__file__), \"data\")\n# デフォルトの再ランキング戦略\nDEFAULT_RANKING_STRATEGY = RankingStrategy.CROSS_ENCODER\nclass RAGService:\n    \"\"\"\n    検索拡張生成（RAG）サービスクラス\n    \"\"\"\n    def __init__(self, collection_name: str = None, embedding_model_name: str = None,\n                 backend: str = None, recreate_collection: bool = False, directory_path: str = None,\n                 force_reimport: bool = False, ranking_strategy: RankingStrategy = DEFAULT_RANKING_STRATEGY,", "detail": "backend.plugins.rag.rag_service", "documentation": {}}, {"label": "DEFAULT_RANKING_STRATEGY", "kind": 5, "importPath": "backend.plugins.rag.rag_service", "description": "backend.plugins.rag.rag_service", "peekOfCode": "DEFAULT_RANKING_STRATEGY = RankingStrategy.CROSS_ENCODER\nclass RAGService:\n    \"\"\"\n    検索拡張生成（RAG）サービスクラス\n    \"\"\"\n    def __init__(self, collection_name: str = None, embedding_model_name: str = None,\n                 backend: str = None, recreate_collection: bool = False, directory_path: str = None,\n                 force_reimport: bool = False, ranking_strategy: RankingStrategy = DEFAULT_RANKING_STRATEGY,\n                 enable_reranking: bool = True, reranker_model: str = \"hotchpotch/japanese-reranker-cross-encoder-base-v1\"):\n        \"\"\"", "detail": "backend.plugins.rag.rag_service", "documentation": {}}, {"label": "FeedbackType", "kind": 6, "importPath": "backend.plugins.rag.reranker", "description": "backend.plugins.rag.reranker", "peekOfCode": "class FeedbackType(Enum):\n    POSITIVE = \"positive\"  # 良い回答だったというフィードバック\n    NEGATIVE = \"negative\"  # 悪い回答だったというフィードバック\n    NEUTRAL = \"neutral\"    # 中立的なフィードバック\nclass RankingStrategy(Enum):\n    \"\"\"\n    ランキング戦略の列挙型\n    \"\"\"\n    CROSS_ENCODER = \"cross_encoder\"  # クロスエンコーダーによる再ランキング（デフォルト）\n    VECTOR_SCORE = \"vector_score\"    # ベクトル検索スコアのみ", "detail": "backend.plugins.rag.reranker", "documentation": {}}, {"label": "RankingStrategy", "kind": 6, "importPath": "backend.plugins.rag.reranker", "description": "backend.plugins.rag.reranker", "peekOfCode": "class RankingStrategy(Enum):\n    \"\"\"\n    ランキング戦略の列挙型\n    \"\"\"\n    CROSS_ENCODER = \"cross_encoder\"  # クロスエンコーダーによる再ランキング（デフォルト）\n    VECTOR_SCORE = \"vector_score\"    # ベクトル検索スコアのみ\n    CUSTOM = \"custom\"                # カスタム戦略（関数を指定）\n    FEEDBACK_ENHANCED = \"feedback_enhanced\"  # フィードバックを活用した再ランキング\nclass Reranker:\n    \"\"\"", "detail": "backend.plugins.rag.reranker", "documentation": {}}, {"label": "<PERSON><PERSON><PERSON>", "kind": 6, "importPath": "backend.plugins.rag.reranker", "description": "backend.plugins.rag.reranker", "peekOfCode": "class Reranker:\n    \"\"\"\n    検索結果の再ランキングを行うクラス\n    langchain v0.3.23 の CrossEncoderReranker を使用\n    \"\"\"\n    def __init__(self,\n                 strategy: RankingStrategy = RankingStrategy.CROSS_ENCODER,\n                 custom_ranking_func: Optional[Callable] = None,\n                 model_name: str = \"hotchpotch/japanese-reranker-cross-encoder-base-v1\",\n                 top_k: int = 5,", "detail": "backend.plugins.rag.reranker", "documentation": {}}, {"label": "logger", "kind": 5, "importPath": "backend.plugins.rag.reranker", "description": "backend.plugins.rag.reranker", "peekOfCode": "logger = logging.getLogger(__name__)\n# フィードバックループのための新しい列挙型\nclass FeedbackType(Enum):\n    POSITIVE = \"positive\"  # 良い回答だったというフィードバック\n    NEGATIVE = \"negative\"  # 悪い回答だったというフィードバック\n    NEUTRAL = \"neutral\"    # 中立的なフィードバック\nclass RankingStrategy(Enum):\n    \"\"\"\n    ランキング戦略の列挙型\n    \"\"\"", "detail": "backend.plugins.rag.reranker", "documentation": {}}, {"label": "TextProcessor", "kind": 6, "importPath": "backend.plugins.rag.text_processor", "description": "backend.plugins.rag.text_processor", "peekOfCode": "class TextProcessor:\n    \"\"\"\n    テキスト前処理クラス\n    ベクトル検索の精度を向上させるためのテキスト処理機能を提供します\n    \"\"\"\n    # 言語コード定数\n    LANG_JAPANESE = \"ja\"\n    LANG_ENGLISH = \"en\"\n    LANG_CHINESE = \"zh\"\n    LANG_UNKNOWN = \"unknown\"", "detail": "backend.plugins.rag.text_processor", "documentation": {}}, {"label": "logger", "kind": 5, "importPath": "backend.plugins.rag.text_processor", "description": "backend.plugins.rag.text_processor", "peekOfCode": "logger = logging.getLogger(__name__)\nclass TextProcessor:\n    \"\"\"\n    テキスト前処理クラス\n    ベクトル検索の精度を向上させるためのテキスト処理機能を提供します\n    \"\"\"\n    # 言語コード定数\n    LANG_JAPANESE = \"ja\"\n    LANG_ENGLISH = \"en\"\n    LANG_CHINESE = \"zh\"", "detail": "backend.plugins.rag.text_processor", "documentation": {}}, {"label": "VectorStoreFactory", "kind": 6, "importPath": "backend.plugins.rag.vector_factory", "description": "backend.plugins.rag.vector_factory", "peekOfCode": "class VectorStoreFactory:\n    \"\"\"\n    ベクトルストアのファクトリークラス\n    設定に基づいて適切なベクトルストアを作成します\n    工場パターンの正しい実装：\n    - 具体的な実装の詳細を隠蔽\n    - 抽象インターフェースに依存\n    - 新しい実装の追加が容易\n    \"\"\"\n    # 利用可能なベクトルストアタイプ", "detail": "backend.plugins.rag.vector_factory", "documentation": {}}, {"label": "logger", "kind": 5, "importPath": "backend.plugins.rag.vector_factory", "description": "backend.plugins.rag.vector_factory", "peekOfCode": "logger = logging.getLogger(__name__)\nclass VectorStoreFactory:\n    \"\"\"\n    ベクトルストアのファクトリークラス\n    設定に基づいて適切なベクトルストアを作成します\n    工場パターンの正しい実装：\n    - 具体的な実装の詳細を隠蔽\n    - 抽象インターフェースに依存\n    - 新しい実装の追加が容易\n    \"\"\"", "detail": "backend.plugins.rag.vector_factory", "documentation": {}}, {"label": "VectorStore", "kind": 6, "importPath": "backend.plugins.rag.vector_store", "description": "backend.plugins.rag.vector_store", "peekOfCode": "class VectorStore:\n    \"\"\"\n    ベクトルストアアダプタークラス\n    異なるベクトルストア実装に対して統一されたインターフェースを提供します\n    \"\"\"\n    def __init__(self, collection_name: str = DEFAULT_COLLECTION_NAME, model_name: str = None, vector_store_type: str = None):\n        \"\"\"\n        ベクトルストアアダプターを初期化します\n        Args:\n            collection_name: コレクション名", "detail": "backend.plugins.rag.vector_store", "documentation": {}}, {"label": "with_fallback", "kind": 2, "importPath": "backend.plugins.rag.vector_store", "description": "backend.plugins.rag.vector_store", "peekOfCode": "def with_fallback(fallback_type: str = None):\n    \"\"\"\n    操作が失敗した場合にエラーを記録するデコレータ\n    注意: 以前はフォールバックを提供していましたが、現在は単にエラーを記録するだけです\n    Args:\n        fallback_type: 互換性のために残されていますが、使用されません\n    \"\"\"\n    def decorator(func: Callable):\n        @functools.wraps(func)\n        def wrapper(self, *args, **kwargs):", "detail": "backend.plugins.rag.vector_store", "documentation": {}}, {"label": "logger", "kind": 5, "importPath": "backend.plugins.rag.vector_store", "description": "backend.plugins.rag.vector_store", "peekOfCode": "logger = logging.getLogger(__name__)\ndef with_fallback(fallback_type: str = None):\n    \"\"\"\n    操作が失敗した場合にエラーを記録するデコレータ\n    注意: 以前はフォールバックを提供していましたが、現在は単にエラーを記録するだけです\n    Args:\n        fallback_type: 互換性のために残されていますが、使用されません\n    \"\"\"\n    def decorator(func: Callable):\n        @functools.wraps(func)", "detail": "backend.plugins.rag.vector_store", "documentation": {}}, {"label": "__all__", "kind": 5, "importPath": "backend.plugins.rag.vector_store", "description": "backend.plugins.rag.vector_store", "peekOfCode": "__all__ = [\n    'VectorStore',\n    'BaseVectorStore',\n    'DEFAULT_COLLECTION_NAME',\n    'DEFAULT_VECTOR_SIZE'\n]", "detail": "backend.plugins.rag.vector_store", "documentation": {}}, {"label": "SimpleAgentPlugin", "kind": 6, "importPath": "backend.plugins.samples.agent_plugin", "description": "backend.plugins.samples.agent_plugin", "peekOfCode": "class SimpleAgentPlugin(AgentPlugin):\n    \"\"\"\n    A simple agent plugin that implements a basic ReAct agent.\n    This is a sample implementation that demonstrates how to create an agent plugin.\n    In a real implementation, you would implement a more sophisticated agent.\n    \"\"\"\n    def __init__(self, config: Optional[Dict[str, Any]] = None):\n        \"\"\"Initialize the plugin with optional configuration.\"\"\"\n        super().__init__(config)\n        self.id = \"simple_agent\"", "detail": "backend.plugins.samples.agent_plugin", "documentation": {}}, {"label": "logger", "kind": 5, "importPath": "backend.plugins.samples.agent_plugin", "description": "backend.plugins.samples.agent_plugin", "peekOfCode": "logger = logging.getLogger(__name__)\nclass SimpleAgentPlugin(AgentPlugin):\n    \"\"\"\n    A simple agent plugin that implements a basic ReAct agent.\n    This is a sample implementation that demonstrates how to create an agent plugin.\n    In a real implementation, you would implement a more sophisticated agent.\n    \"\"\"\n    def __init__(self, config: Optional[Dict[str, Any]] = None):\n        \"\"\"Initialize the plugin with optional configuration.\"\"\"\n        super().__init__(config)", "detail": "backend.plugins.samples.agent_plugin", "documentation": {}}, {"label": "LangChainMCPPlugin", "kind": 6, "importPath": "backend.plugins.samples.langchain_mcp_plugin", "description": "backend.plugins.samples.langchain_mcp_plugin", "peekOfCode": "class LangChainMCPPlugin(MCPPlugin):\n    \"\"\"LangChain MCP plugin implementation.\n    This plugin uses LangChain's tools and agents to implement the MCP protocol.\n    It provides a more robust and standardized implementation compared to the\n    simple MCP plugin.\n    \"\"\"\n    def __init__(self, config: Optional[Dict[str, Any]] = None):\n        super().__init__(config)\n        self.id = \"langchain_mcp\"\n        self.name = \"LangChain MCP Plugin\"", "detail": "backend.plugins.samples.langchain_mcp_plugin", "documentation": {}}, {"label": "logger", "kind": 5, "importPath": "backend.plugins.samples.langchain_mcp_plugin", "description": "backend.plugins.samples.langchain_mcp_plugin", "peekOfCode": "logger = logging.getLogger(__name__)\nclass LangChainMCPPlugin(MCPPlugin):\n    \"\"\"LangChain MCP plugin implementation.\n    This plugin uses LangChain's tools and agents to implement the MCP protocol.\n    It provides a more robust and standardized implementation compared to the\n    simple MCP plugin.\n    \"\"\"\n    def __init__(self, config: Optional[Dict[str, Any]] = None):\n        super().__init__(config)\n        self.id = \"langchain_mcp\"", "detail": "backend.plugins.samples.langchain_mcp_plugin", "documentation": {}}, {"label": "SimpleRAGPlugin", "kind": 6, "importPath": "backend.plugins.samples.rag_plugin", "description": "backend.plugins.samples.rag_plugin", "peekOfCode": "class SimpleRAGPlugin(RAGPlugin):\n    \"\"\"\n    A simple RAG plugin that adds retrieved documents to the system message.\n    This is a sample implementation that demonstrates how to create a RAG plugin.\n    In a real implementation, you would connect to a vector database like Qdrant\n    to retrieve relevant documents.\n    \"\"\"\n    def __init__(self, config: Optional[Dict[str, Any]] = None):\n        \"\"\"Initialize the plugin with optional configuration.\"\"\"\n        super().__init__(config)", "detail": "backend.plugins.samples.rag_plugin", "documentation": {}}, {"label": "logger", "kind": 5, "importPath": "backend.plugins.samples.rag_plugin", "description": "backend.plugins.samples.rag_plugin", "peekOfCode": "logger = logging.getLogger(__name__)\nclass SimpleRAGPlugin(RAGPlugin):\n    \"\"\"\n    A simple RAG plugin that adds retrieved documents to the system message.\n    This is a sample implementation that demonstrates how to create a RAG plugin.\n    In a real implementation, you would connect to a vector database like Qdrant\n    to retrieve relevant documents.\n    \"\"\"\n    def __init__(self, config: Optional[Dict[str, Any]] = None):\n        \"\"\"Initialize the plugin with optional configuration.\"\"\"", "detail": "backend.plugins.samples.rag_plugin", "documentation": {}}, {"label": "SimpleMCPPlugin", "kind": 6, "importPath": "backend.plugins.samples.simple_mcp_plugin", "description": "backend.plugins.samples.simple_mcp_plugin", "peekOfCode": "class SimpleMCPPlugin(MCPPlugin):\n    \"\"\"Simple MCP plugin implementation.\"\"\"\n    def __init__(self, config: Optional[Dict[str, Any]] = None):\n        super().__init__(config)\n        self.id = \"simple_mcp\"\n        self.name = \"Simple MCP Plugin\"\n        self.description = \"A simple MCP plugin that demonstrates the MCP protocol\"\n        self.version = \"0.1.0\"\n        self.author = \"LLM Platform Team\"\n        # Register MCP actions", "detail": "backend.plugins.samples.simple_mcp_plugin", "documentation": {}}, {"label": "logger", "kind": 5, "importPath": "backend.plugins.samples.simple_mcp_plugin", "description": "backend.plugins.samples.simple_mcp_plugin", "peekOfCode": "logger = logging.getLogger(__name__)\nclass SimpleMCPPlugin(MCPPlugin):\n    \"\"\"Simple MCP plugin implementation.\"\"\"\n    def __init__(self, config: Optional[Dict[str, Any]] = None):\n        super().__init__(config)\n        self.id = \"simple_mcp\"\n        self.name = \"Simple MCP Plugin\"\n        self.description = \"A simple MCP plugin that demonstrates the MCP protocol\"\n        self.version = \"0.1.0\"\n        self.author = \"LLM Platform Team\"", "detail": "backend.plugins.samples.simple_mcp_plugin", "documentation": {}}, {"label": "BasePlugin", "kind": 6, "importPath": "backend.plugins.base", "description": "backend.plugins.base", "peekOfCode": "class BasePlugin(ABC):\n    \"\"\"Base class for all plugins.\"\"\"\n    # Class variables\n    plugin_type: ClassVar[PluginType]\n    def __init__(self, config: Optional[Dict[str, Any]] = None):\n        \"\"\"Initialize the plugin with optional configuration.\"\"\"\n        self.config = config or {}\n        self.id = self.__class__.__name__.lower()\n        self.name = self.id\n        self.description = \"Base plugin\"", "detail": "backend.plugins.base", "documentation": {}}, {"label": "PreprocessorPlugin", "kind": 6, "importPath": "backend.plugins.base", "description": "backend.plugins.base", "peekOfCode": "class PreprocessorPlugin(BasePlugin):\n    \"\"\"Plugin that processes messages before they are sent to the LLM.\"\"\"\n    plugin_type = PluginType.PREPROCESSOR\n    def __init__(self, config: Optional[Dict[str, Any]] = None):\n        super().__init__(config)\n        self.description = \"Preprocessor plugin\"\n    @abstractmethod\n    async def process(self, messages: List[Message], **kwargs) -> PluginResult:\n        \"\"\"Process messages before they are sent to the LLM.\"\"\"\n        pass", "detail": "backend.plugins.base", "documentation": {}}, {"label": "PostprocessorPlugin", "kind": 6, "importPath": "backend.plugins.base", "description": "backend.plugins.base", "peekOfCode": "class PostprocessorPlugin(BasePlugin):\n    \"\"\"Plugin that processes messages after they are received from the LLM.\"\"\"\n    plugin_type = PluginType.POSTPROCESSOR\n    def __init__(self, config: Optional[Dict[str, Any]] = None):\n        super().__init__(config)\n        self.description = \"Postprocessor plugin\"\n    @abstractmethod\n    async def process(self, messages: List[Message], llm_response: Message, **kwargs) -> PluginResult:\n        \"\"\"Process messages after they are received from the LLM.\"\"\"\n        pass", "detail": "backend.plugins.base", "documentation": {}}, {"label": "CommandPlugin", "kind": 6, "importPath": "backend.plugins.base", "description": "backend.plugins.base", "peekOfCode": "class CommandPlugin(BasePlugin):\n    \"\"\"Plugin that adds custom commands to the chat interface.\"\"\"\n    plugin_type = PluginType.COMMAND\n    def __init__(self, config: Optional[Dict[str, Any]] = None):\n        super().__init__(config)\n        self.description = \"Command plugin\"\n        self.command_prefix = self.config.get(\"command_prefix\", \"/\")\n        self.commands = {}\n    def register_command(self, name: str, handler, description: str = \"\") -> None:\n        \"\"\"Register a command.\"\"\"", "detail": "backend.plugins.base", "documentation": {}}, {"label": "ToolPlugin", "kind": 6, "importPath": "backend.plugins.base", "description": "backend.plugins.base", "peekOfCode": "class ToolPlugin(BasePlugin):\n    \"\"\"Plugin that implements tools for LLM function/tool calling.\"\"\"\n    plugin_type = PluginType.TOOL\n    def __init__(self, config: Optional[Dict[str, Any]] = None):\n        super().__init__(config)\n        self.description = \"Tool plugin\"\n        self.tools = {}\n    def register_tool(self, name: str, handler, description: str, parameters: Dict[str, Any], required: List[str] = None) -> None:\n        \"\"\"Register a tool.\"\"\"\n        self.tools[name] = {", "detail": "backend.plugins.base", "documentation": {}}, {"label": "UIPlugin", "kind": 6, "importPath": "backend.plugins.base", "description": "backend.plugins.base", "peekOfCode": "class UIPlugin(BasePlugin):\n    \"\"\"Plugin that adds custom UI components to the chat interface.\"\"\"\n    plugin_type = PluginType.UI\n    def __init__(self, config: Optional[Dict[str, Any]] = None):\n        super().__init__(config)\n        self.description = \"UI plugin\"\n    @abstractmethod\n    def get_components(self) -> List[UIComponent]:\n        \"\"\"Get UI components for frontend rendering.\"\"\"\n        pass", "detail": "backend.plugins.base", "documentation": {}}, {"label": "AgentPlugin", "kind": 6, "importPath": "backend.plugins.base", "description": "backend.plugins.base", "peekOfCode": "class AgentPlugin(BasePlugin):\n    \"\"\"Plugin that implements an agent for the chat interface.\"\"\"\n    plugin_type = PluginType.AGENT\n    def __init__(self, config: Optional[Dict[str, Any]] = None):\n        super().__init__(config)\n        self.description = \"Agent plugin\"\n    @abstractmethod\n    async def process(self, messages: List[Message], **kwargs) -> PluginResult:\n        \"\"\"Process messages with the agent.\"\"\"\n        pass", "detail": "backend.plugins.base", "documentation": {}}, {"label": "RAGPlugin", "kind": 6, "importPath": "backend.plugins.base", "description": "backend.plugins.base", "peekOfCode": "class RAGPlugin(BasePlugin):\n    \"\"\"Plugin that implements RAG (Retrieval Augmented Generation) for the chat interface.\"\"\"\n    plugin_type = PluginType.RAG\n    def __init__(self, config: Optional[Dict[str, Any]] = None):\n        super().__init__(config)\n        self.description = \"RAG plugin\"\n    @abstractmethod\n    async def retrieve(self, query: str, **kwargs) -> List[Dict[str, Any]]:\n        \"\"\"Retrieve relevant documents for a query.\"\"\"\n        pass", "detail": "backend.plugins.base", "documentation": {}}, {"label": "MCPPlugin", "kind": 6, "importPath": "backend.plugins.base", "description": "backend.plugins.base", "peekOfCode": "class MCPPlugin(BasePlugin):\n    \"\"\"Plugin that implements Model Control Protocol (MCP) for the chat interface.\n    MCP is a standardized protocol that allows LLMs to interact with external tools and services.\n    It provides a structured way for models to call external tools, perform complex reasoning,\n    manage multi-turn conversations, and handle structured data.\n    \"\"\"\n    plugin_type = PluginType.MCP\n    def __init__(self, config: Optional[Dict[str, Any]] = None):\n        super().__init__(config)\n        self.description = \"MCP plugin\"", "detail": "backend.plugins.base", "documentation": {}}, {"label": "logger", "kind": 5, "importPath": "backend.plugins.base", "description": "backend.plugins.base", "peekOfCode": "logger = logging.getLogger(__name__)\nclass BasePlugin(ABC):\n    \"\"\"Base class for all plugins.\"\"\"\n    # Class variables\n    plugin_type: ClassVar[PluginType]\n    def __init__(self, config: Optional[Dict[str, Any]] = None):\n        \"\"\"Initialize the plugin with optional configuration.\"\"\"\n        self.config = config or {}\n        self.id = self.__class__.__name__.lower()\n        self.name = self.id", "detail": "backend.plugins.base", "documentation": {}}, {"label": "get_tool_definitions", "kind": 2, "importPath": "backend.plugins.chat_integration", "description": "backend.plugins.chat_integration", "peekOfCode": "def get_tool_definitions() -> List[Dict[str, Any]]:\n    \"\"\"\n    Get all tool definitions for LLM function/tool calling.\n    Returns:\n        List of tool definitions\n    \"\"\"\n    if not plugin_manager.enabled:\n        return []\n    try:\n        return plugin_manager.get_tool_definitions()", "detail": "backend.plugins.chat_integration", "documentation": {}}, {"label": "logger", "kind": 5, "importPath": "backend.plugins.chat_integration", "description": "backend.plugins.chat_integration", "peekOfCode": "logger = logging.getLogger(__name__)\nasync def process_with_plugins(messages: List[Dict[str, Any]], **kwargs) -> List[Dict[str, Any]]:\n    \"\"\"\n    Process messages with all registered plugins.\n    This function is called before sending messages to the LLM.\n    It runs the messages through all preprocessor plugins, command plugins, agent plugins, and RAG plugins.\n    Args:\n        messages: List of messages to process\n        **kwargs: Additional arguments to pass to plugins\n    Returns:", "detail": "backend.plugins.chat_integration", "documentation": {}}, {"label": "Plugin<PERSON>anager", "kind": 6, "importPath": "backend.plugins.manager", "description": "backend.plugins.manager", "peekOfCode": "class PluginManager:\n    \"\"\"Manager for loading and managing plugins.\"\"\"\n    def __init__(self):\n        \"\"\"Initialize the plugin manager.\"\"\"\n        self.plugins: Dict[str, BasePlugin] = {}\n        self.plugin_classes: Dict[str, Type[BasePlugin]] = {}\n        self.enabled = True\n    def register_plugin(self, plugin_id: str, plugin_class: Type[BasePlugin], config: Optional[Dict[str, Any]] = None) -> BasePlugin:\n        \"\"\"Register a plugin with the manager.\"\"\"\n        if plugin_id in self.plugins:", "detail": "backend.plugins.manager", "documentation": {}}, {"label": "logger", "kind": 5, "importPath": "backend.plugins.manager", "description": "backend.plugins.manager", "peekOfCode": "logger = logging.getLogger(__name__)\nT = TypeVar('T', bound=BasePlugin)\nclass PluginManager:\n    \"\"\"Manager for loading and managing plugins.\"\"\"\n    def __init__(self):\n        \"\"\"Initialize the plugin manager.\"\"\"\n        self.plugins: Dict[str, BasePlugin] = {}\n        self.plugin_classes: Dict[str, Type[BasePlugin]] = {}\n        self.enabled = True\n    def register_plugin(self, plugin_id: str, plugin_class: Type[BasePlugin], config: Optional[Dict[str, Any]] = None) -> BasePlugin:", "detail": "backend.plugins.manager", "documentation": {}}, {"label": "T", "kind": 5, "importPath": "backend.plugins.manager", "description": "backend.plugins.manager", "peekOfCode": "T = TypeVar('T', bound=BasePlugin)\nclass PluginManager:\n    \"\"\"Manager for loading and managing plugins.\"\"\"\n    def __init__(self):\n        \"\"\"Initialize the plugin manager.\"\"\"\n        self.plugins: Dict[str, BasePlugin] = {}\n        self.plugin_classes: Dict[str, Type[BasePlugin]] = {}\n        self.enabled = True\n    def register_plugin(self, plugin_id: str, plugin_class: Type[BasePlugin], config: Optional[Dict[str, Any]] = None) -> BasePlugin:\n        \"\"\"Register a plugin with the manager.\"\"\"", "detail": "backend.plugins.manager", "documentation": {}}, {"label": "plugin_manager", "kind": 5, "importPath": "backend.plugins.manager", "description": "backend.plugins.manager", "peekOfCode": "plugin_manager = PluginManager()", "detail": "backend.plugins.manager", "documentation": {}}, {"label": "PluginConfigRequest", "kind": 6, "importPath": "backend.plugins.router", "description": "backend.plugins.router", "peekOfCode": "class PluginConfigRequest(BaseModel):\n    \"\"\"Request model for plugin configuration.\"\"\"\n    config: Dict[str, Any]\************(\"/\", response_model=List[PluginInfo])\nasync def list_plugins(current_user = Depends(get_current_user)):\n    \"\"\"List all registered plugins.\"\"\"\n    return plugin_manager.get_all_plugin_info()\************(\"/status\")\nasync def get_plugin_system_status(current_user = Depends(get_current_user)):\n    \"\"\"Get the status of the plugin system.\"\"\"", "detail": "backend.plugins.router", "documentation": {}}, {"label": "router", "kind": 5, "importPath": "backend.plugins.router", "description": "backend.plugins.router", "peekOfCode": "router = APIRouter(prefix=\"/plugins\", tags=[\"Plugins APIs\"])\n# ログ記録用のロガーを設定\nlogger = logging.getLogger(\"plugins.router\")\nclass PluginConfigRequest(BaseModel):\n    \"\"\"Request model for plugin configuration.\"\"\"\n    config: Dict[str, Any]\************(\"/\", response_model=List[PluginInfo])\nasync def list_plugins(current_user = Depends(get_current_user)):\n    \"\"\"List all registered plugins.\"\"\"\n    return plugin_manager.get_all_plugin_info()", "detail": "backend.plugins.router", "documentation": {}}, {"label": "logger", "kind": 5, "importPath": "backend.plugins.router", "description": "backend.plugins.router", "peekOfCode": "logger = logging.getLogger(\"plugins.router\")\nclass PluginConfigRequest(BaseModel):\n    \"\"\"Request model for plugin configuration.\"\"\"\n    config: Dict[str, Any]\************(\"/\", response_model=List[PluginInfo])\nasync def list_plugins(current_user = Depends(get_current_user)):\n    \"\"\"List all registered plugins.\"\"\"\n    return plugin_manager.get_all_plugin_info()\************(\"/status\")\nasync def get_plugin_system_status(current_user = Depends(get_current_user)):", "detail": "backend.plugins.router", "documentation": {}}, {"label": "PluginType", "kind": 6, "importPath": "backend.plugins.types", "description": "backend.plugins.types", "peekOfCode": "class PluginType(str, Enum):\n    \"\"\"Plugin types enumeration.\"\"\"\n    PREPROCESSOR = \"preprocessor\"\n    POSTPROCESSOR = \"postprocessor\"\n    COMMAND = \"command\"\n    TOOL = \"tool\"\n    UI = \"ui\"\n    AGENT = \"agent\"\n    RAG = \"rag\"\n    MCP = \"mcp\"", "detail": "backend.plugins.types", "documentation": {}}, {"label": "MessageRequired", "kind": 6, "importPath": "backend.plugins.types", "description": "backend.plugins.types", "peekOfCode": "class MessageRequired(TypedDict):\n    \"\"\"Required fields for chat message.\"\"\"\n    role: str\n    content: str\nclass Message(MessageRequired, total=False):\n    \"\"\"Chat message type with optional fields.\"\"\"\n    name: str\n    function_call: Dict[str, Any]\n    tool_calls: List[Dict[str, Any]]\n    tool_call_id: str", "detail": "backend.plugins.types", "documentation": {}}, {"label": "Message", "kind": 6, "importPath": "backend.plugins.types", "description": "backend.plugins.types", "peekOfCode": "class Message(MessageRequired, total=False):\n    \"\"\"Chat message type with optional fields.\"\"\"\n    name: str\n    function_call: Dict[str, Any]\n    tool_calls: List[Dict[str, Any]]\n    tool_call_id: str\nclass PluginResult(BaseModel):\n    \"\"\"Result of plugin processing.\"\"\"\n    messages: List[Message]\n    metadata: Dict[str, Any] = {}", "detail": "backend.plugins.types", "documentation": {}}, {"label": "PluginResult", "kind": 6, "importPath": "backend.plugins.types", "description": "backend.plugins.types", "peekOfCode": "class PluginResult(BaseModel):\n    \"\"\"Result of plugin processing.\"\"\"\n    messages: List[Message]\n    metadata: Dict[str, Any] = {}\n    stop_processing: bool = False\n    error: Optional[str] = None\nclass PluginConfig(BaseModel):\n    \"\"\"Plugin configuration.\"\"\"\n    enabled: bool = True\n    priority: int = 100", "detail": "backend.plugins.types", "documentation": {}}, {"label": "PluginConfig", "kind": 6, "importPath": "backend.plugins.types", "description": "backend.plugins.types", "peekOfCode": "class PluginConfig(BaseModel):\n    \"\"\"Plugin configuration.\"\"\"\n    enabled: bool = True\n    priority: int = 100\n    config: Dict[str, Any] = {}\nclass PluginInfo(BaseModel):\n    \"\"\"Plugin information.\"\"\"\n    id: str\n    name: str\n    description: str", "detail": "backend.plugins.types", "documentation": {}}, {"label": "PluginInfo", "kind": 6, "importPath": "backend.plugins.types", "description": "backend.plugins.types", "peekOfCode": "class PluginInfo(BaseModel):\n    \"\"\"Plugin information.\"\"\"\n    id: str\n    name: str\n    description: str\n    version: str\n    type: PluginType\n    author: str\n    config: PluginConfig\n    dependencies: List[str] = []", "detail": "backend.plugins.types", "documentation": {}}, {"label": "ToolDefinition", "kind": 6, "importPath": "backend.plugins.types", "description": "backend.plugins.types", "peekOfCode": "class ToolDefinition(BaseModel):\n    \"\"\"Tool definition for LLM function/tool calling.\"\"\"\n    name: str\n    description: str\n    parameters: Dict[str, Any]\n    required: List[str] = []\nclass UIComponent(BaseModel):\n    \"\"\"UI component definition for frontend rendering.\"\"\"\n    id: str\n    type: str", "detail": "backend.plugins.types", "documentation": {}}, {"label": "UIComponent", "kind": 6, "importPath": "backend.plugins.types", "description": "backend.plugins.types", "peekOfCode": "class UIComponent(BaseModel):\n    \"\"\"UI component definition for frontend rendering.\"\"\"\n    id: str\n    type: str\n    props: Dict[str, Any] = {}\n    children: List[Union[str, 'UIComponent']] = []\nclass MCPAction(BaseModel):\n    \"\"\"MCP action definition.\"\"\"\n    type: str\n    name: str", "detail": "backend.plugins.types", "documentation": {}}, {"label": "MCPAction", "kind": 6, "importPath": "backend.plugins.types", "description": "backend.plugins.types", "peekOfCode": "class MCPAction(BaseModel):\n    \"\"\"MCP action definition.\"\"\"\n    type: str\n    name: str\n    input: Dict[str, Any] = {}\n    thought: Optional[str] = None\nclass MCPResponse(BaseModel):\n    \"\"\"MCP response definition.\"\"\"\n    type: str\n    content: Any", "detail": "backend.plugins.types", "documentation": {}}, {"label": "MCPResponse", "kind": 6, "importPath": "backend.plugins.types", "description": "backend.plugins.types", "peekOfCode": "class MCPResponse(BaseModel):\n    \"\"\"MCP response definition.\"\"\"\n    type: str\n    content: Any\n    status: str = \"success\"\n    error: Optional[str] = None", "detail": "backend.plugins.types", "documentation": {}}, {"label": "list_users", "kind": 2, "importPath": "backend.routers.admin_api", "description": "backend.routers.admin_api", "peekOfCode": "def list_users(current_user = Depends(get_current_admin)):\n    \"\"\"\n    ユーザー一覧を取得します。管理者権限が必要です。\n    \"\"\"\n    # 実際の実装では、データベースからユーザー一覧を取得します\n    return {\n        \"users\": [\n            {\"id\": \"1\", \"username\": \"admin\", \"role\": \"admin\"},\n            {\"id\": \"2\", \"username\": \"user1\", \"role\": \"user\"},\n            {\"id\": \"3\", \"username\": \"user2\", \"role\": \"user\"}", "detail": "backend.routers.admin_api", "documentation": {}}, {"label": "router", "kind": 5, "importPath": "backend.routers.admin_api", "description": "backend.routers.admin_api", "peekOfCode": "router = APIRouter(prefix=\"/admin\", tags=[\"Admin APIs\"])\************(\"/users\")\ndef list_users(current_user = Depends(get_current_admin)):\n    \"\"\"\n    ユーザー一覧を取得します。管理者権限が必要です。\n    \"\"\"\n    # 実際の実装では、データベースからユーザー一覧を取得します\n    return {\n        \"users\": [\n            {\"id\": \"1\", \"username\": \"admin\", \"role\": \"admin\"},", "detail": "backend.routers.admin_api", "documentation": {}}, {"label": "ChatSessionResponse", "kind": 6, "importPath": "backend.routers.chat_api", "description": "backend.routers.chat_api", "peekOfCode": "class ChatSessionResponse(BaseModel):\n    id: str\n    title: str\n    created_at: datetime\n    last_message: str\nclass ChatMessageResponse(BaseModel):\n    id: str\n    session_id: str\n    role: str\n    content: str", "detail": "backend.routers.chat_api", "documentation": {}}, {"label": "ChatMessageResponse", "kind": 6, "importPath": "backend.routers.chat_api", "description": "backend.routers.chat_api", "peekOfCode": "class ChatMessageResponse(BaseModel):\n    id: str\n    session_id: str\n    role: str\n    content: str\n    created_at: datetime\n# モデル定義\nclass Message(BaseModel):\n    role: str\n    content: str", "detail": "backend.routers.chat_api", "documentation": {}}, {"label": "Message", "kind": 6, "importPath": "backend.routers.chat_api", "description": "backend.routers.chat_api", "peekOfCode": "class Message(BaseModel):\n    role: str\n    content: str\nclass ChatCompletionRequest(BaseModel):\n    model: str = Field(default=settings.DEFAULT_LLM_MODEL, description=\"使用するモデル名\")\n    messages: List[Message] = Field(..., description=\"会話内のメッセージリスト\")\n    temperature: float = Field(default=0.7, ge=0.0, le=2.0, description=\"サンプリング温度\")\n    top_p: float = Field(default=0.9, ge=0.0, le=1.0, description=\"核サンプリングパラメータ\")\n    top_k: int = Field(default=50, ge=0, le=1000, description=\"Top-kサンプリングパラメータ\")\n    max_tokens: int = Field(default=500, ge=1, le=4096, description=\"生成する最大トークン数\")", "detail": "backend.routers.chat_api", "documentation": {}}, {"label": "ChatCompletionRequest", "kind": 6, "importPath": "backend.routers.chat_api", "description": "backend.routers.chat_api", "peekOfCode": "class ChatCompletionRequest(BaseModel):\n    model: str = Field(default=settings.DEFAULT_LLM_MODEL, description=\"使用するモデル名\")\n    messages: List[Message] = Field(..., description=\"会話内のメッセージリスト\")\n    temperature: float = Field(default=0.7, ge=0.0, le=2.0, description=\"サンプリング温度\")\n    top_p: float = Field(default=0.9, ge=0.0, le=1.0, description=\"核サンプリングパラメータ\")\n    top_k: int = Field(default=50, ge=0, le=1000, description=\"Top-kサンプリングパラメータ\")\n    max_tokens: int = Field(default=500, ge=1, le=4096, description=\"生成する最大トークン数\")\n    stream: bool = Field(default=False, description=\"レスポンスをストリーミングするかどうか\")\n    backend: Optional[str] = Field(default=None, description=\"使用するバックエンド（ollama, vllm, sglang, x-inference, openai）\")\n    backend_options: Optional[Dict[str, Any]] = Field(default=None, description=\"追加のバックエンド固有のオプション\")", "detail": "backend.routers.chat_api", "documentation": {}}, {"label": "ChatRequest", "kind": 6, "importPath": "backend.routers.chat_api", "description": "backend.routers.chat_api", "peekOfCode": "class ChatRequest(BaseModel):\n    messages: List[Message]\n    model: Optional[str] = \"gpt-3.5-turbo\"\n# チャット完了API\*************(\"/completions\")\nasync def create_chat_completion(request: ChatCompletionRequest):\n    \"\"\"\n    チャット完了を作成（OpenAI互換API）\n    動的に選択されたバックエンドを使用\n    stream=true パラメータを指定するとストリーミングレスポンスが返されます", "detail": "backend.routers.chat_api", "documentation": {}}, {"label": "ChatSessionRequest", "kind": 6, "importPath": "backend.routers.chat_api", "description": "backend.routers.chat_api", "peekOfCode": "class ChatSessionRequest(BaseModel):\n    title: str = \"New Chat\"\*************(\"/sessions\", response_model=ChatSessionResponse)\nasync def create_chat_session(\n    request: ChatSessionRequest = None,\n    title: str = None,\n    db: Session = Depends(get_db),\n    current_user = Depends(get_current_user)\n):\n    # リクエストボディのタイトルを優先し、なければクエリパラメータのタイトルを使用", "detail": "backend.routers.chat_api", "documentation": {}}, {"label": "UpdateSessionTitleRequest", "kind": 6, "importPath": "backend.routers.chat_api", "description": "backend.routers.chat_api", "peekOfCode": "class UpdateSessionTitleRequest(BaseModel):\n    title: str\n# セッションタイトルを更新\************(\"/sessions/{session_id}\", response_model=ChatSessionResponse)\nasync def update_chat_session(\n    session_id: str,\n    request: UpdateSessionTitleRequest,\n    db: Session = Depends(get_db),\n    current_user = Depends(get_current_user)\n):", "detail": "backend.routers.chat_api", "documentation": {}}, {"label": "ChatMessageRequest", "kind": 6, "importPath": "backend.routers.chat_api", "description": "backend.routers.chat_api", "peekOfCode": "class ChatMessageRequest(BaseModel):\n    role: str\n    content: str\*************(\"/sessions/{session_id}/messages\", response_model=ChatMessageResponse)\nasync def add_chat_message(\n    session_id: str,\n    message: ChatMessageRequest,\n    db: Session = Depends(get_db),\n    current_user = Depends(get_current_user)\n):", "detail": "backend.routers.chat_api", "documentation": {}}, {"label": "router", "kind": 5, "importPath": "backend.routers.chat_api", "description": "backend.routers.chat_api", "peekOfCode": "router = APIRouter(prefix=\"/chat\", tags=[\"Chat APIs\"])\n# OPTIONSリクエストを処理するルートを追加\n# OPTIONSリクエストはプリフライトリクエストであり、認証は不要\****************(\"/history\")\nasync def options_chat_history():\n    return {\"detail\": \"OK\"}\****************(\"/sessions\")\nasync def options_chat_sessions():\n    return {}\****************(\"/sessions/{session_id}\")", "detail": "backend.routers.chat_api", "documentation": {}}, {"label": "ChatSessionResponse", "kind": 6, "importPath": "backend.routers.chat_api_with_plugins", "description": "backend.routers.chat_api_with_plugins", "peekOfCode": "class ChatSessionResponse(BaseModel):\n    id: str\n    title: str\n    created_at: datetime\n    last_message: str\nclass ChatMessageResponse(BaseModel):\n    id: str\n    session_id: str\n    role: str\n    content: str", "detail": "backend.routers.chat_api_with_plugins", "documentation": {}}, {"label": "ChatMessageResponse", "kind": 6, "importPath": "backend.routers.chat_api_with_plugins", "description": "backend.routers.chat_api_with_plugins", "peekOfCode": "class ChatMessageResponse(BaseModel):\n    id: str\n    session_id: str\n    role: str\n    content: str\n    created_at: datetime\n# モデル定義\nclass Message(BaseModel):\n    role: str\n    content: str", "detail": "backend.routers.chat_api_with_plugins", "documentation": {}}, {"label": "Message", "kind": 6, "importPath": "backend.routers.chat_api_with_plugins", "description": "backend.routers.chat_api_with_plugins", "peekOfCode": "class Message(BaseModel):\n    role: str\n    content: str\n    name: Optional[str] = None\n    function_call: Optional[Dict[str, Any]] = None\n    tool_calls: Optional[List[Dict[str, Any]]] = None\n    tool_call_id: Optional[str] = None\nclass ChatCompletionRequest(BaseModel):\n    model: str = Field(default=settings.DEFAULT_LLM_MODEL, description=\"使用するモデル名\")\n    messages: List[Message] = Field(..., description=\"会話内のメッセージリスト\")", "detail": "backend.routers.chat_api_with_plugins", "documentation": {}}, {"label": "ChatCompletionRequest", "kind": 6, "importPath": "backend.routers.chat_api_with_plugins", "description": "backend.routers.chat_api_with_plugins", "peekOfCode": "class ChatCompletionRequest(BaseModel):\n    model: str = Field(default=settings.DEFAULT_LLM_MODEL, description=\"使用するモデル名\")\n    messages: List[Message] = Field(..., description=\"会話内のメッセージリスト\")\n    temperature: float = Field(default=0.7, ge=0.0, le=2.0, description=\"サンプリング温度\")\n    top_p: float = Field(default=0.9, ge=0.0, le=1.0, description=\"核サンプリングパラメータ\")\n    top_k: int = Field(default=50, ge=0, le=1000, description=\"Top-kサンプリングパラメータ\")\n    max_tokens: int = Field(default=500, ge=1, le=4096, description=\"生成する最大トークン数\")\n    stream: bool = Field(default=False, description=\"レスポンスをストリーミングするかどうか\")\n    backend: Optional[str] = Field(default=None, description=\"使用するバックエンド（ollama, vllm, sglang, x-inference, openai）\")\n    backend_options: Optional[Dict[str, Any]] = Field(default=None, description=\"追加のバックエンド固有のオプション\")", "detail": "backend.routers.chat_api_with_plugins", "documentation": {}}, {"label": "ChatRequest", "kind": 6, "importPath": "backend.routers.chat_api_with_plugins", "description": "backend.routers.chat_api_with_plugins", "peekOfCode": "class ChatRequest(BaseModel):\n    messages: List[Message]\n    model: Optional[str] = \"gpt-3.5-turbo\"\nclass ChatMessageRequest(BaseModel):\n    role: str\n    content: str\n# チャット完了API\*************(\"/completions\")\nasync def create_chat_completion(request: ChatCompletionRequest):\n    \"\"\"", "detail": "backend.routers.chat_api_with_plugins", "documentation": {}}, {"label": "ChatMessageRequest", "kind": 6, "importPath": "backend.routers.chat_api_with_plugins", "description": "backend.routers.chat_api_with_plugins", "peekOfCode": "class ChatMessageRequest(BaseModel):\n    role: str\n    content: str\n# チャット完了API\*************(\"/completions\")\nasync def create_chat_completion(request: ChatCompletionRequest):\n    \"\"\"\n    チャット完了を作成（OpenAI互換API）\n    動的に選択されたバックエンドを使用\n    stream=true パラメータを指定するとストリーミングレスポンスが返されます", "detail": "backend.routers.chat_api_with_plugins", "documentation": {}}, {"label": "ChatSessionRequest", "kind": 6, "importPath": "backend.routers.chat_api_with_plugins", "description": "backend.routers.chat_api_with_plugins", "peekOfCode": "class ChatSessionRequest(BaseModel):\n    title: str = \"New Chat\"\*************(\"/sessions\", response_model=ChatSessionResponse)\nasync def create_chat_session(\n    request: ChatSessionRequest = None,\n    title: str = None,\n    db: Session = Depends(get_db),\n    current_user = Depends(get_current_user)\n):\n    # リクエストボディのタイトルを優先し、なければクエリパラメータのタイトルを使用", "detail": "backend.routers.chat_api_with_plugins", "documentation": {}}, {"label": "UpdateSessionTitleRequest", "kind": 6, "importPath": "backend.routers.chat_api_with_plugins", "description": "backend.routers.chat_api_with_plugins", "peekOfCode": "class UpdateSessionTitleRequest(BaseModel):\n    title: str\************(\"/sessions/{session_id}/title\", response_model=ChatSessionResponse)\nasync def update_session_title(\n    session_id: str,\n    request: UpdateSessionTitleRequest,\n    db: Session = Depends(get_db),\n    current_user = Depends(get_current_user)\n):\n    \"\"\"", "detail": "backend.routers.chat_api_with_plugins", "documentation": {}}, {"label": "router", "kind": 5, "importPath": "backend.routers.chat_api_with_plugins", "description": "backend.routers.chat_api_with_plugins", "peekOfCode": "router = APIRouter(prefix=\"/chat\", tags=[\"Chat APIs\"])\n# OPTIONSリクエストを処理するルートを追加\n# OPTIONSリクエストはプリフライトリクエストであり、認証は不要\****************(\"/history\")\nasync def options_chat_history():\n    return {\"detail\": \"OK\"}\****************(\"/sessions\")\nasync def options_chat_sessions():\n    return {}\****************(\"/sessions/{session_id}\")", "detail": "backend.routers.chat_api_with_plugins", "documentation": {}}, {"label": "logger", "kind": 5, "importPath": "backend.routers.models_api", "description": "backend.routers.models_api", "peekOfCode": "logger = logging.getLogger(__name__)\nrouter = APIRouter(tags=[\"Models APIs\"])\************(\"/v1/models\", response_model=Dict[str, List[Dict[str, Any]]], operation_id=\"list_models_v1_unique\")\nasync def list_models(request: Request, response: Response):\n    # CORSヘッダーを追加\n    response.headers[\"Access-Control-Allow-Origin\"] = \"*\"\n    response.headers[\"Access-Control-Allow-Methods\"] = \"GET, OPTIONS\"\n    response.headers[\"Access-Control-Allow-Headers\"] = \"Content-Type, Authorization\"\n    # リクエスト情報を記録\n    logger.info(f\"{request.client.host} からのモデルリクエストを受信しました。ヘッダー: {request.headers.get('origin', 'Unknown')}, {request.headers.get('user-agent', 'Unknown')}\")", "detail": "backend.routers.models_api", "documentation": {}}, {"label": "router", "kind": 5, "importPath": "backend.routers.models_api", "description": "backend.routers.models_api", "peekOfCode": "router = APIRouter(tags=[\"Models APIs\"])\************(\"/v1/models\", response_model=Dict[str, List[Dict[str, Any]]], operation_id=\"list_models_v1_unique\")\nasync def list_models(request: Request, response: Response):\n    # CORSヘッダーを追加\n    response.headers[\"Access-Control-Allow-Origin\"] = \"*\"\n    response.headers[\"Access-Control-Allow-Methods\"] = \"GET, OPTIONS\"\n    response.headers[\"Access-Control-Allow-Headers\"] = \"Content-Type, Authorization\"\n    # リクエスト情報を記録\n    logger.info(f\"{request.client.host} からのモデルリクエストを受信しました。ヘッダー: {request.headers.get('origin', 'Unknown')}, {request.headers.get('user-agent', 'Unknown')}\")\n    \"\"\"", "detail": "backend.routers.models_api", "documentation": {}}, {"label": "ChatCompletionMessage", "kind": 6, "importPath": "backend.routers.openai_api", "description": "backend.routers.openai_api", "peekOfCode": "class ChatCompletionMessage(BaseModel):\n    role: str\n    content: str\n    name: Optional[str] = None\n    @model_validator(mode='before')\n    @classmethod\n    def validate_role(cls, data: Dict[str, Any]) -> Dict[str, Any]:\n        allowed_roles = [\"system\", \"user\", \"assistant\", \"function\", \"tool\"]\n        if \"role\" in data and data[\"role\"] not in allowed_roles:\n            raise ValueError(f\"roleは次のいずれかである必要があります: {', '.join(allowed_roles)}\")", "detail": "backend.routers.openai_api", "documentation": {}}, {"label": "ChatCompletionFunctionCall", "kind": 6, "importPath": "backend.routers.openai_api", "description": "backend.routers.openai_api", "peekOfCode": "class ChatCompletionFunctionCall(BaseModel):\n    name: str\n    arguments: str\nclass ChatCompletionTool(BaseModel):\n    type: str = \"function\"\n    function: Dict[str, Any]\nclass ChatCompletionToolChoice(BaseModel):\n    type: str = \"function\"\n    function: Dict[str, Any]\nclass CompletionRequest(BaseModel):", "detail": "backend.routers.openai_api", "documentation": {}}, {"label": "ChatCompletionTool", "kind": 6, "importPath": "backend.routers.openai_api", "description": "backend.routers.openai_api", "peekOfCode": "class ChatCompletionTool(BaseModel):\n    type: str = \"function\"\n    function: Dict[str, Any]\nclass ChatCompletionToolChoice(BaseModel):\n    type: str = \"function\"\n    function: Dict[str, Any]\nclass CompletionRequest(BaseModel):\n    model: str = Field(..., description=\"使用するモデル名\")\n    prompt: str = Field(..., description=\"補完するためのプロンプト\")\n    suffix: Optional[str] = None", "detail": "backend.routers.openai_api", "documentation": {}}, {"label": "ChatCompletionToolChoice", "kind": 6, "importPath": "backend.routers.openai_api", "description": "backend.routers.openai_api", "peekOfCode": "class ChatCompletionToolChoice(BaseModel):\n    type: str = \"function\"\n    function: Dict[str, Any]\nclass CompletionRequest(BaseModel):\n    model: str = Field(..., description=\"使用するモデル名\")\n    prompt: str = Field(..., description=\"補完するためのプロンプト\")\n    suffix: Optional[str] = None\n    temperature: Optional[float] = 1.0\n    top_p: Optional[float] = 1.0\n    n: Optional[int] = 1", "detail": "backend.routers.openai_api", "documentation": {}}, {"label": "CompletionRequest", "kind": 6, "importPath": "backend.routers.openai_api", "description": "backend.routers.openai_api", "peekOfCode": "class CompletionRequest(BaseModel):\n    model: str = Field(..., description=\"使用するモデル名\")\n    prompt: str = Field(..., description=\"補完するためのプロンプト\")\n    suffix: Optional[str] = None\n    temperature: Optional[float] = 1.0\n    top_p: Optional[float] = 1.0\n    n: Optional[int] = 1\n    max_tokens: Optional[int] = 16\n    stop: Optional[Union[str, List[str]]] = None\n    stream: Optional[bool] = False", "detail": "backend.routers.openai_api", "documentation": {}}, {"label": "ChatCompletionRequest", "kind": 6, "importPath": "backend.routers.openai_api", "description": "backend.routers.openai_api", "peekOfCode": "class ChatCompletionRequest(BaseModel):\n    model: str\n    messages: List[ChatCompletionMessage]\n    temperature: Optional[float] = 0.7\n    top_p: Optional[float] = 1.0\n    top_k: Optional[int] = 50\n    n: Optional[int] = 1\n    max_tokens: Optional[int] = 2048\n    stream: Optional[bool] = False\n    presence_penalty: Optional[float] = 0.0", "detail": "backend.routers.openai_api", "documentation": {}}, {"label": "EmbeddingRequest", "kind": 6, "importPath": "backend.routers.openai_api", "description": "backend.routers.openai_api", "peekOfCode": "class EmbeddingRequest(BaseModel):\n    model: str\n    input: Union[str, List[str]]\n    encoding_format: Optional[str] = \"float\"\n    user: Optional[str] = None\n    dimensions: Optional[int] = None\n# 音声文字起こしリクエスト用のモデル\nclass AudioTranscriptionRequest(BaseModel):\n    file: UploadFile = File(...)\n    model: str = Form(...)", "detail": "backend.routers.openai_api", "documentation": {}}, {"label": "AudioTranscriptionRequest", "kind": 6, "importPath": "backend.routers.openai_api", "description": "backend.routers.openai_api", "peekOfCode": "class AudioTranscriptionRequest(BaseModel):\n    file: UploadFile = File(...)\n    model: str = Form(...)\n    prompt: Optional[str] = Form(None)\n    response_format: Optional[str] = Form(\"json\")\n    temperature: Optional[float] = Form(0.0)\n    language: Optional[str] = Form(None)\n# 画像生成リクエスト用のモデル\nclass ImageGenerationRequest(BaseModel):\n    prompt: str", "detail": "backend.routers.openai_api", "documentation": {}}, {"label": "ImageGenerationRequest", "kind": 6, "importPath": "backend.routers.openai_api", "description": "backend.routers.openai_api", "peekOfCode": "class ImageGenerationRequest(BaseModel):\n    prompt: str\n    model: Optional[str] = \"dall-e-3\"\n    n: Optional[int] = 1\n    size: Optional[str] = \"1024x1024\"\n    response_format: Optional[str] = \"url\"\n    style: Optional[str] = \"vivid\"\n    quality: Optional[str] = \"standard\"\n    user: Optional[str] = None\n# モデレーションリクエスト用のモデル", "detail": "backend.routers.openai_api", "documentation": {}}, {"label": "ModerationRequest", "kind": 6, "importPath": "backend.routers.openai_api", "description": "backend.routers.openai_api", "peekOfCode": "class ModerationRequest(BaseModel):\n    input: Union[str, List[str]]\n    model: Optional[str] = \"text-moderation-latest\"\n# ファイルアップロードリクエスト用のモデル\nclass FileUploadRequest(BaseModel):\n    file: UploadFile = File(...)\n    purpose: str = Form(...)\n# ファインチューニングジョブリクエスト用のモデル\nclass FineTuningJobRequest(BaseModel):\n    training_file: str", "detail": "backend.routers.openai_api", "documentation": {}}, {"label": "FileUploadRequest", "kind": 6, "importPath": "backend.routers.openai_api", "description": "backend.routers.openai_api", "peekOfCode": "class FileUploadRequest(BaseModel):\n    file: UploadFile = File(...)\n    purpose: str = Form(...)\n# ファインチューニングジョブリクエスト用のモデル\nclass FineTuningJobRequest(BaseModel):\n    training_file: str\n    validation_file: Optional[str] = None\n    model: str\n    hyperparameters: Optional[Dict[str, Any]] = None\n    suffix: Optional[str] = None", "detail": "backend.routers.openai_api", "documentation": {}}, {"label": "FineTuningJobRequest", "kind": 6, "importPath": "backend.routers.openai_api", "description": "backend.routers.openai_api", "peekOfCode": "class FineTuningJobRequest(BaseModel):\n    training_file: str\n    validation_file: Optional[str] = None\n    model: str\n    hyperparameters: Optional[Dict[str, Any]] = None\n    suffix: Optional[str] = None\n# スレッド作成リクエスト用のモデル\nclass ThreadCreateRequest(BaseModel):\n    messages: Optional[List[Dict[str, Any]]] = None\n    metadata: Optional[Dict[str, Any]] = None", "detail": "backend.routers.openai_api", "documentation": {}}, {"label": "ThreadCreateRequest", "kind": 6, "importPath": "backend.routers.openai_api", "description": "backend.routers.openai_api", "peekOfCode": "class ThreadCreateRequest(BaseModel):\n    messages: Optional[List[Dict[str, Any]]] = None\n    metadata: Optional[Dict[str, Any]] = None\n# スレッドメッセージ作成リクエスト用のモデル\nclass ThreadMessageCreateRequest(BaseModel):\n    role: str\n    content: str\n    file_ids: Optional[List[str]] = None\n    metadata: Optional[Dict[str, Any]] = None\n# スレッド実行リクエスト用のモデル", "detail": "backend.routers.openai_api", "documentation": {}}, {"label": "ThreadMessageCreateRequest", "kind": 6, "importPath": "backend.routers.openai_api", "description": "backend.routers.openai_api", "peekOfCode": "class ThreadMessageCreateRequest(BaseModel):\n    role: str\n    content: str\n    file_ids: Optional[List[str]] = None\n    metadata: Optional[Dict[str, Any]] = None\n# スレッド実行リクエスト用のモデル\nclass ThreadRunCreateRequest(BaseModel):\n    assistant_id: str\n    model: Optional[str] = None\n    instructions: Optional[str] = None", "detail": "backend.routers.openai_api", "documentation": {}}, {"label": "ThreadRunCreateRequest", "kind": 6, "importPath": "backend.routers.openai_api", "description": "backend.routers.openai_api", "peekOfCode": "class ThreadRunCreateRequest(BaseModel):\n    assistant_id: str\n    model: Optional[str] = None\n    instructions: Optional[str] = None\n    tools: Optional[List[Dict[str, Any]]] = None\n    metadata: Optional[Dict[str, Any]] = None\nasync def stream_generator(client, model, messages, temperature, max_tokens, top_p, stream, tools=None, tool_choice=None):\n    \"\"\"ストリーミングレスポンスを生成するジェネレータ\"\"\"\n    try:\n        # 詳細なログを追加", "detail": "backend.routers.openai_api", "documentation": {}}, {"label": "get_backend_type", "kind": 2, "importPath": "backend.routers.openai_api", "description": "backend.routers.openai_api", "peekOfCode": "def get_backend_type(model_name: str) -> BackendType:\n    \"\"\"\n    モデル名からバックエンドタイプを取得します\n    Args:\n        model_name: モデル名\n    Returns:\n        BackendType: バックエンドタイプ\n    \"\"\"\n    # モデルマッピングからバックエンド名を取得\n    backend_name = settings.MODEL_BACKEND_MAPPING.get(model_name, settings.DEFAULT_LLM_BACKEND)", "detail": "backend.routers.openai_api", "documentation": {}}, {"label": "format_openai_error", "kind": 2, "importPath": "backend.routers.openai_api", "description": "backend.routers.openai_api", "peekOfCode": "def format_openai_error(status_code: int, message: str, param: Optional[str] = None, code: Optional[str] = None, type_: str = \"invalid_request_error\"):\n    \"\"\"OpenAI API形式のエラーレスポンスを作成します\"\"\"\n    error = {\n        \"message\": message,\n        \"type\": type_,\n        \"param\": param,\n        \"code\": code\n    }\n    return JSONResponse(status_code=status_code, content={\"error\": error})\n# モデル定義", "detail": "backend.routers.openai_api", "documentation": {}}, {"label": "logger", "kind": 5, "importPath": "backend.routers.openai_api", "description": "backend.routers.openai_api", "peekOfCode": "logger = logging.getLogger(\"openai_api\")\n# ルーターの作成\nrouter = APIRouter(tags=[\"OpenAI Compatible API\"])\n# モデル名からバックエンドタイプを取得する関数\ndef get_backend_type(model_name: str) -> BackendType:\n    \"\"\"\n    モデル名からバックエンドタイプを取得します\n    Args:\n        model_name: モデル名\n    Returns:", "detail": "backend.routers.openai_api", "documentation": {}}, {"label": "router", "kind": 5, "importPath": "backend.routers.openai_api", "description": "backend.routers.openai_api", "peekOfCode": "router = APIRouter(tags=[\"OpenAI Compatible API\"])\n# モデル名からバックエンドタイプを取得する関数\ndef get_backend_type(model_name: str) -> BackendType:\n    \"\"\"\n    モデル名からバックエンドタイプを取得します\n    Args:\n        model_name: モデル名\n    Returns:\n        BackendType: バックエンドタイプ\n    \"\"\"", "detail": "backend.routers.openai_api", "documentation": {}}, {"label": "TextInput", "kind": 6, "importPath": "backend.routers.rag_api", "description": "backend.routers.rag_api", "peekOfCode": "class TextInput(BaseModel):\n    text: str\n    metadata: Optional[Dict[str, Any]] = None\nclass QueryInput(BaseModel):\n    query: str\n    top_k: int = 5\n    filter_params: Optional[Dict[str, Any]] = None\nclass DeleteInput(BaseModel):\n    filter_params: Dict[str, Any]\n# RAGサービスのインスタンスを取得", "detail": "backend.routers.rag_api", "documentation": {}}, {"label": "QueryInput", "kind": 6, "importPath": "backend.routers.rag_api", "description": "backend.routers.rag_api", "peekOfCode": "class QueryInput(BaseModel):\n    query: str\n    top_k: int = 5\n    filter_params: Optional[Dict[str, Any]] = None\nclass DeleteInput(BaseModel):\n    filter_params: Dict[str, Any]\n# RAGサービスのインスタンスを取得\ndef get_rag_service():\n    return RAGService()\n# アップロードディレクトリを作成", "detail": "backend.routers.rag_api", "documentation": {}}, {"label": "DeleteInput", "kind": 6, "importPath": "backend.routers.rag_api", "description": "backend.routers.rag_api", "peekOfCode": "class DeleteInput(BaseModel):\n    filter_params: Dict[str, Any]\n# RAGサービスのインスタンスを取得\ndef get_rag_service():\n    return RAGService()\n# アップロードディレクトリを作成\nUPLOAD_DIR = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), \"uploads\")\nos.makedirs(UPLOAD_DIR, exist_ok=True)\*************(\"/upload-file\")\nasync def upload_file(", "detail": "backend.routers.rag_api", "documentation": {}}, {"label": "get_rag_service", "kind": 2, "importPath": "backend.routers.rag_api", "description": "backend.routers.rag_api", "peekOfCode": "def get_rag_service():\n    return RAGService()\n# アップロードディレクトリを作成\nUPLOAD_DIR = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), \"uploads\")\nos.makedirs(UPLOAD_DIR, exist_ok=True)\*************(\"/upload-file\")\nasync def upload_file(\n    file: UploadFile = File(...),\n    metadata: Optional[str] = Form(None),\n    current_user = Depends(get_current_user),", "detail": "backend.routers.rag_api", "documentation": {}}, {"label": "logger", "kind": 5, "importPath": "backend.routers.rag_api", "description": "backend.routers.rag_api", "peekOfCode": "logger = logging.getLogger(__name__)\nrouter = APIRouter(prefix=\"/rag\", tags=[\"RAG APIs\"])\n# モデル定義\nclass TextInput(BaseModel):\n    text: str\n    metadata: Optional[Dict[str, Any]] = None\nclass QueryInput(BaseModel):\n    query: str\n    top_k: int = 5\n    filter_params: Optional[Dict[str, Any]] = None", "detail": "backend.routers.rag_api", "documentation": {}}, {"label": "router", "kind": 5, "importPath": "backend.routers.rag_api", "description": "backend.routers.rag_api", "peekOfCode": "router = APIRouter(prefix=\"/rag\", tags=[\"RAG APIs\"])\n# モデル定義\nclass TextInput(BaseModel):\n    text: str\n    metadata: Optional[Dict[str, Any]] = None\nclass QueryInput(BaseModel):\n    query: str\n    top_k: int = 5\n    filter_params: Optional[Dict[str, Any]] = None\nclass DeleteInput(BaseModel):", "detail": "backend.routers.rag_api", "documentation": {}}, {"label": "UPLOAD_DIR", "kind": 5, "importPath": "backend.routers.rag_api", "description": "backend.routers.rag_api", "peekOfCode": "UPLOAD_DIR = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), \"uploads\")\nos.makedirs(UPLOAD_DIR, exist_ok=True)\*************(\"/upload-file\")\nasync def upload_file(\n    file: UploadFile = File(...),\n    metadata: Optional[str] = Form(None),\n    current_user = Depends(get_current_user),\n    rag_service = Depends(get_rag_service)\n):\n    \"\"\"", "detail": "backend.routers.rag_api", "documentation": {}}, {"label": "TuningParameter", "kind": 6, "importPath": "backend.routers.tuning_api", "description": "backend.routers.tuning_api", "peekOfCode": "class TuningParameter(BaseModel):\n    name: str = Field(..., description=\"パラメータ名\")\n    value: Any = Field(..., description=\"パラメータ値\")\n    description: Optional[str] = Field(None, description=\"パラメータの説明\")\nclass TuningProfile(BaseModel):\n    name: str = Field(..., description=\"プロファイル名\")\n    description: Optional[str] = Field(None, description=\"プロファイルの説明\")\n    model: str = Field(..., description=\"ターゲットモデル名\")\n    backend: Optional[str] = Field(None, description=\"使用するバックエンド\")\n    parameters: List[TuningParameter] = Field(..., description=\"調整パラメータ\")", "detail": "backend.routers.tuning_api", "documentation": {}}, {"label": "TuningProfile", "kind": 6, "importPath": "backend.routers.tuning_api", "description": "backend.routers.tuning_api", "peekOfCode": "class TuningProfile(BaseModel):\n    name: str = Field(..., description=\"プロファイル名\")\n    description: Optional[str] = Field(None, description=\"プロファイルの説明\")\n    model: str = Field(..., description=\"ターゲットモデル名\")\n    backend: Optional[str] = Field(None, description=\"使用するバックエンド\")\n    parameters: List[TuningParameter] = Field(..., description=\"調整パラメータ\")\nclass TuningRequest(BaseModel):\n    profile_name: str = Field(..., description=\"使用するプロファイル名\")\n    prompt: str = Field(..., description=\"テストに使用するプロンプト\")\n    max_tokens: int = Field(default=128, description=\"生成する最大トークン数\")", "detail": "backend.routers.tuning_api", "documentation": {}}, {"label": "TuningRequest", "kind": 6, "importPath": "backend.routers.tuning_api", "description": "backend.routers.tuning_api", "peekOfCode": "class TuningRequest(BaseModel):\n    profile_name: str = Field(..., description=\"使用するプロファイル名\")\n    prompt: str = Field(..., description=\"テストに使用するプロンプト\")\n    max_tokens: int = Field(default=128, description=\"生成する最大トークン数\")\n# 調整プロファイルを保存およびロードする関数\ndef get_profiles_path():\n    \"\"\"プロファイルの保存パスを取得\"\"\"\n    base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))\n    profiles_dir = os.path.join(base_dir, \"data\")\n    os.makedirs(profiles_dir, exist_ok=True)", "detail": "backend.routers.tuning_api", "documentation": {}}, {"label": "get_profiles_path", "kind": 2, "importPath": "backend.routers.tuning_api", "description": "backend.routers.tuning_api", "peekOfCode": "def get_profiles_path():\n    \"\"\"プロファイルの保存パスを取得\"\"\"\n    base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))\n    profiles_dir = os.path.join(base_dir, \"data\")\n    os.makedirs(profiles_dir, exist_ok=True)\n    return os.path.join(profiles_dir, \"tuning_profiles.json\")\ndef load_profiles():\n    \"\"\"すべての調整プロファイルをロード\"\"\"\n    profiles_path = get_profiles_path()\n    if not os.path.exists(profiles_path):", "detail": "backend.routers.tuning_api", "documentation": {}}, {"label": "load_profiles", "kind": 2, "importPath": "backend.routers.tuning_api", "description": "backend.routers.tuning_api", "peekOfCode": "def load_profiles():\n    \"\"\"すべての調整プロファイルをロード\"\"\"\n    profiles_path = get_profiles_path()\n    if not os.path.exists(profiles_path):\n        return {}\n    try:\n        with open(profiles_path, \"r\", encoding=\"utf-8\") as f:\n            return json.load(f)\n    except Exception as e:\n        logging.error(f\"調整プロファイルのロード中にエラーが発生しました: {e}\")", "detail": "backend.routers.tuning_api", "documentation": {}}, {"label": "save_profiles", "kind": 2, "importPath": "backend.routers.tuning_api", "description": "backend.routers.tuning_api", "peekOfCode": "def save_profiles(profiles):\n    \"\"\"すべての調整プロファイルを保存\"\"\"\n    profiles_path = get_profiles_path()\n    try:\n        with open(profiles_path, \"w\", encoding=\"utf-8\") as f:\n            json.dump(profiles, f, indent=2, ensure_ascii=False)\n        return True\n    except Exception as e:\n        logging.error(f\"調整プロファイルの保存中にエラーが発生しました: {e}\")\n        return False", "detail": "backend.routers.tuning_api", "documentation": {}}, {"label": "router", "kind": 5, "importPath": "backend.routers.tuning_api", "description": "backend.routers.tuning_api", "peekOfCode": "router = APIRouter(tags=[\"Tuning APIs\"])\n# モデル定義\nclass TuningParameter(BaseModel):\n    name: str = Field(..., description=\"パラメータ名\")\n    value: Any = Field(..., description=\"パラメータ値\")\n    description: Optional[str] = Field(None, description=\"パラメータの説明\")\nclass TuningProfile(BaseModel):\n    name: str = Field(..., description=\"プロファイル名\")\n    description: Optional[str] = Field(None, description=\"プロファイルの説明\")\n    model: str = Field(..., description=\"ターゲットモデル名\")", "detail": "backend.routers.tuning_api", "documentation": {}}, {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "kind": 6, "importPath": "backend.services.dependencies", "description": "backend.services.dependencies", "peekOfCode": "class DummyRedis:\n    \"\"\"\n    Redisが利用できない場合のダミークライアント\n    \"\"\"\n    def __init__(self):\n        self.data = {}\n    def set(self, key, value, *args, **kwargs):\n        self.data[key] = value\n        return True\n    def get(self, key):", "detail": "backend.services.dependencies", "documentation": {}}, {"label": "check_service_availability", "kind": 2, "importPath": "backend.services.dependencies", "description": "backend.services.dependencies", "peekOfCode": "def check_service_availability(host: str, port: int, service_name: str, timeout: int = 1) -> Tuple[bool, Optional[str]]:\n    \"\"\"\n    指定されたホストとポートでサービスが利用可能かどうかを確認します\n    Args:\n        host: ホスト名またはIPアドレス\n        port: ポート番号\n        service_name: サービス名（ログ用）\n        timeout: タイムアウト秒数\n    Returns:\n        (利用可能かどうか, エラーメッセージ)", "detail": "backend.services.dependencies", "documentation": {}}, {"label": "init_redis", "kind": 2, "importPath": "backend.services.dependencies", "description": "backend.services.dependencies", "peekOfCode": "def init_redis():\n    \"\"\"\n    Redisクライアントを初期化します\n    Returns:\n        Redisクライアントのインスタンスまたはダミークライアント\n    \"\"\"\n    # Redisが有効かどうか確認\n    if not settings.ENABLE_REDIS:\n        logger.warning(\"Redisは無効化されています。ダミークライアントを使用します。\")\n        return DummyRedis()", "detail": "backend.services.dependencies", "documentation": {}}, {"label": "init_rabbitmq", "kind": 2, "importPath": "backend.services.dependencies", "description": "backend.services.dependencies", "peekOfCode": "def init_rabbitmq():\n    \"\"\"\n    RabbitMQの接続を確認します\n    \"\"\"\n    # RabbitMQが有効かどうか確認\n    if not settings.ENABLE_RABBITMQ:\n        logger.warning(\"RabbitMQは無効化されています。\")\n        return False\n    # RabbitMQ URLからホストとポートを抽出\n    try:", "detail": "backend.services.dependencies", "documentation": {}}, {"label": "init_vector_database", "kind": 2, "importPath": "backend.services.dependencies", "description": "backend.services.dependencies", "peekOfCode": "def init_vector_database():\n    \"\"\"\n    ベクトルデータベースの接続を確認します\n    選択されたベクトルデータベースタイプに基づいて適切な初期化関数を呼び出します\n    \"\"\"\n    # ベクトルデータベース機能が有効かどうか確認\n    if not settings.VECTOR_ENABLED_FLAG:\n        logger.warning(\"ベクトルデータベース機能は無効化されています。\")\n        return False\n    # 選択されたベクトルデータベースタイプに基づいて初期化", "detail": "backend.services.dependencies", "documentation": {}}, {"label": "init_qdrant_connection", "kind": 2, "importPath": "backend.services.dependencies", "description": "backend.services.dependencies", "peekOfCode": "def init_qdrant_connection():\n    \"\"\"\n    Qdrantサービスの接続を確認します\n    \"\"\"\n    # Qdrantサービスの可用性を確認\n    available, error_msg = check_service_availability(settings.QDRANT_HOST, settings.QDRANT_PORT, \"Qdrant\")\n    if available:\n        logger.info(\"Qdrantサービスに接続できました\")\n        return True\n    else:", "detail": "backend.services.dependencies", "documentation": {}}, {"label": "init_weaviate_connection", "kind": 2, "importPath": "backend.services.dependencies", "description": "backend.services.dependencies", "peekOfCode": "def init_weaviate_connection():\n    \"\"\"\n    Weaviateサービスの接続を確認します\n    \"\"\"\n    # Weaviate URLからホストとポートを抽出\n    try:\n        if settings.WEAVIATE_URL:\n            # http://localhost:8080 形式のURLを解析\n            parts = settings.WEAVIATE_URL.split('://')\n            if len(parts) > 1:", "detail": "backend.services.dependencies", "documentation": {}}, {"label": "init_supabase_connection", "kind": 2, "importPath": "backend.services.dependencies", "description": "backend.services.dependencies", "peekOfCode": "def init_supabase_connection():\n    \"\"\"\n    Supabaseサービスの接続を確認します\n    \"\"\"\n    # Supabase接続文字列からホストとポートを抽出\n    try:\n        # postgresql://postgres:password@localhost:54322/postgres 形式の接続文字列を解析\n        if not settings.SUPABASE_CONNECTION_STRING:\n            logger.error(\"SUPABASE_CONNECTION_STRINGが設定されていません\")\n            return False", "detail": "backend.services.dependencies", "documentation": {}}, {"label": "init_keycloak", "kind": 2, "importPath": "backend.services.dependencies", "description": "backend.services.dependencies", "peekOfCode": "def init_keycloak():\n    \"\"\"\n    Keycloakサービスの接続を確認します\n    \"\"\"\n    # Keycloakが有効かどうか確認\n    if not settings.ENABLE_KEYCLOAK:\n        logger.warning(\"Keycloak認証サービスは無効化されています。\")\n        return False\n    # Keycloak URLからホストとポートを抽出\n    try:", "detail": "backend.services.dependencies", "documentation": {}}, {"label": "logger", "kind": 5, "importPath": "backend.services.dependencies", "description": "backend.services.dependencies", "peekOfCode": "logger = logging.getLogger(__name__)\ndef check_service_availability(host: str, port: int, service_name: str, timeout: int = 1) -> Tuple[bool, Optional[str]]:\n    \"\"\"\n    指定されたホストとポートでサービスが利用可能かどうかを確認します\n    Args:\n        host: ホスト名またはIPアドレス\n        port: ポート番号\n        service_name: サービス名（ログ用）\n        timeout: タイムアウト秒数\n    Returns:", "detail": "backend.services.dependencies", "documentation": {}}, {"label": "celery_app", "kind": 5, "importPath": "backend.tasks.celery_app", "description": "backend.tasks.celery_app", "peekOfCode": "celery_app = Celery(\n    \"my_llm_tasks\",\n    broker=settings.RABBITMQ_URL,\n    backend=settings.REDIS_URL\n)\ncelery_app.conf.update(\n    task_serializer=\"json\",\n    result_serializer=\"json\",\n    accept_content=[\"json\"],\n    timezone=\"Asia/Tokyo\",", "detail": "backend.tasks.celery_app", "documentation": {}}, {"label": "rag_task", "kind": 2, "importPath": "backend.tasks.worker", "description": "backend.tasks.worker", "peekOfCode": "def rag_task(query: str):\n    service = RAGService()\n    return service.run_rag(query)", "detail": "backend.tasks.worker", "documentation": {}}, {"label": "client", "kind": 5, "importPath": "backend.tests.check_documents", "description": "backend.tests.check_documents", "peekOfCode": "client = vecs.create_client('postgresql://postgres:postgres@localhost:54322/postgres')\n# documentsコレクションを取得\ntry:\n    collection = client.get_collection('documents')\n    print('documentsコレクションが見つかりました')\n    # データを検索\n    try:\n        results = collection.query(data=list(range(384)), limit=10, include_metadata=True)\n        print(f'\\ndocumentsコレクション内のデータ:')\n        for result in results:", "detail": "backend.tests.check_documents", "documentation": {}}, {"label": "client", "kind": 5, "importPath": "backend.tests.check_vecs", "description": "backend.tests.check_vecs", "peekOfCode": "client = vecs.create_client('postgresql://postgres:postgres@localhost:54322/postgres')\n# コレクション一覧を取得（テーブル名から推測）\nconn = psycopg2.connect('postgresql://postgres:postgres@localhost:54322/postgres')\ncursor = conn.cursor()\ncursor.execute(\"SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name NOT IN ('langchain_pg_collection', 'langchain_pg_embedding', 'todos');\")\ntables = cursor.fetchall()\nconn.close()\ncollections = [table[0] for table in tables]\nprint('コレクション一覧:')\nfor collection in collections:", "detail": "backend.tests.check_vecs", "documentation": {}}, {"label": "conn", "kind": 5, "importPath": "backend.tests.check_vecs", "description": "backend.tests.check_vecs", "peekOfCode": "conn = psycopg2.connect('postgresql://postgres:postgres@localhost:54322/postgres')\ncursor = conn.cursor()\ncursor.execute(\"SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name NOT IN ('langchain_pg_collection', 'langchain_pg_embedding', 'todos');\")\ntables = cursor.fetchall()\nconn.close()\ncollections = [table[0] for table in tables]\nprint('コレクション一覧:')\nfor collection in collections:\n    print(collection)\n# 各コレクションのデータを取得", "detail": "backend.tests.check_vecs", "documentation": {}}, {"label": "cursor", "kind": 5, "importPath": "backend.tests.check_vecs", "description": "backend.tests.check_vecs", "peekOfCode": "cursor = conn.cursor()\ncursor.execute(\"SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name NOT IN ('langchain_pg_collection', 'langchain_pg_embedding', 'todos');\")\ntables = cursor.fetchall()\nconn.close()\ncollections = [table[0] for table in tables]\nprint('コレクション一覧:')\nfor collection in collections:\n    print(collection)\n# 各コレクションのデータを取得\nprint('\\nコレクション内のデータ:')", "detail": "backend.tests.check_vecs", "documentation": {}}, {"label": "tables", "kind": 5, "importPath": "backend.tests.check_vecs", "description": "backend.tests.check_vecs", "peekOfCode": "tables = cursor.fetchall()\nconn.close()\ncollections = [table[0] for table in tables]\nprint('コレクション一覧:')\nfor collection in collections:\n    print(collection)\n# 各コレクションのデータを取得\nprint('\\nコレクション内のデータ:')\nfor collection_name in collections:\n    collection = client.get_collection(collection_name)", "detail": "backend.tests.check_vecs", "documentation": {}}, {"label": "collections", "kind": 5, "importPath": "backend.tests.check_vecs", "description": "backend.tests.check_vecs", "peekOfCode": "collections = [table[0] for table in tables]\nprint('コレクション一覧:')\nfor collection in collections:\n    print(collection)\n# 各コレクションのデータを取得\nprint('\\nコレクション内のデータ:')\nfor collection_name in collections:\n    collection = client.get_collection(collection_name)\n    # データを検索\n    try:", "detail": "backend.tests.check_vecs", "documentation": {}}, {"label": "test_chat_completion_normal", "kind": 2, "importPath": "backend.tests.test_openai_api", "description": "backend.tests.test_openai_api", "peekOfCode": "def test_chat_completion_normal():\n    \"\"\"\n    通常のチャット完了APIのテスト\n    一度に全ての応答を返すケース\n    \"\"\"\n    # テストデータの準備\n    test_data = {\n        \"model\": \"gpt-3.5-turbo\",\n        \"messages\": [\n            {", "detail": "backend.tests.test_openai_api", "documentation": {}}, {"label": "test_chat_completion_streaming", "kind": 2, "importPath": "backend.tests.test_openai_api", "description": "backend.tests.test_openai_api", "peekOfCode": "def test_chat_completion_streaming():\n    \"\"\"\n    ストリーミングチャット完了APIのテスト\n    応答を逐次返すケース\n    \"\"\"\n    # テストデータの準備\n    test_data = {\n        \"model\": \"gpt-3.5-turbo\",\n        \"messages\": [\n            {", "detail": "backend.tests.test_openai_api", "documentation": {}}, {"label": "client", "kind": 5, "importPath": "backend.tests.test_openai_api", "description": "backend.tests.test_openai_api", "peekOfCode": "client = TestClient(app)\ndef test_chat_completion_normal():\n    \"\"\"\n    通常のチャット完了APIのテスト\n    一度に全ての応答を返すケース\n    \"\"\"\n    # テストデータの準備\n    test_data = {\n        \"model\": \"gpt-3.5-turbo\",\n        \"messages\": [", "detail": "backend.tests.test_openai_api", "documentation": {}}, {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kind": 6, "importPath": "backend.tests.test_pgvector", "description": "backend.tests.test_pgvector", "peekOfCode": "class DummyEmbeddings(Embeddings):\n    def embed_documents(self, texts):\n        return [[0.1] * 384 for _ in texts]\n    def embed_query(self, text):\n        return [0.1] * 384\ntry:\n    # PGVectorを初期化\n    pgvector = PGVector(\n        connection_string='postgresql://postgres:postgres@localhost:54322/postgres',\n        collection_name='test_collection',", "detail": "backend.tests.test_pgvector", "documentation": {}}, {"label": "test_plugin_registration", "kind": 2, "importPath": "backend.tests.test_plugins", "description": "backend.tests.test_plugins", "peekOfCode": "def test_plugin_registration():\n    \"\"\"Test plugin registration.\"\"\"\n    logger.info(\"Testing plugin registration...\")\n    # Register the sample plugins\n    try:\n        plugin_manager.register_plugin(\"simple_rag\", SimpleRAGPlugin)\n        plugin_manager.register_plugin(\"simple_agent\", SimpleAgentPlugin)\n        logger.info(f\"Registered plugins: {len(plugin_manager.get_all_plugins())}\")\n        # Get plugin info\n        for plugin in plugin_manager.get_all_plugins():", "detail": "backend.tests.test_plugins", "documentation": {}}, {"label": "logger", "kind": 5, "importPath": "backend.tests.test_plugins", "description": "backend.tests.test_plugins", "peekOfCode": "logger = logging.getLogger(__name__)\n# Import the plugin manager\ntry:\n    from backend.plugins.manager import plugin_manager\n    from backend.plugins.samples.rag_plugin import SimpleRAGPlugin\n    from backend.plugins.samples.agent_plugin import SimpleAgentPlugin\n    from backend.plugins.types import Message, PluginResult\n    logger.info(\"Successfully imported plugin modules\")\nexcept ImportError as e:\n    logger.error(f\"Error importing plugin modules: {e}\")", "detail": "backend.tests.test_plugins", "documentation": {}}, {"label": "diagnose_and_fix", "kind": 2, "importPath": "backend.tests.test_supabase_fix", "description": "backend.tests.test_supabase_fix", "peekOfCode": "def diagnose_and_fix():\n    # 1. vecsを使用してコレクションを確認\n    try:\n        client = vecs.create_client(connection_string)\n        collection = client.get_or_create_collection(name=collection_name, dimension=384)\n        # コレクション内のデータ数を確認\n        results = collection.query(data=list(range(384)), limit=100, include_metadata=True)\n        logger.info(f'vecsコレクション内のデータ数: {len(results)}')\n        if len(results) == 0:\n            # テストデータを追加", "detail": "backend.tests.test_supabase_fix", "documentation": {}}, {"label": "logger", "kind": 5, "importPath": "backend.tests.test_supabase_fix", "description": "backend.tests.test_supabase_fix", "peekOfCode": "logger = logging.getLogger(__name__)\n# 接続文字列\nconnection_string = 'postgresql://postgres:postgres@localhost:54322/postgres'\ncollection_name = 'documents'\n# 問題の診断と修正\ndef diagnose_and_fix():\n    # 1. vecsを使用してコレクションを確認\n    try:\n        client = vecs.create_client(connection_string)\n        collection = client.get_or_create_collection(name=collection_name, dimension=384)", "detail": "backend.tests.test_supabase_fix", "documentation": {}}, {"label": "connection_string", "kind": 5, "importPath": "backend.tests.test_supabase_fix", "description": "backend.tests.test_supabase_fix", "peekOfCode": "connection_string = 'postgresql://postgres:postgres@localhost:54322/postgres'\ncollection_name = 'documents'\n# 問題の診断と修正\ndef diagnose_and_fix():\n    # 1. vecsを使用してコレクションを確認\n    try:\n        client = vecs.create_client(connection_string)\n        collection = client.get_or_create_collection(name=collection_name, dimension=384)\n        # コレクション内のデータ数を確認\n        results = collection.query(data=list(range(384)), limit=100, include_metadata=True)", "detail": "backend.tests.test_supabase_fix", "documentation": {}}, {"label": "collection_name", "kind": 5, "importPath": "backend.tests.test_supabase_fix", "description": "backend.tests.test_supabase_fix", "peekOfCode": "collection_name = 'documents'\n# 問題の診断と修正\ndef diagnose_and_fix():\n    # 1. vecsを使用してコレクションを確認\n    try:\n        client = vecs.create_client(connection_string)\n        collection = client.get_or_create_collection(name=collection_name, dimension=384)\n        # コレクション内のデータ数を確認\n        results = collection.query(data=list(range(384)), limit=100, include_metadata=True)\n        logger.info(f'vecsコレクション内のデータ数: {len(results)}')", "detail": "backend.tests.test_supabase_fix", "documentation": {}}, {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "kind": 6, "importPath": "backend.tests.test_vecs_pgvector", "description": "backend.tests.test_vecs_pgvector", "peekOfCode": "class DummyEmbeddings(Embeddings):\n    def embed_documents(self, texts):\n        return [[0.1] * 384 for _ in texts]\n    def embed_query(self, text):\n        return [0.1] * 384\n# vecsを使用してデータを追加\nprint(\"=== vecsを使用してデータを追加 ===\")\ntry:\n    # vecsクライアントを作成\n    client = vecs.create_client(connection_string)", "detail": "backend.tests.test_vecs_pgvector", "documentation": {}}, {"label": "connection_string", "kind": 5, "importPath": "backend.tests.test_vecs_pgvector", "description": "backend.tests.test_vecs_pgvector", "peekOfCode": "connection_string = 'postgresql://postgres:postgres@localhost:54322/postgres'\n# ダミーの埋め込みクラス\nclass DummyEmbeddings(Embeddings):\n    def embed_documents(self, texts):\n        return [[0.1] * 384 for _ in texts]\n    def embed_query(self, text):\n        return [0.1] * 384\n# vecsを使用してデータを追加\nprint(\"=== vecsを使用してデータを追加 ===\")\ntry:", "detail": "backend.tests.test_vecs_pgvector", "documentation": {}}, {"label": "test_chat_completion_vllm", "kind": 2, "importPath": "backend.tests.test_vllm_api", "description": "backend.tests.test_vllm_api", "peekOfCode": "def test_chat_completion_vllm():\n    \"\"\"\n    vLLMバックエンドを使用したチャット完了APIのテスト\n    \"\"\"\n    # テストデータの準備\n    test_data = {\n        \"model\": \"facebook/opt-125m\",\n        \"messages\": [\n            {\n                \"role\": \"system\",", "detail": "backend.tests.test_vllm_api", "documentation": {}}, {"label": "test_completion_vllm", "kind": 2, "importPath": "backend.tests.test_vllm_api", "description": "backend.tests.test_vllm_api", "peekOfCode": "def test_completion_vllm():\n    \"\"\"\n    vLLMバックエンドを使用したテキスト補完APIのテスト\n    \"\"\"\n    # テストデータの準備\n    test_data = {\n        \"model\": \"facebook/opt-125m\",\n        \"prompt\": \"こんにちは、元気ですか？\",\n        \"temperature\": 0.7,\n        \"max_tokens\": 100,", "detail": "backend.tests.test_vllm_api", "documentation": {}}, {"label": "client", "kind": 5, "importPath": "backend.tests.test_vllm_api", "description": "backend.tests.test_vllm_api", "peekOfCode": "client = TestClient(app)\ndef test_chat_completion_vllm():\n    \"\"\"\n    vLLMバックエンドを使用したチャット完了APIのテスト\n    \"\"\"\n    # テストデータの準備\n    test_data = {\n        \"model\": \"facebook/opt-125m\",\n        \"messages\": [\n            {", "detail": "backend.tests.test_vllm_api", "documentation": {}}, {"label": "ensure_directory_exists", "kind": 2, "importPath": "backend.utils.file_utils", "description": "backend.utils.file_utils", "peekOfCode": "def ensure_directory_exists(directory_path: str) -> None:\n    \"\"\"\n    ディレクトリが存在することを確認し、必要に応じて作成します。\n    Args:\n        directory_path: 確認するディレクトリのパス\n    \"\"\"\n    if not os.path.exists(directory_path):\n        os.makedirs(directory_path, exist_ok=True)\n        logger.info(f\"Created directory: {directory_path}\")\ndef read_text_file(file_path: str, encoding: str = 'utf-8') -> str:", "detail": "backend.utils.file_utils", "documentation": {}}, {"label": "read_text_file", "kind": 2, "importPath": "backend.utils.file_utils", "description": "backend.utils.file_utils", "peekOfCode": "def read_text_file(file_path: str, encoding: str = 'utf-8') -> str:\n    \"\"\"\n    ファイルからテキストを読み込みます。\n    Args:\n        file_path: 読み込むファイルのパス\n        encoding: ファイル読み込み時に使用するエンコーディング\n    Returns:\n        ファイルの内容を文字列として返します\n    \"\"\"\n    try:", "detail": "backend.utils.file_utils", "documentation": {}}, {"label": "write_text_file", "kind": 2, "importPath": "backend.utils.file_utils", "description": "backend.utils.file_utils", "peekOfCode": "def write_text_file(file_path: str, content: str, encoding: str = 'utf-8') -> None:\n    \"\"\"\n    テキストをファイルに書き込みます。\n    Args:\n        file_path: 書き込むファイルのパス\n        content: ファイルに書き込む内容\n        encoding: ファイル書き込み時に使用するエンコーディング\n    \"\"\"\n    try:\n        # ディレクトリが存在することを確認", "detail": "backend.utils.file_utils", "documentation": {}}, {"label": "read_json_file", "kind": 2, "importPath": "backend.utils.file_utils", "description": "backend.utils.file_utils", "peekOfCode": "def read_json_file(file_path: str, encoding: str = 'utf-8') -> Dict[str, Any]:\n    \"\"\"\n    ファイルからJSONを読み込みます。\n    Args:\n        file_path: 読み込むファイルのパス\n        encoding: ファイル読み込み時に使用するエンコーディング\n    Returns:\n        ファイルの内容を辞書として返します\n    \"\"\"\n    try:", "detail": "backend.utils.file_utils", "documentation": {}}, {"label": "write_json_file", "kind": 2, "importPath": "backend.utils.file_utils", "description": "backend.utils.file_utils", "peekOfCode": "def write_json_file(file_path: str, data: Dict[str, Any], encoding: str = 'utf-8', indent: int = 2) -> None:\n    \"\"\"\n    JSONをファイルに書き込みます。\n    Args:\n        file_path: 書き込むファイルのパス\n        data: ファイルに書き込むデータ\n        encoding: ファイル書き込み時に使用するエンコーディング\n        indent: インデントに使用するスペースの数\n    \"\"\"\n    try:", "detail": "backend.utils.file_utils", "documentation": {}}, {"label": "read_csv_file", "kind": 2, "importPath": "backend.utils.file_utils", "description": "backend.utils.file_utils", "peekOfCode": "def read_csv_file(file_path: str, encoding: str = 'utf-8', delimiter: str = ',') -> List[Dict[str, str]]:\n    \"\"\"\n    ファイルからCSVを読み込みます。\n    Args:\n        file_path: 読み込むファイルのパス\n        encoding: ファイル読み込み時に使用するエンコーディング\n        delimiter: ファイル読み込み時に使用する区切り文字\n    Returns:\n        ファイルの内容を辞書のリストとして返します\n    \"\"\"", "detail": "backend.utils.file_utils", "documentation": {}}, {"label": "write_csv_file", "kind": 2, "importPath": "backend.utils.file_utils", "description": "backend.utils.file_utils", "peekOfCode": "def write_csv_file(\n    file_path: str,\n    data: List[Dict[str, Any]],\n    fieldnames: Optional[List[str]] = None,\n    encoding: str = 'utf-8',\n    delimiter: str = ','\n) -> None:\n    \"\"\"\n    CSVをファイルに書き込みます。\n    Args:", "detail": "backend.utils.file_utils", "documentation": {}}, {"label": "file_exists", "kind": 2, "importPath": "backend.utils.file_utils", "description": "backend.utils.file_utils", "peekOfCode": "def file_exists(file_path: str) -> bool:\n    \"\"\"\n    ファイルが存在するかどうかを確認します。\n    Args:\n        file_path: 確認するファイルのパス\n    Returns:\n        ファイルが存在する場合はTrue、存在しない場合はFalse\n    \"\"\"\n    return os.path.isfile(file_path)\ndef get_file_size(file_path: str) -> int:", "detail": "backend.utils.file_utils", "documentation": {}}, {"label": "get_file_size", "kind": 2, "importPath": "backend.utils.file_utils", "description": "backend.utils.file_utils", "peekOfCode": "def get_file_size(file_path: str) -> int:\n    \"\"\"\n    ファイルのサイズをバイト単位で取得します。\n    Args:\n        file_path: 確認するファイルのパス\n    Returns:\n        ファイルのサイズ（バイト単位）\n    \"\"\"\n    if not file_exists(file_path):\n        raise FileNotFoundError(f\"File not found: {file_path}\")", "detail": "backend.utils.file_utils", "documentation": {}}, {"label": "get_file_extension", "kind": 2, "importPath": "backend.utils.file_utils", "description": "backend.utils.file_utils", "peekOfCode": "def get_file_extension(file_path: str) -> str:\n    \"\"\"\n    ファイルの拡張子を取得します。\n    Args:\n        file_path: 確認するファイルのパス\n    Returns:\n        ファイルの拡張子（ドットなし）\n    \"\"\"\n    return os.path.splitext(file_path)[1][1:]\ndef copy_file(source_path: str, destination_path: str) -> None:", "detail": "backend.utils.file_utils", "documentation": {}}, {"label": "copy_file", "kind": 2, "importPath": "backend.utils.file_utils", "description": "backend.utils.file_utils", "peekOfCode": "def copy_file(source_path: str, destination_path: str) -> None:\n    \"\"\"\n    ファイルをソースから宛先にコピーします。\n    Args:\n        source_path: ソースファイルのパス\n        destination_path: 宛先ファイルのパス\n    \"\"\"\n    try:\n        # 宛先ディレクトリが存在することを確認\n        directory = os.path.dirname(destination_path)", "detail": "backend.utils.file_utils", "documentation": {}}, {"label": "move_file", "kind": 2, "importPath": "backend.utils.file_utils", "description": "backend.utils.file_utils", "peekOfCode": "def move_file(source_path: str, destination_path: str) -> None:\n    \"\"\"\n    ファイルをソースから宛先に移動します。\n    Args:\n        source_path: ソースファイルのパス\n        destination_path: 宛先ファイルのパス\n    \"\"\"\n    try:\n        # 宛先ディレクトリが存在することを確認\n        directory = os.path.dirname(destination_path)", "detail": "backend.utils.file_utils", "documentation": {}}, {"label": "delete_file", "kind": 2, "importPath": "backend.utils.file_utils", "description": "backend.utils.file_utils", "peekOfCode": "def delete_file(file_path: str) -> None:\n    \"\"\"\n    ファイルを削除します。\n    Args:\n        file_path: 削除するファイルのパス\n    \"\"\"\n    try:\n        if file_exists(file_path):\n            os.remove(file_path)\n        else:", "detail": "backend.utils.file_utils", "documentation": {}}, {"label": "create_temp_file", "kind": 2, "importPath": "backend.utils.file_utils", "description": "backend.utils.file_utils", "peekOfCode": "def create_temp_file(suffix: str = '', prefix: str = 'tmp', content: Optional[str] = None,\n                    encoding: str = 'utf-8') -> str:\n    \"\"\"\n    一時ファイルを作成し、オプションでコンテンツを書き込みます。\n    Args:\n        suffix: 一時ファイルのサフィックス\n        prefix: 一時ファイルのプレフィックス\n        content: ファイルに書き込むオプションのコンテンツ\n        encoding: コンテンツ書き込み時に使用するエンコーディング\n    Returns:", "detail": "backend.utils.file_utils", "documentation": {}}, {"label": "list_files", "kind": 2, "importPath": "backend.utils.file_utils", "description": "backend.utils.file_utils", "peekOfCode": "def list_files(directory_path: str, pattern: Optional[str] = None,\n              recursive: bool = False) -> List[str]:\n    \"\"\"\n    ディレクトリ内のファイルを一覧表示し、オプションでパターンに一致するものを抽出します。\n    Args:\n        directory_path: ファイルを一覧表示するディレクトリのパス\n        pattern: ファイルを照合するオプションのglobパターン\n        recursive: ファイルを再帰的に一覧表示するかどうか\n    Returns:\n        ファイルパスのリスト", "detail": "backend.utils.file_utils", "documentation": {}}, {"label": "process_files", "kind": 2, "importPath": "backend.utils.file_utils", "description": "backend.utils.file_utils", "peekOfCode": "def process_files(directory_path: str, processor: Callable[[str], Any],\n                 pattern: Optional[str] = None, recursive: bool = False) -> List[Any]:\n    \"\"\"\n    提供された関数を使用してディレクトリ内のファイルを処理します。\n    Args:\n        directory_path: 処理するファイルを含むディレクトリのパス\n        processor: 各ファイルパスに適用する関数\n        pattern: ファイルを照合するオプションのglobパターン\n        recursive: ファイルを再帰的に処理するかどうか\n    Returns:", "detail": "backend.utils.file_utils", "documentation": {}}, {"label": "read_file_chunks", "kind": 2, "importPath": "backend.utils.file_utils", "description": "backend.utils.file_utils", "peekOfCode": "def read_file_chunks(file_path: str, chunk_size: int = 8192,\n                    encoding: str = 'utf-8') -> Iterator[str]:\n    \"\"\"\n    大きなファイルをメモリに読み込まないように、ファイルをチャンクで読み込みます。\n    Args:\n        file_path: 読み込むファイルのパス\n        chunk_size: 各チャンクのサイズ（バイト単位）\n        encoding: ファイル読み込み時に使用するエンコーディング\n    Yields:\n        ファイルコンテンツのチャンク", "detail": "backend.utils.file_utils", "documentation": {}}, {"label": "logger", "kind": 5, "importPath": "backend.utils.file_utils", "description": "backend.utils.file_utils", "peekOfCode": "logger = logging.getLogger(__name__)\ndef ensure_directory_exists(directory_path: str) -> None:\n    \"\"\"\n    ディレクトリが存在することを確認し、必要に応じて作成します。\n    Args:\n        directory_path: 確認するディレクトリのパス\n    \"\"\"\n    if not os.path.exists(directory_path):\n        os.makedirs(directory_path, exist_ok=True)\n        logger.info(f\"Created directory: {directory_path}\")", "detail": "backend.utils.file_utils", "documentation": {}}, {"label": "clean_special_tokens", "kind": 2, "importPath": "backend.utils.response_filter", "description": "backend.utils.response_filter", "peekOfCode": "def clean_special_tokens(text: str) -> str:\n    \"\"\"\n    テキストから特殊トークン（<|xxx|>形式）を削除します。\n    Args:\n        text: 処理するテキスト\n    Returns:\n        特殊トークンが削除されたテキスト\n    \"\"\"\n    # 特殊トークンを削除\n    cleaned_text = SPECIAL_TOKEN_PATTERN.sub('', text)", "detail": "backend.utils.response_filter", "documentation": {}}, {"label": "extract_first_response", "kind": 2, "importPath": "backend.utils.response_filter", "description": "backend.utils.response_filter", "peekOfCode": "def extract_first_response(text: str) -> str:\n    \"\"\"\n    複数の回答候補から最初の有効な回答を抽出します。\n    特殊トークンで区切られた最初のセグメントを返します。\n    Args:\n        text: 処理するテキスト\n    Returns:\n        最初の有効な回答\n    \"\"\"\n    # 特殊トークンで分割", "detail": "backend.utils.response_filter", "documentation": {}}, {"label": "filter_chat_completion_response", "kind": 2, "importPath": "backend.utils.response_filter", "description": "backend.utils.response_filter", "peekOfCode": "def filter_chat_completion_response(response: Dict[str, Any]) -> Dict[str, Any]:\n    \"\"\"\n    チャット完了レスポンスをフィルタリングします。\n    特殊トークンを削除し、最初の有効な回答のみを保持します。\n    Args:\n        response: vLLM APIからのレスポンス\n    Returns:\n        フィルタリングされたレスポンス\n    \"\"\"\n    if \"choices\" not in response:", "detail": "backend.utils.response_filter", "documentation": {}}, {"label": "filter_completion_response", "kind": 2, "importPath": "backend.utils.response_filter", "description": "backend.utils.response_filter", "peekOfCode": "def filter_completion_response(response: Dict[str, Any]) -> Dict[str, Any]:\n    \"\"\"\n    テキスト補完レスポンスをフィルタリングします。\n    特殊トークンを削除し、最初の有効な回答のみを保持します。\n    Args:\n        response: vLLM APIからのレスポンス\n    Returns:\n        フィルタリングされたレスポンス\n    \"\"\"\n    if \"choices\" not in response:", "detail": "backend.utils.response_filter", "documentation": {}}, {"label": "filter_streaming_chunk", "kind": 2, "importPath": "backend.utils.response_filter", "description": "backend.utils.response_filter", "peekOfCode": "def filter_streaming_chunk(chunk: Dict[str, Any], is_chat_completion: bool = True) -> Dict[str, Any]:\n    \"\"\"\n    ストリーミングチャンクをフィルタリングします。\n    特殊トークンを削除します。\n    Args:\n        chunk: ストリーミングチャンク\n        is_chat_completion: チャット完了APIかどうか\n    Returns:\n        フィルタリングされたチャンク\n    \"\"\"", "detail": "backend.utils.response_filter", "documentation": {}}, {"label": "logger", "kind": 5, "importPath": "backend.utils.response_filter", "description": "backend.utils.response_filter", "peekOfCode": "logger = logging.getLogger(__name__)\n# 特殊トークンのパターン（<|xxx|>形式）\nSPECIAL_TOKEN_PATTERN = re.compile(r'<\\|[^|]+\\|>')\ndef clean_special_tokens(text: str) -> str:\n    \"\"\"\n    テキストから特殊トークン（<|xxx|>形式）を削除します。\n    Args:\n        text: 処理するテキスト\n    Returns:\n        特殊トークンが削除されたテキスト", "detail": "backend.utils.response_filter", "documentation": {}}, {"label": "SPECIAL_TOKEN_PATTERN", "kind": 5, "importPath": "backend.utils.response_filter", "description": "backend.utils.response_filter", "peekOfCode": "SPECIAL_TOKEN_PATTERN = re.compile(r'<\\|[^|]+\\|>')\ndef clean_special_tokens(text: str) -> str:\n    \"\"\"\n    テキストから特殊トークン（<|xxx|>形式）を削除します。\n    Args:\n        text: 処理するテキスト\n    Returns:\n        特殊トークンが削除されたテキスト\n    \"\"\"\n    # 特殊トークンを削除", "detail": "backend.utils.response_filter", "documentation": {}}, {"label": "<PERSON><PERSON><PERSON><PERSON>", "kind": 6, "importPath": "backend.utils.response_parser", "description": "backend.utils.response_parser", "peekOfCode": "class ResponseParser:\n    \"\"\"\n    LLMの生成結果を構造化された辞書に変換するクラス。\n    このクラスは、LLMの生成結果からthinking部分、コード部分、説明部分などを\n    抽出し、構造化された辞書として返します。\n    \"\"\"\n    @staticmethod\n    def parse_response(text: str) -> Dict[str, Any]:\n        \"\"\"\n        LLMの生成結果を解析して構造化された辞書に変換します。", "detail": "backend.utils.response_parser", "documentation": {}}, {"label": "Settings", "kind": 6, "importPath": "backend.config", "description": "backend.config", "peekOfCode": "class Settings(BaseSettings):\n    PROJECT_NAME: str = \"My LLM Platform\"\n    DEBUG: bool = True\n    # LLM設定\n    DEFAULT_LLM_BACKEND: str = \"ollama\"  # デフォルトの推論バックエンド (vllm, sglang, ollama, x-inference)\n    DEFAULT_LLM_MODEL: str = \"llama3.2:3b\"    # デフォルトのモデル\n    # テスト用API設定\n    TEST_API_KEY: str = \"test-key-123\"  # テスト用APIキー\n    # API設定\n    API_VERSION: str = \"v1\"  # API バージョン (v1, v2, etc.)", "detail": "backend.config", "documentation": {}}, {"label": "get_versioned_url", "kind": 2, "importPath": "backend.config", "description": "backend.config", "peekOfCode": "def get_versioned_url(base_url: str, include_version: bool = True) -> str:\n    \"\"\"\n    APIバージョンを含むURLを取得します。\n    Args:\n        base_url: ベースURL（例: http://localhost:8000）\n        include_version: APIバージョンを含めるかどうか\n    Returns:\n        APIバージョンを含むURL（例: http://localhost:8000/v1）\n    \"\"\"\n    if not include_version:", "detail": "backend.config", "documentation": {}}, {"label": "validate_settings", "kind": 2, "importPath": "backend.config", "description": "backend.config", "peekOfCode": "def validate_settings():\n    \"\"\"\n    設定の整合性を検証し、警告を表示します。\n    \"\"\"\n    import logging\n    logger = logging.getLogger(__name__)\n    # 企業SSOモードの整合性を検証\n    if settings.AUTH_MODE == \"enterprise\" and not settings.ENABLE_ENTERPRISE_SSO:\n        logger.warning(\n            \"不整合な認証設定: AUTH_MODEが'enterprise'に設定されていますが、ENABLE_ENTERPRISE_SSOがFalseです。\"", "detail": "backend.config", "documentation": {}}, {"label": "settings", "kind": 5, "importPath": "backend.config", "description": "backend.config", "peekOfCode": "settings = Settings()\n# APIバージョンを含むURLを取得するヘルパー関数\ndef get_versioned_url(base_url: str, include_version: bool = True) -> str:\n    \"\"\"\n    APIバージョンを含むURLを取得します。\n    Args:\n        base_url: ベースURL（例: http://localhost:8000）\n        include_version: APIバージョンを含めるかどうか\n    Returns:\n        APIバージョンを含むURL（例: http://localhost:8000/v1）", "detail": "backend.config", "documentation": {}}, {"label": "read_root", "kind": 2, "importPath": "backend.main", "description": "backend.main", "peekOfCode": "def read_root():\n    \"\"\"\n    ルートパスのウェルカムメッセージ\n    \"\"\"\n    return {\"message\": \"My LLM Platformへようこそ！\"}\n# サーバーを起動\nif __name__ == \"__main__\":\n    import argparse\n    # コマンドライン引数を解析\n    parser = argparse.ArgumentParser(description=\"My LLM Platform API Server\")", "detail": "backend.main", "documentation": {}}, {"label": "logger", "kind": 5, "importPath": "backend.main", "description": "backend.main", "peekOfCode": "logger = logging.getLogger(\"myllm\")\n# データベース初期化フラグ\nINIT_DB = settings.FORCE_DB_INIT\n# 認証モード\nAUTH_MODE = settings.AUTH_MODE\n# プラグインシステム有効化フラグ\nENABLE_PLUGINS = settings.ENABLE_PLUGINS\n# 起動時にデータベースを初期化\nif INIT_DB:\n    logger.info(\"データベースを初期化中...\")", "detail": "backend.main", "documentation": {}}, {"label": "INIT_DB", "kind": 5, "importPath": "backend.main", "description": "backend.main", "peekOfCode": "INIT_DB = settings.FORCE_DB_INIT\n# 認証モード\nAUTH_MODE = settings.AUTH_MODE\n# プラグインシステム有効化フラグ\nENABLE_PLUGINS = settings.ENABLE_PLUGINS\n# 起動時にデータベースを初期化\nif INIT_DB:\n    logger.info(\"データベースを初期化中...\")\n    # データベース初期化コードを追加\n    Base.metadata.create_all(bind=engine)", "detail": "backend.main", "documentation": {}}, {"label": "AUTH_MODE", "kind": 5, "importPath": "backend.main", "description": "backend.main", "peekOfCode": "AUTH_MODE = settings.AUTH_MODE\n# プラグインシステム有効化フラグ\nENABLE_PLUGINS = settings.ENABLE_PLUGINS\n# 起動時にデータベースを初期化\nif INIT_DB:\n    logger.info(\"データベースを初期化中...\")\n    # データベース初期化コードを追加\n    Base.metadata.create_all(bind=engine)\n    logger.info(\"データベースの初期化が完了しました\")\n# FastAPIアプリケーションを作成", "detail": "backend.main", "documentation": {}}, {"label": "ENABLE_PLUGINS", "kind": 5, "importPath": "backend.main", "description": "backend.main", "peekOfCode": "ENABLE_PLUGINS = settings.ENABLE_PLUGINS\n# 起動時にデータベースを初期化\nif INIT_DB:\n    logger.info(\"データベースを初期化中...\")\n    # データベース初期化コードを追加\n    Base.metadata.create_all(bind=engine)\n    logger.info(\"データベースの初期化が完了しました\")\n# FastAPIアプリケーションを作成\napp = FastAPI(\n    title=\"My LLM Platform API\",", "detail": "backend.main", "documentation": {}}, {"label": "app", "kind": 5, "importPath": "backend.main", "description": "backend.main", "peekOfCode": "app = FastAPI(\n    title=\"My LLM Platform API\",\n    description=\"LLMプラットフォームのバックエンドAPI\",\n    version=\"0.1.0\",\n    lifespan=None  # 後で定義するlifespanコンテキストマネージャーを使用\n)\n# CORSミドルウェアを設定\napp.add_middleware(\n    CORSMiddleware,\n    allow_origins=[\"*\"],  # 本番環境では特定のオリジンのみを許可するべき", "detail": "backend.main", "documentation": {}}, {"label": "app.router.lifespan_context", "kind": 5, "importPath": "backend.main", "description": "backend.main", "peekOfCode": "app.router.lifespan_context = lifespan\n# リクエストIDミドルウェア\****************(\"http\")\nasync def add_request_id(request: Request, call_next):\n    request_id = str(uuid.uuid4())\n    logger.info(f\"リクエスト開始: {request.method} {request.url.path} (ID: {request_id})\")\n    start_time = time.time()\n    response = await call_next(request)\n    process_time = time.time() - start_time\n    logger.info(f\"リクエスト完了: {request.method} {request.url.path} - {response.status_code} ({process_time:.4f}秒) (ID: {request_id})\")", "detail": "backend.main", "documentation": {}}, {"label": "source", "kind": 5, "importPath": "backend.run_coverage", "description": "backend.run_coverage", "peekOfCode": "source = .\nomit = \n    */tests/*\n    */venv/*\n    */env/*\n    */.venv/*\n    */.env/*\n    */site-packages/*\n    run_*.py\n    */migrations/*", "detail": "backend.run_coverage", "documentation": {}}, {"label": "omit", "kind": 5, "importPath": "backend.run_coverage", "description": "backend.run_coverage", "peekOfCode": "omit = \n    */tests/*\n    */venv/*\n    */env/*\n    */.venv/*\n    */.env/*\n    */site-packages/*\n    run_*.py\n    */migrations/*\n    */alembic/*", "detail": "backend.run_coverage", "documentation": {}}, {"label": "exclude_lines", "kind": 5, "importPath": "backend.run_coverage", "description": "backend.run_coverage", "peekOfCode": "exclude_lines =\n    pragma: no cover\n    def __repr__\n    raise NotImplementedError\n    if __name__ == .__main__.:\n    pass\n    raise ImportError\n\"\"\")\n    # コマンドライン引数を解析\n    args = sys.argv[1:] if len(sys.argv) > 1 else []", "detail": "backend.run_coverage", "documentation": {}}, {"label": "main", "kind": 2, "importPath": "examples.enum_client_example", "description": "examples.enum_client_example", "peekOfCode": "def main():\n    \"\"\"\n    列挙型を使用したLLMクライアントの使用例\n    \"\"\"\n    print(\"=== 列挙型を使用したLLMクライアント使用例 ===\")\n    # 列挙型を使用してクライアントを初期化\n    print(\"\\n=== 列挙型を使用したクライアント初期化 ===\")\n    # 1. 列挙型を使用\n    print(\"1. 列挙型を使用:\")\n    client1 = get_client(BackendType.VLLM)", "detail": "examples.enum_client_example", "documentation": {}}, {"label": "main", "kind": 2, "importPath": "examples.file_utils_example", "description": "examples.file_utils_example", "peekOfCode": "def main():\n    \"\"\"\n    ファイルユーティリティの使用例。\n    \"\"\"\n    # 例用の一時ディレクトリを作成\n    example_dir = os.path.join(os.path.dirname(__file__), 'example_files')\n    ensure_directory_exists(example_dir)\n    logger.info(f\"Created example directory: {example_dir}\")\n    # 例1: テキストファイルの書き込みと読み込み\n    text_file_path = os.path.join(example_dir, 'example.txt')", "detail": "examples.file_utils_example", "documentation": {}}, {"label": "logger", "kind": 5, "importPath": "examples.file_utils_example", "description": "examples.file_utils_example", "peekOfCode": "logger = logging.getLogger(__name__)\ndef main():\n    \"\"\"\n    ファイルユーティリティの使用例。\n    \"\"\"\n    # 例用の一時ディレクトリを作成\n    example_dir = os.path.join(os.path.dirname(__file__), 'example_files')\n    ensure_directory_exists(example_dir)\n    logger.info(f\"Created example directory: {example_dir}\")\n    # 例1: テキストファイルの書き込みと読み込み", "detail": "examples.file_utils_example", "documentation": {}}, {"label": "basic_chat_example", "kind": 2, "importPath": "examples.ollama_client_example", "description": "examples.ollama_client_example", "peekOfCode": "def basic_chat_example():\n    \"\"\"\n    基本的なチャットの例\n    \"\"\"\n    print(\"\\n=== 基本的なチャット ===\")\n    import requests\n    import json\n    try:\n        # Ollama APIを直接呼び出す\n        response = requests.post(", "detail": "examples.ollama_client_example", "documentation": {}}, {"label": "streaming_example", "kind": 2, "importPath": "examples.ollama_client_example", "description": "examples.ollama_client_example", "peekOfCode": "def streaming_example():\n    \"\"\"\n    ストリーミングレスポンスの例\n    \"\"\"\n    print(\"\\n=== ストリーミングレスポンス ===\")\n    import requests\n    import json\n    try:\n        # Ollama APIを直接呼び出す\n        response = requests.post(", "detail": "examples.ollama_client_example", "documentation": {}}, {"label": "generate_example", "kind": 2, "importPath": "examples.ollama_client_example", "description": "examples.ollama_client_example", "peekOfCode": "def generate_example():\n    \"\"\"\n    生成APIの例\n    \"\"\"\n    print(\"\\n=== 生成API ===\")\n    import requests\n    try:\n        # Ollamaの/api/generateエンドポイントを直接呼び出す\n        response = requests.post(\n            \"http://localhost:11434/api/generate\",", "detail": "examples.ollama_client_example", "documentation": {}}, {"label": "check_ollama_server", "kind": 2, "importPath": "examples.ollama_client_example", "description": "examples.ollama_client_example", "peekOfCode": "def check_ollama_server():\n    \"\"\"\n    Ollamaサーバーが実行されているか確認する\n    \"\"\"\n    import requests\n    print(\"Ollamaサーバーの接続を確認しています...\")\n    try:\n        response = requests.get(\"http://localhost:11434/api/version\", timeout=2)\n        print(f\"Ollamaサーバーからのレスポンス: {response.status_code}\")\n        if response.status_code == 200:", "detail": "examples.ollama_client_example", "documentation": {}}, {"label": "main", "kind": 2, "importPath": "examples.ollama_client_example", "description": "examples.ollama_client_example", "peekOfCode": "def main():\n    \"\"\"\n    すべての例を実行\n    \"\"\"\n    print(\"=== Ollamaバックエンドを使用したLLMプラットフォーム使用例 ===\")\n    # Ollamaサーバーが実行されているか確認\n    if not check_ollama_server():\n        print(\"\\nサンプルコードは以下のように使用します:\")\n        print(\"```python\")\n        print(\"from backend.inference import get_client\")", "detail": "examples.ollama_client_example", "documentation": {}}, {"label": "basic_chat_example", "kind": 2, "importPath": "examples.openai_client_example", "description": "examples.openai_client_example", "peekOfCode": "def basic_chat_example():\n    \"\"\"\n    基本的なチャットの例\n    \"\"\"\n    print(\"\\n=== 基本的なチャット ===\")\n    # デフォルトのバックエンドを使用\n    client = get_client()\n    response = client.chat.completions.create(\n        model=\"llama3.2\",\n        messages=[", "detail": "examples.openai_client_example", "documentation": {}}, {"label": "streaming_example", "kind": 2, "importPath": "examples.openai_client_example", "description": "examples.openai_client_example", "peekOfCode": "def streaming_example():\n    \"\"\"\n    ストリーミングレスポンスの例\n    \"\"\"\n    print(\"\\n=== ストリーミングレスポンス ===\")\n    client = get_client()\n    stream = client.chat.completions.create(\n        model=\"llama3.2\",\n        messages=[\n            {\"role\": \"user\", \"content\": \"AIの未来について教えてください。\"}", "detail": "examples.openai_client_example", "documentation": {}}, {"label": "different_backends_example", "kind": 2, "importPath": "examples.openai_client_example", "description": "examples.openai_client_example", "peekOfCode": "def different_backends_example():\n    \"\"\"\n    異なるバックエンドを使用する例\n    \"\"\"\n    print(\"\\n=== 異なるバックエンド ===\")\n    # Ollamaバックエンドを使用\n    ollama_client = get_client(\"ollama\")\n    print(\"Ollamaバックエンド:\")\n    response = ollama_client.chat.completions.create(\n        model=\"llama3.2\",", "detail": "examples.openai_client_example", "documentation": {}}, {"label": "tool_calling_example", "kind": 2, "importPath": "examples.openai_client_example", "description": "examples.openai_client_example", "peekOfCode": "def tool_calling_example():\n    \"\"\"\n    ツール/関数呼び出しの例\n    \"\"\"\n    print(\"\\n=== ツール/関数呼び出し ===\")\n    client = get_client()\n    # ツール定義\n    tools = [\n        {\n            \"type\": \"function\",", "detail": "examples.openai_client_example", "documentation": {}}, {"label": "main", "kind": 2, "importPath": "examples.openai_client_example", "description": "examples.openai_client_example", "peekOfCode": "def main():\n    \"\"\"\n    すべての例を実行\n    \"\"\"\n    print(\"=== OpenAIクライアントを使用したLLMプラットフォーム使用例 ===\")\n    try:\n        # 基本的なチャット\n        basic_chat_example()\n        # ストリーミングレスポンス\n        streaming_example()", "detail": "examples.openai_client_example", "documentation": {}}, {"label": "MyCustomPlugin", "kind": 6, "importPath": "examples.plugin_system_demo", "description": "examples.plugin_system_demo", "peekOfCode": "class MyCustomPlugin(PreprocessorPlugin):\n    def __init__(self, config=None):\n        super().__init__(config)\n        self.id = \"my_custom_plugin\"\n        self.name = \"My Custom Plugin\"\n        self.description = \"A custom preprocessor plugin\"\n    async def process(self, messages: list[Message], **kwargs) -> PluginResult:\n        # メッセージを処理するロジックを実装\n        if messages and messages[-1][\"role\"] == \"user\":\n            messages[-1][\"content\"] = f\"Enhanced: {messages[-1]['content']}\"", "detail": "examples.plugin_system_demo", "documentation": {}}, {"label": "print_separator", "kind": 2, "importPath": "examples.plugin_system_demo", "description": "examples.plugin_system_demo", "peekOfCode": "def print_separator(title):\n    \"\"\"セパレータを表示\"\"\"\n    print(\"\\n\" + \"=\" * 50)\n    print(f\" {title} \".center(50, \"=\"))\n    print(\"=\" * 50 + \"\\n\")\ndef list_plugins():\n    \"\"\"利用可能なプラグインを一覧表示\"\"\"\n    print_separator(\"利用可能なプラグイン\")\n    try:\n        response = requests.get(f\"{API_BASE_URL}/plugins\")", "detail": "examples.plugin_system_demo", "documentation": {}}, {"label": "list_plugins", "kind": 2, "importPath": "examples.plugin_system_demo", "description": "examples.plugin_system_demo", "peekOfCode": "def list_plugins():\n    \"\"\"利用可能なプラグインを一覧表示\"\"\"\n    print_separator(\"利用可能なプラグイン\")\n    try:\n        response = requests.get(f\"{API_BASE_URL}/plugins\")\n        if response.status_code == 200:\n            plugins = response.json()\n            print(f\"利用可能なプラグイン数: {len(plugins)}\")\n            for plugin in plugins:\n                print(f\"\\nプラグインID: {plugin['id']}\")", "detail": "examples.plugin_system_demo", "documentation": {}}, {"label": "rag_plugin_demo", "kind": 2, "importPath": "examples.plugin_system_demo", "description": "examples.plugin_system_demo", "peekOfCode": "def rag_plugin_demo():\n    \"\"\"RAGプラグインのデモ\"\"\"\n    print_separator(\"RAGプラグインのデモ\")\n    try:\n        # RAGプラグインを使用してチャット\n        response = client.chat.completions.create(\n            model=DEFAULT_MODEL,\n            messages=[\n                {\"role\": \"user\", \"content\": \"LLMプラットフォームのプラグインシステムについて教えてください。\"}\n            ],", "detail": "examples.plugin_system_demo", "documentation": {}}, {"label": "agent_plugin_demo", "kind": 2, "importPath": "examples.plugin_system_demo", "description": "examples.plugin_system_demo", "peekOfCode": "def agent_plugin_demo():\n    \"\"\"エージェントプラグインのデモ\"\"\"\n    print_separator(\"エージェントプラグインのデモ\")\n    try:\n        # エージェントプラグインを使用してチャット\n        response = client.chat.completions.create(\n            model=DEFAULT_MODEL,\n            messages=[\n                {\"role\": \"user\", \"content\": \"2023年の東京の人口は何人ですか？また、その数字を2倍にするといくつになりますか？\"}\n            ],", "detail": "examples.plugin_system_demo", "documentation": {}}, {"label": "custom_plugin_demo", "kind": 2, "importPath": "examples.plugin_system_demo", "description": "examples.plugin_system_demo", "peekOfCode": "def custom_plugin_demo():\n    \"\"\"カスタムプラグインの作成と使用のデモ\"\"\"\n    print_separator(\"カスタムプラグインのデモ\")\n    print(\"カスタムプラグインを作成するには、以下の手順に従います：\")\n    print(\"1. backend/plugins/custom/ ディレクトリにプラグインモジュールを作成\")\n    print(\"2. BasePluginクラスを継承したプラグインクラスを実装\")\n    print(\"3. プラグインマネージャーにプラグインを登録\")\n    print(\"4. チャットAPIでプラグインを使用\")\n    print(\"\\nサンプルコード:\")\n    print(\"\"\"", "detail": "examples.plugin_system_demo", "documentation": {}}, {"label": "main", "kind": 2, "importPath": "examples.plugin_system_demo", "description": "examples.plugin_system_demo", "peekOfCode": "def main():\n    \"\"\"メイン関数\"\"\"\n    print(\"=== LLMプラットフォーム プラグインシステムデモ ===\")\n    # 利用可能なプラグインを一覧表示\n    list_plugins()\n    # RAGプラグインのデモ\n    rag_plugin_demo()\n    # エージェントプラグインのデモ\n    agent_plugin_demo()\n    # カスタムプラグインのデモ", "detail": "examples.plugin_system_demo", "documentation": {}}, {"label": "API_BASE_URL", "kind": 5, "importPath": "examples.plugin_system_demo", "description": "examples.plugin_system_demo", "peekOfCode": "API_BASE_URL = \"http://localhost:8000/v1\"\nAPI_KEY = \"dummy-api-key\"  # 認証が不要な場合は任意の値\nDEFAULT_MODEL = \"llama3.2\"  # デフォルトのモデル\n# OpenAIクライアントを初期化\nclient = OpenAI(\n    base_url=API_BASE_URL,\n    api_key=API_KEY\n)\ndef print_separator(title):\n    \"\"\"セパレータを表示\"\"\"", "detail": "examples.plugin_system_demo", "documentation": {}}, {"label": "API_KEY", "kind": 5, "importPath": "examples.plugin_system_demo", "description": "examples.plugin_system_demo", "peekOfCode": "API_KEY = \"dummy-api-key\"  # 認証が不要な場合は任意の値\nDEFAULT_MODEL = \"llama3.2\"  # デフォルトのモデル\n# OpenAIクライアントを初期化\nclient = OpenAI(\n    base_url=API_BASE_URL,\n    api_key=API_KEY\n)\ndef print_separator(title):\n    \"\"\"セパレータを表示\"\"\"\n    print(\"\\n\" + \"=\" * 50)", "detail": "examples.plugin_system_demo", "documentation": {}}, {"label": "DEFAULT_MODEL", "kind": 5, "importPath": "examples.plugin_system_demo", "description": "examples.plugin_system_demo", "peekOfCode": "DEFAULT_MODEL = \"llama3.2\"  # デフォルトのモデル\n# OpenAIクライアントを初期化\nclient = OpenAI(\n    base_url=API_BASE_URL,\n    api_key=API_KEY\n)\ndef print_separator(title):\n    \"\"\"セパレータを表示\"\"\"\n    print(\"\\n\" + \"=\" * 50)\n    print(f\" {title} \".center(50, \"=\"))", "detail": "examples.plugin_system_demo", "documentation": {}}, {"label": "client", "kind": 5, "importPath": "examples.plugin_system_demo", "description": "examples.plugin_system_demo", "peekOfCode": "client = OpenAI(\n    base_url=API_BASE_URL,\n    api_key=API_KEY\n)\ndef print_separator(title):\n    \"\"\"セパレータを表示\"\"\"\n    print(\"\\n\" + \"=\" * 50)\n    print(f\" {title} \".center(50, \"=\"))\n    print(\"=\" * 50 + \"\\n\")\ndef list_plugins():", "detail": "examples.plugin_system_demo", "documentation": {}}, {"label": "basic_chat_completion", "kind": 2, "importPath": "examples.sample_llm", "description": "examples.sample_llm", "peekOfCode": "def basic_chat_completion():\n    \"\"\"\n    基本的なチャット補完の例\n    \"\"\"\n    print(\"\\n=== 基本的なチャット補完 ===\")\n    response = client.chat.completions.create(\n        model=\"llama3.2\",  # 使用するモデル名\n        messages=[\n            {\"role\": \"system\", \"content\": \"あなたは役立つAIアシスタントです。\"},\n            {\"role\": \"user\", \"content\": \"量子コンピューティングの基本原理を説明してください。\"}", "detail": "examples.sample_llm", "documentation": {}}, {"label": "streaming_chat_completion", "kind": 2, "importPath": "examples.sample_llm", "description": "examples.sample_llm", "peekOfCode": "def streaming_chat_completion():\n    \"\"\"\n    ストリーミングレスポンスの例\n    \"\"\"\n    print(\"\\n=== ストリーミングチャット補完 ===\")\n    stream = client.chat.completions.create(\n        model=\"llama3.2\",\n        messages=[\n            {\"role\": \"system\", \"content\": \"あなたは役立つAIアシスタントです。\"},\n            {\"role\": \"user\", \"content\": \"AIの歴史について簡単に説明してください。\"}", "detail": "examples.sample_llm", "documentation": {}}, {"label": "tool_calling_example", "kind": 2, "importPath": "examples.sample_llm", "description": "examples.sample_llm", "peekOfCode": "def tool_calling_example():\n    \"\"\"\n    ツール/関数呼び出しの例\n    \"\"\"\n    print(\"\\n=== ツール/関数呼び出し ===\")\n    # ツール定義\n    tools = [\n        {\n            \"type\": \"function\",\n            \"function\": {", "detail": "examples.sample_llm", "documentation": {}}, {"label": "text_completion_example", "kind": 2, "importPath": "examples.sample_llm", "description": "examples.sample_llm", "peekOfCode": "def text_completion_example():\n    \"\"\"\n    テキスト生成の例\n    \"\"\"\n    print(\"\\n=== テキスト生成 ===\")\n    response = client.completions.create(\n        model=\"llama3.2\",\n        prompt=\"日本の四季について説明してください。\",\n        max_tokens=300,\n        temperature=0.7", "detail": "examples.sample_llm", "documentation": {}}, {"label": "multiple_models_example", "kind": 2, "importPath": "examples.sample_llm", "description": "examples.sample_llm", "peekOfCode": "def multiple_models_example():\n    \"\"\"\n    異なるモデルを使用する例\n    \"\"\"\n    print(\"\\n=== 異なるモデルの使用 ===\")\n    models = [\"llama3.2\", \"mistral\", \"qwen2.5-coder\"]\n    prompt = \"AIの将来について短く説明してください。\"\n    for model in models:\n        try:\n            print(f\"\\nモデル: {model}\")", "detail": "examples.sample_llm", "documentation": {}}, {"label": "custom_parameters_example", "kind": 2, "importPath": "examples.sample_llm", "description": "examples.sample_llm", "peekOfCode": "def custom_parameters_example():\n    \"\"\"\n    カスタムパラメータを使用する例\n    \"\"\"\n    print(\"\\n=== カスタムパラメータの使用 ===\")\n    response = client.chat.completions.create(\n        model=\"llama3.2\",\n        messages=[\n            {\"role\": \"system\", \"content\": \"あなたは創造的なAIアシスタントです。\"},\n            {\"role\": \"user\", \"content\": \"短い詩を書いてください。\"}", "detail": "examples.sample_llm", "documentation": {}}, {"label": "main", "kind": 2, "importPath": "examples.sample_llm", "description": "examples.sample_llm", "peekOfCode": "def main():\n    \"\"\"\n    すべての例を実行\n    \"\"\"\n    print(\"=== LLMプラットフォーム使用例 ===\")\n    print(f\"APIエンドポイント: {API_BASE_URL}\")\n    try:\n        # 基本的な使用例\n        basic_chat_completion()\n        # ストリーミングレスポンス", "detail": "examples.sample_llm", "documentation": {}}, {"label": "API_BASE_URL", "kind": 5, "importPath": "examples.sample_llm", "description": "examples.sample_llm", "peekOfCode": "API_BASE_URL = \"http://localhost:8000/v1\"\nAPI_KEY = \"dummy-api-key\"  # 認証が必要ない場合は任意の値\n# OpenAIクライアントの初期化\nclient = OpenAI(\n    base_url=API_BASE_URL,\n    api_key=API_KEY\n)\ndef basic_chat_completion():\n    \"\"\"\n    基本的なチャット補完の例", "detail": "examples.sample_llm", "documentation": {}}, {"label": "API_KEY", "kind": 5, "importPath": "examples.sample_llm", "description": "examples.sample_llm", "peekOfCode": "API_KEY = \"dummy-api-key\"  # 認証が必要ない場合は任意の値\n# OpenAIクライアントの初期化\nclient = OpenAI(\n    base_url=API_BASE_URL,\n    api_key=API_KEY\n)\ndef basic_chat_completion():\n    \"\"\"\n    基本的なチャット補完の例\n    \"\"\"", "detail": "examples.sample_llm", "documentation": {}}, {"label": "client", "kind": 5, "importPath": "examples.sample_llm", "description": "examples.sample_llm", "peekOfCode": "client = OpenAI(\n    base_url=API_BASE_URL,\n    api_key=API_KEY\n)\ndef basic_chat_completion():\n    \"\"\"\n    基本的なチャット補完の例\n    \"\"\"\n    print(\"\\n=== 基本的なチャット補完 ===\")\n    response = client.chat.completions.create(", "detail": "examples.sample_llm", "documentation": {}}, {"label": "basic_chat_completion", "kind": 2, "importPath": "examples.sample_llm_cn", "description": "examples.sample_llm_cn", "peekOfCode": "def basic_chat_completion():\n    \"\"\"\n    基本的なチャット補完の例\n    \"\"\"\n    print(\"\\n=== 基本的なチャット補完 ===\")\n    response = client.chat.completions.create(\n        model=\"llama3.2\",  # 使用するモデル名\n        messages=[\n            {\"role\": \"system\", \"content\": \"あなたは役に立つAIアシスタントです。\"},\n            {\"role\": \"user\", \"content\": \"量子コンピューティングの基本原理を説明してください。\"}", "detail": "examples.sample_llm_cn", "documentation": {}}, {"label": "streaming_chat_completion", "kind": 2, "importPath": "examples.sample_llm_cn", "description": "examples.sample_llm_cn", "peekOfCode": "def streaming_chat_completion():\n    \"\"\"\n    ストリーミングレスポンスの例\n    \"\"\"\n    print(\"\\n=== ストリーミングチャット補完 ===\")\n    stream = client.chat.completions.create(\n        model=\"llama3.2\",\n        messages=[\n            {\"role\": \"system\", \"content\": \"あなたは役に立つAIアシスタントです。\"},\n            {\"role\": \"user\", \"content\": \"人工知能の歴史を簡単に紹介してください。\"}", "detail": "examples.sample_llm_cn", "documentation": {}}, {"label": "tool_calling_example", "kind": 2, "importPath": "examples.sample_llm_cn", "description": "examples.sample_llm_cn", "peekOfCode": "def tool_calling_example():\n    \"\"\"\n    ツール/関数呼び出しの例\n    \"\"\"\n    print(\"\\n=== ツール/関数呼び出し ===\")\n    # ツール定義\n    tools = [\n        {\n            \"type\": \"function\",\n            \"function\": {", "detail": "examples.sample_llm_cn", "documentation": {}}, {"label": "text_completion_example", "kind": 2, "importPath": "examples.sample_llm_cn", "description": "examples.sample_llm_cn", "peekOfCode": "def text_completion_example():\n    \"\"\"\n    テキスト生成の例\n    \"\"\"\n    print(\"\\n=== テキスト生成 ===\")\n    response = client.completions.create(\n        model=\"llama3.2\",\n        prompt=\"中国の四季の特徴を紹介してください。\",\n        max_tokens=300,\n        temperature=0.7", "detail": "examples.sample_llm_cn", "documentation": {}}, {"label": "multiple_models_example", "kind": 2, "importPath": "examples.sample_llm_cn", "description": "examples.sample_llm_cn", "peekOfCode": "def multiple_models_example():\n    \"\"\"\n    異なるモデルを使用する例\n    \"\"\"\n    print(\"\\n=== 異なるモデルを使用 ===\")\n    models = [\"llama3.2\", \"mistral\", \"qwen2.5-coder\"]\n    prompt = \"人工知能の未来について簡潔に説明してください。\"\n    for model in models:\n        try:\n            print(f\"\\nモデル: {model}\")", "detail": "examples.sample_llm_cn", "documentation": {}}, {"label": "custom_parameters_example", "kind": 2, "importPath": "examples.sample_llm_cn", "description": "examples.sample_llm_cn", "peekOfCode": "def custom_parameters_example():\n    \"\"\"\n    カスタムパラメータを使用する例\n    \"\"\"\n    print(\"\\n=== カスタムパラメータを使用 ===\")\n    response = client.chat.completions.create(\n        model=\"llama3.2\",\n        messages=[\n            {\"role\": \"system\", \"content\": \"あなたは創造力豊かなAIアシスタントです。\"},\n            {\"role\": \"user\", \"content\": \"短い詩を書いてください。\"}", "detail": "examples.sample_llm_cn", "documentation": {}}, {"label": "main", "kind": 2, "importPath": "examples.sample_llm_cn", "description": "examples.sample_llm_cn", "peekOfCode": "def main():\n    \"\"\"\n    すべての例を実行\n    \"\"\"\n    print(\"=== LLMプラットフォーム使用例 ===\")\n    print(f\"APIエンドポイント: {API_BASE_URL}\")\n    try:\n        # 基本的な使用例\n        basic_chat_completion()\n        # ストリーミングレスポンス", "detail": "examples.sample_llm_cn", "documentation": {}}, {"label": "API_BASE_URL", "kind": 5, "importPath": "examples.sample_llm_cn", "description": "examples.sample_llm_cn", "peekOfCode": "API_BASE_URL = \"http://localhost:8000/v1\"\nAPI_KEY = \"dummy-api-key\"  # 認証が不要な場合、任意の値を使用可能\n# OpenAIクライアントの初期化\nclient = OpenAI(\n    base_url=API_BASE_URL,\n    api_key=API_KEY\n)\ndef basic_chat_completion():\n    \"\"\"\n    基本的なチャット補完の例", "detail": "examples.sample_llm_cn", "documentation": {}}, {"label": "API_KEY", "kind": 5, "importPath": "examples.sample_llm_cn", "description": "examples.sample_llm_cn", "peekOfCode": "API_KEY = \"dummy-api-key\"  # 認証が不要な場合、任意の値を使用可能\n# OpenAIクライアントの初期化\nclient = OpenAI(\n    base_url=API_BASE_URL,\n    api_key=API_KEY\n)\ndef basic_chat_completion():\n    \"\"\"\n    基本的なチャット補完の例\n    \"\"\"", "detail": "examples.sample_llm_cn", "documentation": {}}, {"label": "client", "kind": 5, "importPath": "examples.sample_llm_cn", "description": "examples.sample_llm_cn", "peekOfCode": "client = OpenAI(\n    base_url=API_BASE_URL,\n    api_key=API_KEY\n)\ndef basic_chat_completion():\n    \"\"\"\n    基本的なチャット補完の例\n    \"\"\"\n    print(\"\\n=== 基本的なチャット補完 ===\")\n    response = client.chat.completions.create(", "detail": "examples.sample_llm_cn", "documentation": {}}, {"label": "basic_chat_completion", "kind": 2, "importPath": "examples.sample_llm_jp", "description": "examples.sample_llm_jp", "peekOfCode": "def basic_chat_completion():\n    \"\"\"\n    基本的なチャット補完の例\n    \"\"\"\n    print(\"\\n=== 基本的なチャット補完 ===\")\n    response = client.chat.completions.create(\n        model=\"llama3.2\",  # 使用するモデル名\n        messages=[\n            {\"role\": \"system\", \"content\": \"あなたは役立つAIアシスタントです。\"},\n            {\"role\": \"user\", \"content\": \"量子コンピューティングの基本原理を説明してください。\"}", "detail": "examples.sample_llm_jp", "documentation": {}}, {"label": "streaming_chat_completion", "kind": 2, "importPath": "examples.sample_llm_jp", "description": "examples.sample_llm_jp", "peekOfCode": "def streaming_chat_completion():\n    \"\"\"\n    ストリーミングレスポンスの例\n    \"\"\"\n    print(\"\\n=== ストリーミングチャット補完 ===\")\n    stream = client.chat.completions.create(\n        model=\"llama3.2\",\n        messages=[\n            {\"role\": \"system\", \"content\": \"あなたは役立つAIアシスタントです。\"},\n            {\"role\": \"user\", \"content\": \"AIの歴史について簡単に説明してください。\"}", "detail": "examples.sample_llm_jp", "documentation": {}}, {"label": "tool_calling_example", "kind": 2, "importPath": "examples.sample_llm_jp", "description": "examples.sample_llm_jp", "peekOfCode": "def tool_calling_example():\n    \"\"\"\n    ツール/関数呼び出しの例\n    \"\"\"\n    print(\"\\n=== ツール/関数呼び出し ===\")\n    # ツール定義\n    tools = [\n        {\n            \"type\": \"function\",\n            \"function\": {", "detail": "examples.sample_llm_jp", "documentation": {}}, {"label": "text_completion_example", "kind": 2, "importPath": "examples.sample_llm_jp", "description": "examples.sample_llm_jp", "peekOfCode": "def text_completion_example():\n    \"\"\"\n    テキスト生成の例\n    \"\"\"\n    print(\"\\n=== テキスト生成 ===\")\n    response = client.completions.create(\n        model=\"llama3.2\",\n        prompt=\"日本の四季について説明してください。\",\n        max_tokens=300,\n        temperature=0.7", "detail": "examples.sample_llm_jp", "documentation": {}}, {"label": "multiple_models_example", "kind": 2, "importPath": "examples.sample_llm_jp", "description": "examples.sample_llm_jp", "peekOfCode": "def multiple_models_example():\n    \"\"\"\n    異なるモデルを使用する例\n    \"\"\"\n    print(\"\\n=== 異なるモデルの使用 ===\")\n    models = [\"llama3.2\", \"mistral\", \"qwen2.5-coder\"]\n    prompt = \"AIの将来について短く説明してください。\"\n    for model in models:\n        try:\n            print(f\"\\nモデル: {model}\")", "detail": "examples.sample_llm_jp", "documentation": {}}, {"label": "custom_parameters_example", "kind": 2, "importPath": "examples.sample_llm_jp", "description": "examples.sample_llm_jp", "peekOfCode": "def custom_parameters_example():\n    \"\"\"\n    カスタムパラメータを使用する例\n    \"\"\"\n    print(\"\\n=== カスタムパラメータの使用 ===\")\n    response = client.chat.completions.create(\n        model=\"llama3.2\",\n        messages=[\n            {\"role\": \"system\", \"content\": \"あなたは創造的なAIアシスタントです。\"},\n            {\"role\": \"user\", \"content\": \"短い詩を書いてください。\"}", "detail": "examples.sample_llm_jp", "documentation": {}}, {"label": "main", "kind": 2, "importPath": "examples.sample_llm_jp", "description": "examples.sample_llm_jp", "peekOfCode": "def main():\n    \"\"\"\n    すべての例を実行\n    \"\"\"\n    print(\"=== LLMプラットフォーム使用例 ===\")\n    print(f\"APIエンドポイント: {API_BASE_URL}\")\n    try:\n        # 基本的な使用例\n        basic_chat_completion()\n        # ストリーミングレスポンス", "detail": "examples.sample_llm_jp", "documentation": {}}, {"label": "API_BASE_URL", "kind": 5, "importPath": "examples.sample_llm_jp", "description": "examples.sample_llm_jp", "peekOfCode": "API_BASE_URL = \"http://localhost:8000/v1\"\nAPI_KEY = \"dummy-api-key\"  # 認証が必要ない場合は任意の値\nDEFAULT_MODEL = \"llama3.2\"  # 使用するモデル名\n# OpenAIクライアントの初期化\nclient = OpenAI(\n    base_url=API_BASE_URL,\n    api_key=API_KEY\n)\ndef basic_chat_completion():\n    \"\"\"", "detail": "examples.sample_llm_jp", "documentation": {}}, {"label": "API_KEY", "kind": 5, "importPath": "examples.sample_llm_jp", "description": "examples.sample_llm_jp", "peekOfCode": "API_KEY = \"dummy-api-key\"  # 認証が必要ない場合は任意の値\nDEFAULT_MODEL = \"llama3.2\"  # 使用するモデル名\n# OpenAIクライアントの初期化\nclient = OpenAI(\n    base_url=API_BASE_URL,\n    api_key=API_KEY\n)\ndef basic_chat_completion():\n    \"\"\"\n    基本的なチャット補完の例", "detail": "examples.sample_llm_jp", "documentation": {}}, {"label": "DEFAULT_MODEL", "kind": 5, "importPath": "examples.sample_llm_jp", "description": "examples.sample_llm_jp", "peekOfCode": "DEFAULT_MODEL = \"llama3.2\"  # 使用するモデル名\n# OpenAIクライアントの初期化\nclient = OpenAI(\n    base_url=API_BASE_URL,\n    api_key=API_KEY\n)\ndef basic_chat_completion():\n    \"\"\"\n    基本的なチャット補完の例\n    \"\"\"", "detail": "examples.sample_llm_jp", "documentation": {}}, {"label": "client", "kind": 5, "importPath": "examples.sample_llm_jp", "description": "examples.sample_llm_jp", "peekOfCode": "client = OpenAI(\n    base_url=API_BASE_URL,\n    api_key=API_KEY\n)\ndef basic_chat_completion():\n    \"\"\"\n    基本的なチャット補完の例\n    \"\"\"\n    print(\"\\n=== 基本的なチャット補完 ===\")\n    response = client.chat.completions.create(", "detail": "examples.sample_llm_jp", "documentation": {}}, {"label": "clear_screen", "kind": 2, "importPath": "examples.simple_chatbot", "description": "examples.simple_chatbot", "peekOfCode": "def clear_screen():\n    \"\"\"清除终端屏幕\"\"\"\n    os.system('cls' if os.name == 'nt' else 'clear')\ndef print_header():\n    \"\"\"打印聊天机器人标题\"\"\"\n    print(\"=\" * 50)\n    print(\"       简单聊天机器人 - 使用LLM平台API       \")\n    print(\"=\" * 50)\n    print(f\"使用模型: {DEFAULT_MODEL}\")\n    print(\"输入 'exit' 或 'quit' 退出\")", "detail": "examples.simple_chatbot", "documentation": {}}, {"label": "print_header", "kind": 2, "importPath": "examples.simple_chatbot", "description": "examples.simple_chatbot", "peekOfCode": "def print_header():\n    \"\"\"打印聊天机器人标题\"\"\"\n    print(\"=\" * 50)\n    print(\"       简单聊天机器人 - 使用LLM平台API       \")\n    print(\"=\" * 50)\n    print(f\"使用模型: {DEFAULT_MODEL}\")\n    print(\"输入 'exit' 或 'quit' 退出\")\n    print(\"输入 'clear' 清除聊天历史\")\n    print(\"=\" * 50)\n    print()", "detail": "examples.simple_chatbot", "documentation": {}}, {"label": "chat_with_llm", "kind": 2, "importPath": "examples.simple_chatbot", "description": "examples.simple_chatbot", "peekOfCode": "def chat_with_llm():\n    \"\"\"与LLM进行交互的主函数\"\"\"\n    clear_screen()\n    print_header()\n    # 初始化聊天历史\n    messages = [\n        {\"role\": \"system\", \"content\": \"你是一个有用的AI助手，名叫小智。你会提供友好、准确、有帮助的回答。\"}\n    ]\n    while True:\n        # 获取用户输入", "detail": "examples.simple_chatbot", "documentation": {}}, {"label": "API_BASE_URL", "kind": 5, "importPath": "examples.simple_chatbot", "description": "examples.simple_chatbot", "peekOfCode": "API_BASE_URL = \"http://localhost:8000/v1\"\nAPI_KEY = \"dummy-api-key\"  # 如果不需要认证，可以是任意值\nDEFAULT_MODEL = \"llama3.2\"  # 默认使用的模型\n# 初始化OpenAI客户端\nclient = OpenAI(\n    base_url=API_BASE_URL,\n    api_key=API_KEY\n)\ndef clear_screen():\n    \"\"\"清除终端屏幕\"\"\"", "detail": "examples.simple_chatbot", "documentation": {}}, {"label": "API_KEY", "kind": 5, "importPath": "examples.simple_chatbot", "description": "examples.simple_chatbot", "peekOfCode": "API_KEY = \"dummy-api-key\"  # 如果不需要认证，可以是任意值\nDEFAULT_MODEL = \"llama3.2\"  # 默认使用的模型\n# 初始化OpenAI客户端\nclient = OpenAI(\n    base_url=API_BASE_URL,\n    api_key=API_KEY\n)\ndef clear_screen():\n    \"\"\"清除终端屏幕\"\"\"\n    os.system('cls' if os.name == 'nt' else 'clear')", "detail": "examples.simple_chatbot", "documentation": {}}, {"label": "DEFAULT_MODEL", "kind": 5, "importPath": "examples.simple_chatbot", "description": "examples.simple_chatbot", "peekOfCode": "DEFAULT_MODEL = \"llama3.2\"  # 默认使用的模型\n# 初始化OpenAI客户端\nclient = OpenAI(\n    base_url=API_BASE_URL,\n    api_key=API_KEY\n)\ndef clear_screen():\n    \"\"\"清除终端屏幕\"\"\"\n    os.system('cls' if os.name == 'nt' else 'clear')\ndef print_header():", "detail": "examples.simple_chatbot", "documentation": {}}, {"label": "client", "kind": 5, "importPath": "examples.simple_chatbot", "description": "examples.simple_chatbot", "peekOfCode": "client = OpenAI(\n    base_url=API_BASE_URL,\n    api_key=API_KEY\n)\ndef clear_screen():\n    \"\"\"清除终端屏幕\"\"\"\n    os.system('cls' if os.name == 'nt' else 'clear')\ndef print_header():\n    \"\"\"打印聊天机器人标题\"\"\"\n    print(\"=\" * 50)", "detail": "examples.simple_chatbot", "documentation": {}}, {"label": "clear_screen", "kind": 2, "importPath": "examples.simple_chatbot_jp", "description": "examples.simple_chatbot_jp", "peekOfCode": "def clear_screen():\n    \"\"\"ターミナル画面をクリアする\"\"\"\n    os.system('cls' if os.name == 'nt' else 'clear')\ndef print_header():\n    \"\"\"チャットボットのヘッダーを表示\"\"\"\n    print(\"=\" * 50)\n    print(\"       シンプルチャットボット - LLMプラットフォームAPI使用       \")\n    print(\"=\" * 50)\n    print(f\"使用モデル: {DEFAULT_MODEL}\")\n    print(\"'exit'または'quit'と入力して終了\")", "detail": "examples.simple_chatbot_jp", "documentation": {}}, {"label": "print_header", "kind": 2, "importPath": "examples.simple_chatbot_jp", "description": "examples.simple_chatbot_jp", "peekOfCode": "def print_header():\n    \"\"\"チャットボットのヘッダーを表示\"\"\"\n    print(\"=\" * 50)\n    print(\"       シンプルチャットボット - LLMプラットフォームAPI使用       \")\n    print(\"=\" * 50)\n    print(f\"使用モデル: {DEFAULT_MODEL}\")\n    print(\"'exit'または'quit'と入力して終了\")\n    print(\"'clear'と入力して会話履歴をクリア\")\n    print(\"=\" * 50)\n    print()", "detail": "examples.simple_chatbot_jp", "documentation": {}}, {"label": "chat_with_llm", "kind": 2, "importPath": "examples.simple_chatbot_jp", "description": "examples.simple_chatbot_jp", "peekOfCode": "def chat_with_llm():\n    \"\"\"LLMとの対話を行うメイン関数\"\"\"\n    clear_screen()\n    print_header()\n    # チャット履歴の初期化\n    messages = [\n        {\"role\": \"system\", \"content\": \"あなたは役立つAIアシスタントの「アイ」です。友好的で正確で役立つ回答を提供してください。\"}\n    ]\n    while True:\n        # ユーザー入力を取得", "detail": "examples.simple_chatbot_jp", "documentation": {}}, {"label": "API_BASE_URL", "kind": 5, "importPath": "examples.simple_chatbot_jp", "description": "examples.simple_chatbot_jp", "peekOfCode": "API_BASE_URL = \"http://localhost:8000/v1\"\nAPI_KEY = \"dummy-api-key\"  # 認証が必要ない場合は任意の値\nDEFAULT_MODEL = \"llama3.2\"  # デフォルトのモデル\n# OpenAIクライアントの初期化\nclient = OpenAI(\n    base_url=API_BASE_URL,\n    api_key=API_KEY\n)\ndef clear_screen():\n    \"\"\"ターミナル画面をクリアする\"\"\"", "detail": "examples.simple_chatbot_jp", "documentation": {}}, {"label": "API_KEY", "kind": 5, "importPath": "examples.simple_chatbot_jp", "description": "examples.simple_chatbot_jp", "peekOfCode": "API_KEY = \"dummy-api-key\"  # 認証が必要ない場合は任意の値\nDEFAULT_MODEL = \"llama3.2\"  # デフォルトのモデル\n# OpenAIクライアントの初期化\nclient = OpenAI(\n    base_url=API_BASE_URL,\n    api_key=API_KEY\n)\ndef clear_screen():\n    \"\"\"ターミナル画面をクリアする\"\"\"\n    os.system('cls' if os.name == 'nt' else 'clear')", "detail": "examples.simple_chatbot_jp", "documentation": {}}, {"label": "DEFAULT_MODEL", "kind": 5, "importPath": "examples.simple_chatbot_jp", "description": "examples.simple_chatbot_jp", "peekOfCode": "DEFAULT_MODEL = \"llama3.2\"  # デフォルトのモデル\n# OpenAIクライアントの初期化\nclient = OpenAI(\n    base_url=API_BASE_URL,\n    api_key=API_KEY\n)\ndef clear_screen():\n    \"\"\"ターミナル画面をクリアする\"\"\"\n    os.system('cls' if os.name == 'nt' else 'clear')\ndef print_header():", "detail": "examples.simple_chatbot_jp", "documentation": {}}, {"label": "client", "kind": 5, "importPath": "examples.simple_chatbot_jp", "description": "examples.simple_chatbot_jp", "peekOfCode": "client = OpenAI(\n    base_url=API_BASE_URL,\n    api_key=API_KEY\n)\ndef clear_screen():\n    \"\"\"ターミナル画面をクリアする\"\"\"\n    os.system('cls' if os.name == 'nt' else 'clear')\ndef print_header():\n    \"\"\"チャットボットのヘッダーを表示\"\"\"\n    print(\"=\" * 50)", "detail": "examples.simple_chatbot_jp", "documentation": {}}, {"label": "main", "kind": 2, "importPath": "examples.simple_openai_client", "description": "examples.simple_openai_client", "peekOfCode": "def main():\n    \"\"\"\n    シンプルなOpenAIクライアントの使用例\n    \"\"\"\n    print(\"=== シンプルなOpenAIクライアント使用例 ===\")\n    # 1. デフォルトのバックエンドを使用\n    print(\"\\n=== デフォルトのバックエンド ===\")\n    client = get_client()\n    response = client.chat.completions.create(\n        model=\"llama3.2\",", "detail": "examples.simple_openai_client", "documentation": {}}, {"label": "basic_chat_completion", "kind": 2, "importPath": "examples.simplified_llm", "description": "examples.simplified_llm", "peekOfCode": "def basic_chat_completion():\n    \"\"\"\n    基本的なチャット補完の例\n    \"\"\"\n    print(\"\\n=== 基本的なチャット補完 ===\")\n    response = client.chat.completions.create(\n        model=\"llama3.2\",  # 使用するモデル名\n        messages=[\n            {\"role\": \"system\", \"content\": \"あなたは役立つAIアシスタントです。\"},\n            {\"role\": \"user\", \"content\": \"量子コンピューティングの基本原理を説明してください。\"}", "detail": "examples.simplified_llm", "documentation": {}}, {"label": "streaming_chat_completion", "kind": 2, "importPath": "examples.simplified_llm", "description": "examples.simplified_llm", "peekOfCode": "def streaming_chat_completion():\n    \"\"\"\n    ストリーミングレスポンスの例\n    \"\"\"\n    print(\"\\n=== ストリーミングチャット補完 ===\")\n    stream = client.chat.completions.create(\n        model=\"llama3.2\",\n        messages=[\n            {\"role\": \"system\", \"content\": \"あなたは役立つAIアシスタントです。\"},\n            {\"role\": \"user\", \"content\": \"AIの歴史について簡単に説明してください。\"}", "detail": "examples.simplified_llm", "documentation": {}}, {"label": "tool_calling_example", "kind": 2, "importPath": "examples.simplified_llm", "description": "examples.simplified_llm", "peekOfCode": "def tool_calling_example():\n    \"\"\"\n    ツール/関数呼び出しの例\n    \"\"\"\n    print(\"\\n=== ツール/関数呼び出し ===\")\n    # ツール定義\n    tools = [\n        {\n            \"type\": \"function\",\n            \"function\": {", "detail": "examples.simplified_llm", "documentation": {}}, {"label": "main", "kind": 2, "importPath": "examples.simplified_llm", "description": "examples.simplified_llm", "peekOfCode": "def main():\n    \"\"\"\n    すべての例を実行\n    \"\"\"\n    print(\"=== 簡略化されたLLMプラットフォーム使用例 ===\")\n    print(f\"APIエンドポイント: {API_BASE_URL}\")\n    try:\n        # 基本的な使用例\n        basic_chat_completion()\n        # ストリーミングレスポンス", "detail": "examples.simplified_llm", "documentation": {}}, {"label": "API_BASE_URL", "kind": 5, "importPath": "examples.simplified_llm", "description": "examples.simplified_llm", "peekOfCode": "API_BASE_URL = \"http://localhost:8000/v1\"\nAPI_KEY = \"dummy-api-key\"  # 認証が必要ない場合は任意の値\n# OpenAIクライアントの初期化\nclient = OpenAI(\n    base_url=API_BASE_URL,\n    api_key=API_KEY\n)\ndef basic_chat_completion():\n    \"\"\"\n    基本的なチャット補完の例", "detail": "examples.simplified_llm", "documentation": {}}, {"label": "API_KEY", "kind": 5, "importPath": "examples.simplified_llm", "description": "examples.simplified_llm", "peekOfCode": "API_KEY = \"dummy-api-key\"  # 認証が必要ない場合は任意の値\n# OpenAIクライアントの初期化\nclient = OpenAI(\n    base_url=API_BASE_URL,\n    api_key=API_KEY\n)\ndef basic_chat_completion():\n    \"\"\"\n    基本的なチャット補完の例\n    \"\"\"", "detail": "examples.simplified_llm", "documentation": {}}, {"label": "client", "kind": 5, "importPath": "examples.simplified_llm", "description": "examples.simplified_llm", "peekOfCode": "client = OpenAI(\n    base_url=API_BASE_URL,\n    api_key=API_KEY\n)\ndef basic_chat_completion():\n    \"\"\"\n    基本的なチャット補完の例\n    \"\"\"\n    print(\"\\n=== 基本的なチャット補完 ===\")\n    response = client.chat.completions.create(", "detail": "examples.simplified_llm", "documentation": {}}, {"label": "main", "kind": 2, "importPath": "examples.supabase_vector_store_example", "description": "examples.supabase_vector_store_example", "peekOfCode": "def main():\n    \"\"\"\n    Supabaseベクトルストアの使用例を実行します\n    \"\"\"\n    # 設定を確認\n    if not settings.VECTOR_ENABLED_FLAG:\n        logger.info(\"ベクトルデータベース機能が無効になっています\")\n        logger.info(\"ベクトルデータベース機能を有効にします\")\n        settings.VECTOR_ENABLED_FLAG = True\n    if settings.VECTOR_DATABASE_TYPE != \"supabase\":", "detail": "examples.supabase_vector_store_example", "documentation": {}}, {"label": "logger", "kind": 5, "importPath": "examples.supabase_vector_store_example", "description": "examples.supabase_vector_store_example", "peekOfCode": "logger = logging.getLogger(__name__)\n# プロジェクトのルートディレクトリをパスに追加\nsys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))\n# 必要なモジュールをインポート\nfrom backend.plugins.rag.vector_factory import VectorStoreFactory\nfrom backend.plugins.rag.document_loader import DocumentLoader\nfrom backend.config import settings\ndef main():\n    \"\"\"\n    Supabaseベクトルストアの使用例を実行します", "detail": "examples.supabase_vector_store_example", "documentation": {}}, {"label": "main", "kind": 2, "importPath": "examples.super_simple_llm", "description": "examples.super_simple_llm", "peekOfCode": "def main():\n    \"\"\"\n    シンプルなクライアントの使用例\n    \"\"\"\n    print(\"=== 超シンプルなLLMプラットフォーム使用例 ===\")\n    # 1. デフォルトのバックエンドとモデルを使用\n    print(\"\\n=== デフォルトのバックエンドとモデル ===\")\n    client = get_client()\n    response = client.chat.completions.create(\n        model=\"llama3.2\",", "detail": "examples.super_simple_llm", "documentation": {}}, {"label": "get_weather", "kind": 2, "importPath": "examples.tool_calling_assistant", "description": "examples.tool_calling_assistant", "peekOfCode": "def get_weather(city, unit=\"celsius\"):\n    \"\"\"\n    获取指定城市的天气信息（模拟）\n    在实际应用中，这里应该调用真实的天气API\n    \"\"\"\n    # 模拟天气数据\n    weather_data = {\n        \"北京\": {\"condition\": \"晴朗\", \"temperature\": 25, \"humidity\": 40},\n        \"上海\": {\"condition\": \"多云\", \"temperature\": 28, \"humidity\": 65},\n        \"广州\": {\"condition\": \"小雨\", \"temperature\": 30, \"humidity\": 80},", "detail": "examples.tool_calling_assistant", "documentation": {}}, {"label": "get_current_time", "kind": 2, "importPath": "examples.tool_calling_assistant", "description": "examples.tool_calling_assistant", "peekOfCode": "def get_current_time(timezone=\"Asia/Shanghai\"):\n    \"\"\"\n    获取当前时间\n    在实际应用中，应该处理不同的时区\n    \"\"\"\n    now = datetime.datetime.now()\n    return f\"当前时间是：{now.strftime('%Y-%m-%d %H:%M:%S')}（{timezone}时区）\"\ndef search_web(query):\n    \"\"\"\n    模拟网络搜索", "detail": "examples.tool_calling_assistant", "documentation": {}}, {"label": "search_web", "kind": 2, "importPath": "examples.tool_calling_assistant", "description": "examples.tool_calling_assistant", "peekOfCode": "def search_web(query):\n    \"\"\"\n    模拟网络搜索\n    在实际应用中，这里应该调用真实的搜索API\n    \"\"\"\n    return f\"以下是关于\\\"{query}\\\"的搜索结果（模拟）：\\n1. {query}的维基百科页面\\n2. 关于{query}的最新新闻\\n3. {query}的相关学术研究\"\ndef calculate_expression(expression):\n    \"\"\"\n    计算数学表达式\n    \"\"\"", "detail": "examples.tool_calling_assistant", "documentation": {}}, {"label": "calculate_expression", "kind": 2, "importPath": "examples.tool_calling_assistant", "description": "examples.tool_calling_assistant", "peekOfCode": "def calculate_expression(expression):\n    \"\"\"\n    计算数学表达式\n    \"\"\"\n    try:\n        # 警告：在生产环境中使用eval是不安全的\n        # 这里仅作为示例，实际应用中应使用更安全的方法\n        result = eval(expression)\n        return f\"表达式 {expression} 的计算结果是 {result}\"\n    except Exception as e:", "detail": "examples.tool_calling_assistant", "documentation": {}}, {"label": "clear_screen", "kind": 2, "importPath": "examples.tool_calling_assistant", "description": "examples.tool_calling_assistant", "peekOfCode": "def clear_screen():\n    \"\"\"清除终端屏幕\"\"\"\n    os.system('cls' if os.name == 'nt' else 'clear')\ndef print_header():\n    \"\"\"打印助手标题\"\"\"\n    print(\"=\" * 60)\n    print(\"       工具调用助手 - 使用LLM平台API       \")\n    print(\"=\" * 60)\n    print(f\"使用模型: {DEFAULT_MODEL}\")\n    print(\"可用工具: 天气查询、时间查询、网络搜索、数学计算\")", "detail": "examples.tool_calling_assistant", "documentation": {}}, {"label": "print_header", "kind": 2, "importPath": "examples.tool_calling_assistant", "description": "examples.tool_calling_assistant", "peekOfCode": "def print_header():\n    \"\"\"打印助手标题\"\"\"\n    print(\"=\" * 60)\n    print(\"       工具调用助手 - 使用LLM平台API       \")\n    print(\"=\" * 60)\n    print(f\"使用模型: {DEFAULT_MODEL}\")\n    print(\"可用工具: 天气查询、时间查询、网络搜索、数学计算\")\n    print(\"输入 'exit' 或 'quit' 退出\")\n    print(\"输入 'clear' 清除聊天历史\")\n    print(\"=\" * 60)", "detail": "examples.tool_calling_assistant", "documentation": {}}, {"label": "execute_tool_call", "kind": 2, "importPath": "examples.tool_calling_assistant", "description": "examples.tool_calling_assistant", "peekOfCode": "def execute_tool_call(tool_call):\n    \"\"\"执行工具调用并返回结果\"\"\"\n    function_name = tool_call.function.name\n    function_args = json.loads(tool_call.function.arguments)\n    if function_name in tool_functions:\n        try:\n            result = tool_functions[function_name](**function_args)\n            return result\n        except Exception as e:\n            return f\"执行工具 {function_name} 时出错: {str(e)}\"", "detail": "examples.tool_calling_assistant", "documentation": {}}, {"label": "chat_with_tools", "kind": 2, "importPath": "examples.tool_calling_assistant", "description": "examples.tool_calling_assistant", "peekOfCode": "def chat_with_tools():\n    \"\"\"与支持工具调用的LLM进行交互的主函数\"\"\"\n    clear_screen()\n    print_header()\n    # 初始化聊天历史\n    messages = [\n        {\"role\": \"system\", \"content\": \"你是一个有用的AI助手，名叫小智。你可以使用各种工具来帮助用户。请根据需要使用工具，并清晰地解释你的回答。\"}\n    ]\n    while True:\n        # 获取用户输入", "detail": "examples.tool_calling_assistant", "documentation": {}}, {"label": "API_BASE_URL", "kind": 5, "importPath": "examples.tool_calling_assistant", "description": "examples.tool_calling_assistant", "peekOfCode": "API_BASE_URL = \"http://localhost:8000/v1\"\nAPI_KEY = \"dummy-api-key\"  # 如果不需要认证，可以是任意值\nDEFAULT_MODEL = \"llama3.2\"  # 默认使用的模型\n# 初始化OpenAI客户端\nclient = OpenAI(\n    base_url=API_BASE_URL,\n    api_key=API_KEY\n)\n# 定义工具函数\ndef get_weather(city, unit=\"celsius\"):", "detail": "examples.tool_calling_assistant", "documentation": {}}, {"label": "API_KEY", "kind": 5, "importPath": "examples.tool_calling_assistant", "description": "examples.tool_calling_assistant", "peekOfCode": "API_KEY = \"dummy-api-key\"  # 如果不需要认证，可以是任意值\nDEFAULT_MODEL = \"llama3.2\"  # 默认使用的模型\n# 初始化OpenAI客户端\nclient = OpenAI(\n    base_url=API_BASE_URL,\n    api_key=API_KEY\n)\n# 定义工具函数\ndef get_weather(city, unit=\"celsius\"):\n    \"\"\"", "detail": "examples.tool_calling_assistant", "documentation": {}}, {"label": "DEFAULT_MODEL", "kind": 5, "importPath": "examples.tool_calling_assistant", "description": "examples.tool_calling_assistant", "peekOfCode": "DEFAULT_MODEL = \"llama3.2\"  # 默认使用的模型\n# 初始化OpenAI客户端\nclient = OpenAI(\n    base_url=API_BASE_URL,\n    api_key=API_KEY\n)\n# 定义工具函数\ndef get_weather(city, unit=\"celsius\"):\n    \"\"\"\n    获取指定城市的天气信息（模拟）", "detail": "examples.tool_calling_assistant", "documentation": {}}, {"label": "client", "kind": 5, "importPath": "examples.tool_calling_assistant", "description": "examples.tool_calling_assistant", "peekOfCode": "client = OpenAI(\n    base_url=API_BASE_URL,\n    api_key=API_KEY\n)\n# 定义工具函数\ndef get_weather(city, unit=\"celsius\"):\n    \"\"\"\n    获取指定城市的天气信息（模拟）\n    在实际应用中，这里应该调用真实的天气API\n    \"\"\"", "detail": "examples.tool_calling_assistant", "documentation": {}}, {"label": "available_tools", "kind": 5, "importPath": "examples.tool_calling_assistant", "description": "examples.tool_calling_assistant", "peekOfCode": "available_tools = [\n    {\n        \"type\": \"function\",\n        \"function\": {\n            \"name\": \"get_weather\",\n            \"description\": \"获取指定城市的当前天气信息\",\n            \"parameters\": {\n                \"type\": \"object\",\n                \"properties\": {\n                    \"city\": {", "detail": "examples.tool_calling_assistant", "documentation": {}}, {"label": "tool_functions", "kind": 5, "importPath": "examples.tool_calling_assistant", "description": "examples.tool_calling_assistant", "peekOfCode": "tool_functions = {\n    \"get_weather\": get_weather,\n    \"get_current_time\": get_current_time,\n    \"search_web\": search_web,\n    \"calculate_expression\": calculate_expression\n}\ndef clear_screen():\n    \"\"\"清除终端屏幕\"\"\"\n    os.system('cls' if os.name == 'nt' else 'clear')\ndef print_header():", "detail": "examples.tool_calling_assistant", "documentation": {}}, {"label": "get_weather", "kind": 2, "importPath": "examples.tool_calling_assistant_jp", "description": "examples.tool_calling_assistant_jp", "peekOfCode": "def get_weather(city, unit=\"celsius\"):\n    \"\"\"\n    指定された都市の天気情報を取得（シミュレーション）\n    実際のアプリケーションでは、実際の天気APIを呼び出すべきです\n    \"\"\"\n    # 天気データのシミュレーション\n    weather_data = {\n        \"東京\": {\"condition\": \"晴れ\", \"temperature\": 25, \"humidity\": 40},\n        \"大阪\": {\"condition\": \"曇り\", \"temperature\": 28, \"humidity\": 65},\n        \"名古屋\": {\"condition\": \"小雨\", \"temperature\": 30, \"humidity\": 80},", "detail": "examples.tool_calling_assistant_jp", "documentation": {}}, {"label": "get_current_time", "kind": 2, "importPath": "examples.tool_calling_assistant_jp", "description": "examples.tool_calling_assistant_jp", "peekOfCode": "def get_current_time(timezone=\"Asia/Tokyo\"):\n    \"\"\"\n    現在の時刻を取得\n    実際のアプリケーションでは、異なるタイムゾーンを処理すべきです\n    \"\"\"\n    now = datetime.datetime.now()\n    return f\"現在の時刻は：{now.strftime('%Y-%m-%d %H:%M:%S')}（{timezone}タイムゾーン）\"\ndef search_web(query):\n    \"\"\"\n    ウェブ検索をシミュレート", "detail": "examples.tool_calling_assistant_jp", "documentation": {}}, {"label": "search_web", "kind": 2, "importPath": "examples.tool_calling_assistant_jp", "description": "examples.tool_calling_assistant_jp", "peekOfCode": "def search_web(query):\n    \"\"\"\n    ウェブ検索をシミュレート\n    実際のアプリケーションでは、実際の検索APIを呼び出すべきです\n    \"\"\"\n    return f\"「{query}」に関する検索結果（シミュレーション）：\\n1. {query}のウィキペディアページ\\n2. {query}に関する最新ニュース\\n3. {query}に関する学術研究\"\ndef calculate_expression(expression):\n    \"\"\"\n    数学式を計算\n    \"\"\"", "detail": "examples.tool_calling_assistant_jp", "documentation": {}}, {"label": "calculate_expression", "kind": 2, "importPath": "examples.tool_calling_assistant_jp", "description": "examples.tool_calling_assistant_jp", "peekOfCode": "def calculate_expression(expression):\n    \"\"\"\n    数学式を計算\n    \"\"\"\n    try:\n        # 警告：本番環境でevalを使用することは安全ではありません\n        # これはサンプルとしてのみ使用し、実際のアプリケーションではより安全な方法を使用すべきです\n        result = eval(expression)\n        return f\"式 {expression} の計算結果は {result} です\"\n    except Exception as e:", "detail": "examples.tool_calling_assistant_jp", "documentation": {}}, {"label": "clear_screen", "kind": 2, "importPath": "examples.tool_calling_assistant_jp", "description": "examples.tool_calling_assistant_jp", "peekOfCode": "def clear_screen():\n    \"\"\"ターミナル画面をクリアする\"\"\"\n    os.system('cls' if os.name == 'nt' else 'clear')\ndef print_header():\n    \"\"\"アシスタントのヘッダーを表示\"\"\"\n    print(\"=\" * 60)\n    print(\"       ツール呼び出しアシスタント - LLMプラットフォームAPI使用       \")\n    print(\"=\" * 60)\n    print(f\"使用モデル: {DEFAULT_MODEL}\")\n    print(\"利用可能なツール: 天気照会、時刻照会、ウェブ検索、数学計算\")", "detail": "examples.tool_calling_assistant_jp", "documentation": {}}, {"label": "print_header", "kind": 2, "importPath": "examples.tool_calling_assistant_jp", "description": "examples.tool_calling_assistant_jp", "peekOfCode": "def print_header():\n    \"\"\"アシスタントのヘッダーを表示\"\"\"\n    print(\"=\" * 60)\n    print(\"       ツール呼び出しアシスタント - LLMプラットフォームAPI使用       \")\n    print(\"=\" * 60)\n    print(f\"使用モデル: {DEFAULT_MODEL}\")\n    print(\"利用可能なツール: 天気照会、時刻照会、ウェブ検索、数学計算\")\n    print(\"'exit'または'quit'と入力して終了\")\n    print(\"'clear'と入力して会話履歴をクリア\")\n    print(\"=\" * 60)", "detail": "examples.tool_calling_assistant_jp", "documentation": {}}, {"label": "execute_tool_call", "kind": 2, "importPath": "examples.tool_calling_assistant_jp", "description": "examples.tool_calling_assistant_jp", "peekOfCode": "def execute_tool_call(tool_call):\n    \"\"\"ツール呼び出しを実行し、結果を返す\"\"\"\n    function_name = tool_call.function.name\n    function_args = json.loads(tool_call.function.arguments)\n    if function_name in tool_functions:\n        try:\n            result = tool_functions[function_name](**function_args)\n            return result\n        except Exception as e:\n            return f\"ツール {function_name} の実行中にエラーが発生しました: {str(e)}\"", "detail": "examples.tool_calling_assistant_jp", "documentation": {}}, {"label": "chat_with_tools", "kind": 2, "importPath": "examples.tool_calling_assistant_jp", "description": "examples.tool_calling_assistant_jp", "peekOfCode": "def chat_with_tools():\n    \"\"\"ツール呼び出しをサポートするLLMとの対話を行うメイン関数\"\"\"\n    clear_screen()\n    print_header()\n    # チャット履歴の初期化\n    messages = [\n        {\"role\": \"system\", \"content\": \"あなたは役立つAIアシスタントの「アイ」です。様々なツールを使用してユーザーを支援できます。必要に応じてツールを使用し、回答を明確に説明してください。\"}\n    ]\n    while True:\n        # ユーザー入力を取得", "detail": "examples.tool_calling_assistant_jp", "documentation": {}}, {"label": "API_BASE_URL", "kind": 5, "importPath": "examples.tool_calling_assistant_jp", "description": "examples.tool_calling_assistant_jp", "peekOfCode": "API_BASE_URL = \"http://localhost:8000/v1\"\nAPI_KEY = \"dummy-api-key\"  # 認証が必要ない場合は任意の値\nDEFAULT_MODEL = \"llama3.2\"  # デフォルトのモデル\n# OpenAIクライアントの初期化\nclient = OpenAI(\n    base_url=API_BASE_URL,\n    api_key=API_KEY\n)\n# ツール関数の定義\ndef get_weather(city, unit=\"celsius\"):", "detail": "examples.tool_calling_assistant_jp", "documentation": {}}, {"label": "API_KEY", "kind": 5, "importPath": "examples.tool_calling_assistant_jp", "description": "examples.tool_calling_assistant_jp", "peekOfCode": "API_KEY = \"dummy-api-key\"  # 認証が必要ない場合は任意の値\nDEFAULT_MODEL = \"llama3.2\"  # デフォルトのモデル\n# OpenAIクライアントの初期化\nclient = OpenAI(\n    base_url=API_BASE_URL,\n    api_key=API_KEY\n)\n# ツール関数の定義\ndef get_weather(city, unit=\"celsius\"):\n    \"\"\"", "detail": "examples.tool_calling_assistant_jp", "documentation": {}}, {"label": "DEFAULT_MODEL", "kind": 5, "importPath": "examples.tool_calling_assistant_jp", "description": "examples.tool_calling_assistant_jp", "peekOfCode": "DEFAULT_MODEL = \"llama3.2\"  # デフォルトのモデル\n# OpenAIクライアントの初期化\nclient = OpenAI(\n    base_url=API_BASE_URL,\n    api_key=API_KEY\n)\n# ツール関数の定義\ndef get_weather(city, unit=\"celsius\"):\n    \"\"\"\n    指定された都市の天気情報を取得（シミュレーション）", "detail": "examples.tool_calling_assistant_jp", "documentation": {}}, {"label": "client", "kind": 5, "importPath": "examples.tool_calling_assistant_jp", "description": "examples.tool_calling_assistant_jp", "peekOfCode": "client = OpenAI(\n    base_url=API_BASE_URL,\n    api_key=API_KEY\n)\n# ツール関数の定義\ndef get_weather(city, unit=\"celsius\"):\n    \"\"\"\n    指定された都市の天気情報を取得（シミュレーション）\n    実際のアプリケーションでは、実際の天気APIを呼び出すべきです\n    \"\"\"", "detail": "examples.tool_calling_assistant_jp", "documentation": {}}, {"label": "available_tools", "kind": 5, "importPath": "examples.tool_calling_assistant_jp", "description": "examples.tool_calling_assistant_jp", "peekOfCode": "available_tools = [\n    {\n        \"type\": \"function\",\n        \"function\": {\n            \"name\": \"get_weather\",\n            \"description\": \"指定された都市の現在の天気情報を取得します\",\n            \"parameters\": {\n                \"type\": \"object\",\n                \"properties\": {\n                    \"city\": {", "detail": "examples.tool_calling_assistant_jp", "documentation": {}}, {"label": "tool_functions", "kind": 5, "importPath": "examples.tool_calling_assistant_jp", "description": "examples.tool_calling_assistant_jp", "peekOfCode": "tool_functions = {\n    \"get_weather\": get_weather,\n    \"get_current_time\": get_current_time,\n    \"search_web\": search_web,\n    \"calculate_expression\": calculate_expression\n}\ndef clear_screen():\n    \"\"\"ターミナル画面をクリアする\"\"\"\n    os.system('cls' if os.name == 'nt' else 'clear')\ndef print_header():", "detail": "examples.tool_calling_assistant_jp", "documentation": {}}, {"label": "index", "kind": 2, "importPath": "examples.web_chat_app", "description": "examples.web_chat_app", "peekOfCode": "def index():\n    \"\"\"渲染主页\"\"\"\n    return HTML_TEMPLATE\***********('/api/chat', methods=['POST'])\ndef chat():\n    \"\"\"处理聊天请求（非流式）\"\"\"\n    data = request.json\n    messages = data.get('messages', [])\n    model = data.get('model', DEFAULT_MODEL)\n    try:", "detail": "examples.web_chat_app", "documentation": {}}, {"label": "chat", "kind": 2, "importPath": "examples.web_chat_app", "description": "examples.web_chat_app", "peekOfCode": "def chat():\n    \"\"\"处理聊天请求（非流式）\"\"\"\n    data = request.json\n    messages = data.get('messages', [])\n    model = data.get('model', DEFAULT_MODEL)\n    try:\n        response = client.chat.completions.create(\n            model=model,\n            messages=messages,\n            temperature=0.7,", "detail": "examples.web_chat_app", "documentation": {}}, {"label": "chat_stream", "kind": 2, "importPath": "examples.web_chat_app", "description": "examples.web_chat_app", "peekOfCode": "def chat_stream():\n    \"\"\"处理聊天请求（流式）\"\"\"\n    data = request.json\n    messages = data.get('messages', [])\n    model = data.get('model', DEFAULT_MODEL)\n    def generate():\n        try:\n            stream = client.chat.completions.create(\n                model=model,\n                messages=messages,", "detail": "examples.web_chat_app", "documentation": {}}, {"label": "API_BASE_URL", "kind": 5, "importPath": "examples.web_chat_app", "description": "examples.web_chat_app", "peekOfCode": "API_BASE_URL = \"http://localhost:8000/v1\"\nAPI_KEY = \"dummy-api-key\"  # 如果不需要认证，可以是任意值\nDEFAULT_MODEL = \"llama3.2\"  # 默认使用的模型\n# 初始化OpenAI客户端\nclient = OpenAI(\n    base_url=API_BASE_URL,\n    api_key=API_KEY\n)\n# 初始化Flask应用\napp = Flask(__name__)", "detail": "examples.web_chat_app", "documentation": {}}, {"label": "API_KEY", "kind": 5, "importPath": "examples.web_chat_app", "description": "examples.web_chat_app", "peekOfCode": "API_KEY = \"dummy-api-key\"  # 如果不需要认证，可以是任意值\nDEFAULT_MODEL = \"llama3.2\"  # 默认使用的模型\n# 初始化OpenAI客户端\nclient = OpenAI(\n    base_url=API_BASE_URL,\n    api_key=API_KEY\n)\n# 初始化Flask应用\napp = Flask(__name__)\n# 创建HTML模板", "detail": "examples.web_chat_app", "documentation": {}}, {"label": "DEFAULT_MODEL", "kind": 5, "importPath": "examples.web_chat_app", "description": "examples.web_chat_app", "peekOfCode": "DEFAULT_MODEL = \"llama3.2\"  # 默认使用的模型\n# 初始化OpenAI客户端\nclient = OpenAI(\n    base_url=API_BASE_URL,\n    api_key=API_KEY\n)\n# 初始化Flask应用\napp = Flask(__name__)\n# 创建HTML模板\nHTML_TEMPLATE = \"\"\"", "detail": "examples.web_chat_app", "documentation": {}}, {"label": "client", "kind": 5, "importPath": "examples.web_chat_app", "description": "examples.web_chat_app", "peekOfCode": "client = OpenAI(\n    base_url=API_BASE_URL,\n    api_key=API_KEY\n)\n# 初始化Flask应用\napp = Flask(__name__)\n# 创建HTML模板\nHTML_TEMPLATE = \"\"\"\n<!DOCTYPE html>\n<html lang=\"zh-CN\">", "detail": "examples.web_chat_app", "documentation": {}}, {"label": "app", "kind": 5, "importPath": "examples.web_chat_app", "description": "examples.web_chat_app", "peekOfCode": "app = Flask(__name__)\n# 创建HTML模板\nHTML_TEMPLATE = \"\"\"\n<!DOCTYPE html>\n<html lang=\"zh-CN\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>LLM平台聊天示例</title>\n    <style>", "detail": "examples.web_chat_app", "documentation": {}}, {"label": "HTML_TEMPLATE", "kind": 5, "importPath": "examples.web_chat_app", "description": "examples.web_chat_app", "peekOfCode": "HTML_TEMPLATE = \"\"\"\n<!DOCTYPE html>\n<html lang=\"zh-CN\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>LLM平台聊天示例</title>\n    <style>\n        body {\n            font-family: 'Arial', sans-serif;", "detail": "examples.web_chat_app", "documentation": {}}, {"label": "index", "kind": 2, "importPath": "examples.web_chat_app_jp", "description": "examples.web_chat_app_jp", "peekOfCode": "def index():\n    \"\"\"メインページをレンダリング\"\"\"\n    return HTML_TEMPLATE\***********('/api/chat', methods=['POST'])\ndef chat():\n    \"\"\"チャットリクエストを処理（非ストリーミング）\"\"\"\n    data = request.json\n    messages = data.get('messages', [])\n    model = data.get('model', DEFAULT_MODEL)\n    try:", "detail": "examples.web_chat_app_jp", "documentation": {}}, {"label": "chat", "kind": 2, "importPath": "examples.web_chat_app_jp", "description": "examples.web_chat_app_jp", "peekOfCode": "def chat():\n    \"\"\"チャットリクエストを処理（非ストリーミング）\"\"\"\n    data = request.json\n    messages = data.get('messages', [])\n    model = data.get('model', DEFAULT_MODEL)\n    try:\n        response = client.chat.completions.create(\n            model=model,\n            messages=messages,\n            temperature=0.7,", "detail": "examples.web_chat_app_jp", "documentation": {}}, {"label": "chat_stream", "kind": 2, "importPath": "examples.web_chat_app_jp", "description": "examples.web_chat_app_jp", "peekOfCode": "def chat_stream():\n    \"\"\"チャットリクエストを処理（ストリーミング）\"\"\"\n    data = request.json\n    messages = data.get('messages', [])\n    model = data.get('model', DEFAULT_MODEL)\n    def generate():\n        try:\n            stream = client.chat.completions.create(\n                model=model,\n                messages=messages,", "detail": "examples.web_chat_app_jp", "documentation": {}}, {"label": "API_BASE_URL", "kind": 5, "importPath": "examples.web_chat_app_jp", "description": "examples.web_chat_app_jp", "peekOfCode": "API_BASE_URL = \"http://localhost:8000/v1\"\nAPI_KEY = \"dummy-api-key\"  # 認証が必要ない場合は任意の値\nDEFAULT_MODEL = \"llama3.2\"  # デフォルトのモデル\n# OpenAIクライアントの初期化\nclient = OpenAI(\n    base_url=API_BASE_URL,\n    api_key=API_KEY\n)\n# Flaskアプリケーションの初期化\napp = Flask(__name__)", "detail": "examples.web_chat_app_jp", "documentation": {}}, {"label": "API_KEY", "kind": 5, "importPath": "examples.web_chat_app_jp", "description": "examples.web_chat_app_jp", "peekOfCode": "API_KEY = \"dummy-api-key\"  # 認証が必要ない場合は任意の値\nDEFAULT_MODEL = \"llama3.2\"  # デフォルトのモデル\n# OpenAIクライアントの初期化\nclient = OpenAI(\n    base_url=API_BASE_URL,\n    api_key=API_KEY\n)\n# Flaskアプリケーションの初期化\napp = Flask(__name__)\n# HTMLテンプレートの作成", "detail": "examples.web_chat_app_jp", "documentation": {}}, {"label": "DEFAULT_MODEL", "kind": 5, "importPath": "examples.web_chat_app_jp", "description": "examples.web_chat_app_jp", "peekOfCode": "DEFAULT_MODEL = \"llama3.2\"  # デフォルトのモデル\n# OpenAIクライアントの初期化\nclient = OpenAI(\n    base_url=API_BASE_URL,\n    api_key=API_KEY\n)\n# Flaskアプリケーションの初期化\napp = Flask(__name__)\n# HTMLテンプレートの作成\nHTML_TEMPLATE = \"\"\"", "detail": "examples.web_chat_app_jp", "documentation": {}}, {"label": "client", "kind": 5, "importPath": "examples.web_chat_app_jp", "description": "examples.web_chat_app_jp", "peekOfCode": "client = OpenAI(\n    base_url=API_BASE_URL,\n    api_key=API_KEY\n)\n# Flaskアプリケーションの初期化\napp = Flask(__name__)\n# HTMLテンプレートの作成\nHTML_TEMPLATE = \"\"\"\n<!DOCTYPE html>\n<html lang=\"ja\">", "detail": "examples.web_chat_app_jp", "documentation": {}}, {"label": "app", "kind": 5, "importPath": "examples.web_chat_app_jp", "description": "examples.web_chat_app_jp", "peekOfCode": "app = Flask(__name__)\n# HTMLテンプレートの作成\nHTML_TEMPLATE = \"\"\"\n<!DOCTYPE html>\n<html lang=\"ja\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>LLMプラットフォームチャット例</title>\n    <style>", "detail": "examples.web_chat_app_jp", "documentation": {}}, {"label": "HTML_TEMPLATE", "kind": 5, "importPath": "examples.web_chat_app_jp", "description": "examples.web_chat_app_jp", "peekOfCode": "HTML_TEMPLATE = \"\"\"\n<!DOCTYPE html>\n<html lang=\"ja\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>LLMプラットフォームチャット例</title>\n    <style>\n        body {\n            font-family: 'Arial', sans-serif;", "detail": "examples.web_chat_app_jp", "documentation": {}}]